stages:
  - test
  - build
  - deploy
  - deploy_gray
  - deploy_online

variables:
  NAMESPACE: "devops"
  REGISTRY: "registry-dev.youle.game"
  NAMESPACE_ONLINE: "topjoy"
  REGISTRY_ONLINE: "registry.cn-hangzhou.aliyuncs.com"

coverage:
  stage: test
  image: mirror.youle.game/golang:1.23
  tags:
    - docker
  except:
    - tags
  script:
    - go env -w GOPROXY=https://goproxy.cn,direct
    - go fmt $(go list ./... | grep -v /vendor/)
    - go vet $(go list ./... | awk 'NR!=1 { print }')
    - go test -coverprofile=./coverage.txt -covermode=atomic -gcflags=all=-l  $(go list ./... | awk 'NR!=1 { print }')
    - total=$(go tool cover -func=./coverage.txt | tail -1)
    - echo ${total}
    - coverage=$(echo ${total} | grep -P '\d+\.\d+(?=\%)' -o)

build:
  stage: build
  image: mirror.youle.game/docker:latest
  only:
    - master
    - release
  tags:
    - docker
  script:
    - docker build -t $REGISTRY/$NAMESPACE/zeus:$CI_COMMIT_SHORT_SHA -t $REGISTRY_ONLINE/$NAMESPACE_ONLINE/zeus:$CI_COMMIT_SHORT_SHA .
    - echo $CI_COMMIT_SHORT_SHA
    - docker login -u $REGISTRY_USERNAME -p $REGISTRY_PASSWORD $REGISTRY
    - docker push $REGISTRY/$NAMESPACE/zeus:$CI_COMMIT_SHORT_SHA
    - docker rmi $REGISTRY/$NAMESPACE/zeus:$CI_COMMIT_SHORT_SHA
    - docker login -u $REGISTRY_ONLINE_USERNAME -p $REGISTRY_ONLINE_PASSWORD $REGISTRY_ONLINE
    - docker push $REGISTRY_ONLINE/$NAMESPACE_ONLINE/zeus:$CI_COMMIT_SHORT_SHA
    - docker rmi $REGISTRY_ONLINE/$NAMESPACE_ONLINE/zeus:$CI_COMMIT_SHORT_SHA

deploy:
  stage: deploy
  image: mirror.youle.game/prophet/kubectl
  only:
    - master
  tags:
    - docker
  environment:
    name: youle
  script:
    - kubectl config view
    - export IMAGE_NAME=$REGISTRY/$NAMESPACE/zeus:$CI_COMMIT_SHORT_SHA
    - sed -i "s|#IMAGE_NAME|$IMAGE_NAME|g" specs/dev/deployment.yaml
    - kubectl apply -f specs/dev/configmap.yaml
    - kubectl apply -f specs/dev/deployment.yaml

deploy_prerelease:
  stage: deploy
  image: mirror.youle.game/prophet/kubectl
  only:
    - master
  when: manual
  tags:
    - docker
  environment:
    name: youle
  script:
    - kubectl config view
    - export IMAGE_NAME=$REGISTRY/$NAMESPACE/zeus:$CI_COMMIT_SHORT_SHA
    - sed -i "s|#IMAGE_NAME|$IMAGE_NAME|g" specs/prerelease/deployment.yaml
    - kubectl apply -f specs/prerelease/configmap.yaml
    - kubectl apply -f specs/prerelease/deployment.yaml

deploy_gray:
  stage: deploy_gray
  image: mirror.youle.game/prophet/kubectl
  only:
    - master
  when: manual
  tags:
    - docker
  environment:
    name: alicloud-us-online
  script:
    - kubectl config view
    - export IMAGE_NAME=$REGISTRY_ONLINE/$NAMESPACE_ONLINE/zeus:$CI_COMMIT_SHORT_SHA
    - sed -i "s|#IMAGE_NAME|$IMAGE_NAME|g" specs/gray/deployment.yaml
    - kubectl apply -f specs/gray/secret.yaml
    - kubectl apply -f specs/gray/configmap.yaml
    - kubectl apply -f specs/gray/deployment.yaml
    - kubectl apply -f specs/gray/ingress.yaml

deploy_revert_gray:
  stage: deploy_gray
  image: mirror.youle.game/prophet/kubectl
  only:
    - master
  when: manual
  tags:
    - docker
  environment:
    name: alicloud-us-online
  script:
    - kubectl config view
    - kubectl apply -f specs/gray/origin_ingress.yaml

deploy_online_us-east:
  stage: deploy_online
  image: mirror.youle.game/prophet/kubectl
  only:
    - master
  when: manual
  tags:
    - docker
  environment:
    name: alicloud-us-online
  script:
    - kubectl config view
    - export IMAGE_NAME=$REGISTRY_ONLINE/$NAMESPACE_ONLINE/zeus:$CI_COMMIT_SHORT_SHA
    - sed -i "s|#IMAGE_NAME|$IMAGE_NAME|g" specs/online_us-east/deployment.yaml
    - sed -i "s|#IMAGE_NAME|$IMAGE_NAME|g" specs/online_us-east/deployment_api.yaml
    - kubectl apply -f specs/online_us-east/configmap.yaml
    - kubectl apply -f specs/online_us-east/secret.yaml
    - kubectl apply -f specs/online_us-east/deployment.yaml
    - kubectl apply -f specs/gray/origin_ingress.yaml
    - kubectl apply -f specs/online_us-east/deployment_api.yaml

deploy_online_jp:
  stage: deploy_online
  image: mirror.youle.game/prophet/kubectl
  only:
    - master
  when: manual
  tags:
    - docker
  environment:
    name: alicloud-jp-online
  script:
    - kubectl config view
    - export IMAGE_NAME=$REGISTRY_ONLINE/$NAMESPACE_ONLINE/zeus:$CI_COMMIT_SHORT_SHA
    - export ENVIRONMENT=online_jp
    - sed -i "s|#IMAGE_NAME|$IMAGE_NAME|g" specs/$ENVIRONMENT/deployment.yaml
    - sed -i "s|#IMAGE_NAME|$IMAGE_NAME|g" specs/$ENVIRONMENT/deployment_api.yaml
    - kubectl apply -f specs/$ENVIRONMENT/configmap.yaml
    - kubectl apply -f specs/$ENVIRONMENT/secret.yaml
    - kubectl apply -f specs/$ENVIRONMENT/deployment.yaml
    - kubectl apply -f specs/$ENVIRONMENT/deployment_api.yaml

deploy_online_hz:
  stage: deploy_online
  image: mirror.youle.game/prophet/kubectl
  only:
    - master
  when: manual
  tags:
    - docker
  environment:
    name: alicloud-hz-online
  script:
    - kubectl config view
    - export IMAGE_NAME=$REGISTRY_ONLINE/$NAMESPACE_ONLINE/zeus:$CI_COMMIT_SHORT_SHA
    - sed -i "s|#IMAGE_NAME|$IMAGE_NAME|g" specs/online_hz/deployment.yaml
    - sed -i "s|#IMAGE_NAME|$IMAGE_NAME|g" specs/online_hz/deployment_api.yaml
    - kubectl apply -f specs/online_hz/configmap.yaml
    - kubectl apply -f specs/online_hz/secret.yaml
    - kubectl apply -f specs/online_hz/deployment.yaml
    - kubectl apply -f specs/online_hz/deployment_api.yaml
