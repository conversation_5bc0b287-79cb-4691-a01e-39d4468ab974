# Zeus API 接口概览

## 🎮 游戏服务平台 API

Zeus 是一个综合性的游戏服务平台，为游戏开发者提供完整的后端服务解决方案。

## 📋 主要功能模块

### 1. 👤 用户管理 (User Management)
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 用户注册 | POST | `/user/register` | 新用户注册 |
| 用户登录 | POST | `/user/login` | 用户登录验证 |
| 第三方登录 | POST | `/user/third-login` | 支持多种第三方登录 |
| 获取用户信息 | POST | `/user/info` | 获取用户详细信息 |
| 绑定第三方 | POST | `/user/bind-third` | 绑定第三方账号 |
| 解绑第三方 | POST | `/user/unbind-third` | 解绑第三方账号 |
| 用户注销 | POST | `/user/unregister` | 用户账号注销 |

### 2. 💰 支付系统 (Payment System)
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 支付宝支付 | POST | `/order/ali-exchange` | 创建支付宝订单 |
| 支付宝验证 | POST | `/order/ali-verify` | 验证支付宝支付结果 |
| 微信支付 | POST | `/order/wechat-exchange` | 创建微信支付订单 |
| 微信验证 | POST | `/order/wechat-verify` | 验证微信支付结果 |
| Google支付 | POST | `/order/google-exchange` | 创建Google Play订单 |
| iOS支付 | POST | `/order/ios-exchange` | 创建App Store订单 |
| iOS验证 | POST | `/order/ios-verify` | 验证App Store支付 |

### 3. 🛡️ 防沉迷系统 (Anti-Addiction)
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 实名认证 | POST | `/anti-addiction/identity-check` | 用户实名认证 |
| 支付限制 | POST | `/anti-addiction/pay-limit` | 检查支付限制 |
| 游戏时长检查 | POST | `/anti-addiction/playable` | 检查是否可以游戏 |

### 4. 🚫 反垃圾系统 (Anti-Spam)
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 文本检测 | POST | `/antispam/text-check` | 检测文本内容合规性 |

### 5. 🎯 游戏管理 (Game Management)
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 获取游戏信息 | POST | `/game/info` | 获取游戏基本信息 |
| 角色数据上传 | POST | `/role/save` | 上传角色数据 |

### 6. 💳 第三方支付 (Third Party Payment)
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 第三方登录 | POST | `/third-pay/login` | 第三方支付平台登录 |
| 创建订单 | POST | `/third-pay/order` | 创建第三方支付订单 |
| 订单查询 | POST | `/third-pay/query` | 查询订单状态 |
| 支付回调 | POST | `/third-pay/notify` | 支付结果通知 |

### 7. 🔄 订阅服务 (Subscription)
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| Google订阅 | POST | `/subscription/google-exchange` | 创建Google订阅 |
| Google订阅验证 | POST | `/subscription/google-verify` | 验证Google订阅 |
| iOS订阅 | POST | `/subscription/ios-exchange` | 创建iOS订阅 |
| iOS订阅验证 | POST | `/subscription/ios-verify` | 验证iOS订阅 |

### 8. 🔧 管理后台 API (Admin API)
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 管理员登录 | GET | `/api/v1/login` | 管理员登录 |
| 游戏管理 | GET/POST/PUT | `/api/v1/game` | 游戏信息管理 |
| 项目管理 | GET/POST/PUT | `/api/v1/project` | 项目配置管理 |
| 订单管理 | GET/POST | `/api/v1/orders` | 订单查询和管理 |
| 用户管理 | GET | `/api/v1/user` | 用户信息管理 |
| 统计数据 | GET/POST | `/api/v1/orders/statistics` | 订单统计数据 |

## 🔐 认证方式

### API Key 认证
```http
Authorization: Bearer YOUR_API_KEY
```

### 签名认证
大部分SDK接口使用签名认证，需要在请求中包含：
- `appid`: 应用ID
- `timestamp`: 时间戳
- `sign`: 签名

## 📊 响应格式

### 成功响应
```json
{
    "error_no": 0,
    "message": "success",
    "result": {
        // 具体业务数据
    }
}
```

### 错误响应
```json
{
    "error_no": 1001,
    "message": "参数错误"
}
```

## 🌐 环境信息

- **开发环境**: `http://localhost:8000`
- **文档地址**: `http://localhost:8000/swagger/index.html`
- **健康检查**: `http://localhost:8000/healthz`

## 📚 快速开始

1. **启动服务**
   ```bash
   go run main.go
   ```

2. **查看API文档**
   ```bash
   # 方式1: 浏览器访问
   open http://localhost:8000/swagger/index.html
   
   # 方式2: 使用脚本启动文档服务
   ./start-swagger.sh
   ```

3. **测试接口**
   ```bash
   # 健康检查
   curl http://localhost:8000/healthz
   ```

## 🔗 相关链接

- [完整API文档](./SWAGGER_README.md)
- [项目README](./README.md)
- [Swagger YAML](./docs/swagger.yaml)
- [Swagger JSON](./docs/swagger.json)
