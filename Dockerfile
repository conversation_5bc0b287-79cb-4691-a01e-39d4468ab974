FROM mirror.youle.game/golang:1.23-alpine as builder

WORKDIR /app/
COPY . .
RUN GOPROXY=https://goproxy.cn,direct go get
RUN go build -o zeus main.go

FROM registry-dev.youle.game/common/alpine:3.16-tz
COPY --from=builder /app/zeus /app/zeus
COPY migrations /app/migrations
COPY GeoLite2-Country.mmdb /app/GeoLite2-Country.mmdb

COPY del_account_docs /app/del_account_docs
WORKDIR /app/
EXPOSE      8000
ENTRYPOINT  [ "/app/zeus" ]
