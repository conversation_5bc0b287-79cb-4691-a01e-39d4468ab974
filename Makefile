# Zeus API Makefile

.PHONY: help build run swagger-install swagger-gen swagger-serve clean

# 默认目标
help:
	@echo "Zeus API 项目管理命令："
	@echo "  build          - 编译项目"
	@echo "  run            - 运行项目"
	@echo "  swagger-install - 安装 swag 工具"
	@echo "  swagger-gen    - 生成 Swagger 文档"
	@echo "  swagger-serve  - 启动 Swagger UI 服务"
	@echo "  clean          - 清理编译文件"

# 编译项目
build:
	@echo "编译 Zeus API..."
	go build -o zeus main.go

# 运行项目
run:
	@echo "启动 Zeus API 服务..."
	go run main.go

# 安装 swag 工具
swagger-install:
	@echo "安装 swag 工具..."
	go install github.com/swaggo/swag/cmd/swag@latest

# 生成 Swagger 文档
swagger-gen:
	@echo "生成 Swagger 文档..."
	swag init

# 启动 Swagger UI 服务 (需要 Python)
swagger-serve:
	@echo "启动 Swagger UI 服务在 http://localhost:8080"
	@echo "请在浏览器中打开 http://localhost:8080/swagger-ui.html"
	python3 -m http.server 8080

# 清理编译文件
clean:
	@echo "清理编译文件..."
	rm -f zeus
	rm -f *.exe

# 检查依赖
deps:
	@echo "检查并下载依赖..."
	go mod tidy
	go mod download

# 格式化代码
fmt:
	@echo "格式化代码..."
	go fmt ./...

# 运行测试
test:
	@echo "运行测试..."
	go test ./...

# 一键部署开发环境
dev: deps swagger-gen run

# 显示项目信息
info:
	@echo "项目信息："
	@echo "  名称: Zeus API Server"
	@echo "  版本: 1.0"
	@echo "  Go版本: $(shell go version)"
	@echo "  Swagger文档: http://localhost:8000/swagger/index.html"
	@echo "  健康检查: http://localhost:8000/healthz"
