# Zeus 支付系统 API 文档

## 📋 概述

Zeus支付系统提供了完整的多平台支付解决方案，支持Google Play、App Store、微信支付、支付宝、Xsol<PERSON>、第三方支付等多种支付方式，以及订阅服务管理。

## 🔗 基础信息

- **Base URL**: `http://localhost:8000`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 📊 通用响应格式

### 成功响应
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        // 具体业务数据
    }
}
```

### 错误响应
```json
{
    "error_no": 1003,
    "message": "参数缺失"
}
```

## 🔢 错误码说明

| 错误码 | 说明 | 详细描述 | 解决方案 |
|--------|------|----------|----------|
| 0 | 成功 | 请求处理成功 | 无需处理 |
| 1003 | 参数缺失 | 必填参数未提供或为空 | 检查请求参数，确保所有必填字段都已提供 |
| 1004 | 数据库更新错误 | 数据库操作失败 | 重试请求，如持续失败请联系技术支持 |
| 1005 | 参数无效 | 参数格式错误或值不在有效范围内 | 检查参数格式和取值范围 |
| 1006 | 登录失败 | 用户认证失败 | 检查用户凭证，重新登录 |
| 1007 | 注册失败 | 用户注册过程中出现错误 | 检查注册信息，重试注册 |
| 1008 | 账号已被封号 | 用户账号被系统封禁 | 联系客服处理账号问题 |
| 1009 | 支付关闭 | 支付功能已关闭或维护中 | 等待支付功能恢复或联系管理员 |
| 1010 | 用户不存在 | 指定的用户ID不存在 | 检查用户ID是否正确 |
| 1011 | 订单不存在 | 指定的订单ID不存在 | 检查订单ID是否正确 |
| 1012 | 订单重复 | 订单已存在，不能重复创建 | 使用现有订单或创建新的订单 |
| 1013 | 签名验证失败 | 请求签名不正确 | 检查签名算法和密钥配置 |
| 1014 | 支付验证失败 | 第三方支付平台验证失败 | 检查支付凭证和配置信息 |
| 1015 | 订单已过期 | 订单超过有效期 | 创建新的订单 |
| 1016 | 支付金额不匹配 | 实际支付金额与订单金额不符 | 检查订单金额和支付金额 |
| 1017 | 商品不存在 | 指定的商品ID不存在 | 检查商品ID是否正确 |
| 1018 | 余额不足 | 账户余额不足以完成支付 | 充值后重试 |
| 1019 | 支付渠道异常 | 支付渠道暂时不可用 | 稍后重试或使用其他支付方式 |
| 1020 | 订单状态异常 | 订单状态不允许当前操作 | 检查订单状态 |

---

## 1. 🤖 Google Play 支付

### 1.1 创建Google订单

**功能描述**: 创建Google Play支付订单

**前置条件**:
- 用户已登录
- Google支付已开启
- 提供有效的商品信息

**方法签名**: `POST /order/google-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | com.game.coin.pack1 |
| price | float64 | 是 | 价格(分) | 599 |
| device | string | 否 | 设备信息 | Samsung Galaxy S21 |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| extend | string | 否 | 扩展信息 | {"custom": "data"} |
| vip | string | 否 | VIP等级 | 5 |
| level | string | 否 | 角色等级 | 50 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/google-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "com.game.coin.pack1",
    "price": 599,
    "device": "Samsung Galaxy S21",
    "role_name": "勇敢的战士",
    "server_id": "server_001",
    "server_name": "服务器1区"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "google_pay_key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...",
        "order_id": "GO20250110**********"
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功，其他值表示失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.google_pay_key | string | Google Play支付验证公钥，用于客户端验证支付结果 |
| result.order_id | string | 系统生成的订单ID，格式为GO+时间戳+随机数 |

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

**注意事项**:
- **价格单位**: 价格参数单位为分，例如5.99元应传入599
- **Google Pay Key**: 返回的google_pay_key是RSA公钥，用于客户端验证Google Play的支付回执
- **订单ID**: order_id是唯一标识，用于后续的订单查询、验证和回调
- **商品ID**: product_id必须与Google Play Console中配置的商品ID完全一致
- **用户验证**: 确保user_id对应的用户已经在系统中注册并登录
- **支付配置**: 需要在管理后台正确配置Google Play的相关参数（应用包名、服务账号密钥等）
- **环境要求**: 支持生产环境和测试环境，测试时可使用Google Play的测试账号
- **超时处理**: 订单创建后有效期为30分钟，超时后需要重新创建
- **重复创建**: 相同参数的订单在短时间内不能重复创建，建议间隔至少1分钟
- **设备限制**: 仅支持Android设备，iOS设备请使用iOS支付接口

---

### 1.2 验证Google订单

**功能描述**: 验证Google Play支付结果

**前置条件**:
- 订单已创建
- 用户已完成Google Play支付

**方法签名**: `POST /v2/order/google-verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| developer_payload | string | 是 | 开发者载荷(订单ID) | GO20250110********** |

**请求示例**:
```bash
curl -X POST http://localhost:8000/v2/order/google-verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "developer_payload": "GO20250110**********"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功"
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示验证成功，其他值表示验证失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |

**状态码**:
- 200: 验证成功
- 400: 验证失败

**注意事项**:
- **验证时机**: 必须在用户完成Google Play支付后立即调用此接口
- **developer_payload**: 必须是创建订单时返回的order_id
- **验证机制**: 系统会向Google Play服务器验证支付凭证的真实性
- **幂等性**: 同一订单可以重复验证，但只会发放一次道具
- **超时处理**: 验证请求超时时间为30秒，超时后可重试
- **错误处理**: 验证失败时不会发放道具，需要检查错误信息并重试
- **安全性**: 验证过程中会检查订单状态、用户身份、商品信息等
- **回调通知**: 验证成功后会触发游戏服务器的道具发放回调

---

## 2. 🍎 iOS App Store 支付

### 2.1 创建iOS订单

**功能描述**: 创建App Store支付订单

**前置条件**:
- 用户已登录
- iOS支付已开启
- 提供有效的商品信息

**方法签名**: `POST /order/ios-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | com.game.coin.pack1 |
| price | float64 | 是 | 价格(分) | 599 |
| device | string | 否 | 设备信息 | iPhone 14 Pro |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| extend | string | 否 | 扩展信息 | {"custom": "data"} |
| vip | string | 否 | VIP等级 | 5 |
| level | string | 否 | 角色等级 | 50 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/ios-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "com.game.coin.pack1",
    "price": 599,
    "device": "iPhone 14 Pro"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "IO20250110**********"
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功，其他值表示失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.order_id | string | 系统生成的订单ID，格式为IO+时间戳+随机数 |

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

**注意事项**:
- **价格单位**: 价格参数单位为分，例如5.99美元应传入599
- **商品ID**: product_id必须与App Store Connect中配置的商品ID完全一致
- **用户验证**: 确保user_id对应的用户已经在系统中注册并登录
- **设备限制**: 仅支持iOS设备，Android设备请使用Google Play支付接口
- **沙盒环境**: 支持App Store沙盒环境测试，使用测试账号进行支付
- **订单有效期**: 订单创建后有效期为30分钟，超时后需要重新创建
- **重复创建**: 相同参数的订单在短时间内不能重复创建，建议间隔至少1分钟
- **配置要求**: 需要在管理后台正确配置App Store的相关参数（Bundle ID、共享密钥等）

---

### 2.2 验证iOS订单

**功能描述**: 验证App Store支付结果

**前置条件**:
- 订单已创建
- 用户已完成App Store支付

**方法签名**: `POST /order/ios-verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| order_id | string | 是 | 订单ID | IO20250110********** |
| paper | string | 是 | 支付凭证 | base64编码的receipt |
| pay_currency | string | 是 | 支付货币 | USD |
| pay_amount | string | 是 | 支付金额 | 5.99 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/ios-verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "order_id": "IO20250110**********",
    "paper": "ewoJInNpZ25hdHVyZSIgPSAiQW...",
    "pay_currency": "USD",
    "pay_amount": "5.99"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "success"
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示验证成功，其他值表示验证失败 |
| message | string | 返回消息，成功时为"success"，失败时为具体错误信息 |

**状态码**:
- 200: 验证成功
- 400: 验证失败

**注意事项**:
- **Receipt验证**: paper参数必须是从App Store获取的有效receipt，需要base64编码
- **沙盒环境**: 系统会自动检测并使用相应的App Store验证环境（生产或沙盒）
- **金额校验**: pay_amount必须与App Store返回的实际支付金额一致
- **货币校验**: pay_currency必须与App Store返回的货币类型一致
- **幂等性**: 同一订单可以重复验证，但只会发放一次道具
- **超时处理**: 向App Store验证的超时时间为30秒，超时后可重试
- **自动续费**: 对于订阅类商品，系统会自动处理续费逻辑
- **收据验证**: 系统会验证收据的完整性、签名和有效期
- **防重放**: 系统会检查收据是否已被使用过，防止重复验证

---

## 3. 💰 微信支付

### 3.1 创建微信订单

**功能描述**: 创建微信支付订单

**前置条件**:
- 用户已登录
- 微信支付已开启
- 提供有效的商品信息

**方法签名**: `POST /order/wechat-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | coin_pack_1 |
| product_name | string | 是 | 商品名称 | 金币礼包 |
| price | float64 | 是 | 价格(分) | 599 |
| device | string | 否 | 设备信息 | Android |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| extend | string | 否 | 扩展信息 | {"custom": "data"} |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/wechat-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "coin_pack_1",
    "product_name": "金币礼包",
    "price": 599
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "WX20250110**********",
        "prepay_id": "wx20250110********************1234",
        "app_id": "wxd930ea5d5a258f4f",
        "partner_id": "1900000109",
        "package": "Sign=WXPay",
        "nonce_str": "1add1a30ac87aa2db72f57a2375d8fec",
        "timestamp": "1641801600",
        "sign": "C380BEC2BFD727A4B6845133519F3AD6"
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示订单创建成功，其他值表示创建失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.order_id | string | 系统生成的订单ID，格式为WX+时间戳+随机数 |
| result.prepay_id | string | 微信预支付交易会话标识，用于后续调起支付接口 |
| result.app_id | string | 微信开放平台审核通过的应用APPID |
| result.partner_id | string | 微信支付分配的商户号 |
| result.package | string | 扩展字段，固定值"Sign=WXPay" |
| result.nonce_str | string | 随机字符串，不长于32位 |
| result.timestamp | string | 时间戳，标准北京时间，时区为东八区 |
| result.sign | string | 签名，用于验证支付请求的合法性 |

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

**注意事项**:
- **价格单位**: 价格参数单位为分，例如5.99元应传入599
- **商品名称**: product_name必须提供，用于微信支付页面显示
- **预支付ID**: prepay_id有效期为2小时，超时后需要重新获取
- **签名验证**: 返回的sign用于客户端验证，确保数据完整性
- **时间戳**: timestamp用于防重放攻击，客户端需要验证时间有效性
- **应用配置**: 需要在微信开放平台配置正确的APPID和商户号
- **回调地址**: pay_notify_url必须是可访问的HTTPS地址，用于接收支付结果
- **订单有效期**: 订单创建后有效期为30分钟，超时后需要重新创建
- **重复创建**: 相同参数的订单在短时间内不能重复创建
- **网络环境**: 支持生产环境和沙盒环境，测试时使用微信支付沙盒

---

### 3.2 验证微信订单

**功能描述**: 验证微信支付结果

**前置条件**:
- 订单已创建
- 用户已完成微信支付

**方法签名**: `POST /order/wechat-verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| order_id | string | 是 | 订单ID | WX20250110********** |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/wechat-verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "order_id": "WX20250110**********"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功"
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示验证成功，其他值表示验证失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |

**状态码**:
- 200: 验证成功
- 400: 验证失败

**注意事项**:
- **验证时机**: 必须在用户完成微信支付后立即调用此接口
- **订单状态**: 系统会向微信服务器查询订单的实际支付状态
- **幂等性**: 同一订单可以重复验证，但只会发放一次道具
- **超时处理**: 验证请求超时时间为30秒，超时后可重试
- **错误处理**: 验证失败时不会发放道具，需要检查错误信息并重试
- **安全性**: 验证过程中会检查订单状态、用户身份、商品信息等
- **回调通知**: 验证成功后会触发游戏服务器的道具发放回调
- **支付状态**: 只有支付状态为"SUCCESS"的订单才会验证成功

---

## 4. 🔵 支付宝支付

### 4.1 创建支付宝订单

**功能描述**: 创建支付宝支付订单

**前置条件**:
- 用户已登录
- 支付宝支付已开启
- 提供有效的商品信息

**方法签名**: `POST /order/ali-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | coin_pack_1 |
| product_name | string | 是 | 商品名称 | 金币礼包 |
| price | float64 | 是 | 价格(分) | 599 |
| device | string | 否 | 设备信息 | Android |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| extend | string | 否 | 扩展信息 | {"custom": "data"} |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/ali-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "coin_pack_1",
    "product_name": "金币礼包",
    "price": 599
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "AL20250110**********",
        "pay_url": "alipays://platformapi/startapp?saId=10000007&qrcode=https%3A%2F%2Fqr.alipay.com%2Fbax08431..."
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示订单创建成功，其他值表示创建失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.order_id | string | 系统生成的订单ID，格式为AL+时间戳+随机数 |
| result.pay_url | string | 支付宝支付链接，用于调起支付宝APP或跳转支付页面 |

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

**注意事项**:
- **价格单位**: 价格参数单位为分，例如5.99元应传入599
- **商品名称**: product_name必须提供，用于支付宝支付页面显示
- **支付链接**: pay_url支持alipays://协议调起支付宝APP，也支持HTTPS链接
- **链接有效期**: pay_url有效期为2小时，超时后需要重新获取
- **应用配置**: 需要在支付宝开放平台配置正确的APPID和私钥
- **回调地址**: pay_notify_url必须是可访问的HTTPS地址，用于接收支付结果
- **订单有效期**: 订单创建后有效期为30分钟，超时后需要重新创建
- **重复创建**: 相同参数的订单在短时间内不能重复创建
- **沙盒环境**: 支持支付宝沙盒环境测试，使用沙盒账号进行支付
- **签名算法**: 使用RSA2签名算法确保数据安全

---

### 4.2 验证支付宝订单

**功能描述**: 验证支付宝支付结果

**前置条件**:
- 订单已创建
- 用户已完成支付宝支付

**方法签名**: `POST /order/ali-verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| order_id | string | 是 | 订单ID | AL20250110********** |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/ali-verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "order_id": "AL20250110**********"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功"
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示验证成功，其他值表示验证失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |

**状态码**:
- 200: 验证成功
- 400: 验证失败

**注意事项**:
- **验证时机**: 必须在用户完成支付宝支付后立即调用此接口
- **订单状态**: 系统会向支付宝服务器查询订单的实际支付状态
- **幂等性**: 同一订单可以重复验证，但只会发放一次道具
- **超时处理**: 验证请求超时时间为30秒，超时后可重试
- **错误处理**: 验证失败时不会发放道具，需要检查错误信息并重试
- **安全性**: 验证过程中会检查订单状态、用户身份、商品信息等
- **回调通知**: 验证成功后会触发游戏服务器的道具发放回调
- **支付状态**: 只有支付状态为"TRADE_SUCCESS"的订单才会验证成功
- **签名验证**: 系统会验证支付宝返回数据的签名确保数据完整性

---

## 5. 🌍 Xsolla 支付

### 5.1 Xsolla用户登录

**功能描述**: Xsolla平台的用户登录验证

**前置条件**:
- Xsolla项目已配置
- 提供有效的用户信息

**方法签名**: `POST /xsolla/login`

**Query参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| merchant_id | string | 是 | 商户ID | 12345 |
| project_id | string | 是 | 项目ID | 67890 |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| user.id | string | 是 | 用户角色ID | role_12345 |
| user.name | string | 否 | 用户名称 | PlayerName |
| user.email | string | 否 | 用户邮箱 | <EMAIL> |
| user.phone | string | 否 | 用户手机号 | +********** |
| user.country | string | 否 | 用户国家 | US |
| user.ip | string | 否 | 用户IP地址 | ************* |
| user.zip | string | 否 | 邮政编码 | 12345 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/xsolla/login?merchant_id=12345&project_id=67890" \
  -H "Content-Type: application/json" \
  -d '{
    "user": {
      "id": "role_12345",
      "name": "PlayerName",
      "email": "<EMAIL>",
      "phone": "+**********",
      "country": "US",
      "ip": "*************",
      "zip": "12345"
    }
  }'
```

**返回示例**:
```json
{
    "attributes": [
        {
            "key": "level",
            "value": "50"
        }
    ],
    "user": {
        "id": "role_12345",
        "name": "勇敢的战士"
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| attributes | array | 用户属性列表 |
| attributes[].key | string | 属性键名 |
| attributes[].value | string | 属性值 |
| user.id | string | 用户角色ID |
| user.name | string | 角色名称 |

**状态码**:
- 200: 登录成功
- 400: 参数错误
- 404: 用户不存在

**注意事项**:
- **角色验证**: 系统会根据user.id查找对应的角色信息
- **项目验证**: 系统会验证project_id是否存在且已启用
- **商户验证**: 系统会验证merchant_id的有效性
- **角色属性**: 返回的attributes包含角色的等级等信息
- **角色名称**: 返回的user.name是角色在游戏中的名称
- **数据库选择**: 系统会根据project_id自动选择对应的游戏数据库
- **错误处理**: 如果角色不存在，返回404状态码
- **安全性**: 接口会验证请求的合法性和参数完整性

---

### 5.2 Xsolla支付回调

**功能描述**: 接收Xsolla支付成功的回调通知

**前置条件**:
- Xsolla项目已配置
- 支付已完成

**方法签名**: `POST /xsolla/pay`

**Query参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| merchant_id | string | 是 | 商户ID | 12345 |
| project_id | string | 是 | 项目ID | 67890 |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| notification_type | string | 是 | 通知类型 | order_paid |
| items | array | 是 | 商品列表 | 见下方示例 |
| order | object | 是 | 订单信息 | 见下方示例 |
| user | object | 是 | 用户信息 | 见下方示例 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/xsolla/pay?merchant_id=12345&project_id=67890" \
  -H "Content-Type: application/json" \
  -d '{
    "notification_type": "order_paid",
    "items": [
      {
        "order_id": "XS20250110**********",
        "sku": "coin_pack_1",
        "name": "金币礼包",
        "price": 5.99,
        "currency": "USD"
      }
    ],
    "order": {
      "id": 123456,
      "mode": "live",
      "currency_type": "real",
      "currency": "USD",
      "amount": "5.99",
      "status": "paid",
      "platform": "pc"
    },
    "user": {
      "external_id": "user_10001",
      "email": "<EMAIL>"
    }
  }'
```

**返回示例**:
```http
HTTP/1.1 204 No Content
Content-Length: 0
Date: Mon, 10 Jan 2025 10:00:00 GMT
Server: Zeus-Payment-Server/1.0
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| HTTP状态码 | int | 204表示处理成功，无返回内容 |
| Content-Length | int | 内容长度，固定为0 |
| Date | string | 服务器响应时间 |
| Server | string | 服务器标识信息 |

**状态码**:
- 204: 处理成功
- 400: 参数错误
- 500: 处理失败

**注意事项**:
- **回调性质**: 这是Xsolla服务器主动调用的回调接口，不是客户端主动请求
- **返回要求**: 必须返回HTTP 204状态码表示处理成功，其他状态码Xsolla会认为处理失败
- **批量处理**: 支持批量商品处理，一次回调可能包含多个商品
- **幂等性**: 同一笔订单可能会收到多次回调，需要做幂等性处理
- **签名验证**: 需要验证Xsolla的回调签名确保请求来源的合法性
- **超时处理**: 回调处理时间不能超过30秒，否则Xsolla会重试
- **重试机制**: 如果返回非204状态码，Xsolla会按指数退避算法重试
- **数据格式**: 请求体为JSON格式，包含订单、商品、用户等完整信息
- **安全要求**: 建议验证merchant_id和project_id参数确保是正确的项目
- **日志记录**: 建议记录所有回调请求用于问题排查和对账

---

## 6. 🔄 第三方支付 (ThirdPay)

### 6.1 第三方支付登录

**功能描述**: 第三方支付平台的用户登录

**前置条件**:
- 提供有效的角色ID和项目ID

**方法签名**: `POST /third-pay/login`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| role_id | string | 是 | 角色ID | role_12345 |
| project_id | string | 是 | 项目ID | project_001 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/third-pay/login \
  -H "Content-Type: application/json" \
  -d '{
    "role_id": "role_12345",
    "project_id": "project_001"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "token": "third_pay_token_12345",
        "user_info": {
            "role_id": "role_12345",
            "project_id": "project_001"
        }
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示登录成功，其他值表示登录失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.token | string | 第三方支付平台的访问令牌，用于后续API调用 |
| result.user_info.role_id | string | 角色ID，与请求参数一致 |
| result.user_info.project_id | string | 项目ID，与请求参数一致 |

**状态码**:
- 200: 登录成功
- 400: 登录失败

**注意事项**:
- **Token有效期**: 返回的token有效期通常为24小时，过期后需要重新登录
- **角色验证**: 系统会验证role_id是否存在且有效
- **项目验证**: 系统会验证project_id是否存在且已启用
- **安全存储**: token应安全存储，不要在日志中记录
- **并发限制**: 同一角色同时只能有一个有效token
- **重复登录**: 重复登录会使之前的token失效
- **权限控制**: token仅用于第三方支付相关操作，不能用于其他业务

---

### 6.2 创建第三方支付订单

**功能描述**: 创建第三方支付订单

**前置条件**:
- 用户已登录第三方支付平台
- 提供有效的支付信息

**方法签名**: `POST /third-pay/order`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| project_id | string | 是 | 项目ID | project_001 |
| pay_way | int | 是 | 支付方式 | 1 |
| region | string | 否 | 地区 | CN |
| role_id | string | 否 | 角色ID | role_12345 |
| server_id | string | 否 | 服务器ID | server_001 |
| product_id | string | 否 | 商品ID | product_001 |
| product_name | string | 否 | 商品名称 | 金币礼包 |
| price | float64 | 否 | 价格 | 5.99 |
| currency | string | 否 | 货币 | USD |
| pay_return_url | string | 否 | 支付返回地址 | https://game.com/return |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| timestamp | string | 否 | 时间戳 | 1641801600 |
| sign | string | 否 | 签名 | abc123def456 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/third-pay/order \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "project_001",
    "pay_way": 1,
    "role_id": "role_12345",
    "product_id": "product_001",
    "product_name": "金币礼包",
    "price": 5.99,
    "currency": "USD"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "TP20250110**********",
        "pay_url": "https://pay.thirdparty.com/pay?order_id=TP20250110**********"
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示订单创建成功，其他值表示创建失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.order_id | string | 系统生成的订单ID，格式为TP+时间戳+随机数 |
| result.pay_url | string | 第三方支付页面链接，用户需要跳转到此链接完成支付 |

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

**注意事项**:
- **支付方式**: pay_way参数决定使用的第三方支付渠道，需要确保该渠道已配置
- **价格货币**: price和currency必须匹配，确保货币单位正确
- **地区限制**: region参数影响可用的支付方式，某些支付方式仅在特定地区可用
- **签名验证**: 如果提供sign参数，系统会验证签名的正确性
- **回调地址**: pay_notify_url用于接收支付结果通知，必须是可访问的HTTPS地址
- **返回地址**: pay_return_url是用户支付完成后的跳转地址
- **订单有效期**: 订单创建后有效期为2小时，超时后需要重新创建
- **支付链接**: pay_url有效期为30分钟，过期后需要重新获取

---

### 6.3 查询第三方支付订单

**功能描述**: 查询第三方支付订单状态

**前置条件**:
- 订单已创建
- 提供有效的查询参数

**方法签名**: `POST /third-pay/query`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| project_id | string | 是 | 项目ID | project_001 |
| pay_way | int | 是 | 支付方式 | 1 |
| start_date_time | string | 否 | 开始时间 | 2025-01-01T00:00:00Z |
| end_date_time | string | 否 | 结束时间 | 2025-01-31T23:59:59Z |
| pay_id | string | 否 | 支付ID | pay_12345 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/third-pay/query \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "project_001",
    "pay_way": 1,
    "start_date_time": "2025-01-01T00:00:00Z",
    "end_date_time": "2025-01-31T23:59:59Z"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": [
        {
            "order_id": "TP20250110**********",
            "pay_id": "pay_12345",
            "status": "paid",
            "amount": 5.99,
            "currency": "USD",
            "create_time": "2025-01-10T10:00:00Z",
            "pay_time": "2025-01-10T10:05:00Z"
        }
    ]
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示查询成功，其他值表示查询失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result[].order_id | string | 订单ID |
| result[].pay_id | string | 第三方支付平台的支付ID |
| result[].status | string | 支付状态：pending(待支付)、paid(已支付)、failed(支付失败)、cancelled(已取消) |
| result[].amount | float | 支付金额 |
| result[].currency | string | 货币类型 |
| result[].create_time | string | 订单创建时间，ISO 8601格式 |
| result[].pay_time | string | 支付完成时间，ISO 8601格式 |

**状态码**:
- 200: 查询成功
- 400: 查询失败

**注意事项**:
- **时间范围**: start_date_time和end_date_time的时间跨度不能超过31天
- **查询限制**: 单次查询最多返回1000条记录
- **状态说明**: status字段的值由第三方支付平台决定，不同平台可能有不同的状态值
- **时区处理**: 所有时间字段都使用UTC时区
- **分页查询**: 如果结果较多，建议使用时间范围进行分页查询
- **缓存机制**: 查询结果可能有5分钟的缓存延迟
- **权限验证**: 只能查询当前项目下的订单信息

---

## 7. 📱 小游戏支付

### 7.1 微信小游戏支付

**功能描述**: 创建微信小游戏支付订单

**前置条件**:
- 小游戏用户已登录
- 微信小游戏支付已开启

**方法签名**: `POST /minigame/order/wechat-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 小游戏AppID | wx**********abcdef |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | coin_pack_1 |
| price | float64 | 是 | 价格(分) | 599 |
| pay_type | string | 是 | 支付类型 | jsapi |
| env | int | 是 | 环境(0:正式,1:沙盒) | 0 |
| open_id | string | 是 | 用户OpenID | openid_12345 |
| server_id | string | 否 | 服务器ID | server_001 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |

**请求示例**:
```bash
curl -X POST http://localhost:8000/minigame/order/wechat-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "wx**********abcdef",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "coin_pack_1",
    "price": 599,
    "pay_type": "jsapi",
    "env": 0,
    "open_id": "openid_12345"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "prepay_id": "wx20250110********************1234",
        "order_id": "MG20250110**********"
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示订单创建成功，其他值表示创建失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.prepay_id | string | 微信预支付交易会话标识，用于调起小游戏支付接口 |
| result.order_id | string | 系统生成的订单ID，格式为MG+时间戳+随机数 |

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

**注意事项**:
- **价格单位**: 价格参数单位为分，例如5.99元应传入599
- **小游戏环境**: env参数决定使用正式环境还是沙盒环境
- **OpenID验证**: open_id必须是有效的微信小游戏用户OpenID
- **支付类型**: pay_type通常为"jsapi"，适用于小游戏内支付
- **预支付ID**: prepay_id有效期为2小时，超时后需要重新获取
- **AppID配置**: 需要在微信小游戏后台配置正确的支付参数
- **回调地址**: pay_notify_url必须是可访问的HTTPS地址
- **订单有效期**: 订单创建后有效期为30分钟，超时后需要重新创建
- **用户验证**: 确保user_id和role_id对应的用户已在系统中注册
- **商品配置**: product_id必须在系统中已配置且有效

---

### 7.2 抖音小游戏支付

**功能描述**: 创建抖音小游戏支付订单

**前置条件**:
- 小游戏用户已登录
- 抖音小游戏支付已开启

**方法签名**: `POST /minigame/order/douyin-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 小游戏AppID | tt**********abcdef |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | coin_pack_1 |
| price | float64 | 是 | 价格(分) | 599 |
| pay_type | string | 是 | 支付类型 | virtual |
| env | int | 是 | 环境(0:正式,1:沙盒) | 0 |
| open_id | string | 是 | 用户OpenID | openid_12345 |
| server_id | string | 否 | 服务器ID | server_001 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/minigame/order/douyin-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "tt**********abcdef",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "coin_pack_1",
    "price": 599,
    "pay_type": "virtual",
    "env": 0,
    "open_id": "openid_12345"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "DY20250110**********",
        "sign_data": {
            "mode": "live",
            "offer_id": "offer_12345",
            "buy_quantity": 1,
            "env": 0,
            "currency_type": "CNY",
            "platform": "android",
            "product_id": "coin_pack_1",
            "goods_price": 599,
            "out_trade_no": "DY20250110**********"
        }
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示订单创建成功，其他值表示创建失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.order_id | string | 系统生成的订单ID，格式为DY+时间戳+随机数 |
| result.sign_data.mode | string | 支付模式，live表示正式环境，sandbox表示沙盒环境 |
| result.sign_data.offer_id | string | 抖音小游戏的offer ID |
| result.sign_data.buy_quantity | int | 购买数量，通常为1 |
| result.sign_data.env | int | 环境标识，0表示正式环境，1表示沙盒环境 |
| result.sign_data.currency_type | string | 货币类型，如CNY、USD等 |
| result.sign_data.platform | string | 平台标识，如android、ios等 |
| result.sign_data.product_id | string | 商品ID，与请求参数一致 |
| result.sign_data.goods_price | int | 商品价格，单位为分 |
| result.sign_data.out_trade_no | string | 外部交易号，与order_id一致 |

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

**注意事项**:
- **价格单位**: 价格参数单位为分，例如5.99元应传入599
- **小游戏环境**: env参数决定使用正式环境还是沙盒环境
- **OpenID验证**: open_id必须是有效的抖音小游戏用户OpenID
- **支付类型**: pay_type通常为"virtual"，适用于虚拟商品支付
- **签名数据**: sign_data包含调起抖音支付所需的完整参数
- **AppID配置**: 需要在抖音小游戏后台配置正确的支付参数
- **Offer配置**: offer_id必须在抖音开发者平台正确配置
- **订单有效期**: 订单创建后有效期为30分钟，超时后需要重新创建
- **用户验证**: 确保user_id和role_id对应的用户已在系统中注册
- **商品配置**: product_id必须在系统中已配置且有效
- **平台限制**: 仅支持抖音小游戏平台，不支持其他平台

---

## 8. 🔄 订阅服务 (Subscription)

### 8.1 创建iOS订阅

**功能描述**: 创建iOS App Store订阅服务

**前置条件**:
- 用户已登录
- iOS订阅服务已开启
- 提供有效的订阅商品信息

**方法签名**: `POST /v2/subscription/ios-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 订阅商品ID | com.game.vip.monthly |
| price | float64 | 是 | 价格(分) | 2999 |
| device | string | 否 | 设备信息 | iPhone 14 Pro |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| extend | string | 否 | 扩展信息 | {"subscription_type": "monthly"} |
| vip | string | 否 | VIP等级 | 5 |
| level | string | 否 | 角色等级 | 50 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/v2/subscription/ios-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "com.game.vip.monthly",
    "price": 2999,
    "device": "iPhone 14 Pro"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "SUB_IO20250110**********"
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示订阅订单创建成功，其他值表示创建失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.order_id | string | 系统生成的订阅订单ID，格式为SUB_IO+时间戳+随机数 |

**状态码**:
- 200: 订阅订单创建成功
- 400: 创建失败

**注意事项**:
- **价格单位**: 价格参数单位为分，例如29.99美元应传入2999
- **订阅商品**: product_id必须是在App Store Connect中配置的订阅类型商品
- **用户验证**: 确保user_id和role_id对应的用户已在系统中注册
- **设备信息**: device参数有助于统计和分析，建议提供
- **扩展信息**: extend参数可以包含订阅类型、周期等额外信息
- **回调地址**: pay_notify_url用于接收App Store的订阅状态变更通知
- **订阅周期**: 系统支持月度、季度、年度等多种订阅周期
- **自动续费**: iOS订阅默认开启自动续费功能
- **沙盒测试**: 支持App Store沙盒环境进行订阅测试
- **订单唯一性**: 每次创建都会生成新的订单ID，即使参数相同

---

### 8.2 验证iOS订阅

**功能描述**: 验证iOS App Store订阅支付结果

**前置条件**:
- 订阅订单已创建
- 用户已完成App Store订阅支付

**方法签名**: `POST /v2/subscription/ios-verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| order_id | string | 否 | 订单ID | SUB_IO20250110********** |
| paper | string | 是 | 支付凭证 | base64编码的receipt |
| pay_currency | string | 是 | 支付货币 | USD |
| pay_amount | string | 是 | 支付金额 | 29.99 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/v2/subscription/ios-verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "order_id": "SUB_IO20250110**********",
    "paper": "ewoJInNpZ25hdHVyZSIgPSAiQW...",
    "pay_currency": "USD",
    "pay_amount": "29.99"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "success"
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示验证成功，其他值表示验证失败 |
| message | string | 返回消息，成功时为"success"，失败时为具体错误信息 |

**状态码**:
- 200: 验证成功
- 400: 验证失败

**注意事项**:
- **Receipt验证**: paper参数必须是从App Store获取的有效receipt，需要base64编码
- **订单匹配**: order_id必须是之前创建的有效订阅订单ID
- **金额校验**: pay_amount必须与App Store返回的实际支付金额一致
- **沙盒环境**: 系统会自动检测并使用相应的App Store验证环境
- **验证时机**: 必须在用户完成App Store订阅支付后立即调用
- **幂等性**: 同一订单可以重复验证，但只会发放一次订阅权益
- **超时处理**: 验证请求超时时间为30秒，超时后可重试
- **错误处理**: 验证失败时不会激活订阅，需要检查错误信息并重试
- **安全性**: 验证过程中会检查订单状态、用户身份、订阅商品信息等
- **回调通知**: 验证成功后会触发游戏服务器的订阅激活回调

---

### 8.3 获取订阅信息 (管理后台)

**功能描述**: 管理后台获取订阅信息列表

**前置条件**:
- 管理员已登录
- 提供有效的JWT Token

**方法签名**: `GET /api/v1/subscriptions`

**请求参数**: Query参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| game_id | int | 是 | 游戏ID | 1 |
| page_size | int | 否 | 每页数量 | 10 |
| page_number | int | 否 | 页码 | 1 |
| order_id | string | 否 | 订单ID | SUB_IO20250110********** |
| user_id | int | 否 | 用户ID | 10001 |
| pay_status | int | 否 | 支付状态 | 1 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/subscriptions?game_id=1&page_size=10&page_number=1" \
  -H "Authorization: Bearer jwt_token_here"
```

**返回示例**:
```json
{
    "code": 0,
    "message": "成功",
    "result": {
        "data": [
            {
                "id": 1001,
                "order_id": "SUB_IO20250110**********",
                "game_id": 1,
                "user_id": 10001,
                "account": "**********",
                "role_id": "role_12345",
                "product_id": "com.game.vip.monthly",
                "price": 2999,
                "pay_status": 1,
                "pay_time": "2025-01-10T10:05:00Z",
                "expires_date": **********,
                "auto_renew_status": 1,
                "create_time": "2025-01-10T10:00:00Z"
            }
        ],
        "total": 1,
        "page": 1,
        "page_size": 10
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 错误码，0表示获取成功，其他值表示获取失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.data[].id | int | 订阅记录的唯一ID |
| result.data[].order_id | string | 订阅订单ID |
| result.data[].game_id | int | 游戏ID |
| result.data[].user_id | int | 用户ID |
| result.data[].account | string | 用户账号 |
| result.data[].role_id | string | 角色ID |
| result.data[].product_id | string | 订阅商品ID |
| result.data[].price | int | 订阅价格，单位为分 |
| result.data[].pay_status | int | 支付状态，1表示已支付，0表示未支付 |
| result.data[].pay_time | string | 支付时间，ISO 8601格式 |
| result.data[].expires_date | int | 订阅到期时间，Unix时间戳 |
| result.data[].auto_renew_status | int | 自动续费状态，1表示开启，0表示关闭 |
| result.data[].create_time | string | 订单创建时间，ISO 8601格式 |
| result.total | int | 总记录数 |
| result.page | int | 当前页码 |
| result.page_size | int | 每页记录数 |

**状态码**:
- 200: 获取成功
- 400: 获取失败
- 401: 未授权

**注意事项**:
- **管理员权限**: 此接口仅限管理员使用，需要有效的JWT Token
- **分页查询**: 支持分页查询，默认每页10条记录，最大100条
- **游戏过滤**: game_id参数用于过滤特定游戏的订阅信息
- **状态过滤**: pay_status参数可以过滤不同支付状态的订阅
- **时间格式**: 所有时间字段都使用UTC时区
- **到期时间**: expires_date使用Unix时间戳格式，便于程序处理
- **自动续费**: auto_renew_status反映用户的自动续费设置
- **数据权限**: 只能查看当前管理员有权限的游戏数据
- **查询性能**: 大量数据时建议使用分页和过滤条件

---

### 8.4 订阅补单 (管理后台)

**功能描述**: 管理后台手动触发订阅补单

**前置条件**:
- 管理员已登录
- 提供有效的JWT Token
- 订阅订单存在

**方法签名**: `POST /api/v1/subscriptions/notify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| game_id | int | 是 | 游戏ID | 1 |
| order_id | string | 是 | 订单ID | SUB_IO20250110********** |

**请求示例**:
```bash
curl -X POST http://localhost:8000/api/v1/subscriptions/notify \
  -H "Authorization: Bearer jwt_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "game_id": 1,
    "order_id": "SUB_IO20250110**********"
  }'
```

**返回示例**:
```json
{
    "code": 0,
    "message": "成功",
    "result": ""
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 错误码，0表示补单成功，其他值表示补单失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result | string | 结果数据，补单成功时为空字符串 |

**状态码**:
- 200: 补单成功
- 400: 补单失败
- 401: 未授权

**注意事项**:
- **管理员权限**: 此接口仅限管理员使用，需要有效的JWT Token
- **订单验证**: 系统会验证order_id是否存在且属于指定的游戏
- **补单逻辑**: 手动触发订阅权益发放，通常用于处理异常情况
- **幂等性**: 同一订单可以重复补单，但不会重复发放权益
- **权益发放**: 补单成功后会立即发放对应的订阅权益
- **日志记录**: 所有补单操作都会记录详细日志用于审计
- **状态检查**: 只有已支付但未发放权益的订单才能补单
- **游戏通知**: 补单成功后会通知对应的游戏服务器

---

### 8.5 获取续订信息 (管理后台)

**功能描述**: 管理后台获取订阅续订信息

**前置条件**:
- 管理员已登录
- 提供有效的JWT Token

**方法签名**: `GET /api/v1/subscriptions/renew`

**请求参数**: Query参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| game_id | int | 是 | 游戏ID | 1 |
| order_id | string | 是 | 原始订单ID | SUB_IO20250110********** |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/subscriptions/renew?game_id=1&order_id=SUB_IO20250110**********" \
  -H "Authorization: Bearer jwt_token_here"
```

**返回示例**:
```json
{
    "code": 0,
    "message": "成功",
    "result": {
        "data": [
            {
                "id": 1002,
                "original_order_id": "SUB_IO20250110**********",
                "renew_order_id": "SUB_IO202502101234567891",
                "renew_time": "2025-02-10T10:00:00Z",
                "expires_date": 1646265600,
                "auto_renew_status": 1
            }
        ],
        "total": 1
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 错误码，0表示获取成功，其他值表示获取失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.data[].id | int | 续订记录的唯一ID |
| result.data[].original_order_id | string | 原始订阅订单ID |
| result.data[].renew_order_id | string | 续订订单ID |
| result.data[].renew_time | string | 续订时间，ISO 8601格式 |
| result.data[].expires_date | int | 续订后的到期时间，Unix时间戳 |
| result.data[].auto_renew_status | int | 自动续费状态，1表示开启，0表示关闭 |
| result.total | int | 总续订记录数 |

**状态码**:
- 200: 获取成功
- 400: 获取失败
- 401: 未授权

**注意事项**:
- **管理员权限**: 此接口仅限管理员使用，需要有效的JWT Token
- **订单关联**: 通过original_order_id查找所有相关的续订记录
- **续订历史**: 返回指定订阅的完整续订历史记录
- **时间格式**: renew_time使用ISO 8601格式，expires_date使用Unix时间戳
- **自动续费**: auto_renew_status反映续订时的自动续费设置
- **数据完整性**: 包含续订的完整信息，便于分析和统计
- **权限验证**: 只能查看当前管理员有权限的游戏续订数据
- **续订链**: 可以通过续订记录追踪完整的订阅续订链

---

## 9. 🔧 模拟支付 (MockPay)

### 9.1 创建模拟支付订单

**功能描述**: 创建模拟支付订单，用于测试环境

**前置条件**:
- 测试环境
- 模拟支付已开启

**方法签名**: `POST /order/mockpay-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | test_product_1 |
| price | float64 | 是 | 价格(分) | 599 |
| device | string | 否 | 设备信息 | Test Device |
| role_name | string | 否 | 角色名称 | 测试角色 |
| server_id | string | 否 | 服务器ID | test_server |
| server_name | string | 否 | 服务器名称 | 测试服务器 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/mockpay-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "test_product_1",
    "price": 599
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "MOCK20250110**********",
        "pay_url": "mock://pay/success"
    }
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示订单创建成功，其他值表示创建失败 |
| message | string | 返回消息，成功时为"成功"，失败时为具体错误信息 |
| result.order_id | string | 系统生成的模拟订单ID，格式为MOCK+时间戳+随机数 |
| result.pay_url | string | 模拟支付链接，固定为"mock://pay/success" |

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

**注意事项**:
- **测试专用**: 仅用于测试环境，不能在生产环境使用
- **自动成功**: 创建订单后会自动模拟支付成功，无需真实支付
- **无真实费用**: 不会产生任何真实的费用扣除
- **完整流程**: 模拟完整的支付流程，包括订单创建、支付、回调等
- **道具发放**: 支付成功后会正常发放游戏道具，与真实支付一致
- **订单记录**: 模拟订单会正常记录在系统中，便于测试验证
- **环境检测**: 系统会检测当前环境，只有测试环境才允许使用
- **开发调试**: 主要用于开发阶段的功能测试和调试
- **集成测试**: 适用于支付流程的集成测试和自动化测试
- **数据隔离**: 模拟支付数据与真实支付数据完全隔离

---

## 📝 总结

Zeus支付系统提供了完整的多平台支付解决方案：

### 💳 支持的支付方式
- **Google Play**: Android应用内购买
- **App Store**: iOS应用内购买
- **微信支付**: 移动端微信支付
- **支付宝**: 移动端支付宝支付
- **Xsolla**: 国际支付解决方案
- **第三方支付**: 自定义支付渠道

### 🔐 安全特性
- 订单签名验证
- 支付凭证校验
- 重复订单检测
- 安全回调机制

### 🌍 多平台支持
- iOS/Android原生应用
- 微信小游戏
- 网页游戏
- 国际化支付

如需更多详细信息或技术支持，请联系开发团队。
