# Zeus 支付系统 API 文档

## 📋 概述

Zeus支付系统提供了完整的多平台支付解决方案，支持Google Play、App Store、微信支付、支付宝、<PERSON>sol<PERSON>、第三方支付等多种支付方式，以及订阅服务管理。

## 🔗 基础信息

- **Base URL**: `http://localhost:8000`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 📊 通用响应格式

### 成功响应
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        // 具体业务数据
    }
}
```

### 错误响应
```json
{
    "error_no": 1003,
    "message": "参数缺失"
}
```

## 🔢 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1003 | 参数缺失 |
| 1004 | 数据库更新错误 |
| 1005 | 参数无效 |
| 1006 | 登录失败 |
| 1007 | 注册失败 |
| 1008 | 账号已被封号 |
| 1009 | 支付关闭 |
| 1010 | 用户不存在 |
| 1011 | 订单不存在 |
| 1012 | 订单重复 |

---

## 1. 🤖 Google Play 支付

### 1.1 创建Google订单

**功能描述**: 创建Google Play支付订单

**前置条件**:
- 用户已登录
- Google支付已开启
- 提供有效的商品信息

**方法签名**: `POST /order/google-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | com.game.coin.pack1 |
| price | float64 | 是 | 价格(分) | 599 |
| device | string | 否 | 设备信息 | Samsung Galaxy S21 |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| extend | string | 否 | 扩展信息 | {"custom": "data"} |
| vip | string | 否 | VIP等级 | 5 |
| level | string | 否 | 角色等级 | 50 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/google-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "com.game.coin.pack1",
    "price": 599,
    "device": "Samsung Galaxy S21",
    "role_name": "勇敢的战士",
    "server_id": "server_001",
    "server_name": "服务器1区"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "google_pay_key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...",
        "order_id": "GO20250110**********"
    }
}
```

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

**注意事项**:
- 返回的google_pay_key用于Google Play支付验证
- order_id用于后续订单查询和验证
- 价格单位为分，需要乘以100

---

### 1.2 验证Google订单

**功能描述**: 验证Google Play支付结果

**前置条件**:
- 订单已创建
- 用户已完成Google Play支付

**方法签名**: `POST /v2/order/google-verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| developer_payload | string | 是 | 开发者载荷(订单ID) | GO20250110********** |

**请求示例**:
```bash
curl -X POST http://localhost:8000/v2/order/google-verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "developer_payload": "GO20250110**********"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功"
}
```

**状态码**:
- 200: 验证成功
- 400: 验证失败

---

## 2. 🍎 iOS App Store 支付

### 2.1 创建iOS订单

**功能描述**: 创建App Store支付订单

**前置条件**:
- 用户已登录
- iOS支付已开启
- 提供有效的商品信息

**方法签名**: `POST /order/ios-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | com.game.coin.pack1 |
| price | float64 | 是 | 价格(分) | 599 |
| device | string | 否 | 设备信息 | iPhone 14 Pro |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| extend | string | 否 | 扩展信息 | {"custom": "data"} |
| vip | string | 否 | VIP等级 | 5 |
| level | string | 否 | 角色等级 | 50 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/ios-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "com.game.coin.pack1",
    "price": 599,
    "device": "iPhone 14 Pro"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "IO20250110**********"
    }
}
```

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

---

### 2.2 验证iOS订单

**功能描述**: 验证App Store支付结果

**前置条件**:
- 订单已创建
- 用户已完成App Store支付

**方法签名**: `POST /order/ios-verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| order_id | string | 是 | 订单ID | IO20250110********** |
| paper | string | 是 | 支付凭证 | base64编码的receipt |
| pay_currency | string | 是 | 支付货币 | USD |
| pay_amount | string | 是 | 支付金额 | 5.99 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/ios-verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "order_id": "IO20250110**********",
    "paper": "ewoJInNpZ25hdHVyZSIgPSAiQW...",
    "pay_currency": "USD",
    "pay_amount": "5.99"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "success"
}
```

**状态码**:
- 200: 验证成功
- 400: 验证失败

---

## 3. 💰 微信支付

### 3.1 创建微信订单

**功能描述**: 创建微信支付订单

**前置条件**:
- 用户已登录
- 微信支付已开启
- 提供有效的商品信息

**方法签名**: `POST /order/wechat-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | coin_pack_1 |
| product_name | string | 是 | 商品名称 | 金币礼包 |
| price | float64 | 是 | 价格(分) | 599 |
| device | string | 否 | 设备信息 | Android |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| extend | string | 否 | 扩展信息 | {"custom": "data"} |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/wechat-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "coin_pack_1",
    "product_name": "金币礼包",
    "price": 599
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "WX20250110**********",
        "prepay_id": "wx20250110********************1234",
        "app_id": "wxd930ea5d5a258f4f",
        "partner_id": "1900000109",
        "package": "Sign=WXPay",
        "nonce_str": "1add1a30ac87aa2db72f57a2375d8fec",
        "timestamp": "1641801600",
        "sign": "C380BEC2BFD727A4B6845133519F3AD6"
    }
}
```

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

---

### 3.2 验证微信订单

**功能描述**: 验证微信支付结果

**前置条件**:
- 订单已创建
- 用户已完成微信支付

**方法签名**: `POST /order/wechat-verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| order_id | string | 是 | 订单ID | WX20250110********** |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/wechat-verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "order_id": "WX20250110**********"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功"
}
```

**状态码**:
- 200: 验证成功
- 400: 验证失败

---

## 4. 🔵 支付宝支付

### 4.1 创建支付宝订单

**功能描述**: 创建支付宝支付订单

**前置条件**:
- 用户已登录
- 支付宝支付已开启
- 提供有效的商品信息

**方法签名**: `POST /order/ali-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | coin_pack_1 |
| product_name | string | 是 | 商品名称 | 金币礼包 |
| price | float64 | 是 | 价格(分) | 599 |
| device | string | 否 | 设备信息 | Android |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| extend | string | 否 | 扩展信息 | {"custom": "data"} |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/ali-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "coin_pack_1",
    "product_name": "金币礼包",
    "price": 599
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "AL20250110**********",
        "pay_url": "alipays://platformapi/startapp?saId=10000007&qrcode=https%3A%2F%2Fqr.alipay.com%2Fbax08431..."
    }
}
```

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

---

### 4.2 验证支付宝订单

**功能描述**: 验证支付宝支付结果

**前置条件**:
- 订单已创建
- 用户已完成支付宝支付

**方法签名**: `POST /order/ali-verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| order_id | string | 是 | 订单ID | AL20250110********** |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/ali-verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "order_id": "AL20250110**********"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功"
}
```

**状态码**:
- 200: 验证成功
- 400: 验证失败

---

## 5. 🌍 Xsolla 支付

### 5.1 Xsolla支付回调

**功能描述**: 接收Xsolla支付成功的回调通知

**前置条件**:
- Xsolla项目已配置
- 支付已完成

**方法签名**: `POST /xsolla/pay`

**Query参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| merchant_id | string | 是 | 商户ID | 12345 |
| project_id | string | 是 | 项目ID | 67890 |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| notification_type | string | 是 | 通知类型 | order_paid |
| items | array | 是 | 商品列表 | 见下方示例 |
| order | object | 是 | 订单信息 | 见下方示例 |
| user | object | 是 | 用户信息 | 见下方示例 |

**请求示例**:
```bash
curl -X POST "http://localhost:8000/xsolla/pay?merchant_id=12345&project_id=67890" \
  -H "Content-Type: application/json" \
  -d '{
    "notification_type": "order_paid",
    "items": [
      {
        "order_id": "XS20250110**********",
        "sku": "coin_pack_1",
        "name": "金币礼包",
        "price": 5.99,
        "currency": "USD"
      }
    ],
    "order": {
      "id": 123456,
      "mode": "live",
      "currency_type": "real",
      "currency": "USD",
      "amount": "5.99",
      "status": "paid",
      "platform": "pc"
    },
    "user": {
      "external_id": "user_10001",
      "email": "<EMAIL>"
    }
  }'
```

**返回示例**:
```
HTTP 204 No Content
```

**状态码**:
- 204: 处理成功
- 400: 参数错误
- 500: 处理失败

**注意事项**:
- 这是Xsolla服务器主动调用的回调接口
- 返回204状态码表示处理成功
- 支持批量商品处理

---

## 6. 🔄 第三方支付 (ThirdPay)

### 6.1 第三方支付登录

**功能描述**: 第三方支付平台的用户登录

**前置条件**:
- 提供有效的角色ID和项目ID

**方法签名**: `POST /third-pay/login`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| role_id | string | 是 | 角色ID | role_12345 |
| project_id | string | 是 | 项目ID | project_001 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/third-pay/login \
  -H "Content-Type: application/json" \
  -d '{
    "role_id": "role_12345",
    "project_id": "project_001"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "token": "third_pay_token_12345",
        "user_info": {
            "role_id": "role_12345",
            "project_id": "project_001"
        }
    }
}
```

**状态码**:
- 200: 登录成功
- 400: 登录失败

---

### 6.2 创建第三方支付订单

**功能描述**: 创建第三方支付订单

**前置条件**:
- 用户已登录第三方支付平台
- 提供有效的支付信息

**方法签名**: `POST /third-pay/order`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| project_id | string | 是 | 项目ID | project_001 |
| pay_way | int | 是 | 支付方式 | 1 |
| region | string | 否 | 地区 | CN |
| role_id | string | 否 | 角色ID | role_12345 |
| server_id | string | 否 | 服务器ID | server_001 |
| product_id | string | 否 | 商品ID | product_001 |
| product_name | string | 否 | 商品名称 | 金币礼包 |
| price | float64 | 否 | 价格 | 5.99 |
| currency | string | 否 | 货币 | USD |
| pay_return_url | string | 否 | 支付返回地址 | https://game.com/return |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| timestamp | string | 否 | 时间戳 | 1641801600 |
| sign | string | 否 | 签名 | abc123def456 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/third-pay/order \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "project_001",
    "pay_way": 1,
    "role_id": "role_12345",
    "product_id": "product_001",
    "product_name": "金币礼包",
    "price": 5.99,
    "currency": "USD"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "TP20250110**********",
        "pay_url": "https://pay.thirdparty.com/pay?order_id=TP20250110**********"
    }
}
```

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

---

### 6.3 查询第三方支付订单

**功能描述**: 查询第三方支付订单状态

**前置条件**:
- 订单已创建
- 提供有效的查询参数

**方法签名**: `POST /third-pay/query`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| project_id | string | 是 | 项目ID | project_001 |
| pay_way | int | 是 | 支付方式 | 1 |
| start_date_time | string | 否 | 开始时间 | 2025-01-01T00:00:00Z |
| end_date_time | string | 否 | 结束时间 | 2025-01-31T23:59:59Z |
| pay_id | string | 否 | 支付ID | pay_12345 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/third-pay/query \
  -H "Content-Type: application/json" \
  -d '{
    "project_id": "project_001",
    "pay_way": 1,
    "start_date_time": "2025-01-01T00:00:00Z",
    "end_date_time": "2025-01-31T23:59:59Z"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": [
        {
            "order_id": "TP20250110**********",
            "pay_id": "pay_12345",
            "status": "paid",
            "amount": 5.99,
            "currency": "USD",
            "create_time": "2025-01-10T10:00:00Z",
            "pay_time": "2025-01-10T10:05:00Z"
        }
    ]
}
```

**状态码**:
- 200: 查询成功
- 400: 查询失败

---

## 7. 📱 小游戏支付

### 7.1 微信小游戏支付

**功能描述**: 创建微信小游戏支付订单

**前置条件**:
- 小游戏用户已登录
- 微信小游戏支付已开启

**方法签名**: `POST /minigame/order/wechat-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 小游戏AppID | wx**********abcdef |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | coin_pack_1 |
| price | float64 | 是 | 价格(分) | 599 |
| pay_type | string | 是 | 支付类型 | jsapi |
| env | int | 是 | 环境(0:正式,1:沙盒) | 0 |
| open_id | string | 是 | 用户OpenID | openid_12345 |
| server_id | string | 否 | 服务器ID | server_001 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |

**请求示例**:
```bash
curl -X POST http://localhost:8000/minigame/order/wechat-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "wx**********abcdef",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "coin_pack_1",
    "price": 599,
    "pay_type": "jsapi",
    "env": 0,
    "open_id": "openid_12345"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "prepay_id": "wx20250110********************1234",
        "order_id": "MG20250110**********"
    }
}
```

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

---

### 7.2 抖音小游戏支付

**功能描述**: 创建抖音小游戏支付订单

**前置条件**:
- 小游戏用户已登录
- 抖音小游戏支付已开启

**方法签名**: `POST /minigame/order/douyin-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 小游戏AppID | tt**********abcdef |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | coin_pack_1 |
| price | float64 | 是 | 价格(分) | 599 |
| pay_type | string | 是 | 支付类型 | virtual |
| env | int | 是 | 环境(0:正式,1:沙盒) | 0 |
| open_id | string | 是 | 用户OpenID | openid_12345 |
| server_id | string | 否 | 服务器ID | server_001 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/minigame/order/douyin-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "tt**********abcdef",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "coin_pack_1",
    "price": 599,
    "pay_type": "virtual",
    "env": 0,
    "open_id": "openid_12345"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "DY20250110**********",
        "sign_data": {
            "mode": "live",
            "offer_id": "offer_12345",
            "buy_quantity": 1,
            "env": 0,
            "currency_type": "CNY",
            "platform": "android",
            "product_id": "coin_pack_1",
            "goods_price": 599,
            "out_trade_no": "DY20250110**********"
        }
    }
}
```

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

---

## 8. 🔄 订阅服务 (Subscription)

### 8.1 创建iOS订阅

**功能描述**: 创建iOS App Store订阅服务

**前置条件**:
- 用户已登录
- iOS订阅服务已开启
- 提供有效的订阅商品信息

**方法签名**: `POST /v2/subscription/ios-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 订阅商品ID | com.game.vip.monthly |
| price | float64 | 是 | 价格(分) | 2999 |
| device | string | 否 | 设备信息 | iPhone 14 Pro |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| pay_notify_url | string | 否 | 支付回调地址 | https://game.com/notify |
| extend | string | 否 | 扩展信息 | {"subscription_type": "monthly"} |
| vip | string | 否 | VIP等级 | 5 |
| level | string | 否 | 角色等级 | 50 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/v2/subscription/ios-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "com.game.vip.monthly",
    "price": 2999,
    "device": "iPhone 14 Pro"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "SUB_IO20250110**********"
    }
}
```

**状态码**:
- 200: 订阅订单创建成功
- 400: 创建失败

---

### 8.2 验证iOS订阅

**功能描述**: 验证iOS App Store订阅支付结果

**前置条件**:
- 订阅订单已创建
- 用户已完成App Store订阅支付

**方法签名**: `POST /v2/subscription/ios-verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| order_id | string | 否 | 订单ID | SUB_IO20250110********** |
| paper | string | 是 | 支付凭证 | base64编码的receipt |
| pay_currency | string | 是 | 支付货币 | USD |
| pay_amount | string | 是 | 支付金额 | 29.99 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/v2/subscription/ios-verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "order_id": "SUB_IO20250110**********",
    "paper": "ewoJInNpZ25hdHVyZSIgPSAiQW...",
    "pay_currency": "USD",
    "pay_amount": "29.99"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "success"
}
```

**状态码**:
- 200: 验证成功
- 400: 验证失败

---

### 8.3 获取订阅信息 (管理后台)

**功能描述**: 管理后台获取订阅信息列表

**前置条件**:
- 管理员已登录
- 提供有效的JWT Token

**方法签名**: `GET /api/v1/subscriptions`

**请求参数**: Query参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| game_id | int | 是 | 游戏ID | 1 |
| page_size | int | 否 | 每页数量 | 10 |
| page_number | int | 否 | 页码 | 1 |
| order_id | string | 否 | 订单ID | SUB_IO20250110********** |
| user_id | int | 否 | 用户ID | 10001 |
| pay_status | int | 否 | 支付状态 | 1 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/subscriptions?game_id=1&page_size=10&page_number=1" \
  -H "Authorization: Bearer jwt_token_here"
```

**返回示例**:
```json
{
    "code": 0,
    "message": "成功",
    "result": {
        "data": [
            {
                "id": 1001,
                "order_id": "SUB_IO20250110**********",
                "game_id": 1,
                "user_id": 10001,
                "account": "**********",
                "role_id": "role_12345",
                "product_id": "com.game.vip.monthly",
                "price": 2999,
                "pay_status": 1,
                "pay_time": "2025-01-10T10:05:00Z",
                "expires_date": **********,
                "auto_renew_status": 1,
                "create_time": "2025-01-10T10:00:00Z"
            }
        ],
        "total": 1,
        "page": 1,
        "page_size": 10
    }
}
```

**状态码**:
- 200: 获取成功
- 400: 获取失败
- 401: 未授权

---

### 8.4 订阅补单 (管理后台)

**功能描述**: 管理后台手动触发订阅补单

**前置条件**:
- 管理员已登录
- 提供有效的JWT Token
- 订阅订单存在

**方法签名**: `POST /api/v1/subscriptions/notify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| game_id | int | 是 | 游戏ID | 1 |
| order_id | string | 是 | 订单ID | SUB_IO20250110********** |

**请求示例**:
```bash
curl -X POST http://localhost:8000/api/v1/subscriptions/notify \
  -H "Authorization: Bearer jwt_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "game_id": 1,
    "order_id": "SUB_IO20250110**********"
  }'
```

**返回示例**:
```json
{
    "code": 0,
    "message": "成功",
    "result": ""
}
```

**状态码**:
- 200: 补单成功
- 400: 补单失败
- 401: 未授权

---

### 8.5 获取续订信息 (管理后台)

**功能描述**: 管理后台获取订阅续订信息

**前置条件**:
- 管理员已登录
- 提供有效的JWT Token

**方法签名**: `GET /api/v1/subscriptions/renew`

**请求参数**: Query参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| game_id | int | 是 | 游戏ID | 1 |
| order_id | string | 是 | 原始订单ID | SUB_IO20250110********** |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/subscriptions/renew?game_id=1&order_id=SUB_IO20250110**********" \
  -H "Authorization: Bearer jwt_token_here"
```

**返回示例**:
```json
{
    "code": 0,
    "message": "成功",
    "result": {
        "data": [
            {
                "id": 1002,
                "original_order_id": "SUB_IO20250110**********",
                "renew_order_id": "SUB_IO202502101234567891",
                "renew_time": "2025-02-10T10:00:00Z",
                "expires_date": 1646265600,
                "auto_renew_status": 1
            }
        ],
        "total": 1
    }
}
```

**状态码**:
- 200: 获取成功
- 400: 获取失败
- 401: 未授权

---

## 9. 🔧 模拟支付 (MockPay)

### 9.1 创建模拟支付订单

**功能描述**: 创建模拟支付订单，用于测试环境

**前置条件**:
- 测试环境
- 模拟支付已开启

**方法签名**: `POST /order/mockpay-exchange`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| role_id | string | 是 | 角色ID | role_12345 |
| product_id | string | 是 | 商品ID | test_product_1 |
| price | float64 | 是 | 价格(分) | 599 |
| device | string | 否 | 设备信息 | Test Device |
| role_name | string | 否 | 角色名称 | 测试角色 |
| server_id | string | 否 | 服务器ID | test_server |
| server_name | string | 否 | 服务器名称 | 测试服务器 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/order/mockpay-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "role_id": "role_12345",
    "product_id": "test_product_1",
    "price": 599
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "MOCK20250110**********",
        "pay_url": "mock://pay/success"
    }
}
```

**状态码**:
- 200: 订单创建成功
- 400: 创建失败

**注意事项**:
- 仅用于测试环境
- 自动模拟支付成功
- 不产生真实费用

---

## 📝 总结

Zeus支付系统提供了完整的多平台支付解决方案：

### 💳 支持的支付方式
- **Google Play**: Android应用内购买
- **App Store**: iOS应用内购买
- **微信支付**: 移动端微信支付
- **支付宝**: 移动端支付宝支付
- **Xsolla**: 国际支付解决方案
- **第三方支付**: 自定义支付渠道

### 🔐 安全特性
- 订单签名验证
- 支付凭证校验
- 重复订单检测
- 安全回调机制

### 🌍 多平台支持
- iOS/Android原生应用
- 微信小游戏
- 网页游戏
- 国际化支付

如需更多详细信息或技术支持，请联系开发团队。
