# Zeus 支付系统 API 快速参考

## 📋 接口概览

### 1. Google Play 支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建Google订单 | POST | `/order/google-exchange` | 创建Google Play支付订单 |
| 验证Google订单 | POST | `/v2/order/google-verify` | 验证Google Play支付结果 |

### 2. iOS App Store 支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建iOS订单 | POST | `/order/ios-exchange` | 创建App Store支付订单 |
| 验证iOS订单 | POST | `/order/ios-verify` | 验证App Store支付结果 |

### 3. 微信支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建微信订单 | POST | `/order/wechat-exchange` | 创建微信支付订单 |
| 验证微信订单 | POST | `/order/wechat-verify` | 验证微信支付结果 |

### 4. 支付宝支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建支付宝订单 | POST | `/order/ali-exchange` | 创建支付宝支付订单 |
| 验证支付宝订单 | POST | `/order/ali-verify` | 验证支付宝支付结果 |

### 5. Xsolla 支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| Xsolla用户登录 | POST | `/xsolla/login` | Xsolla平台用户登录验证 |
| Xsolla支付回调 | POST | `/xsolla/pay` | 接收Xsolla支付回调 |

### 6. 第三方支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 第三方支付登录 | POST | `/third-pay/login` | 第三方支付平台登录 |
| 创建第三方订单 | POST | `/third-pay/order` | 创建第三方支付订单 |
| 查询第三方订单 | POST | `/third-pay/query` | 查询第三方支付订单 |

### 7. 小游戏支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 微信小游戏支付 | POST | `/minigame/order/wechat-exchange` | 微信小游戏支付 |
| 抖音小游戏支付 | POST | `/minigame/order/douyin-exchange` | 抖音小游戏支付 |

### 8. 订阅服务
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建iOS订阅 | POST | `/v2/subscription/ios-exchange` | 创建iOS订阅服务 |
| 验证iOS订阅 | POST | `/v2/subscription/ios-verify` | 验证iOS订阅支付 |
| 获取订阅信息 | GET | `/api/v1/subscriptions` | 管理后台获取订阅信息 |
| 订阅补单 | POST | `/api/v1/subscriptions/notify` | 管理后台订阅补单 |
| 获取续订信息 | GET | `/api/v1/subscriptions/renew` | 获取订阅续订信息 |

### 9. 模拟支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建模拟订单 | POST | `/order/mockpay-exchange` | 创建模拟支付订单(测试) |

## 📋 API返回示例和字段说明

### 1. Google Play 支付

#### 创建Google订单返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "google_pay_key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...",
        "order_id": "GO20250110**********"
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.google_pay_key | string | Google支付验证密钥 |
| result.order_id | string | 订单ID |

#### 验证Google订单返回示例
```json
{
    "error_no": 0,
    "message": "成功"
}
```

### 2. iOS App Store 支付

#### 创建iOS订单返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "IO20250110**********"
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.order_id | string | 订单ID |

#### 验证iOS订单返回示例
```json
{
    "error_no": 0,
    "message": "success"
}
```

### 3. 微信支付

#### 创建微信订单返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "WX20250110**********",
        "prepay_id": "wx20250110********************1234",
        "app_id": "wxd930ea5d5a258f4f",
        "partner_id": "1900000109",
        "package": "Sign=WXPay",
        "nonce_str": "1add1a30ac87aa2db72f57a2375d8fec",
        "timestamp": "1641801600",
        "sign": "C380BEC2BFD727A4B6845133519F3AD6"
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.order_id | string | 订单ID |
| result.prepay_id | string | 微信预支付ID |
| result.app_id | string | 微信应用ID |
| result.partner_id | string | 微信商户号 |
| result.package | string | 扩展字段 |
| result.nonce_str | string | 随机字符串 |
| result.timestamp | string | 时间戳 |
| result.sign | string | 签名 |

#### 验证微信订单返回示例
```json
{
    "error_no": 0,
    "message": "成功"
}
```

### 4. 支付宝支付

#### 创建支付宝订单返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "AL20250110**********",
        "pay_url": "alipays://platformapi/startapp?saId=10000007&qrcode=https%3A%2F%2Fqr.alipay.com%2Fbax08431..."
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.order_id | string | 订单ID |
| result.pay_url | string | 支付宝支付链接 |

#### 验证支付宝订单返回示例
```json
{
    "error_no": 0,
    "message": "成功"
}
```

### 5. Xsolla 支付

#### Xsolla用户登录返回示例
```json
{
    "attributes": [
        {
            "key": "level",
            "value": "50"
        }
    ],
    "user": {
        "id": "role_12345",
        "name": "勇敢的战士"
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| attributes | array | 用户属性列表 |
| attributes[].key | string | 属性键名 |
| attributes[].value | string | 属性值 |
| user.id | string | 用户角色ID |
| user.name | string | 角色名称 |

### 6. 第三方支付

#### 第三方支付登录返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "token": "third_pay_token_12345",
        "user_info": {
            "role_id": "role_12345",
            "project_id": "project_001"
        }
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.token | string | 第三方支付Token |
| result.user_info.role_id | string | 角色ID |
| result.user_info.project_id | string | 项目ID |

#### 创建第三方订单返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "TP20250110**********",
        "pay_url": "https://pay.thirdparty.com/pay?order_id=TP20250110**********"
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.order_id | string | 订单ID |
| result.pay_url | string | 第三方支付链接 |

#### 查询第三方订单返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": [
        {
            "order_id": "TP20250110**********",
            "pay_id": "pay_12345",
            "status": "paid",
            "amount": 5.99,
            "currency": "USD",
            "create_time": "2025-01-10T10:00:00Z",
            "pay_time": "2025-01-10T10:05:00Z"
        }
    ]
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result[].order_id | string | 订单ID |
| result[].pay_id | string | 支付ID |
| result[].status | string | 支付状态 |
| result[].amount | float | 支付金额 |
| result[].currency | string | 货币类型 |
| result[].create_time | string | 创建时间 |
| result[].pay_time | string | 支付时间 |

### 6. 小游戏支付

#### 微信小游戏支付返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "prepay_id": "wx20250110********************1234",
        "order_id": "MG20250110**********"
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.prepay_id | string | 微信预支付ID |
| result.order_id | string | 订单ID |

#### 抖音小游戏支付返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "DY20250110**********",
        "sign_data": {
            "mode": "live",
            "offer_id": "offer_12345",
            "buy_quantity": 1,
            "env": 0,
            "currency_type": "CNY",
            "platform": "android",
            "product_id": "coin_pack_1",
            "goods_price": 599,
            "out_trade_no": "DY20250110**********"
        }
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.order_id | string | 订单ID |
| result.sign_data.mode | string | 支付模式 |
| result.sign_data.offer_id | string | 商品ID |
| result.sign_data.buy_quantity | int | 购买数量 |
| result.sign_data.env | int | 环境标识 |
| result.sign_data.currency_type | string | 货币类型 |
| result.sign_data.platform | string | 平台标识 |
| result.sign_data.product_id | string | 商品ID |
| result.sign_data.goods_price | int | 商品价格 |
| result.sign_data.out_trade_no | string | 外部订单号 |

### 7. 订阅服务

#### 创建iOS订阅返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "SUB_IO20250110**********"
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.order_id | string | 订阅订单ID |

#### 验证iOS订阅返回示例
```json
{
    "error_no": 0,
    "message": "success"
}
```

#### 获取订阅信息返回示例
```json
{
    "code": 0,
    "message": "成功",
    "result": {
        "data": [
            {
                "id": 1001,
                "order_id": "SUB_IO20250110**********",
                "game_id": 1,
                "user_id": 10001,
                "account": "**********",
                "role_id": "role_12345",
                "product_id": "com.game.vip.monthly",
                "price": 2999,
                "pay_status": 1,
                "pay_time": "2025-01-10T10:05:00Z",
                "expires_date": **********,
                "auto_renew_status": 1,
                "create_time": "2025-01-10T10:00:00Z"
            }
        ],
        "total": 1,
        "page": 1,
        "page_size": 10
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.data[].id | int | 订阅记录ID |
| result.data[].order_id | string | 订单ID |
| result.data[].game_id | int | 游戏ID |
| result.data[].user_id | int | 用户ID |
| result.data[].account | string | 用户账号 |
| result.data[].role_id | string | 角色ID |
| result.data[].product_id | string | 订阅商品ID |
| result.data[].price | int | 价格(分) |
| result.data[].pay_status | int | 支付状态 |
| result.data[].pay_time | string | 支付时间 |
| result.data[].expires_date | int | 过期时间戳 |
| result.data[].auto_renew_status | int | 自动续费状态 |
| result.data[].create_time | string | 创建时间 |
| result.total | int | 总数量 |
| result.page | int | 当前页码 |
| result.page_size | int | 每页数量 |

#### 订阅补单返回示例
```json
{
    "code": 0,
    "message": "成功",
    "result": ""
}
```

#### 获取续订信息返回示例
```json
{
    "code": 0,
    "message": "成功",
    "result": {
        "data": [
            {
                "id": 1002,
                "original_order_id": "SUB_IO20250110**********",
                "renew_order_id": "SUB_IO202502101234567891",
                "renew_time": "2025-02-10T10:00:00Z",
                "expires_date": 1646265600,
                "auto_renew_status": 1
            }
        ],
        "total": 1
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.data[].id | int | 续订记录ID |
| result.data[].original_order_id | string | 原始订单ID |
| result.data[].renew_order_id | string | 续订订单ID |
| result.data[].renew_time | string | 续订时间 |
| result.data[].expires_date | int | 过期时间戳 |
| result.data[].auto_renew_status | int | 自动续费状态 |
| result.total | int | 总数量 |

### 8. 模拟支付

#### 创建模拟订单返回示例
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "order_id": "MOCK20250110**********",
        "pay_url": "mock://pay/success"
    }
}
```

**返回字段说明**:
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error_no | int | 错误码，0表示成功 |
| message | string | 返回消息 |
| result.order_id | string | 模拟订单ID |
| result.pay_url | string | 模拟支付链接 |

## 🔢 常用错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1003 | 参数缺失 |
| 1004 | 数据库更新错误 |
| 1005 | 参数无效 |
| 1009 | 支付关闭 |
| 1010 | 用户不存在 |
| 1011 | 订单不存在 |
| 1012 | 订单重复 |

## 💳 支付方式说明

| 支付方式 | 平台 | 说明 |
|----------|------|------|
| Google Play | Android | Google Play应用内购买 |
| App Store | iOS | Apple App Store应用内购买 |
| 微信支付 | 移动端 | 微信APP支付 |
| 支付宝 | 移动端 | 支付宝APP支付 |
| Xsolla | 国际 | 国际支付解决方案 |
| 第三方支付 | 自定义 | 自定义支付渠道 |
| 小游戏支付 | 小游戏 | 微信/抖音小游戏支付 |

## 📱 订单状态说明

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 未支付 | 订单已创建，等待支付 |
| 1 | 支付成功 | 支付完成，订单成功 |
| 2 | 支付失败 | 支付失败或取消 |
| 3 | 已退款 | 订单已退款 |

## 🔐 认证方式

### SDK接口认证
- 使用签名验证机制
- 需要正确的appid和签名

### 管理后台接口认证
- 需要JWT Token
- 需要管理员权限
- Token在Header中传递: `Authorization: Bearer <token>`

## 💡 核心参数说明

### 通用订单参数
- `appid`: 应用ID，必填
- `user_id`: 用户ID，必填
- `role_id`: 角色ID，必填
- `product_id`: 商品ID，必填
- `price`: 价格(分)，必填
- `server_id`: 服务器ID，可选
- `pay_notify_url`: 支付回调地址，可选

### 价格说明
- 所有价格单位为**分**
- 例如：5.99元 = 599分
- Google Play和iOS需要除以100转换

### 订单ID格式
- Google: `GO` + 时间戳 + 随机数
- iOS: `IO` + 时间戳 + 随机数
- 微信: `WX` + 时间戳 + 随机数
- 支付宝: `AL` + 时间戳 + 随机数
- 订阅: `SUB_` + 平台前缀 + 时间戳

## 🔄 支付流程

### 标准支付流程
1. **创建订单**: 调用对应平台的创建订单接口
2. **发起支付**: 使用返回的支付参数调起支付
3. **支付完成**: 用户完成支付操作
4. **验证订单**: 调用验证接口确认支付结果
5. **发放道具**: 根据验证结果发放游戏道具

### 订阅支付流程
1. **创建订阅**: 调用订阅创建接口
2. **发起订阅**: 使用返回参数调起订阅支付
3. **验证订阅**: 验证订阅支付结果
4. **自动续费**: 系统自动处理续费逻辑

## 📊 支付回调

### 回调地址配置
- 微信支付: `/callback/app/wechat/:appid`
- 支付宝: `/callback/app/alipay/:appid`
- Xsolla: `/xsolla/pay`

### 回调验证
- 验证签名
- 验证订单状态
- 防重复处理
- 返回正确响应

## 🌍 国际化支持

### 支持的货币
- CNY: 人民币
- USD: 美元
- EUR: 欧元
- JPY: 日元
- KRW: 韩元

### 支持的地区
- 中国大陆
- 香港、台湾、澳门
- 美国、欧洲
- 日本、韩国
- 东南亚

## 📝 注意事项

### 安全要求
- 所有支付接口都需要签名验证
- 敏感信息不能明文传输
- 支付凭证需要向官方验证

### 测试环境
- 使用MockPay进行测试
- iOS支持沙盒环境
- Google Play支持测试账号

### 性能优化
- 支付验证有超时机制
- 支持异步回调处理
- 订单状态实时更新

## 📞 技术支持

如需详细文档，请查看 [完整支付API文档](./PAYMENT_API_DOCUMENTATION.md)

技术支持：<EMAIL>
