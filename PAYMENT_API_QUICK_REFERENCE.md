# Zeus 支付系统 API 快速参考

## 📋 接口概览

### 1. Google Play 支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建Google订单 | POST | `/order/google-exchange` | 创建Google Play支付订单 |
| 验证Google订单 | POST | `/v2/order/google-verify` | 验证Google Play支付结果 |

### 2. iOS App Store 支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建iOS订单 | POST | `/order/ios-exchange` | 创建App Store支付订单 |
| 验证iOS订单 | POST | `/order/ios-verify` | 验证App Store支付结果 |

### 3. 微信支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建微信订单 | POST | `/order/wechat-exchange` | 创建微信支付订单 |
| 验证微信订单 | POST | `/order/wechat-verify` | 验证微信支付结果 |

### 4. 支付宝支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建支付宝订单 | POST | `/order/ali-exchange` | 创建支付宝支付订单 |
| 验证支付宝订单 | POST | `/order/ali-verify` | 验证支付宝支付结果 |

### 5. Xsolla 支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| Xsolla支付回调 | POST | `/xsolla/pay` | 接收Xsolla支付回调 |

### 6. 第三方支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 第三方支付登录 | POST | `/third-pay/login` | 第三方支付平台登录 |
| 创建第三方订单 | POST | `/third-pay/order` | 创建第三方支付订单 |
| 查询第三方订单 | POST | `/third-pay/query` | 查询第三方支付订单 |

### 7. 小游戏支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 微信小游戏支付 | POST | `/minigame/order/wechat-exchange` | 微信小游戏支付 |
| 抖音小游戏支付 | POST | `/minigame/order/douyin-exchange` | 抖音小游戏支付 |

### 8. 订阅服务
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建iOS订阅 | POST | `/v2/subscription/ios-exchange` | 创建iOS订阅服务 |
| 验证iOS订阅 | POST | `/v2/subscription/ios-verify` | 验证iOS订阅支付 |
| 获取订阅信息 | GET | `/api/v1/subscriptions` | 管理后台获取订阅信息 |
| 订阅补单 | POST | `/api/v1/subscriptions/notify` | 管理后台订阅补单 |
| 获取续订信息 | GET | `/api/v1/subscriptions/renew` | 获取订阅续订信息 |

### 9. 模拟支付
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 创建模拟订单 | POST | `/order/mockpay-exchange` | 创建模拟支付订单(测试) |

## 🔢 常用错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1003 | 参数缺失 |
| 1004 | 数据库更新错误 |
| 1005 | 参数无效 |
| 1009 | 支付关闭 |
| 1010 | 用户不存在 |
| 1011 | 订单不存在 |
| 1012 | 订单重复 |

## 💳 支付方式说明

| 支付方式 | 平台 | 说明 |
|----------|------|------|
| Google Play | Android | Google Play应用内购买 |
| App Store | iOS | Apple App Store应用内购买 |
| 微信支付 | 移动端 | 微信APP支付 |
| 支付宝 | 移动端 | 支付宝APP支付 |
| Xsolla | 国际 | 国际支付解决方案 |
| 第三方支付 | 自定义 | 自定义支付渠道 |
| 小游戏支付 | 小游戏 | 微信/抖音小游戏支付 |

## 📱 订单状态说明

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 未支付 | 订单已创建，等待支付 |
| 1 | 支付成功 | 支付完成，订单成功 |
| 2 | 支付失败 | 支付失败或取消 |
| 3 | 已退款 | 订单已退款 |

## 🔐 认证方式

### SDK接口认证
- 使用签名验证机制
- 需要正确的appid和签名

### 管理后台接口认证
- 需要JWT Token
- 需要管理员权限
- Token在Header中传递: `Authorization: Bearer <token>`

## 💡 核心参数说明

### 通用订单参数
- `appid`: 应用ID，必填
- `user_id`: 用户ID，必填
- `role_id`: 角色ID，必填
- `product_id`: 商品ID，必填
- `price`: 价格(分)，必填
- `server_id`: 服务器ID，可选
- `pay_notify_url`: 支付回调地址，可选

### 价格说明
- 所有价格单位为**分**
- 例如：5.99元 = 599分
- Google Play和iOS需要除以100转换

### 订单ID格式
- Google: `GO` + 时间戳 + 随机数
- iOS: `IO` + 时间戳 + 随机数
- 微信: `WX` + 时间戳 + 随机数
- 支付宝: `AL` + 时间戳 + 随机数
- 订阅: `SUB_` + 平台前缀 + 时间戳

## 🔄 支付流程

### 标准支付流程
1. **创建订单**: 调用对应平台的创建订单接口
2. **发起支付**: 使用返回的支付参数调起支付
3. **支付完成**: 用户完成支付操作
4. **验证订单**: 调用验证接口确认支付结果
5. **发放道具**: 根据验证结果发放游戏道具

### 订阅支付流程
1. **创建订阅**: 调用订阅创建接口
2. **发起订阅**: 使用返回参数调起订阅支付
3. **验证订阅**: 验证订阅支付结果
4. **自动续费**: 系统自动处理续费逻辑

## 📊 支付回调

### 回调地址配置
- 微信支付: `/callback/app/wechat/:appid`
- 支付宝: `/callback/app/alipay/:appid`
- Xsolla: `/xsolla/pay`

### 回调验证
- 验证签名
- 验证订单状态
- 防重复处理
- 返回正确响应

## 🌍 国际化支持

### 支持的货币
- CNY: 人民币
- USD: 美元
- EUR: 欧元
- JPY: 日元
- KRW: 韩元

### 支持的地区
- 中国大陆
- 香港、台湾、澳门
- 美国、欧洲
- 日本、韩国
- 东南亚

## 📝 注意事项

### 安全要求
- 所有支付接口都需要签名验证
- 敏感信息不能明文传输
- 支付凭证需要向官方验证

### 测试环境
- 使用MockPay进行测试
- iOS支持沙盒环境
- Google Play支持测试账号

### 性能优化
- 支付验证有超时机制
- 支持异步回调处理
- 订单状态实时更新

## 📞 技术支持

如需详细文档，请查看 [完整支付API文档](./PAYMENT_API_DOCUMENTATION.md)

技术支持：<EMAIL>
