# 🚀 Zeus API Swagger 快速启动指南

## 📋 概述

Zeus API 是一个游戏服务平台，提供完整的后端服务解决方案。本指南将帮助您快速启动并查看 Swagger API 文档。

## ⚡ 快速启动（推荐）

### 1. 启动演示服务

```bash
# 无需数据库配置，直接启动演示服务
go run main_swagger_demo.go
```

### 2. 访问 Swagger 文档

在浏览器中打开：[http://localhost:8000/swagger/index.html](http://localhost:8000/swagger/index.html)

### 3. 测试 API

```bash
# 健康检查
curl http://localhost:8000/healthz

# API 信息
curl http://localhost:8000/api/info

# 演示接口
curl "http://localhost:8000/demo?name=test&age=25"
```

## 🎯 演示接口

演示服务包含以下接口：

### 系统接口
- `GET /healthz` - 健康检查
- `GET /api/info` - API 信息
- `GET /` - 重定向到 Swagger UI

### 演示接口
- `GET /demo` - 演示 GET 接口
- `POST /demo/user/login` - 演示用户登录
- `POST /demo/payment/create` - 演示支付创建

### 文档接口
- `GET /swagger/index.html` - Swagger UI
- `GET /swagger/doc.json` - Swagger JSON

## 📝 测试示例

### 用户登录演示

```bash
curl -X POST http://localhost:8000/demo/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "demo_user",
    "password": "password123",
    "device": "mobile"
  }'
```

### 支付订单演示

```bash
curl -X POST http://localhost:8000/demo/payment/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer demo_token" \
  -d '{
    "amount": 99.99,
    "currency": "CNY",
    "product_id": "product_001",
    "user_id": "12345"
  }'
```

## 🔧 完整服务启动

如果需要启动完整的 Zeus 服务（需要数据库）：

### 1. 配置环境

```bash
# 复制并编辑配置文件
cp config/config.yaml config/config.local.yaml
# 编辑 config.local.yaml，配置数据库和 Redis
```

### 2. 启动服务

```bash
go run main.go
```

## 📚 其他启动方式

### 使用脚本启动

```bash
# 交互式启动脚本
./start-swagger.sh

# 测试脚本
./test-api.sh
```

### 使用 Docker

```bash
# 启动 Swagger UI 服务
docker-compose -f docker-compose.swagger.yml up -d

# 访问地址：
# - Swagger UI: http://localhost:8080
# - Swagger Editor: http://localhost:8081
# - 文件服务器: http://localhost:8082
```

### 使用 Makefile

```bash
# 查看可用命令
make help

# 生成 Swagger 文档
make swagger-gen

# 启动开发环境
make dev
```

## 🎨 Swagger UI 功能

在 Swagger UI 中，您可以：

1. **浏览 API** - 查看所有可用的接口
2. **测试接口** - 直接在浏览器中测试 API
3. **查看模型** - 了解请求和响应的数据结构
4. **下载文档** - 导出 OpenAPI 规范文件

## 🔍 故障排除

### 端口被占用

```bash
# 查看端口占用
lsof -i :8000

# 杀死占用进程
kill -9 <PID>
```

### 依赖问题

```bash
# 更新依赖
go mod tidy
go mod download
```

### 重新生成文档

```bash
# 安装 swag 工具
go install github.com/swaggo/swag/cmd/swag@latest

# 生成文档
swag init
```

## 📖 相关文档

- [完整 API 文档](./SWAGGER_README.md)
- [API 概览](./API_OVERVIEW.md)
- [项目 README](./README.md)

## 🎉 成功！

如果您看到了 Swagger UI 界面，恭喜您已经成功启动了 Zeus API 文档服务！

现在您可以：
- 浏览所有 API 接口
- 在线测试接口功能
- 查看详细的请求/响应格式
- 了解认证和错误处理机制

享受使用 Zeus API 的过程！🚀
