# Zeus
> 发行系统后端

## 开始
本文档会指导你完成本地开发环境后端服务的搭建。前端服务搭建参考[zeus-admin](https://git.youle.game/TC/TSD/DevOps/zeus-admin/-/tree/master)

### 环境准备
- go(1.18版本及以上)环境准备

- mysql(5.7)数据库准备,建议使用docker，创建名为zeus的数据库。

- redis数据库准备, 建议使用docker

- yaml配置文件准备,  参数如下。数据库为测试地址，如果使用自己本地数据库，请修改相关数据库参数
```
docs: true
db:
  DriverName: mysql
  Database: zeus
  Port: 3306
  UserName: root
  Password: 1234
  Host: **************
  Charset: utf8
redis:
  Host: **************:6379
  Password: password
  MaxIdle: 60
  MaxActive: 60
  IdleTimeout: 50
AntiAddiction:
  FestivalTime: [
    "2022-12-31", "2023-01-01", "2023-01-02",
    "2023-01-21", "2023-01-22", "2023-01-23", "2023-01-24", "2023-01-25", "2023-01-26", "2023-01-27",
    "2023-04-05",
    "2023-05-01", "2023-05-02", "2023-05-03", "2023-05-04", "2023-05-05",
    "2023-06-22", "2023-06-23", "2023-06-24",
    "2023-09-29", "2023-09-30",
    "2023-10-01", "2023-10-02", "2023-10-03", "2023-10-04", "2023-10-05", "2023-10-06", "2022-12-06",
  ]
  Exception: [
    "2023-01-28", "2023-01-29",
    "2023-04-29", "2023-04-30",
    "2023-06-25",
    "2023-10-07", "2023-10-08",
  ]
  StartTime: "12:00:00"
  EndTime: "21:00:00"
Ucenter:
  APIKey: 9380100115
  APISecret: 314635b93085f16cd9ba05c18abdc9ee
IsAutoNotifyFix: false
```

### 启动项目
在项目根目录下执行命令
``` bash
go run main.go
 ```

### 测试
在项目根目录下执行命令,会生成coverage.txt文件，记录测试内容以及覆盖率
```bash
go test -coverprofile=./coverage.txt -covermode=atomic -gcflags=all=-l  $(go list ./... | awk 'NR!=1 { print }')
# 查看总覆盖率
go tool cover -func=./coverage.txt | tail -1
```


### 数据库版本管理
数据库版本管理使用了 migrate库（github.com/golang-migrate/migrate）
1. 数据库版本管理
``` bash
# 安装数据库版本生成工具
go install -tags 'mysql' github.com/golang-migrate/migrate/v4/cmd/migrate@latest
```

2. 创建版本
``` bash
#  ./migrations/game 存放基础数据库信息
#  ./migrations/game 存放项目数据库信息
# 根据变更内容创建数据库升级语句文件
migrate create -ext sql -dir ./migrations/game -seq subscription 
```

3. 编写升级和回滚脚本


### 删除账号文档管理

添加其他语言的文档
``` bash
 1. 在word中编辑文档，另存为html格式，并保存到zeus/del_account_docs文件夹下
 2. 调整index.js与script.js文件中的语言列表
 3. 在导出的html中添加以下代码：
    <script src="./script.js"></script>
    <link href="./styles.css" rel="stylesheet">
```