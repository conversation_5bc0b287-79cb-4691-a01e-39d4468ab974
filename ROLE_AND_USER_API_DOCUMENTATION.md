# Zeus 角色和用户管理 API 文档

## 📋 概述

本文档描述了Zeus系统中角色管理和用户绑定相关的API接口，包括角色信息保存、用户绑定查询和用户角色查询等功能。

## 🔗 基础信息

- **Base URL**: `http://localhost:8000`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 📊 通用响应格式

### 成功响应
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        // 具体业务数据
    }
}
```

### 错误响应
```json
{
    "error_no": 1003,
    "message": "参数缺失"
}
```

---

## 1. 🎮 角色管理接口

### 1.1 保存角色信息

**功能描述**: 上传和保存用户的角色信息，包括角色基本信息、游戏数据等

**前置条件**: 
- 提供有效的应用ID和用户ID
- 角色ID和服务器ID必须提供

**方法签名**: `POST /role/save`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| user_id | string | 是 | 用户ID | 10001 |
| appid | string | 是 | 应用ID | demo_app_001 |
| role_id | string | 是 | 角色ID | role_12345 |
| device | string | 否 | 设备信息 | iPhone 14 Pro |
| platform | string | 否 | 平台标识 | ios |
| server_id | string | 否 | 服务器ID | server_001 |
| server_name | string | 否 | 服务器名称 | 服务器1区 |
| role_name | string | 否 | 角色名称 | 勇敢的战士 |
| vip | string | 否 | VIP等级 | 5 |
| level | string | 否 | 角色等级 | 50 |
| play_time | string | 否 | 游戏时间 | 2025-01-10T10:00:00Z |
| extend | string | 否 | 扩展信息(JSON格式) | {"custom_data": "value"} |

**请求示例**:
```bash
curl -X POST http://localhost:8000/role/save \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "10001",
    "appid": "demo_app_001",
    "role_id": "role_12345",
    "device": "iPhone 14 Pro",
    "platform": "ios",
    "server_id": "server_001",
    "server_name": "服务器1区",
    "role_name": "勇敢的战士",
    "vip": "5",
    "level": "50",
    "extend": "{\"custom_data\": \"value\"}"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功"
}
```

**状态码**: 
- 200: 保存成功
- 400: 参数错误或保存失败

**注意事项**:
- 如果角色已存在，会更新现有角色信息
- play_time会自动设置为当前时间
- play_ip会自动设置为客户端IP
- vip和level会自动转换为整数类型
- extend字段可以存储JSON格式的扩展信息

---

## 2. 👥 用户绑定管理接口

### 2.1 获取用户绑定信息

**功能描述**: 获取指定用户的所有第三方账号绑定信息，包括区号信息

**前置条件**: 
- 管理员已登录
- 提供有效的JWT Token
- 提供有效的用户ID

**方法签名**: `GET /api/v1/user/bind/:user_id`

**请求参数**: Query参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| user_id | string | 是 | 用户ID | 10001 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/user/bind/:user_id?user_id=10001" \
  -H "Authorization: Bearer jwt_token_here"
```

**返回示例**:
```json
{
    "code": 0,
    "message": "成功",
    "result": [
        {
            "id": 1,
            "user_id": 10001,
            "union_id": "google_user_12345",
            "open_id": "google_open_id_67890",
            "type": 1,
            "create_time": "2025-01-10T10:00:00Z",
            "update_time": "2025-01-10T10:00:00Z",
            "status": 1,
            "user_name": "Google用户",
            "detail": "{\"email\": \"<EMAIL>\"}",
            "area_code": "+86"
        },
        {
            "id": 2,
            "user_id": 10001,
            "union_id": "facebook_user_54321",
            "open_id": "facebook_open_id_09876",
            "type": 2,
            "create_time": "2025-01-10T11:00:00Z",
            "update_time": "2025-01-10T11:00:00Z",
            "status": 1,
            "user_name": "Facebook用户",
            "detail": "{\"email\": \"<EMAIL>\"}",
            "area_code": "+1"
        }
    ]
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 绑定记录ID |
| user_id | int | 用户ID |
| union_id | string | 第三方平台唯一标识 |
| open_id | string | 第三方平台开放ID |
| type | int | 第三方类型 (1:Google, 2:Facebook, 3:Apple等) |
| create_time | string | 创建时间 |
| update_time | string | 更新时间 |
| status | int | 状态 (0:解绑, 1:正常) |
| user_name | string | 第三方账号用户名 |
| detail | string | 第三方信息详情(JSON格式) |
| area_code | string | 区号信息 |

**状态码**: 
- 200: 获取成功
- 400: 获取失败
- 401: 未授权

**注意事项**:
- 需要管理员权限和有效的JWT Token
- 返回所有绑定状态的第三方账号信息
- 包含区号信息，通过关联account表获取
- detail字段包含第三方平台的详细信息

---

## 3. 🎯 用户角色查询接口

### 3.1 获取用户角色列表

**功能描述**: 获取指定用户的所有角色信息

**前置条件**: 
- 管理员已登录
- 提供有效的JWT Token
- 提供有效的用户ID

**方法签名**: `GET /api/v1/user/roles`

**请求参数**: Query参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| user_id | string | 是 | 用户ID | 10001 |

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/user/roles?user_id=10001" \
  -H "Authorization: Bearer jwt_token_here"
```

**返回示例**:
```json
{
    "code": 0,
    "message": "成功",
    "result": [
        {
            "id": 1001,
            "game_id": 1,
            "user_id": 10001,
            "platform": "ios",
            "device": "iPhone 14 Pro",
            "server_id": "server_001",
            "server_name": "服务器1区",
            "role_id": "role_12345",
            "role_name": "勇敢的战士",
            "vip": 5,
            "level": 50,
            "play_time": "2025-01-10T17:30:00Z",
            "play_ip": "*************",
            "extend": "{\"custom_data\": \"value\"}"
        },
        {
            "id": 1002,
            "game_id": 1,
            "user_id": 10001,
            "platform": "android",
            "device": "Samsung Galaxy S21",
            "server_id": "server_002",
            "server_name": "服务器2区",
            "role_id": "role_67890",
            "role_name": "智慧法师",
            "vip": 3,
            "level": 35,
            "play_time": "2025-01-10T16:45:00Z",
            "play_ip": "*************",
            "extend": "{\"magic_power\": 1500}"
        }
    ]
}
```

**返回字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 角色记录ID |
| game_id | int | 游戏ID |
| user_id | int | 用户ID |
| platform | string | 平台标识 |
| device | string | 设备信息 |
| server_id | string | 服务器ID |
| server_name | string | 服务器名称 |
| role_id | string | 角色ID |
| role_name | string | 角色名称 |
| vip | int | VIP等级 |
| level | int | 角色等级 |
| play_time | string | 最后游戏时间 |
| play_ip | string | 最后游戏IP |
| extend | string | 扩展信息(JSON格式) |

**状态码**: 
- 200: 获取成功
- 400: 获取失败
- 401: 未授权

**注意事项**:
- 需要管理员权限和有效的JWT Token
- 返回用户在所有游戏中的角色信息
- 按照角色创建时间排序
- extend字段包含角色的扩展信息

---

## 📝 总结

这三个API接口提供了完整的角色和用户绑定管理功能：

### 🎮 角色管理
- **角色信息保存**: 支持角色数据的创建和更新
- **自动数据处理**: 自动设置时间戳和IP信息
- **扩展信息支持**: 支持JSON格式的自定义数据

### 👥 用户绑定管理
- **绑定信息查询**: 获取用户的所有第三方绑定
- **区号信息**: 包含手机号区号等详细信息
- **状态管理**: 支持绑定状态的查询

### 🎯 角色查询
- **多角色支持**: 用户可以拥有多个角色
- **跨服务器**: 支持不同服务器的角色查询
- **详细信息**: 包含角色的完整游戏数据

### 🔐 安全特性
- JWT Token验证
- 管理员权限控制
- 参数验证和错误处理
- 数据库事务支持

### 🌍 应用场景
- 游戏角色数据同步
- 用户账号绑定管理
- 客服系统用户查询
- 数据分析和统计

如需更多详细信息或技术支持，请联系开发团队。
