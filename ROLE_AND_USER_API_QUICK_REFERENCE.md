# Zeus 角色和用户管理 API 快速参考

## 📋 接口概览

| 接口 | 方法 | 路径 | 描述 | 权限要求 |
|------|------|------|------|----------|
| 保存角色信息 | POST | `/role/save` | 上传和保存用户角色信息 | SDK签名验证 |
| 获取用户绑定信息 | GET | `/api/v1/user/bind/:user_id` | 获取用户第三方绑定信息 | JWT Token + 管理员权限 |
| 获取用户角色列表 | GET | `/api/v1/user/roles` | 获取用户所有角色信息 | JWT Token + 管理员权限 |

## 🔢 常用错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1003 | 参数缺失 |
| 1004 | 数据库更新错误 |
| 1005 | 参数无效 |
| 401 | 未授权 |

## 🎮 角色管理

### POST /role/save - 保存角色信息

**核心参数**:
- `user_id` (必填): 用户ID
- `appid` (必填): 应用ID  
- `role_id` (必填): 角色ID

**可选参数**:
- `server_id`: 服务器ID
- `role_name`: 角色名称
- `vip`: VIP等级
- `level`: 角色等级
- `extend`: 扩展信息(JSON)

**返回**: 成功状态

## 👥 用户绑定管理

### GET /api/v1/user/bind/:user_id - 获取用户绑定信息

**Query参数**:
- `user_id` (必填): 用户ID

**返回字段**:
- `user_id`: 用户ID
- `union_id`: 第三方唯一标识
- `open_id`: 第三方开放ID
- `type`: 第三方类型
- `status`: 绑定状态
- `area_code`: 区号信息

**第三方类型说明**:
| 类型值 | 平台 |
|--------|------|
| 1 | Google |
| 2 | Facebook |
| 3 | Apple |
| 4 | Line |
| 5 | WeChat |
| 6 | QQ |

## 🎯 用户角色查询

### GET /api/v1/user/roles - 获取用户角色列表

**Query参数**:
- `user_id` (必填): 用户ID

**返回字段**:
- `role_id`: 角色ID
- `role_name`: 角色名称
- `server_id`: 服务器ID
- `server_name`: 服务器名称
- `vip`: VIP等级
- `level`: 角色等级
- `play_time`: 最后游戏时间
- `extend`: 扩展信息

## 📊 数据模型

### Role 角色模型
```json
{
    "id": 1001,
    "game_id": 1,
    "user_id": 10001,
    "platform": "ios",
    "device": "iPhone 14 Pro",
    "server_id": "server_001",
    "server_name": "服务器1区",
    "role_id": "role_12345",
    "role_name": "勇敢的战士",
    "vip": 5,
    "level": 50,
    "play_time": "2025-01-10T17:30:00Z",
    "play_ip": "*************",
    "extend": "{\"custom_data\": \"value\"}"
}
```

### UserBindThird 用户绑定模型
```json
{
    "id": 1,
    "user_id": 10001,
    "union_id": "google_user_12345",
    "open_id": "google_open_id_67890",
    "type": 1,
    "create_time": "2025-01-10T10:00:00Z",
    "update_time": "2025-01-10T10:00:00Z",
    "status": 1,
    "user_name": "Google用户",
    "detail": "{\"email\": \"<EMAIL>\"}",
    "area_code": "+86"
}
```

## 🔐 认证说明

### SDK接口认证
- 使用签名验证机制
- 需要正确的appid和签名

### 管理后台接口认证
- 需要JWT Token
- 需要管理员权限
- Token在Header中传递: `Authorization: Bearer <token>`

## 💡 使用示例

### 保存角色信息
```bash
curl -X POST http://localhost:8000/role/save \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "10001",
    "appid": "demo_app_001",
    "role_id": "role_12345",
    "role_name": "勇敢的战士",
    "vip": "5",
    "level": "50"
  }'
```

### 获取用户绑定信息
```bash
curl -X GET "http://localhost:8000/api/v1/user/bind/:user_id?user_id=10001" \
  -H "Authorization: Bearer jwt_token_here"
```

### 获取用户角色列表
```bash
curl -X GET "http://localhost:8000/api/v1/user/roles?user_id=10001" \
  -H "Authorization: Bearer jwt_token_here"
```

## 📝 注意事项

### 角色保存
- 支持角色信息的创建和更新
- 自动设置play_time为当前时间
- 自动设置play_ip为客户端IP
- extend字段支持JSON格式数据

### 用户绑定查询
- 返回所有绑定状态的第三方账号
- 包含区号信息
- detail字段包含第三方平台详细信息

### 角色查询
- 返回用户在所有游戏中的角色
- 支持多服务器角色查询
- 包含完整的角色游戏数据

## 📞 技术支持

如需详细文档，请查看 [完整API文档](./ROLE_AND_USER_API_DOCUMENTATION.md)

技术支持：<EMAIL>
