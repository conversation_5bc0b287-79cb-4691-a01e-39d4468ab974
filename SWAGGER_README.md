# Zeus API Swagger 文档

## 概述

Zeus是一个游戏服务平台，提供用户管理、支付、防沉迷、反垃圾等服务的API接口。

## 功能模块

### 1. 用户管理 (User)
- 用户注册
- 用户登录
- 第三方登录
- 用户信息管理
- 用户绑定/解绑

### 2. 支付系统 (Payment)
- 支付宝支付
- 微信支付
- Google Play支付
- iOS App Store支付
- 订单管理
- 订单验证

### 3. 防沉迷系统 (Anti-Addiction)
- 实名认证
- 支付限制
- 游戏时长控制

### 4. 反垃圾系统 (Antispam)
- 文本内容检测
- 敏感词过滤

### 5. 游戏管理 (Game)
- 游戏信息管理
- 项目配置
- 统计数据

### 6. 第三方支付 (Third Pay)
- Gash支付
- MyCard支付
- 其他第三方支付渠道

### 7. Xsolla集成
- Xsolla支付
- 用户管理

## 访问Swagger文档

### 方法1: 启动演示服务（推荐）
1. 启动演示服务（无需数据库）：
   ```bash
   go run main_swagger_demo.go
   ```

2. 在浏览器中访问：
   ```
   http://localhost:8000/swagger/index.html
   ```

### 方法2: 启动完整服务
1. 配置数据库和Redis（参考config/config.yaml）
2. 启动完整服务：
   ```bash
   go run main.go
   ```

3. 在浏览器中访问：
   ```
   http://localhost:8000/swagger/index.html
   ```

### 方法2: 使用静态HTML文件
1. 在项目根目录下打开 `swagger-ui.html` 文件
2. 确保 `docs/swagger.yaml` 文件存在

### 方法3: 重新生成文档
如果需要更新Swagger文档，可以使用swag工具：

1. 安装swag工具：
   ```bash
   go install github.com/swaggo/swag/cmd/swag@latest
   ```

2. 生成文档：
   ```bash
   swag init
   ```

## API认证

大部分API需要认证，支持以下认证方式：

1. **API Key认证**: 在请求头中添加 `Authorization` 字段
2. **Basic认证**: 使用用户名和密码进行基础认证

## 请求格式

- 请求格式：`application/json`
- 响应格式：`application/json`
- 字符编码：`UTF-8`

## 响应格式

### 成功响应
```json
{
    "error_no": 0,
    "message": "success",
    "result": {
        // 业务数据
    }
}
```

### 错误响应
```json
{
    "error_no": 1001,
    "message": "错误描述"
}
```

## 环境配置

### 开发环境
- Host: `localhost:8000`
- Base Path: `/`

### 生产环境
请根据实际部署环境修改 `docs/docs.go` 中的 `SwaggerInfo` 配置。

## 注意事项

1. 所有时间格式使用 ISO 8601 标准
2. 金额字段使用字符串类型，避免精度丢失
3. 敏感信息（如密码、密钥）在响应中会被过滤
4. 请求频率限制：根据具体接口而定

## 联系方式

如有问题，请联系：
- 邮箱：<EMAIL>
- 文档：http://www.swagger.io/support

## 许可证

Apache 2.0 License
