# Zeus User API 文档

## 📋 概述

Zeus User API 提供完整的用户管理功能，包括用户注册、登录、第三方登录、账号绑定/解绑、用户信息管理等核心功能。

## 🔗 基础信息

- **Base URL**: `http://localhost:8000`
- **API Version**: v1, v2
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 📊 通用响应格式

### 成功响应
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        // 具体业务数据
    }
}
```

### 错误响应
```json
{
    "error_no": 1003,
    "message": "参数缺失"
}
```

## 🔢 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1003 | 参数缺失 |
| 1004 | 数据库更新错误 |
| 1005 | 参数无效 |
| 1006 | 登录失败 |
| 1007 | 注册失败 |
| 1008 | 账号已被封号 |
| 10001 | 账号不存在 |
| 10002 | token错误 |
| 10003 | 已绑定第三方账号 |
| 10004 | 当前账号已被绑定 |
| 10005 | 第三方登录错误 |
| 10017 | 用户不存在 |
| 10018 | 用户已绑定三方账户 |

---

## 1. 👤 用户基础管理

### 1.1 用户注册

**功能描述**: 创建新用户账号，支持设备注册、邮箱注册、手机号注册

**前置条件**:
- 有效的应用ID (appid)
- 设备信息和平台信息

**方法签名**: `POST /user/register`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| appid | string | 是 | 应用ID |
| device | string | 是 | 设备ID |
| platform | string | 是 | 平台标识 (ios/android) |
| force_renew | string | 否 | 是否强制更新 (true/false) |
| mobile_info | string | 否 | 设备信息 |
| email | string | 否 | 邮箱地址 |
| phone | string | 否 | 手机号码 |
| password | string | 否 | 密码（邮箱/手机注册时必填） |
| name | string | 否 | 用户名称 |

**请求示例**:

| 参数 | 值 | 说明 |
|------|-----|------|
| URL | `http://localhost:8000/user/register` | 请求地址 |
| Method | `POST` | 请求方法 |
| Content-Type | `application/json` | 请求头 |
| appid | `demo_app_001` | 应用ID |
| device | `device_12345` | 设备ID |
| platform | `android` | 平台标识 |
| force_renew | `true` | 强制更新 |
| mobile_info | `Android 12, Samsung Galaxy S21` | 设备信息 |

**cURL命令**:
```bash
curl -X POST http://localhost:8000/user/register \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "device": "device_12345",
    "platform": "android",
    "force_renew": "true",
    "mobile_info": "Android 12, Samsung Galaxy S21"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "id": 10001,
        "account": "**********"
    }
}
```

**状态码**:
- 200: 注册成功
- 400: 参数错误或注册失败

**注意事项**:
- 设备注册时不需要密码
- 邮箱/手机注册时密码为必填项
- 同一设备可以强制重新注册（force_renew=true）
- 注册成功后会自动生成唯一的账号ID

---

### 1.2 用户登录

**功能描述**: 用户登录验证，支持设备登录、邮箱登录、手机号登录

**前置条件**:
- 用户已注册
- 提供正确的登录凭据

**方法签名**: `POST /user/login`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| appid | string | 是 | 应用ID |
| device | string | 是 | 设备ID |
| account | string | 否 | 账号 |
| email | string | 否 | 邮箱 |
| phone | string | 否 | 手机号 |
| password | string | 否 | 密码（邮箱/手机登录时必填） |

**请求示例**:
```bash
curl -X POST http://localhost:8000/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "device": "device_12345",
    "account": "**********"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "id": 10001,
        "account": **********,
        "token": "abc123def456ghi789",
        "is_real_name_authentication": false,
        "adult": false
    }
}
```

**状态码**:
- 200: 登录成功
- 400: 登录失败

**注意事项**:
- Token有效期为10分钟（600秒）
- 登录成功后会更新登录时间和登录次数
- 实名认证状态影响游戏功能使用
- 成年状态影响防沉迷限制

---

### 1.3 登录状态验证

**功能描述**: 验证用户登录Token的有效性

**前置条件**:
- 用户已登录
- 提供有效的Token

**方法签名**: `POST /login/verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| appid | string | 是 | 应用ID |
| user_id | string | 是 | 用户ID |
| token | string | 是 | 登录Token |

**请求示例**:
```bash
curl -X POST http://localhost:8000/login/verify \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "user_id": "10001",
    "token": "abc123def456ghi789"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "user_id": 10001,
        "account": "**********"
    }
}
```

**状态码**:
- 200: 验证成功
- 400: Token无效或已过期

**注意事项**:
- Token过期时间为10分钟
- 验证失败需要重新登录
- 用于CP服务端验证用户身份

---

### 1.4 获取用户信息

**功能描述**: 获取用户的基本信息

**前置条件**:
- 用户已存在
- 提供有效的用户ID

**方法签名**: `POST /user/info`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | string | 是 | 用户ID |

**请求示例**:
```bash
curl -X POST http://localhost:8000/user/info \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "10001"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "name": "用户昵称",
        "account": "**********"
    }
}
```

**状态码**:
- 200: 获取成功
- 400: 用户不存在

**注意事项**:
- 只返回基本的公开信息
- 敏感信息不会在此接口返回

---

### 1.5 更新用户信息

**功能描述**: 更新用户的基本信息

**前置条件**:
- 用户已存在
- 提供有效的用户ID

**方法签名**: `POST /user/update`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | string | 是 | 用户ID |
| name | string | 否 | 用户昵称 |

**请求示例**:
```bash
curl -X POST http://localhost:8000/user/update \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "10001",
    "name": "新的昵称"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功"
}
```

**状态码**:
- 200: 更新成功
- 400: 更新失败

**注意事项**:
- 目前只支持更新用户昵称
- 昵称长度限制为20个字符

---

## 2. 🔗 第三方登录管理

### 2.1 第三方登录 (V1 - 已废弃)

**功能描述**: 使用第三方账号进行登录（此接口已废弃，建议使用V2版本）

**前置条件**:
- 第三方账号有效
- 游戏已配置第三方登录

**方法签名**: `POST /user/third-login`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "device": "string",         // 必填，设备ID
    "type": "string",           // 必填，第三方类型 (1:Google, 2:Facebook, 3:Apple等)
    "union_id": "string",       // 必填，第三方唯一标识
    "platform": "string",      // 必填，平台标识
    "token": "string",          // 可选，第三方Token
    "secret_token": "string",   // 可选，第三方密钥Token
    "mobile_info": "string"     // 可选，设备信息
}
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "id": "10001",
        "account": "**********",
        "token": "abc123def456ghi789"
    }
}
```

**注意事项**:
- 此接口已废弃，建议使用 `/v2/user/third-login`
- 如果第三方账号未绑定，会自动创建新用户

---

### 2.2 第三方登录 (V2 - 推荐)

**功能描述**: 使用第三方账号进行登录，支持多角色返回

**前置条件**:
- 第三方账号有效
- 游戏已配置第三方登录

**方法签名**: `POST /v2/user/third-login`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "type": "int",              // 必填，第三方类型
    "union_id": "string",       // 可选，第三方唯一标识
    "token": "string",          // 可选，第三方Token
    "secret_token": "string",   // 可选，第三方密钥Token
    "user_name": "string",      // 可选，第三方用户名
    "device": "string",         // 必填，设备ID
    "platform": "string",      // 必填，平台标识
    "mobile_info": "string"     // 可选，设备信息
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/v2/user/third-login \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "type": 1,
    "union_id": "google_user_12345",
    "token": "google_access_token",
    "device": "device_12345",
    "platform": "android"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": [
        {
            "id": "10001",
            "account": "**********",
            "token": "abc123def456ghi789",
            "login_time": "2025-06-10T17:40:00Z",
            "roles": [
                {
                    "server_id": "s001",
                    "server_name": "服务器1",
                    "role_id": "role_001",
                    "role_name": "角色名称",
                    "vip": 5,
                    "level": 50,
                    "extend": "{\"custom_data\": \"value\"}"
                }
            ]
        }
    ]
}
```

**状态码**:
- 200: 登录成功
- 400: 登录失败

**注意事项**:
- 支持返回多个角色信息
- 如果第三方账号未绑定，会自动创建新用户
- 支持多账号绑定同一第三方账号（根据游戏配置）

---

## 3. 🔗 第三方账号绑定管理

### 3.1 绑定第三方账号

**功能描述**: 将第三方账号绑定到现有用户账号

**前置条件**:
- 用户已存在且已登录
- 第三方账号有效且未被其他用户绑定
- 游戏已配置相应的第三方登录

**方法签名**: `POST /user/bind-third`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "type": "int",              // 必填，第三方类型 (1:Google, 2:Facebook, 3:Apple等)
    "union_id": "string",       // 可选，第三方唯一标识
    "token": "string",          // 可选，第三方Token
    "secret_token": "string",   // 可选，第三方密钥Token
    "user_name": "string",      // 可选，第三方用户名
    "user_id": "int"            // 必填，要绑定的用户ID
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/user/bind-third \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "type": 1,
    "union_id": "google_user_12345",
    "token": "google_access_token",
    "user_id": 10001
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "appid": "demo_app_001",
        "type": 1,
        "union_id": "google_user_12345",
        "token": "google_access_token",
        "user_id": 10001
    }
}
```

**状态码**:
- 200: 绑定成功
- 400: 绑定失败

**注意事项**:
- 每个用户对同一第三方类型只能绑定一次
- 第三方账号绑定数量受游戏配置限制
- 绑定成功后可以使用第三方账号登录

---

### 3.2 解绑第三方账号

**功能描述**: 解除用户与第三方账号的绑定关系

**前置条件**:
- 用户已存在
- 第三方账号已绑定到该用户
- 提供正确的绑定信息

**方法签名**: `POST /user/unbind-third`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "type": "int",              // 必填，第三方类型
    "union_id": "string",       // 可选，第三方唯一标识
    "token": "string",          // 可选，第三方Token
    "secret_token": "string",   // 可选，第三方密钥Token
    "user_name": "string",      // 可选，第三方用户名
    "user_id": "int"            // 必填，用户ID
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/user/unbind-third \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "type": 1,
    "union_id": "google_user_12345",
    "user_id": 10001
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "type": "1"
    }
}
```

**状态码**:
- 200: 解绑成功
- 400: 解绑失败

**注意事项**:
- 解绑后无法再使用该第三方账号登录
- 解绑不会删除用户账号，只是移除绑定关系
- 需要验证第三方账号的有效性

---

### 3.3 获取用户绑定列表

**功能描述**: 获取用户已绑定的第三方账号列表

**前置条件**:
- 用户已存在
- 提供有效的用户ID

**方法签名**: `POST /user/bind-list`

**请求参数**:
```json
{
    "user_id": "string"         // 必填，用户ID
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/user/bind-list \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "10001"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": [
        {
            "type": 1,
            "type_name": "Google",
            "union_id": "google_user_12345",
            "user_name": "Google用户",
            "bind_time": "2025-06-10T17:40:00Z",
            "status": 1
        },
        {
            "type": 3,
            "type_name": "Apple",
            "union_id": "apple_user_67890",
            "user_name": "Apple用户",
            "bind_time": "2025-06-09T15:30:00Z",
            "status": 1
        }
    ]
}
```

**状态码**:
- 200: 获取成功
- 400: 获取失败

**注意事项**:
- 只返回已绑定且状态正常的第三方账号
- 包含绑定时间和第三方用户名信息
- 敏感信息（如Token）不会返回

---

### 3.4 获取游戏第三方登录配置

**功能描述**: 获取游戏支持的第三方登录方式和配置信息

**前置条件**:
- 提供有效的应用ID
- 游戏已配置第三方登录

**方法签名**: `POST /user/third-list`

**请求参数**:
```json
{
    "appid": "string"           // 必填，应用ID
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/user/third-list \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "is_google_login": 1,
        "google_client_id": "google_client_id_12345",
        "is_facebook_login": 1,
        "is_apple_login": 1,
        "is_line_login": 1,
        "line_appid": "line_app_id_67890"
    }
}
```

**状态码**:
- 200: 获取成功
- 400: 获取失败

**注意事项**:
- 返回的配置信息会被缓存，提高响应速度
- 只返回已开启的第三方登录方式
- 包含客户端所需的配置参数（如Client ID）

---

### 3.5 根据第三方ID获取用户信息

**功能描述**: 通过第三方账号的union_id查询对应的用户账号信息

**前置条件**:
- 第三方账号已绑定到用户
- 提供有效的union_id和类型

**方法签名**: `POST /user/third-user`

**请求参数**:
```json
{
    "union_id": "string",       // 必填，第三方唯一标识
    "type": "int"               // 必填，第三方类型
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/user/third-user \
  -H "Content-Type: application/json" \
  -d '{
    "union_id": "google_user_12345",
    "type": 1
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "account": "**********",
        "user_id": 10001,
        "bind_time": "2025-06-10T17:40:00Z"
    }
}
```

**状态码**:
- 200: 查询成功
- 400: 查询失败或未找到

**注意事项**:
- 用于验证第三方账号是否已绑定
- 可用于防止重复绑定
- 只返回基本的账号信息

---

## 4. 👥 用户账号管理

### 4.1 用户注销

**功能描述**: 注销用户账号，标记账号为删除状态

**前置条件**:
- 用户账号存在
- 提供有效的应用ID和账号

**方法签名**: `POST /user/unregister`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "account": "string"         // 必填，用户账号
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/user/unregister \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "account": "**********"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "账号注销成功"
}
```

**状态码**:
- 200: 注销成功
- 400: 注销失败

**注意事项**:
- 注销操作不可逆，请谨慎使用
- 注销后账号无法再次登录
- 相关的游戏数据可能会被清理

---

### 4.2 撤销Apple Token

**功能描述**: 撤销Apple登录的授权Token

**前置条件**:
- 用户使用Apple登录
- 提供有效的Apple Token

**方法签名**: `POST /user/unregister/apple_token`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "token": "string"           // 必填，Apple授权Token
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/user/unregister/apple_token \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "token": "apple_auth_token_12345"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "apple token 撤销成功"
}
```

**状态码**:
- 200: 撤销成功
- 400: 撤销失败

**注意事项**:
- 符合Apple的隐私政策要求
- 撤销后用户需要重新授权Apple登录
- 用于处理用户删除应用时的清理工作

---

## 5. 🎮 小游戏用户管理

### 5.1 小游戏用户登录

**功能描述**: 小游戏平台的用户登录接口

**前置条件**:
- 用户已在小游戏平台注册
- 提供有效的账号和设备信息

**方法签名**: `POST /minigame/user/login`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "device": "string",         // 必填，设备ID
    "account": "string"         // 必填，用户账号
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/minigame/user/login \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "minigame_app_001",
    "device": "device_12345",
    "account": "**********"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "id": 10001,
        "account": "**********",
        "token": "abc123def456ghi789",
        "login_time": "2025-06-10T17:40:00Z",
        "detail": {
            "platform": "wechat_minigame",
            "version": "1.0.0"
        }
    }
}
```

**状态码**:
- 200: 登录成功
- 400: 登录失败

**注意事项**:
- 专为小游戏平台设计的登录接口
- 返回详细的登录信息和平台相关数据
- 支持微信小游戏、抖音小游戏等平台

---

### 5.2 小游戏第三方登录

**功能描述**: 小游戏平台的第三方登录接口

**前置条件**:
- 第三方平台授权有效
- 提供有效的授权码

**方法签名**: `POST /minigame/user/third-login`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "device": "string",         // 必填，设备ID
    "type": "int",              // 必填，第三方类型
    "code": "string",           // 必填，授权码
    "platform": "string",      // 必填，平台标识
    "sdk_version": "string",    // 可选，SDK版本
    "mobile_info": "string"     // 可选，设备信息
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/minigame/user/third-login \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "minigame_app_001",
    "device": "device_12345",
    "type": 1,
    "code": "wechat_auth_code_12345",
    "platform": "wechat_minigame"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "id": 10001,
        "account": "**********",
        "token": "abc123def456ghi789",
        "login_time": "2025-06-10T17:40:00Z",
        "detail": {
            "union_id": "wechat_union_id_12345",
            "open_id": "wechat_open_id_67890",
            "nickname": "微信用户",
            "avatar": "https://avatar.url"
        }
    }
}
```

**状态码**:
- 200: 登录成功
- 400: 登录失败

**注意事项**:
- 支持微信、抖音等小游戏平台的第三方登录
- 自动处理用户注册和绑定逻辑
- 返回第三方平台的用户详细信息

---

### 5.3 小游戏用户查询

**功能描述**: 通过第三方平台的open_id查询用户账号信息

**前置条件**:
- 用户已通过第三方登录
- 提供有效的open_id

**方法签名**: `POST /minigame/user/query`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "type": "int",              // 必填，第三方类型
    "open_id": "string"         // 必填，第三方open_id
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/minigame/user/query \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "minigame_app_001",
    "type": 1,
    "open_id": "wechat_open_id_67890"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "account": "**********"
    }
}
```

**状态码**:
- 200: 查询成功
- 400: 查询失败或用户不存在

**注意事项**:
- 用于验证用户是否已注册
- 只返回基本的账号信息
- 支持跨平台用户查询

---

## 6. 📧 账号系统管理

### 6.1 邮箱注册

**功能描述**: 使用邮箱地址注册新账号

**前置条件**:
- 提供有效的邮箱地址
- 邮箱未被其他账号使用
- 通过邮箱验证码验证

**方法签名**: `POST /account/register-with-email`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "email": "string",          // 必填，邮箱地址
    "password": "string",       // 必填，密码
    "captcha": "string"         // 可选，验证码
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/account/register-with-email \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "email": "<EMAIL>",
    "password": "password123",
    "captcha": "123456"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "union_id": "email_union_id_12345",
        "token": "account_token_67890"
    }
}
```

**状态码**:
- 200: 注册成功
- 400: 注册失败

**注意事项**:
- 密码会进行MD5加密存储
- 邮箱地址必须符合格式要求
- 可能需要邮箱验证码验证

---

### 6.2 邮箱登录

**功能描述**: 使用邮箱和密码进行登录

**前置条件**:
- 邮箱账号已注册
- 提供正确的密码

**方法签名**: `POST /account/login-with-email`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "email": "string",          // 必填，邮箱地址
    "password": "string"        // 必填，密码
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/account/login-with-email \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "union_id": "email_union_id_12345",
        "token": "account_token_67890"
    }
}
```

**状态码**:
- 200: 登录成功
- 400: 登录失败

**注意事项**:
- 密码错误会返回登录失败
- 邮箱地址必须已注册
- 返回的token用于后续身份验证

---

### 6.3 手机号登录

**功能描述**: 使用手机号和验证码进行登录

**前置条件**:
- 手机号已注册或支持自动注册
- 获取有效的短信验证码

**方法签名**: `POST /account/login-with-phone`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "area_code": "string",      // 必填，区号
    "phone": "string",          // 必填，手机号
    "captcha": "string"         // 必填，验证码
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/account/login-with-phone \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "area_code": "86",
    "phone": "***********",
    "captcha": "123456"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "union_id": "phone_union_id_12345",
        "token": "account_token_67890"
    }
}
```

**状态码**:
- 200: 登录成功
- 400: 登录失败

**注意事项**:
- 验证码有效期为5分钟
- 支持多个国家和地区的手机号
- 验证码错误或过期会导致登录失败

---

### 6.4 发送邮箱验证码

**功能描述**: 向指定邮箱发送验证码

**前置条件**:
- 提供有效的邮箱地址
- 未超过发送频率限制

**方法签名**: `POST /account/send-email-captcha`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "language": "string",       // 必填，语言代码 (zh/en)
    "email": "string"           // 必填，邮箱地址
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/account/send-email-captcha \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "language": "zh",
    "email": "<EMAIL>"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "验证码发送成功"
}
```

**状态码**:
- 200: 发送成功
- 400: 发送失败

**注意事项**:
- 验证码有效期为30分钟
- 目前无发送频率限制（可无限制发送）
- 每次发送新验证码会覆盖之前的验证码
- 支持多语言邮件模板
- 建议客户端实现前端限制避免频繁发送

---

### 6.5 发送短信验证码

**功能描述**: 向指定手机号发送短信验证码

**前置条件**:
- 提供有效的手机号
- 未超过发送频率限制

**方法签名**: `POST /account/send-sms-captcha`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "area_code": "string",      // 必填，区号
    "phone": "string",          // 必填，手机号
    "language": "string"        // 必填，语言代码
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/account/send-sms-captcha \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "area_code": "86",
    "phone": "***********",
    "language": "zh"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "验证码发送成功"
}
```

**状态码**:
- 200: 发送成功
- 400: 发送失败

**注意事项**:
- 验证码有效期为5分钟
- 目前无发送频率限制（可无限制发送）
- 每次发送新验证码会覆盖之前的验证码
- 仅支持配置的区号：+86(中国大陆)、+886(台湾)、+852(香港)、+853(澳门)、+82(韩国)
- 验证码错误或过期会导致登录失败
- 建议客户端实现前端限制避免频繁发送

---

### 6.6 获取支持的区号

**功能描述**: 获取系统支持的国家和地区区号列表

**前置条件**:
- 提供有效的应用ID

**方法签名**: `POST /account/get-area-codes`

**请求参数**:
```json
{
    "appid": "string"           // 必填，应用ID
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/account/get-area-codes \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": [
        {
            "area_code": "86",
            "area_name": "中国大陆"
        },
        {
            "area_code": "1",
            "area_name": "美国"
        },
        {
            "area_code": "81",
            "area_name": "日本"
        }
    ]
}
```

**状态码**:
- 200: 获取成功
- 400: 获取失败

**注意事项**:
- 返回游戏配置支持的区号列表
- 用于前端显示区号选择器
- 不同游戏可能支持不同的区号

---

### 6.7 修改密码

**功能描述**: 修改邮箱账号的登录密码

**前置条件**:
- 邮箱账号已注册
- 通过邮箱验证码验证

**方法签名**: `POST /account/modify-password`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "email": "string",          // 必填，邮箱地址
    "password": "string",       // 必填，新密码
    "captcha": "string"         // 必填，邮箱验证码
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/account/modify-password \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "email": "<EMAIL>",
    "password": "new_password123",
    "captcha": "123456"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "密码修改成功"
}
```

**状态码**:
- 200: 修改成功
- 400: 修改失败

**注意事项**:
- 需要先发送邮箱验证码
- 新密码会进行MD5加密存储
- 修改成功后需要重新登录

---

### 6.8 重置密码

**功能描述**: 重置忘记的邮箱账号密码

**前置条件**:
- 邮箱账号已注册
- 通过邮箱验证码验证

**方法签名**: `POST /account/reset-password`

**请求参数**:
```json
{
    "appid": "string",          // 必填，应用ID
    "email": "string",          // 必填，邮箱地址
    "password": "string",       // 必填，新密码
    "captcha": "string"         // 必填，邮箱验证码
}
```

**请求示例**:
```bash
curl -X POST http://localhost:8000/account/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "appid": "demo_app_001",
    "email": "<EMAIL>",
    "password": "reset_password123",
    "captcha": "123456"
  }'
```

**返回示例**:
```json
{
    "error_no": 0,
    "message": "密码重置成功"
}
```

**状态码**:
- 200: 重置成功
- 400: 重置失败

**注意事项**:
- 功能与修改密码类似，用于忘记密码场景
- 需要邮箱验证码验证身份
- 重置成功后可使用新密码登录

---

## 7. 📊 管理后台用户接口

### 7.1 获取用户信息 (管理后台)

**功能描述**: 管理后台获取用户详细信息

**前置条件**:
- 管理员已登录
- 提供有效的JWT Token

**方法签名**: `GET /api/v1/user/info`

**请求参数**: Query参数
- `user_id`: 用户ID
- `game_id`: 游戏ID

**请求示例**:
```bash
curl -X GET "http://localhost:8000/api/v1/user/info?user_id=10001&game_id=1" \
  -H "Authorization: Bearer jwt_token_here"
```

**返回示例**:
```json
{
    "code": 0,
    "message": "成功",
    "result": {
        "id": 10001,
        "account": "**********",
        "name": "用户昵称",
        "email": "<EMAIL>",
        "phone": "***********",
        "platform": "android",
        "register_time": "2025-06-10T10:00:00Z",
        "login_time": "2025-06-10T17:40:00Z",
        "login_count": 15,
        "status": 0,
        "is_real_name_authentication": 1
    }
}
```

**状态码**:
- 200: 获取成功
- 400: 获取失败
- 401: 未授权

**注意事项**:
- 需要管理员权限
- 返回完整的用户信息
- 包含敏感信息，仅限管理后台使用

---

## 📝 总结

Zeus User API 提供了完整的用户管理功能，涵盖：

1. **基础用户管理**: 注册、登录、信息获取和更新
2. **第三方登录**: 支持多种第三方平台的登录和绑定
3. **账号绑定管理**: 第三方账号的绑定、解绑和查询
4. **用户账号管理**: 账号注销和Apple Token撤销
5. **小游戏支持**: 专门的小游戏平台用户管理
6. **账号系统**: 邮箱、手机号注册登录和验证码服务
7. **管理后台**: 管理员用户信息查询

### 🔐 安全特性

- 密码MD5加密存储
- Token有效期控制
- 验证码防刷机制
- 第三方账号验证
- JWT权限控制

### 🌍 多平台支持

- iOS/Android原生应用
- 微信小游戏
- 抖音小游戏
- 网页游戏
- 多国家地区支持

### 📈 性能优化

- Redis缓存机制
- 配置信息缓存
- 数据库连接池
- 异步处理机制

如需更多详细信息或技术支持，请联系开发团队。