# Zeus User API 快速参考

## 📋 接口概览

### 1. 用户基础管理
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 用户注册 | POST | `/user/register` | 创建新用户账号 |
| 用户登录 | POST | `/user/login` | 用户登录验证 |
| 登录验证 | POST | `/login/verify` | 验证Token有效性 |
| 获取用户信息 | POST | `/user/info` | 获取用户基本信息 |
| 更新用户信息 | POST | `/user/update` | 更新用户昵称等信息 |

### 2. 第三方登录管理
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 第三方登录(V1) | POST | `/user/third-login` | 第三方登录(已废弃) |
| 第三方登录(V2) | POST | `/v2/user/third-login` | 第三方登录(推荐) |

### 3. 第三方账号绑定
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 绑定第三方 | POST | `/user/bind-third` | 绑定第三方账号 |
| 解绑第三方 | POST | `/user/unbind-third` | 解绑第三方账号 |
| 绑定列表 | POST | `/user/bind-list` | 获取绑定列表 |
| 第三方配置 | POST | `/user/third-list` | 获取第三方登录配置 |
| 第三方用户查询 | POST | `/user/third-user` | 根据union_id查询用户 |

### 4. 用户账号管理
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 用户注销 | POST | `/user/unregister` | 注销用户账号 |
| 撤销Apple Token | POST | `/user/unregister/apple_token` | 撤销Apple授权 |

### 5. 小游戏用户管理
| 接口 | 方径 | 路径 | 描述 |
|------|------|------|------|
| 小游戏登录 | POST | `/minigame/user/login` | 小游戏用户登录 |
| 小游戏第三方登录 | POST | `/minigame/user/third-login` | 小游戏第三方登录 |
| 小游戏用户查询 | POST | `/minigame/user/query` | 通过open_id查询用户 |

### 6. 账号系统管理
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 邮箱注册 | POST | `/account/register-with-email` | 邮箱注册账号 |
| 邮箱登录 | POST | `/account/login-with-email` | 邮箱登录 |
| 手机号登录 | POST | `/account/login-with-phone` | 手机号登录 |
| 发送邮箱验证码 | POST | `/account/send-email-captcha` | 发送邮箱验证码 |
| 发送短信验证码 | POST | `/account/send-sms-captcha` | 发送短信验证码 |
| 获取区号列表 | POST | `/account/get-area-codes` | 获取支持的区号 |
| 修改密码 | POST | `/account/modify-password` | 修改邮箱密码 |
| 重置密码 | POST | `/account/reset-password` | 重置邮箱密码 |

### 7. 管理后台接口
| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 获取用户信息 | GET | `/api/v1/user/info` | 管理后台获取用户信息 |

## 🔢 常用错误码

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1003 | 参数缺失 |
| 1004 | 数据库更新错误 |
| 1005 | 参数无效 |
| 1006 | 登录失败 |
| 1007 | 注册失败 |
| 1008 | 账号已被封号 |
| 10001 | 账号不存在 |
| 10002 | token错误 |
| 10003 | 已绑定第三方账号 |
| 10004 | 当前账号已被绑定 |
| 10005 | 第三方登录错误 |
| 10017 | 用户不存在 |
| 10018 | 用户已绑定三方账户 |

## 🔐 第三方类型说明

| 类型值 | 平台 | 说明 |
|--------|------|------|
| 1 | Google | Google登录 |
| 2 | Facebook | Facebook登录 |
| 3 | Apple | Apple登录 |
| 4 | Line | Line登录 |
| 5 | WeChat | 微信登录 |
| 6 | QQ | QQ登录 |

## 📱 平台标识说明

| 平台 | 说明 |
|------|------|
| ios | iOS平台 |
| android | Android平台 |
| wechat_minigame | 微信小游戏 |
| douyin_minigame | 抖音小游戏 |
| web | 网页版 |

## 🌍 语言代码说明

| 代码 | 语言 |
|------|------|
| zh | 中文 |
| en | 英文 |
| jp | 日文 |

## 📞 联系方式

如需详细文档，请查看 [完整API文档](./USER_API_DOCUMENTATION.md)

技术支持：<EMAIL>
