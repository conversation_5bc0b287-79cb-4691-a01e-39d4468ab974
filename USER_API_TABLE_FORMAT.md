# Zeus User API 文档 - 表格格式

## 📋 概述

Zeus User API 提供完整的用户管理功能，包括用户注册、登录、第三方登录、账号绑定/解绑、用户信息管理等核心功能。

## 🔗 基础信息

- **Base URL**: `http://localhost:8000`
- **API Version**: v1, v2
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

## 📊 通用响应格式

### 成功响应
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        // 具体业务数据
    }
}
```

### 错误响应
```json
{
    "error_no": 1003,
    "message": "参数缺失"
}
```

## 🔢 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1003 | 参数缺失 |
| 1004 | 数据库更新错误 |
| 1005 | 参数无效 |
| 1006 | 登录失败 |
| 1007 | 注册失败 |
| 1008 | 账号已被封号 |
| 10001 | 账号不存在 |
| 10002 | token错误 |
| 10003 | 已绑定第三方账号 |
| 10004 | 当前账号已被绑定 |
| 10005 | 第三方登录错误 |
| 10017 | 用户不存在 |
| 10018 | 用户已绑定三方账户 |

---

## 1. 👤 用户基础管理

### 1.1 用户注册

**功能描述**: 创建新用户账号，支持设备注册、邮箱注册、手机号注册

**前置条件**: 
- 有效的应用ID (appid)
- 设备信息和平台信息

**方法签名**: `POST /user/register`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| device | string | 是 | 设备ID | device_12345 |
| platform | string | 是 | 平台标识 (ios/android) | android |
| force_renew | string | 否 | 是否强制更新 (true/false) | true |
| mobile_info | string | 否 | 设备信息 | Android 12, Samsung Galaxy S21 |
| email | string | 否 | 邮箱地址 | <EMAIL> |
| phone | string | 否 | 手机号码 | *********** |
| password | string | 否 | 密码（邮箱/手机注册时必填） | password123 |
| name | string | 否 | 用户名称 | 用户昵称 |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "id": 10001,
        "account": "**********"
    }
}
```

**状态码**: 
- 200: 注册成功
- 400: 参数错误或注册失败

---

### 1.2 用户登录

**功能描述**: 用户登录验证，支持设备登录、邮箱登录、手机号登录

**前置条件**: 
- 用户已注册
- 提供正确的登录凭据

**方法签名**: `POST /user/login`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| device | string | 是 | 设备ID | device_12345 |
| account | string | 否 | 账号 | ********** |
| email | string | 否 | 邮箱 | <EMAIL> |
| phone | string | 否 | 手机号 | *********** |
| password | string | 否 | 密码（邮箱/手机登录时必填） | password123 |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "id": 10001,
        "account": **********,
        "token": "abc123def456ghi789",
        "is_real_name_authentication": false,
        "adult": false
    }
}
```

**状态码**: 
- 200: 登录成功
- 400: 登录失败

---

### 1.3 登录状态验证

**功能描述**: 验证用户登录Token的有效性

**前置条件**: 
- 用户已登录
- 提供有效的Token

**方法签名**: `POST /login/verify`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| user_id | string | 是 | 用户ID | 10001 |
| token | string | 是 | 登录Token | abc123def456ghi789 |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "user_id": 10001,
        "account": "**********"
    }
}
```

**状态码**: 
- 200: 验证成功
- 400: Token无效或已过期

---

### 1.4 获取用户信息

**功能描述**: 获取用户的基本信息

**前置条件**: 
- 用户已存在
- 提供有效的用户ID

**方法签名**: `POST /user/info`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| user_id | string | 是 | 用户ID | 10001 |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "name": "用户昵称",
        "account": "**********"
    }
}
```

**状态码**: 
- 200: 获取成功
- 400: 用户不存在

---

### 1.5 更新用户信息

**功能描述**: 更新用户的基本信息

**前置条件**: 
- 用户已存在
- 提供有效的用户ID

**方法签名**: `POST /user/update`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| user_id | string | 是 | 用户ID | 10001 |
| name | string | 否 | 用户昵称 | 新的昵称 |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功"
}
```

**状态码**: 
- 200: 更新成功
- 400: 更新失败

---

## 2. 🔗 第三方登录管理

### 2.1 第三方登录 (V2 - 推荐)

**功能描述**: 使用第三方账号进行登录，支持多角色返回

**前置条件**: 
- 第三方账号有效
- 游戏已配置第三方登录

**方法签名**: `POST /v2/user/third-login`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| type | int | 是 | 第三方类型 | 1 |
| union_id | string | 否 | 第三方唯一标识 | google_user_12345 |
| token | string | 否 | 第三方Token | google_access_token |
| secret_token | string | 否 | 第三方密钥Token | google_secret_token |
| user_name | string | 否 | 第三方用户名 | Google用户 |
| device | string | 是 | 设备ID | device_12345 |
| platform | string | 是 | 平台标识 | android |
| mobile_info | string | 否 | 设备信息 | Android 12 |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": [
        {
            "id": "10001",
            "account": "**********",
            "token": "abc123def456ghi789",
            "login_time": "2025-06-10T17:40:00Z",
            "roles": [
                {
                    "server_id": "s001",
                    "server_name": "服务器1",
                    "role_id": "role_001",
                    "role_name": "角色名称",
                    "vip": 5,
                    "level": 50,
                    "extend": "{\"custom_data\": \"value\"}"
                }
            ]
        }
    ]
}
```

**状态码**: 
- 200: 登录成功
- 400: 登录失败

---

## 3. 🔗 第三方账号绑定管理

### 3.1 绑定第三方账号

**功能描述**: 将第三方账号绑定到现有用户账号

**前置条件**: 
- 用户已存在且已登录
- 第三方账号有效且未被其他用户绑定
- 游戏已配置相应的第三方登录

**方法签名**: `POST /user/bind-third`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| type | int | 是 | 第三方类型 (1:Google, 2:Facebook, 3:Apple等) | 1 |
| union_id | string | 否 | 第三方唯一标识 | google_user_12345 |
| token | string | 否 | 第三方Token | google_access_token |
| secret_token | string | 否 | 第三方密钥Token | google_secret_token |
| user_name | string | 否 | 第三方用户名 | Google用户 |
| user_id | int | 是 | 要绑定的用户ID | 10001 |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "appid": "demo_app_001",
        "type": 1,
        "union_id": "google_user_12345",
        "token": "google_access_token",
        "user_id": 10001
    }
}
```

**状态码**: 
- 200: 绑定成功
- 400: 绑定失败

---

### 3.2 解绑第三方账号

**功能描述**: 解除用户与第三方账号的绑定关系

**前置条件**: 
- 用户已存在
- 第三方账号已绑定到该用户
- 提供正确的绑定信息

**方法签名**: `POST /user/unbind-third`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| type | int | 是 | 第三方类型 | 1 |
| union_id | string | 否 | 第三方唯一标识 | google_user_12345 |
| token | string | 否 | 第三方Token | google_access_token |
| secret_token | string | 否 | 第三方密钥Token | google_secret_token |
| user_name | string | 否 | 第三方用户名 | Google用户 |
| user_id | int | 是 | 用户ID | 10001 |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "type": "1"
    }
}
```

**状态码**: 
- 200: 解绑成功
- 400: 解绑失败

---

## 4. 📧 账号系统管理

### 4.1 邮箱注册

**功能描述**: 使用邮箱地址注册新账号

**前置条件**: 
- 提供有效的邮箱地址
- 邮箱未被其他账号使用
- 通过邮箱验证码验证

**方法签名**: `POST /account/register-with-email`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| email | string | 是 | 邮箱地址 | <EMAIL> |
| password | string | 是 | 密码 | password123 |
| captcha | string | 否 | 验证码 | 123456 |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "union_id": "email_union_id_12345",
        "token": "account_token_67890"
    }
}
```

**状态码**: 
- 200: 注册成功
- 400: 注册失败

---

### 4.2 手机号登录

**功能描述**: 使用手机号和验证码进行登录

**前置条件**: 
- 手机号已注册或支持自动注册
- 获取有效的短信验证码

**方法签名**: `POST /account/login-with-phone`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | demo_app_001 |
| area_code | string | 是 | 区号 | 86 |
| phone | string | 是 | 手机号 | *********** |
| captcha | string | 是 | 验证码 | 123456 |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "union_id": "phone_union_id_12345",
        "token": "account_token_67890"
    }
}
```

**状态码**: 
- 200: 登录成功
- 400: 登录失败

---

## 5. 🎮 小游戏用户管理

### 5.1 小游戏用户登录

**功能描述**: 小游戏平台的用户登录接口

**前置条件**: 
- 用户已在小游戏平台注册
- 提供有效的账号和设备信息

**方法签名**: `POST /minigame/user/login`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| appid | string | 是 | 应用ID | minigame_app_001 |
| device | string | 是 | 设备ID | device_12345 |
| account | string | 是 | 用户账号 | ********** |

**返回示例**:
```json
{
    "error_no": 0,
    "message": "成功",
    "result": {
        "id": 10001,
        "account": "**********",
        "token": "abc123def456ghi789",
        "login_time": "2025-06-10T17:40:00Z",
        "detail": {
            "platform": "wechat_minigame",
            "version": "1.0.0"
        }
    }
}
```

**状态码**: 
- 200: 登录成功
- 400: 登录失败

---

## 📝 总结

Zeus User API 提供了完整的用户管理功能，涵盖：

1. **基础用户管理**: 注册、登录、信息获取和更新
2. **第三方登录**: 支持多种第三方平台的登录和绑定
3. **账号绑定管理**: 第三方账号的绑定、解绑和查询
4. **用户账号管理**: 账号注销和Apple Token撤销
5. **小游戏支持**: 专门的小游戏平台用户管理
6. **账号系统**: 邮箱、手机号注册登录和验证码服务
7. **管理后台**: 管理员用户信息查询

### 🔐 安全特性

- 密码MD5加密存储
- Token有效期控制
- 验证码防刷机制
- 第三方账号验证
- JWT权限控制

### 🌍 多平台支持

- iOS/Android原生应用
- 微信小游戏
- 抖音小游戏
- 网页游戏
- 多国家地区支持

如需更多详细信息或技术支持，请联系开发团队。
