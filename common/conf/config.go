package conf

import (
	"errors"
	"strings"
	"sync"

	"github.com/spf13/viper"
)

type DBConfig struct {
	DriverName string
	Host       string
	Port       string
	Username   string
	Password   string
	Database   string
	Charset    string
}

type RedisConfig struct {
	Host     string
	Password string
	Database int
}

type EmailTemplate struct {
	Subject string
	Body    string
}

type EmailTemplates map[string]map[string]*EmailTemplate

func (ts EmailTemplates) GetTemplate(language string) (*EmailTemplate, error) {
	language = strings.ToLower(language)
	q := strings.Split(language, "-")

	// 短信模版匹配策略
	// 1. 先匹配语言
	// 2、再匹配语言下对应地区的短信模版
	// 3、如果没有匹配到，则返回该语言下的默认短信模版
	// 4、如果还是没有匹配到对应语言，则返回默认语言的默认短信模版
	lang := q[0]
	region := q[len(q)-1]
	if v, ok := ts[lang]; ok {
		if res, ok := v[region]; ok {
			return res, nil
		}
		if res, ok := v["default"]; ok {
			return res, nil
		}
	}
	if v, ok := ts["en"]; ok {
		if res, ok := v["default"]; ok {
			return res, nil
		}
	}

	return nil, errors.New("缺少邮箱模版配置")
}

type EmailService struct {
	User      string
	Sender    string
	Password  string
	Host      string
	Templates EmailTemplates
}

type SMSArea struct {
	AreaCode     string
	AreaName     string
	TemplateCode string
	SignName     string
}

type SMSAreas []SMSArea

func (ts SMSAreas) GetAreaByCode(areaCode string) (*SMSArea, error) {
	for _, t := range ts {
		if t.AreaCode == areaCode {
			return &t, nil
		}
	}
	return nil, errors.New("缺少短信模版配置")
}

type SMS struct {
	AccessKey string
	SecretKey string
	Areas     SMSAreas
}

type GormConfig struct {
	SlowThreshold int
}

type Config struct {
	DB                         DBConfig
	Redis                      RedisConfig
	Debug                      bool
	Ucenter                    Ucenter
	ThirdPaySecret             string
	AntiAddiction              AntiAddiction
	AntispamInfo               AntispamInfo
	GormConfig                 GormConfig
	RunCronJob                 bool
	TracerInfo                 TracerInfo
	EmailService               EmailService
	SMS                        SMS
	OrderAlertRules            []OrderAlertRule
	AilYunIdentifyCheckAppCode string
	StaticTarget               StaticTarget
	PayLimit                   []PayLimit
}

type AntiAddiction struct {
	FestivalTime []string
	Exception    []string
	StartTime    string
	EndTime      string
}

type AntispamInfo struct {
	ApiURL string
}

type OrderAlertRule struct {
	Desc         string
	TimeInterval int
	Threshold    int
}

type LogError struct {
	Message string
	Err     string
	Data    interface{}
}

type Ucenter struct {
	APIKey    string
	APISecret string
}

type TracerInfo struct {
	ServiceName    string
	Endpoint       string
	Authentication string
}

type StaticTarget struct {
	AcceleratedDomain string
	EndPoint          string
	AccessKeyID       string
	AccessKeySecret   string
	BucketName        string
}
type PayLimitInformation struct {
	TextTitle    string
	TextContent  string
	LimitContent [][]string
}

type PayLimit struct {
	Region      string
	Information PayLimitInformation
}

var (
	once = sync.Once{}
	Conf = &Config{}
)

func ConfigInit() {
	once.Do(func() {
		viper.SetConfigType("yaml")
		viper.SetConfigFile("./config/config.yaml")
		viper.SetDefault("GormConfig.SlowThreshold", 200)

		err := viper.ReadInConfig()
		if err != nil {
			panic(err)
		}
		err = viper.Unmarshal(&Conf)
		if err != nil {
			panic(err)
		}
		viper.WatchConfig()
	})
}
