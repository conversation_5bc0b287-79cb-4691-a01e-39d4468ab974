package conf

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestGetAreaByCode(t *testing.T) {
	Convey("TestGetAreaByCode", t, func() {
		Convey("未配置任何地区地区", func() {
			smsAreas := make(SMSAreas, 0)

			area, err := smsAreas.GetAreaByCode("testAreaCode")
			So(area, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})

		Convey("配置了未匹配的地区信息", func() {
			smsAreas := make(SMSAreas, 0)
			smsAreas = append(smsAreas, SMSArea{AreaCode: "testAreaCode1"})

			area, err := smsAreas.GetAreaByCode("testAreaCode2")
			So(area, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})

		Convey("配置了匹配的地区信息", func() {
			testAreaCode := "testAreaCode"
			smsAreas := make(SMSAreas, 0)
			smsAreas = append(smsAreas, SMSArea{AreaCode: testAreaCode})

			area, err := smsAreas.GetAreaByCode(testAreaCode)
			So(area.AreaCode, ShouldEqual, testAreaCode)
			So(err, ShouldBeNil)
		})
	})

	Convey("TestGetTemplate", t, func() {
		Convey("未配置任何邮件模板", func() {
			testLang := "test-lang"
			emailTemplates := make(EmailTemplates, 0)

			tmpl, err := emailTemplates.GetTemplate(testLang)
			So(tmpl, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})

		Convey("配置了未匹配的邮件模板", func() {
			testLang := "test-lang"
			emailTemplates := make(EmailTemplates, 0)
			testLangs := make(map[string]*EmailTemplate)
			emailTemplates[testLang] = testLangs

			tmpl, err := emailTemplates.GetTemplate("test_lang2")
			So(tmpl, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})

		Convey("配置了匹配的邮件模板", func() {
			testLang := "test-lang"
			emailTemplates := make(EmailTemplates, 0)
			testLangs := make(map[string]*EmailTemplate, 0)
			testLangs["lang"] = &EmailTemplate{Subject: "xxx"}
			emailTemplates["test"] = testLangs

			tmpl, err := emailTemplates.GetTemplate(testLang)
			So(tmpl.Subject, ShouldEqual, "xxx")
			So(err, ShouldBeNil)
		})
	})
}
