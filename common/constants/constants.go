package constants

const (
	LoginTokenTimeout = 600
	GameInfoTimeout   = 600
	OrderHashTimeout  = 300
	LockTimeout       = 30
	PaymentTimeout    = 300
)

const MaxValue = 1 << 31

const (
	IsOpen    = 1
	ComeOfAge = 18
)

const (
	UnRestricted = 0
	Restricted   = 1
)

const DefaultPassword = "d41d8cd98f00b204e9800998ecf8427e"

// Language 语言设置
type Language int

const (
	CN Language = iota + 1 // 中文
	EN                     // 英文
	JP                     // 日文
)

const (
	China = "CN"
	Japan = "JP"
)

const (
	Facebook   int = iota + 1 // facebook
	Twitter                   // twitter
	Line                      // line
	GameCenter                // game center
	GooglePlay                // google play
	GoogleID                  // google id
	AppleID                   // apple id
	QQ                        // qq
	Wechat                    // 微信
	Mobile                    // 手机验证码
	Email                     // 电子邮箱
	Douyin                    // 抖音
)

const (
	GooglePayLabel = "Google"
	GooglePayName  = "google"
	ApplePayLabel  = "苹果"
	ApplePayName   = "ios"
	WechatPayLabel = "微信"
	WechatPayName  = "wechat"
	AliPayLabel    = "支付宝"
	AliPayName     = "ali"
	XsollaPayLabel = "Xsolla"
	XsollaPayName  = "xsolla"
	MyCardPayLabel = "MyCard"
	MyCardPayName  = "mycard"
	GashPayLabel   = "Gash"
	GashPayName    = "gash"
	PointsPayName  = "WebPayPoints"
)

// 手机验证码配置
const (
	MobileCaptchaTTL = 300  // 验证码失效时间，5分钟
	EmailCaptchaTTL  = 1800 // 验证码失效时间，30分钟
)

const (
	BoundStatus   = 1 // 绑定
	UnBoundStatus = 0 // 未绑定
)

const (
	Authorized   = 1 // 已经进行实名认证
	Unauthorized = 0 // 未进行实名认证
)

var OrderNotifyIntervals = map[int]int{
	1: 60,
	2: 180,
	3: 300,
	4: 420,
	5: 600,
}

// 订单状态
const (
	PaymentSuccess         = 1
	PaymentUnpaid          = 0
	PaymentFailure         = -1
	PaymentRefund          = 2 // 已退款
	NotifySuccess          = 1
	NotifyError            = -1
	UnNotified             = 0
	IsRechargeOpen         = 1
	IOSVerifyStatus  int64 = 21007
	PaySuccessStatus int64 = 0
)

// 订单支付方式
const (
	PayWayForce          = -1 // 人工干预
	PayWayApple          = 1
	PayWayGoogle         = 2
	PayWayWeChat         = 3
	PayWayAli            = 4
	GoogleSubscription   = 5
	IosSubscription      = 6
	PayWayXsolla         = 7
	PayWayMyCard         = 8
	PayWayGash           = 9
	PayWayHuawei         = 10
	PayWayMock           = 11
	PayWayMiniGameWeChat = 12
	PayWayMiniGameDouyin = 13
	PayWayWebPoints      = 60
)

const (
	PayWayAliString            = "AliPay"
	PayWayWeChatString         = "WeChatPay"
	PayWayMiniGameWeChatString = "MiniGameWeChatPay"
	PayWayGoogleString         = "GooglePay"
	PayWayAppleString          = "ApplePay"
	PayWayXsollaString         = "XsollaPay"
	PayWayMyCardString         = "MyCardPay"
	PayWayGashString           = "GashPay"
	PayWayHuaweiString         = "HuaweiPay"
	PayWayDouyinString         = "DouyinPay"
	PayWayMockString           = "MockPay"
)

// google oauth2 url
// https://developers.google.com/android-publisher/api-ref/rest
const (
	GoogleOauth2Url                  = "https://oauth2.googleapis.com/token"
	GooglePurchaseSubUrl             = "https://www.googleapis.com/androidpublisher/v3/applications/%s/purchases/subscriptions/%s/tokens/%s%s?access_token=%s"
	GooglePurchaseProductUrl         = "https://www.googleapis.com/androidpublisher/v3/applications/%s/purchases/products/%s/tokens/%s%s?access_token=%s"
	GooglePurchaseVoidedpurchasesUrl = "https://www.googleapis.com/androidpublisher/v3/applications/%s/purchases/voidedpurchases?access_token=%s"
)

const RedisKeyTimeOut = 1800

const NotificationTypeRefund = "REFUND"

// appstore url
const (
	AppStoreUrlSandbox                  = "https://sandbox.itunes.apple.com/verifyReceipt"
	AppStoreUrlOnline                   = "https://buy.itunes.apple.com/verifyReceipt"
	AppleUrlNotificationHistory         = "https://api.storekit.itunes.apple.com/inApps/v1/notifications/history"
	AppleInAppsV1TransactionsUrl        = "https://api.storekit.itunes.apple.com/inApps/v1/transactions"
	AppleSandboxInAppsV1TransactionsUrl = "https://api.storekit-sandbox.itunes.apple.com/inApps/v1/transactions"
	AppleInAppsV1LookUpUrl              = "https://api.storekit.itunes.apple.com/inApps/v1/lookup/"
)

const (
	PaySuccessMessage      = "订单支付成功"
	PayFailMessage         = "订单支付失败"
	PayUnpaidMessage       = "订单未支付"
	OrderNotExitMessage    = "订单不存在"
	OrderAlreadyPayMessage = "订单重复"
)

const (
	OrderFixStart = "order_fix_start"
	OrderFixClose = "order_fix_close"
)

const (
	SubscriptionOrderNormal int = iota
	SubscriptionOrderCancel
	SubscriptionOrderExpire
	SubscriptionOrderIgnore
	SubscriptionOrderRefund
)

const (
	GoogleSubscriptionRecoveredType = 1
	GoogleSubscriptionRenewedType   = 2
	GoogleSubscriptionPurchasedType = 4
	GoogleSubscriptionRevokedType   = 12
)

const (
	OrderIDTokenSize = 6
)

const (
	PlatformIOS     = "ios"
	PlatformAndroid = "android"
	PlatformAll     = "all"
)

const (
	XsollaOrderPaidNotification = "order_paid"
)

const (
	RegisterTypeEmail  = 1
	RegisterTypePhone  = 2
	RegisterTypeDevice = 3
)

const (
	LoginTypeEmail  = 1
	LoginTypePhone  = 2
	LoginTypeDevice = 3
)

const (
	UnregisterStatusCoolingOffPeriod    = 0
	UnregisterStatusDeleted             = 1
	UnregisterStatusDeletedDeleteCancel = 2
	UnregisterStatusProcessingFailed    = -1
	AppIDSize                           = 12
	SecretKeySize                       = 16
)

const (
	BaseMigrationPath = "./migrations/base/"
	GameMigrationPath = "./migrations/game/"
)

const (
	NormalOrder    = 0 // 正常购买
	TestAccount    = 1 // 从许可测试帐号中购买的服务
	SalesPromotion = 2 // 使用促销代码购买
	Advertising    = 3 // 通过观看视频广告而不是付费
	ActivityOrder  = 4 // 活动订单
)

const (
	NotVerify       = 0 // 订单未进行二次验证
	VerifyPassed    = 1 // 验证通过
	VerifyFailed    = 2 // 验证未通过
	VerifySuspected = 3 // 验证嫌疑，订单正常发货，需要后续验证
)

const QueryOrderMaxCount = 3000

const (
	UserStatusNormal   = 0
	UserStatusDeleting = 1
	UserStatusDeleted  = 2
	UserStatusBlocked  = 3
)

const (
	ActivityProduct = 1
	NormalProduct   = 2
)

const (
	TimeLimitedActivity      = 1
	WithoutTimeLimitActivity = 2
)

const (
	MiniGameWechatJsapiPay   = "jsapi"
	MiniGameWechatVirtualPay = "virtual-payment"
	MiniGameDouyinVirtualPay = "virtual-payment"
)

const PayCurrencyCNY = "CNY"

const MiniGameOrderExpireMinute = 15

const MaxDownloadCount = 100000

const DateTimeMinuteOnly = "2006-01-02 15:04"
