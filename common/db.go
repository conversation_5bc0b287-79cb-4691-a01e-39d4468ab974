package common

import (
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"zeus/common/conf"
	"zeus/common/constants"

	"gorm.io/gorm/logger"
	"gorm.io/plugin/opentelemetry/tracing"

	"github.com/changsongl/gorm-plugin/query"
	"github.com/prometheus/client_golang/prometheus"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	baseDB *gorm.DB
	dbOnce sync.Once
)

var slowLogger = logger.New(
	log.New(os.Stdout, "", log.LstdFlags),
	logger.Config{
		// 设定慢查询时间阈值为100ms
		SlowThreshold:             time.Duration(conf.Conf.GormConfig.SlowThreshold) * time.Millisecond,
		LogLevel:                  logger.Warn,
		IgnoreRecordNotFoundError: true,
	},
)

func InitInfoDB() {
	dbOnce.Do(func() {
		host := conf.Conf.DB.Host
		port := conf.Conf.DB.Port
		username := conf.Conf.DB.Username
		password := conf.Conf.DB.Password
		database := conf.Conf.DB.Database
		charset := conf.Conf.DB.Charset
		args := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=true&loc=Local", username, password, host, port, database, charset)
		err := Migrate(args, constants.BaseMigrationPath)
		fmt.Println("Migrating database:", "Base", err)

		baseDB, err = gorm.Open(mysql.Open(args), &gorm.Config{Logger: slowLogger})
		baseDB.Use(tracing.NewPlugin(tracing.WithoutMetrics(), tracing.WithDBName(database)))
		sqlDB, _ := baseDB.DB()
		sqlDB.SetMaxIdleConns(10)
		sqlDB.SetMaxOpenConns(100)
		sqlDB.SetConnMaxIdleTime(time.Minute)

		plugin := query.New(query.SlowQueryCallback(query.Config{
			DBName:        conf.Conf.DB.Database,
			NamePrefix:    "zeus_database",
			SlowThreshold: time.Duration(conf.Conf.GormConfig.SlowThreshold) * time.Millisecond,
		}))

		baseDB.Use(plugin)
		prometheus.MustRegister(plugin.MetricsCollectors()...)

		if err != nil {
			panic("连接数据库失败" + err.Error())
		}
	})
}

func GetInfoDB() *gorm.DB {
	return baseDB
}
