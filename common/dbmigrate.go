package common

import (
	"context"
	"fmt"

	"zeus/common/log"

	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/mysql"
	_ "github.com/golang-migrate/migrate/v4/source/file"
)

const dbType = "mysql"

func Migrate(dbConnection string, migrationPath string) error {
	migrater, err := migrate.New(fmt.Sprintf("file://%s", migrationPath), fmt.Sprintf("%s://%s", dbType, dbConnection))
	if err != nil {
		log.Error(context.Background(), err.Error())
		return err
	}
	err = migrater.Up()
	if err != nil {
		if err != migrate.ErrNoChange {
			log.Error(context.Background(), err.Error())
		}
		return err
	}
	return err
}
