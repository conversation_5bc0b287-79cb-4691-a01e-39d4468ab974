package common

import (
	"fmt"
	"sync"
	"time"

	"zeus/common/conf"
	"zeus/common/constants"

	"github.com/changsongl/gorm-plugin/query"
	"github.com/prometheus/client_golang/prometheus"
	"gorm.io/plugin/opentelemetry/tracing"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type DBInfo struct {
	Host     string
	Port     string
	Username string
	Password string
	Database string
	Charset  string
}

func (m *DBInfo) connection() (*gorm.DB, error) {
	args := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=true&loc=Local&timeout=3s",
		m.Username, m.Password, m.Host, m.Port, m.Database, m.Charset)
	err := Migrate(args, constants.GameMigrationPath)
	fmt.Println("Migrating database:", m.Database, err)
	gameDB, err := gorm.Open(mysql.Open(args), &gorm.Config{Logger: slowLogger})

	sqlDB, _ := gameDB.DB()
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxIdleTime(time.Minute)

	gameDB.Use(tracing.NewPlugin(tracing.WithoutMetrics(), tracing.WithDBName(m.Database)))
	plugin := query.New(query.SlowQueryCallback(query.Config{
		DBName:        m.Database,
		NamePrefix:    "zeus_database",
		SlowThreshold: time.Duration(conf.Conf.GormConfig.SlowThreshold) * time.Millisecond,
	}))

	gameDB.Use(plugin)

	prometheus.MustRegister(plugin.MetricsCollectors()...)
	return gameDB, err
}

type DBPool struct {
	mu  sync.RWMutex
	dbs map[string]*gorm.DB
}

func (d *DBPool) Get(k string) (*gorm.DB, bool) {
	d.mu.RLock()
	defer d.mu.RUnlock()
	v, ok := d.dbs[k]
	return v, ok
}

func (d *DBPool) Add(k string, v *DBInfo) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	_, ok := d.dbs[k]
	if ok {
		return nil
	}
	db, err := v.connection()
	if err != nil {
		return err
	}
	d.dbs[k] = db
	return err
}

func (d *DBPool) Update(k string, v *DBInfo) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	db, err := v.connection()
	if err != nil {
		return err
	}
	d.dbs[k] = db
	return nil
}

func (d *DBPool) Delete(k string) {
	d.mu.Lock()
	defer d.mu.Unlock()

	delete(d.dbs, k)
}

func (d *DBPool) Len() int {
	d.mu.RLock()
	defer d.mu.RUnlock()
	return len(d.dbs)
}

var (
	poolOnce sync.Once
	dbPool   DBPool
)

func InitDBPool() {
	poolOnce.Do(func() {
		dbPool.dbs = make(map[string]*gorm.DB)
	})
}

func GetDBPool() *DBPool {
	InitDBPool()
	return &dbPool
}
