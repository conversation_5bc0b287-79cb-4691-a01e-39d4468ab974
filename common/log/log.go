package log

import (
	"context"
	"os"

	"zeus/common/conf"

	"go.opentelemetry.io/otel/trace"
	"golang.org/x/exp/slog"
)

var logger *slog.Logger

func Log(ctx context.Context, level slog.Level, msg string, args ...any) {
	span := trace.SpanFromContext(ctx)

	if span.IsRecording() {
		args = append([]any{"trace_id", span.SpanContext().TraceID()}, args...)
	}
	logger.Log(ctx, level, msg, args...)
}

func Debug(ctx context.Context, msg string, args ...any) {
	Log(ctx, slog.LevelDebug, msg, args...)
}

func Info(ctx context.Context, msg string, args ...any) {
	Log(ctx, slog.LevelInfo, msg, args...)
}

func Warn(ctx context.Context, msg string, args ...any) {
	Log(ctx, slog.LevelWarn, msg, args...)
}

func Error(ctx context.Context, msg string, args ...any) {
	Log(ctx, slog.LevelError, msg, args...)
}

func init() {
	opts := slog.HandlerOptions{}
	if conf.Conf.Debug {
		opts.Level = slog.LevelDebug
	}
	logger = slog.New(slog.NewJSONHandler(os.Stdout, &opts))
}
