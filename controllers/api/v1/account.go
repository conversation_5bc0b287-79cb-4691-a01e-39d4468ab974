package v1

import (
	"reflect"
	"strconv"

	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/field"
	"zeus/services/settings/store"
	"zeus/services/settings/thirdlogin"

	"github.com/gin-gonic/gin"
)

type ModuleInfo struct {
	Items []field.ConfigField `json:"items"`
	Title struct {
		Label      string `json:"label"`
		Name       string `json:"name"`
		OpenStatus int    `json:"open_status"`
	} `json:"title"`
}
type GameAccountConfigs struct {
	thirdlogin.ThirdLogin
	Modules []ModuleInfo `json:"modules"`
}

// GetGameAccounts
// @Summary	获取游戏三方登录信息
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param   game_id    query  string  true  "游戏id"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/game/accounts [get]
func (gc APIV1Controller) GetGameAccounts(c *gin.Context) {
	gameID, _ := strconv.Atoi(c.Query("game_id"))

	var result GameAccountConfigs

	var base thirdlogin.ThirdLogin
	converter.QueryByGameId(&base, gameID)
	converter.StructConverter(base, &result)

	dbGame, _ := game.GetGameById(gameID)

	for _, account := range store.ThirdLoginSettings {
		config := reflect.New(reflect.TypeOf(account)).Interface().(field.ThirdLoginConfiger)

		if dbGame.Platform != "all" && config.GetPlatform() != "all" && dbGame.Platform != config.GetPlatform() {
			continue
		}

		converter.QueryByGameId(config.(field.BaseConfiger), gameID)
		var accountModuleInfo ModuleInfo
		accountModuleInfo.Items = converter.ConvertToConfig(config)
		accountModuleInfo.Title.Name = config.GetType()
		accountModuleInfo.Title.Label = config.GetLabel()
		accountModuleInfo.Title.OpenStatus = config.GetOpenStatus()
		result.Modules = append(result.Modules, accountModuleInfo)
	}

	for _, account := range store.MiniGameThirdLoginSettings {
		config := reflect.New(reflect.TypeOf(account)).Interface().(field.MiniGameThirdLoginConfiger)

		if dbGame.Platform != "all" && config.GetPlatform() != "all" && dbGame.Platform != config.GetPlatform() {
			continue
		}

		converter.QueryByGameId(config.(field.BaseConfiger), gameID)
		var accountModuleInfo ModuleInfo
		accountModuleInfo.Items = converter.ConvertToConfig(config)
		accountModuleInfo.Title.Name = config.GetType()
		accountModuleInfo.Title.Label = config.GetLabel()
		accountModuleInfo.Title.OpenStatus = config.GetOpenStatus()
		result.Modules = append(result.Modules, accountModuleInfo)
	}

	gc.APISuccess(c, result)
}

// GetAreaCodes
// @Summary	获取支持的区号
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param   game_id    query  string  true  "游戏id"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/account/area_codes [get]
func (gc APIV1Controller) GetAreaCodes(c *gin.Context) {
	gameID, _ := strconv.Atoi(c.Query("game_id"))

	var config thirdlogin.Mobile
	converter.QueryByGameId(&config, gameID)
	areaCodes := *config.AreaCodes

	gc.Success(c, areaCodes)
}
