package v1

import (
	"sort"
	"strconv"
	"time"
	"zeus/common/constants"

	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"

	"github.com/mohae/deepcopy"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ActivityProductFrom struct {
	ProductId int `json:"product_id" binding:"required"`
	Limit     int `json:"limit" binding:"required"`
	Strategy  int `json:"strategy"`
	Index     int `json:"index"`
}

type ActivityProductsFrom []ActivityProductFrom

type ActivityFrom struct {
	Name                        string               `json:"name" binding:"required"`
	Type                        int                  `json:"type" binding:"required"`
	Description                 string               `json:"description"`
	IsSupportCumulativePurchase *bool                `json:"is_support_cumulative_purchase" binding:"required"`
	RangeTime                   []string             `json:"range_time"`
	ActivityProducts            ActivityProductsFrom `json:"activity_products"`
}

type ActivityProductResp struct {
	Name          string `json:"name"`
	GameProductId string `json:"game_product_id"`
	ProductId     int    `json:"product_id"`
	Limit         int    `json:"limit"`
	Strategy      int    `json:"strategy"`
	index         int
}

type ActivityProductsResp []ActivityProductResp

type ActivityResp struct {
	Id                          int                  `json:"id"`
	Name                        string               `json:"name"`
	Type                        int                  `json:"type"`
	Description                 string               `json:"description"`
	IsSupportCumulativePurchase bool                 `json:"is_support_cumulative_purchase"`
	RangeTime                   []string             `json:"range_time"`
	ActivityProducts            ActivityProductsResp `json:"activity_products"`
}

// CreateActivity
// @Summary	创建活动
// @Tags	API Activity
// @Accept	json
// @Produce json
// @Param   activity  body    ActivityFrom  true  "创建活动"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/activitys [post]
func (ac APIV1Controller) CreateActivity(c *gin.Context) {
	var form ActivityFrom
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	startTime, _ := time.ParseInLocation(constants.DateTimeMinuteOnly, form.RangeTime[0], time.Local)
	endTime, _ := time.ParseInLocation(constants.DateTimeMinuteOnly, form.RangeTime[1], time.Local)

	activityProducts := make(models.ActivityProducts, 0)
	index := 0
	for _, v := range form.ActivityProducts {
		if activityProducts.GetProduct(v.ProductId) != nil {
			continue
		}
		activityProducts.SetProduct(v.ProductId, &models.ActivityProduct{
			Limit:    v.Limit,
			Strategy: v.Strategy,
			Index:    index,
		})
		index++
	}

	activityInfo := &models.Activity{
		Name:                        form.Name,
		Type:                        form.Type,
		Description:                 form.Description,
		IsSupportCumulativePurchase: *form.IsSupportCumulativePurchase,
		StartTime:                   startTime,
		EndTime:                     endTime,
		ActivityProducts:            activityProducts,
	}

	err = models.CreateActivity(db, activityInfo)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	gameID := c.MustGet("gameID").(int)
	gameDB, _ := game.GetGameInfoById(gameID)
	if gameDB.ProjectID != 0 {
		services.AddOperation(c, gameDB.ProjectID, "activity", activityInfo.Name, "创建活动", "create", form, "", activityInfo)
	}
	ac.APISuccess(c, "")
	return
}

// UpdateActivity
// @Summary	修改活动
// @Tags	API Activity
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "活动id"
// @Param   activity  body    ActivityFrom  true  "修改活动"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/activitys/{id} [put]
func (ac APIV1Controller) UpdateActivity(c *gin.Context) {
	var form ActivityFrom
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	db := c.MustGet("db").(*gorm.DB)
	startTime, _ := time.ParseInLocation(constants.DateTimeMinuteOnly, form.RangeTime[0], time.Local)
	endTime, _ := time.ParseInLocation(constants.DateTimeMinuteOnly, form.RangeTime[1], time.Local)
	activityId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		ac.APIError(c, err)
		return
	}

	activityInfo, err := models.GetActivityById(db, activityId)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	oldActivity := deepcopy.Copy(activityInfo)

	activityProducts := make(models.ActivityProducts, 0)
	for _, v := range form.ActivityProducts {
		if activityProducts.GetProduct(v.ProductId) != nil {
			continue
		}
		activityProducts.SetProduct(v.ProductId, &models.ActivityProduct{
			Limit:    v.Limit,
			Strategy: v.Strategy,
			Index:    v.Index,
		})
	}

	activityInfo.Name = form.Name
	activityInfo.Type = form.Type
	activityInfo.Description = form.Description
	activityInfo.IsSupportCumulativePurchase = *form.IsSupportCumulativePurchase
	activityInfo.StartTime = startTime
	activityInfo.EndTime = endTime
	activityInfo.UpdateTime = time.Now()
	activityInfo.ActivityProducts = activityProducts
	err = models.SaveActivity(db, activityInfo)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	gameID := c.MustGet("gameID").(int)
	gameDB, _ := game.GetGameInfoById(gameID)
	if gameDB.ProjectID != 0 {
		services.AddOperation(c, gameDB.ProjectID, "activity", activityInfo.Name, "修改活动", "update", form, oldActivity, activityInfo)
	}

	ac.APISuccess(c, "")
	return
}

// GetActivity
// @Summary	获取活动
// @Tags	API Activity
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "活动id"
// @Success 200 {object} controllers.APISuccessResponse{result=ActivityResp}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/activitys/{id} [get]
func (ac APIV1Controller) GetActivity(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)

	activityId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		ac.APIError(c, err)
		return
	}

	activityInfo, err := models.GetActivityById(db, activityId)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	products, err := models.GetProductByIds(db, activityInfo.ActivityProducts.GetProductIds())
	if err != nil {
		ac.APIError(c, err)
		return
	}

	activityProductsResp := make(ActivityProductsResp, 0)
	for _, v := range products {
		activityProductsResp = append(activityProductsResp, ActivityProductResp{
			Name:          v.Name,
			GameProductId: v.GameProductId,
			ProductId:     v.Id,
			Limit:         activityInfo.ActivityProducts.GetProduct(v.Id).Limit,
			Strategy:      activityInfo.ActivityProducts.GetProduct(v.Id).Strategy,
			index:         activityInfo.ActivityProducts.GetProduct(v.Id).Index,
		})
	}
	sort.Slice(activityProductsResp, func(i, j int) bool {
		return activityProductsResp[i].index < activityProductsResp[j].index
	})

	activityResp := ActivityResp{
		Id:                          activityInfo.Id,
		Name:                        activityInfo.Name,
		Type:                        activityInfo.Type,
		Description:                 activityInfo.Description,
		IsSupportCumulativePurchase: activityInfo.IsSupportCumulativePurchase,
		RangeTime: []string{
			activityInfo.StartTime.Format(constants.DateTimeMinuteOnly),
			activityInfo.EndTime.Format(constants.DateTimeMinuteOnly),
		},
		ActivityProducts: activityProductsResp,
	}

	ac.APISuccess(c, activityResp)
	return
}

// GetActivitys
// @Summary	获取活动列表
// @Tags	API Activity
// @Accept	json
// @Produce json
// @Param   pageNumber    query  int  true  "页数"
// @Param   pageSize    query  int  true  "每页条数"
// @Param   name    query  string  false  "活动名称"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/activitys [get]
func (ac APIV1Controller) GetActivitys(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)

	var opt []models.ActivityQueryOption
	opt = append(opt, models.WithIsNotDeleted())

	validActivitiesFlag, _ := strconv.Atoi(c.Query("validActivitiesFlag"))
	if validActivitiesFlag == 1 {
		opt = append(opt, models.WithValidActivitiesFlag())
	}

	isSupportCumulativePurchase := c.Query("is_support_cumulative_purchase")
	if isSupportCumulativePurchase != "" {
		value, _ := strconv.Atoi(isSupportCumulativePurchase)
		opt = append(opt, models.WithIsSupportCumulativePurchase(value))
	}

	name := c.Query("name")
	if name != "" {
		opt = append(opt, models.WithName(name))
	}

	activitys, err := models.GetActivitys(db, opt)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	activitysResp := make([]ActivityResp, 0)
	for _, v := range activitys {
		products, err := models.GetProductByIds(db, v.ActivityProducts.GetProductIds())
		if err != nil {
			ac.APIError(c, err)
			return
		}

		activityProductsResp := make(ActivityProductsResp, 0)
		for _, k := range products {
			activityProductsResp = append(activityProductsResp, ActivityProductResp{
				Name:          k.Name,
				GameProductId: k.GameProductId,
				ProductId:     k.Id,
				Limit:         v.ActivityProducts.GetProduct(k.Id).Limit,
				Strategy:      v.ActivityProducts.GetProduct(k.Id).Strategy,
				index:         v.ActivityProducts.GetProduct(k.Id).Index,
			})
		}
		sort.Slice(activityProductsResp, func(i, j int) bool {
			return activityProductsResp[i].index < activityProductsResp[j].index
		})

		activitysResp = append(activitysResp, ActivityResp{
			Id:                          v.Id,
			Name:                        v.Name,
			Type:                        v.Type,
			Description:                 v.Description,
			IsSupportCumulativePurchase: v.IsSupportCumulativePurchase,
			RangeTime: []string{
				v.StartTime.Format(constants.DateTimeMinuteOnly),
				v.EndTime.Format(constants.DateTimeMinuteOnly),
			},
			ActivityProducts: activityProductsResp,
		})
	}

	resp := schema.APIResponse{
		Data: activitysResp,
	}

	ac.APISuccess(c, resp)
	return
}

// DeleteActivity
// @Summary	删除活动
// @Tags	API Activity
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "活动id"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/activitys/{id} [delete]
func (ac APIV1Controller) DeleteActivity(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	activityId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		ac.APIError(c, err)
		return
	}

	activityInfo, _ := models.GetActivityById(db, activityId)

	err = models.DeleteActivityById(db, activityId)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	if activityInfo.Id != 0 {
		gameID := c.MustGet("gameID").(int)
		gameDB, _ := game.GetGameInfoById(gameID)
		if gameDB.ProjectID != 0 {
			services.AddOperation(c, gameDB.ProjectID, "activity", activityInfo.Name, "删除活动", "delete", activityId, activityInfo, "")
		}
	}

	ac.APISuccess(c, "")
	return
}
