package v1

import (
	"github.com/gin-gonic/gin"

	"zeus/common/conf"
	"zeus/services/antispam"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/other"
)

type AntispamForm struct {
	Text   string `json:"text"`
	GameId int    `json:"game_id"`
}

// CheckAntispam
// @Summary	屏蔽字
// @Tags	API Antispam
// @Accept	json
// @Produce json
// @Param   activity  body    AntispamForm  true  "创建活动"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/antispam/check [post]
func (ac APIV1Controller) CheckAntispam(c *gin.Context) {
	var form AntispamForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	var antispamConfig other.AntispamConfig
	dbGame, _ := game.GetGameById(form.GameId)
	converter.QueryByProjectId(&antispamConfig, dbGame.ProjectID)

	checker := antispam.NewYiDunChecker(conf.Conf.AntispamInfo.ApiURL,
		*antispamConfig.AntispamSecretID,
		*antispamConfig.AntispamSecretKey,
		*antispamConfig.AntispamBusinessID)
	result, err := checker.GetTextCheckInfo(c.Request.Context(), form.Text)
	if err != nil || result == nil {
		ac.APISuccess(c, []string{})
		return
	}
	ac.APISuccess(c, result)
}
