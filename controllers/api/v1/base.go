package v1

import (
	"gorm.io/gorm"

	"zeus/controllers"

	"github.com/gin-gonic/gin"
)

type APIV1Controller struct {
	controllers.BaseController
}

func (ac *APIV1Controller) GetDB(c *gin.Context) *gorm.DB {
	db := c.MustGet("db")
	return db.(*gorm.DB)
}

var AV1C = APIV1Controller{}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.GET("/login", AV1C.Login)
	routerGroup.GET("/game", AV1C.GetGamesByProject)
	routerGroup.POST("/game", AV1C.CreateGame)
	routerGroup.PUT("/game/:id", AV1C.UpdateGame)
	routerGroup.GET("/game/accounts", AV1C.GetGameAccounts)
	routerGroup.GET("/game/payments", AV1C.GetGamePayments)
	routerGroup.GET("/account/area_codes", AV1C.GetAreaCodes)
	routerGroup.GET("/settings/:type", AV1C.GetSettings)
	routerGroup.GET("/settings/:type/fields", AV1C.GetSettingsFields)
	routerGroup.PUT("/settings/:type", AV1C.UpdateGameSettings)
	routerGroup.POST("/settings/check/:type", AV1C.CheckGooglePaySettings)
	routerGroup.GET("/project", AV1C.GetProjects)
	routerGroup.POST("/project", AV1C.CreateProject)
	routerGroup.PUT("/project/:id", AV1C.UpdateProject)
	routerGroup.POST("/project/check_connection", AV1C.CheckMysqlConnection)
	routerGroup.GET("/user/info", AV1C.GetUser)
	routerGroup.GET("/user/bind", AV1C.GetUserBind)
	routerGroup.PUT("/user/bind", AV1C.UpdateUserBind)
	routerGroup.PUT("/user/unbind", AV1C.UpdateUserUnbind)
	routerGroup.GET("/user/roles", AV1C.GetUserRoles)
	routerGroup.GET("/user/account", AV1C.GetUserAccount)
	routerGroup.POST("/user/query_status", AV1C.QueryUserStatus)
	routerGroup.PUT("/user/status", AV1C.UpdateUserStatus)
	routerGroup.GET("/orders", AV1C.GetOrderInfo)
	routerGroup.GET("/orders/web_pay", AV1C.SearchWebPayOrders)
	routerGroup.GET("/point_orders", AV1C.GetPointOrders)
	routerGroup.POST("/orders/notify", AV1C.OrderNotify)
	routerGroup.POST("/orders/google_fix", AV1C.GoogleOrderFix)
	routerGroup.POST("/orders/google_query", AV1C.GoogleOrderQuery)
	routerGroup.POST("/orders/huawei_query", AV1C.HuaweiOrderQuery)
	routerGroup.POST("/orders/huawei_fix", AV1C.HuaweiOrderFix)
	routerGroup.POST("/orders/verify", AV1C.OrderVerify)
	routerGroup.GET("/subscriptions", AV1C.GetSubscriptionsInfo)
	routerGroup.POST("/subscriptions/download", AV1C.SubscriptionsDownload)
	routerGroup.POST("/subscriptions/notify", AV1C.SubscriptionNotify)
	routerGroup.GET("/subscriptions/renew", AV1C.GetRenewSubscriptions)
	routerGroup.GET("/operation", AV1C.GetOperation)
	routerGroup.POST("/orders/statistics", AV1C.OrderStatistics)
	routerGroup.POST("/orders/statistics_reload", AV1C.UpdateOrderStatistics)
	routerGroup.GET("/orders/refund", AV1C.GetRefundOrder)
	routerGroup.GET("/orders/refund-info", AV1C.GetRefundOrderInfo)
	routerGroup.PUT("/orders/refund", AV1C.GetOrderRefund)
	routerGroup.GET("/request_count", AV1C.GetRequestCount)
	routerGroup.GET("/request_statistics", AV1C.GetRequestStatistics)
	routerGroup.GET("/request_statistics/percentage", AV1C.GetRequestPercentage)
	routerGroup.POST("/orders/ios_query", AV1C.IOSOrderQuery)
	routerGroup.POST("/orders/ios_fix", AV1C.IOSOrderFix)
	routerGroup.POST("/orders/download", AV1C.OrdersDownload)
	routerGroup.POST("/orders/statistics_download", AV1C.OrderStatisticsDownload)
	routerGroup.GET("/dial_testing/targets", AV1C.GetDialTestingTarget)
	routerGroup.POST("/dial_testing/targets", AV1C.CreateDialTestingTarget)
	routerGroup.PUT("/dial_testing/targets", AV1C.UpdateDialTestingTarget)
	routerGroup.DELETE("/dial_testing/targets/:id", AV1C.DeleteDialTestingTarget)

	routerGroup.GET("/activitys", AV1C.GetActivitys)
	routerGroup.POST("/activitys", AV1C.CreateActivity)
	routerGroup.PUT("/activitys/:id", AV1C.UpdateActivity)
	routerGroup.GET("/activitys/:id", AV1C.GetActivity)
	routerGroup.DELETE("/activitys/:id", AV1C.DeleteActivity)

	routerGroup.POST("/products", AV1C.CreateProduct)
	routerGroup.PUT("/products/:id", AV1C.UpdateProducts)
	routerGroup.GET("/products", AV1C.GetProducts)
	routerGroup.DELETE("/products/:id", AV1C.DeleteProducts)

	routerGroup.POST("/antispam/check", AV1C.CheckAntispam)
	routerGroup.POST("/web_pay/upload", AV1C.WebPayUpload)

	routerGroup.GET("/web_pay/point_strategy_record", AV1C.GetWebPayPointStrategyRecord)
	routerGroup.POST("/web_pay/point_strategy_record", AV1C.CreateWebPayPointStrategyRecord)
	routerGroup.PUT("/web_pay/point_strategy_record/:id", AV1C.UpdateWebPayPointStrategyRecord)

	routerGroup.GET("/web_pay/point_strategy", AV1C.GetWebPayPointStrategy)
	routerGroup.POST("/web_pay/point_strategy", AV1C.CreateWebPayPointStrategy)
	routerGroup.PUT("/web_pay/point_strategy/:id", AV1C.UpdateWebPayPointStrategy)
	routerGroup.DELETE("/web_pay/point_strategy/:id", AV1C.DeleteWebPayPointStrategy)
	routerGroup.GET("/web_pay/whitelist", AV1C.GetWebPayWhiteList)
	routerGroup.POST("/web_pay/whitelist", AV1C.CreateWebPayWhiteList)
	routerGroup.PUT("/web_pay/whitelist/:id", AV1C.UpdateWebPayWhiteList)
	routerGroup.DELETE("/web_pay/whitelist/:id", AV1C.DeleteWebPayWhiteList)
}
