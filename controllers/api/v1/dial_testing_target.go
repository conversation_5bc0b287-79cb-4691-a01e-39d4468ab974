package v1

import (
	"encoding/json"
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"

	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
)

// GetDialTestingTarget
// @Summary	获取拨测目标配置
// @Tags	Game
// @Accept	json
// @Produce json
// @Param   game_id    query  string  true  "游戏id"
// @Success 200 {object} controllers.APISuccessResponse{result=[]models.DialTestingTarget}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/dial_testing/targets [get]
func (gc APIV1Controller) GetDialTestingTarget(c *gin.Context) {
	gameID, err := strconv.Atoi(c.Query("game_id"))
	if err != nil {
		gc.APIError(c, err)
		return
	}
	targets, _ := models.GetDialTestingTargetsByGameID(gameID)

	gc.APISuccess(c, targets)
}

// CreateDialTestingTarget
// @Summary	创建拨测目标配置
// @Tags	Game
// @Accept	json
// @Produce json
// @Param   person  body    schema.Targets  true  "创建拨测目标配置"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/dial_testing/targets [post]
func (gc APIV1Controller) CreateDialTestingTarget(c *gin.Context) {
	var targetForm *schema.Targets
	err := c.ShouldBindJSON(&targetForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	dbGame, err := game.GetGameById(targetForm.GameId)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	_, err = models.GetDialTestingTargetByName(targetForm.Name)
	if err == nil {
		gc.APIError(c, errors.New("拨测目标点名称重复"))
		return
	}

	dbTarget, err := services.CreateDialTestingTarget(targetForm.GameId, targetForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	services.AddOperation(c, dbGame.ProjectID, "game", dbGame.Name, "拨测配置", "create", targetForm, "", dbTarget)

	gc.APISuccess(c, "")
}

// UpdateDialTestingTarget
// @Summary	修改拨测目标配置
// @Tags	Game
// @Accept	json
// @Produce json
// @Param   id    query  string  true  "拨测目标id"
// @Param   person  body    schema.Targets  true  "修改拨测目标配置"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/dial_testing/targets [put]
func (gc APIV1Controller) UpdateDialTestingTarget(c *gin.Context) {
	var targetForm *schema.UpdateTarget
	err := c.ShouldBindJSON(&targetForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	dbGame, err := game.GetGameById(targetForm.GameId)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	dbTarget, err := models.GetDialTestingTargetByID(targetForm.ID)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	dbTargetJson, _ := json.Marshal(dbTarget)
	var dbTargetOld models.DialTestingTarget
	json.Unmarshal(dbTargetJson, &dbTargetOld)

	dbTargetNew, err := services.UpdateDialTestingTarget(dbTarget, targetForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	services.AddOperation(c, dbGame.ProjectID, "game", dbGame.Name, "拨测配置", "update", targetForm, dbTargetOld, dbTargetNew)
	gc.APISuccess(c, "")
}

// DeleteDialTestingTarget
// @Summary	删除拨测目标配置
// @Tags	Game
// @Accept	json
// @Produce json
// @Param   id    query  string  true  "拨测目标id"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/dial_testing/targets [delete]
func (gc APIV1Controller) DeleteDialTestingTarget(c *gin.Context) {
	targetID, err := strconv.Atoi(c.Param("id"))
	dbTarget, err := models.GetDialTestingTargetByID(targetID)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	err = models.DeleteDialTestingTarget(dbTarget)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	dbGame, err := game.GetGameById(dbTarget.GameId)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	services.AddOperation(c, dbGame.ProjectID, "game", dbGame.Name, "拨测配置", "delete", "", dbTarget, "")

	gc.APISuccess(c, "")
}
