package v1

import (
	"fmt"
	"net/http"
	"strconv"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/json"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/utils"

	"github.com/gin-gonic/gin"
)

// GetGames
// @Summary	获取所有游戏
// @Tags	API Game
// @Accept	json
// @Produce json
// @Success 200 {object} controllers.APISuccessResponse{result=[]schema.GameSummary}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/games [get]
func (gc APIV1Controller) GetGames(c *gin.Context) {
	games := game.GetGames()
	gc.APISuccess(c, games)
}

// GetGamesByProject
// @Summary	获取游戏
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param   alias    query  string  true  "游戏别名"
// @Success 200 {object} controllers.APISuccessResponse{result=[]schema.GameSummary}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/game [get]
func (gc APIV1Controller) GetGamesByProject(c *gin.Context) {
	alias := c.Query("alias")
	project, err := services.GetProjectByAlias(alias)
	if err != nil {
		var data [0]int
		gc.APISuccess(c, data)
		return
	}
	gameInfo, err := game.GetGameInfoByProjectID(project.ID)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	data := map[string][]schema.GameSummary{
		"data": gameInfo,
	}
	gc.APISuccess(c, data)
}

// CreateGame
// @Summary	添加游戏
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param	person	body	schema.GameInfoCreateForm	true	"添加游戏"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Game}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/game [post]
func (gc APIV1Controller) CreateGame(c *gin.Context) {
	var gameForm schema.GameInfoCreateForm
	err := c.ShouldBindJSON(&gameForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	project, _ := services.GetProjectByAlias(gameForm.ProjectAlias)
	if project.ID == 0 {
		gc.APIError(c, fmt.Errorf("游戏项目: %s 不存在！", gameForm.ProjectAlias))
		return
	}
	filter := map[string]interface{}{
		"project_id": project.ID,
		"name":       gameForm.Name,
	}
	gameDB, _ := game.QueryGamesByFilter(filter)
	if len(gameDB) != 0 {
		gc.APIError(c, fmt.Errorf("应用名称: %s 存在，请修改！", gameForm.Name))
		return
	}

	gameForm.Appid = utils.GenerateRandomStr(constants.AppIDSize)
	gameForm.SecretKey = utils.GenerateRandomStr(constants.SecretKeySize)
	dbGame, err := game.CreateGame(project.ID, gameForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	services.AddOperation(c, project.ID, "game", dbGame.Name, "应用配置", "create", gameForm, "", dbGame)
	gc.APISuccess(c, dbGame)
}

// UpdateGame
// @Summary	更新游戏
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param	person	body	schema.GameSummary	true	"更新游戏"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Game}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Success 200
// @Router /api/v1/game/:id [put]
func (gc APIV1Controller) UpdateGame(c *gin.Context) {
	var gameForm schema.GameSummary
	err := c.ShouldBindJSON(&gameForm)
	if err != nil {
		gc.Error(c, http.StatusBadRequest, err)
		return
	}

	gameID, _ := strconv.Atoi(c.Param("id"))

	dbGame, err := game.GetGameInfoById(gameID)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	redisKey := game.RedisKey{
		DB: dbGame,
	}
	redisKey.CleanRedisGameInfo(c)
	dbGameJson, _ := json.Marshal(dbGame)
	var dbGameOld models.Game
	json.Unmarshal(dbGameJson, &dbGameOld)
	dbGameNew, funcName, err := game.UpdateGame(dbGame, &gameForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	services.AddOperation(c, dbGameNew.ProjectID, "game", dbGameOld.Name, funcName, "update", gameForm, dbGameOld, dbGameNew)
	gc.APISuccess(c, "")
}
