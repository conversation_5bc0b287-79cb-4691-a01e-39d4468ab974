package v1

import (
	"errors"

	"zeus/common/conf"
	"zeus/middlewares/checkJWT"

	"zeus/pkg/ucenter"

	"github.com/gin-gonic/gin"
)

type Token struct {
	TokenStr string `json:"token"`
}

// Login
// @Summary	登录
// @Tags	API-Game
// @Accept	json
// @Produce json
// @Param   token    query  string  true  "登录使用的token"
// @Success 200 {object} controllers.APISuccessResponse{result=Token}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/login [get]
func (gc APIV1Controller) Login(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		gc.APIError(c, errors.New("登录失败"))
		return
	}
	user := checkJWT.JWTUser{}
	client := ucenter.NewClient(conf.Conf.Ucenter.APIKey, conf.Conf.Ucenter.APISecret)
	userID := client.CheckToken(token)
	if userID == "" {
		gc.APIError(c, errors.New("非法的token"))
		return
	}
	user.UserID = userID
	user.Name = client.GetUserById(userID).Name
	IsAdmin, allowGames := client.GetIsAdminAndAllowGamesByUserId(userID)
	user.AllowGames = allowGames
	user.IsAdmin = IsAdmin

	jwtToken, err := user.GenToken()
	if err != nil {
		gc.APIError(c, errors.New("登录失败"))
		return
	}
	tokenData := Token{}
	tokenData.TokenStr = jwtToken

	gc.APISuccess(c, tokenData)
}
