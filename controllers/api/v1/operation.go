package v1

import (
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"

	"github.com/gin-gonic/gin"
)

type OperationFilter struct {
	GameID       int    `form:"game_id" binding:"required"`
	PageSize     int    `form:"pageSize"`
	PageNumber   int    `form:"pageNumber"`
	User         string `form:"user"`
	ResourceName string `form:"resource_name"`
	ResourceFunc string `form:"resource_func"`
	StartTime    string `form:"start_time"`
	EndTime      string `form:"end_time"`
}

// GetOperation
// @Summary	获取操作记录
// @Tags	API Operation
// @Accept	json
// @Produce json
// @Param	person	body  OperationFilter	true	"获取操作记录"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/operation [get]
func (gc APIV1Controller) GetOperation(c *gin.Context) {
	var operationFilter OperationFilter
	err := c.ShouldBindQuery(&operationFilter)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	var pageSize, pageNum int
	if operationFilter.PageSize == 0 {
		pageSize = 10
	} else {
		pageSize = operationFilter.PageSize
	}
	if operationFilter.PageNumber == 0 {
		pageNum = 1
	} else {
		pageNum = operationFilter.PageNumber
	}
	dbGame, err := game.GetGameById(operationFilter.GameID)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	filter := map[string]interface{}{
		"project_id": dbGame.ProjectID,
	}
	if operationFilter.User != "" {
		filter["user"] = operationFilter.User
	}
	if operationFilter.ResourceName != "" {
		filter["resource_name"] = operationFilter.ResourceName
	}
	if operationFilter.ResourceFunc != "" {
		filter["resource_func"] = operationFilter.ResourceFunc
	}

	operationData, total, err := services.GetOperation(filter, pageSize, pageNum, operationFilter.StartTime, operationFilter.EndTime)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	var operationResponse schema.APIResponse
	operationResponse.PageSize = pageSize
	operationResponse.PageNum = pageNum
	operationResponse.Total = total
	operationResponse.Data = operationData

	gc.APISuccess(c, operationResponse)
}
