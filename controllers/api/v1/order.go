package v1

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
	"zeus/common/constants"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/download"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/pay/apple"
	"zeus/services/refund"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/services/third_pay"

	"github.com/gin-gonic/gin"
)

type OrderNotifyParams struct {
	GameID  int    `json:"game_id"`
	OrderID string `json:"order_id"`
}

type OrderGoogleForm struct {
	GameID        int    `json:"game_id"`
	ProductId     string `json:"product_id"`
	PurchaseToken string `json:"purchase_token"`
}
type OrderHuaweiForm struct {
	GameID        int    `json:"game_id"`
	ProductId     string `json:"product_id"`
	PurchaseToken string `json:"purchase_token"`
}

type GetIOSOrderForm struct {
	GameID          int    `json:"game_id" binding:"required"`
	RoleID          string `json:"role_id" binding:"required"`
	PurchaseOrderID string `json:"purchase_order_id" binding:"required"`
}

type FixIOSOrderForm struct {
	GameID        int    `json:"game_id" binding:"required"`
	OrderID       string `json:"order_id" binding:"required"`
	TransactionID string `json:"transaction_id" binding:"required"`
}

type OrderGoogleQueryResult struct {
	RoleId           string `json:"role_id"`
	RoleName         string `json:"role_name"`
	ServerId         string `json:"server_id"`
	ServerName       string `json:"server_name"`
	PayStatus        int    `json:"pay_status"`
	NotifyStatus     int    `json:"notify_status"`
	PurchaseState    int    `json:"purchase_state"`
	AckState         int    `json:"ack_state"`
	ConsumptionState int    `json:"consumption_state"`
	PayId            string `json:"pay_id"`
	OrderId          string `json:"order_id"`
}

type OrderHuaweiQueryResult struct {
	RoleId           string `json:"role_id"`
	RoleName         string `json:"role_name"`
	ServerId         string `json:"server_id"`
	ServerName       string `json:"server_name"`
	PayStatus        int    `json:"pay_status"`
	NotifyStatus     int    `json:"notify_status"`
	PurchaseState    int    `json:"purchase_state"`
	AckState         int    `json:"ack_state"`
	ConsumptionState int    `json:"consumption_state"`
	PayId            string `json:"pay_id"`
	OrderId          string `json:"order_id"`
}

type VerifyForm struct {
	GameID  int    `json:"game_id"`
	OrderId string `json:"order_id"`
	Force   bool   `json:"force"`
}

type OrderQueryFields struct {
	GameID          int64  `form:"game_id" binding:"required"`
	OrderID         string `form:"order_id"`
	PayID           string `form:"pay_id"`
	PayWay          *int   `form:"pay_way"`
	PayStatus       *int   `form:"pay_status"`
	NotifyStatus    *int   `form:"notify_status"`
	RoleID          string `form:"role_id"`
	RoleName        string `form:"role_name"`
	ServerID        string `form:"server_id"`
	Device          string `form:"device"`
	ProductId       string `form:"product_id"`
	Account         string `form:"account"`
	UserId          string `form:"user_id"`
	OrderAttribute  string `form:"order_attribute"`
	PageSize        int    `form:"pageSize,default=10"`
	PageNumber      int    `form:"pageNumber,default=1"`
	CreateStartTime string `form:"start_time"`
	CreateEndTime   string `form:"end_time"`
	PayIp           string `form:"pay_ip"`
	Platform        string `form:"platform"`
	RefundStartTime string `form:"refund_start_time"`
	RefundEndTime   string `form:"refund_end_time"`
}

// GetOrderInfo
// @Summary	获取订单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body	OrderQueryFields	true	"获取订单信息"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders [get]
func (ac APIV1Controller) GetOrderInfo(c *gin.Context) {
	queryForm := OrderQueryFields{}
	err := c.ShouldBindQuery(&queryForm)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	var options []models.QueryOrderOptions
	if queryForm.OrderID != "" {
		options = append(options, models.QueryOrderWithOrderId(queryForm.OrderID))
	}
	if queryForm.RoleID != "" {
		options = append(options, models.QueryOrderWithRoleId(queryForm.RoleID))
	}
	if queryForm.RoleName != "" {
		options = append(options, models.QueryOrderWithRoleName(queryForm.RoleName))
	}
	if queryForm.ServerID != "" {
		options = append(options, models.QueryOrderWithServerId(queryForm.ServerID))
	}
	if queryForm.UserId != "" {
		options = append(options, models.QueryOrderWithUserId(queryForm.UserId))
	}
	if queryForm.Account != "" {
		options = append(options, models.QueryOrderWithAccount(queryForm.Account))
	}
	if queryForm.PayID != "" {
		options = append(options, models.QueryOrderWithPayID(queryForm.PayID))
	}
	if queryForm.Platform != "" {
		options = append(options, models.QueryOrderWithPlatform(queryForm.Platform))
	}
	if queryForm.PayIp != "" {
		options = append(options, models.QueryOrderWithPayIp(queryForm.PayIp))
	}
	if queryForm.ProductId != "" {
		options = append(options, models.QueryOrderWithProductId(queryForm.ProductId))
	}
	if queryForm.PayWay != nil {
		options = append(options, models.QueryOrderWithPayWay(*queryForm.PayWay))
	}
	if queryForm.Device != "" {
		options = append(options, models.QueryOrderWithDevice(queryForm.Device))
	}
	if queryForm.PayStatus != nil {
		options = append(options, models.QueryOrderWithPayStatus(*queryForm.PayStatus))
	}
	if queryForm.NotifyStatus != nil {
		options = append(options, models.QueryOrderWithNotifyStatus(*queryForm.NotifyStatus))
	}
	if queryForm.OrderAttribute != "" {
		options = append(options, models.QueryOrderWithOrderAttribute(strings.Split(queryForm.OrderAttribute, ",")))
	}
	if queryForm.CreateStartTime != "" {
		options = append(options, models.QueryOrderWithStartTime(queryForm.CreateStartTime))
	}
	if queryForm.CreateEndTime != "" {
		options = append(options, models.QueryOrderWithEndTime(queryForm.CreateEndTime))
	}
	if queryForm.RefundStartTime != "" {
		options = append(options, models.QueryOrderWithRefundStartTime(queryForm.RefundStartTime))
	}
	if queryForm.RefundEndTime != "" {
		options = append(options, models.QueryOrderWithRefundEndTime(queryForm.RefundEndTime))
	}

	options = append(options, models.QueryOrderWithPage(queryForm.PageSize, queryForm.PageNumber))
	if queryForm.RefundStartTime != "" || queryForm.RefundEndTime != "" {
		options = append(options, models.QueryOrderWithRefundTimeSort())
	} else {
		options = append(options, models.QueryOrderWithCreateTimeSort())
	}
	db := c.MustGet("db").(*gorm.DB)
	orderData, total, err := models.GetOrderByOptions(db, options...)

	var orderResponse schema.APIResponse
	orderResponse.PageSize = queryForm.PageSize
	orderResponse.PageNum = queryForm.PageNumber
	orderResponse.Total = total
	orderResponse.Data = orderData
	ac.APISuccess(c, orderResponse)
}

// OrderNotify
// @Summary	补单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  OrderNotifyParams	true	"补单"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/order/notify [post]
func (ac APIV1Controller) OrderNotify(c *gin.Context) {
	var orderNotifyParams OrderNotifyParams
	err := c.ShouldBindJSON(&orderNotifyParams)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	orderID := orderNotifyParams.OrderID
	orderDB, err := services.GetOrderByOrderID(c, orderID)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	orderDBOld := map[string]int{
		"pay_status":    orderDB.PayStatus,
		"notify_status": orderDB.NotifyStatus,
	}
	funcName := "订单补单"
	if orderDB.PayStatus != constants.PaymentSuccess {
		funcName = "订单补发"
		switch orderDB.PayWay {
		case constants.PayWayGash:
			client := third_pay.GashPayClient{}
			err = client.FixOrder(orderNotifyParams.GameID, orderDB)
			if err != nil {
				ac.APIError(c, err)
				return
			}
		case constants.PayWayMyCard:
			if orderDB.OrderAttribute == constants.ActivityOrder {
				db := c.MustGet("db").(*gorm.DB)
				purchaseRecord, err := models.GetPurchaseRecordByOrderId(db, orderDB.OrderId)
				if err != nil {
					ac.APIError(c, err)
					return
				}
				purchaseRecord.PayStatus = constants.PaymentSuccess
				err = models.SavePurchaseRecord(db, purchaseRecord)
				if err != nil {
					ac.APIError(c, err)
					return
				}
			}
		}
		orderDB.PayStatus = constants.PaymentSuccess
		orderDB.PayTime = time.Now()
	}
	if err := services.SaveOrder(c, orderDB); err != nil {
		ac.APIError(c, errors.New("失败"))
		return
	}
	orderDBNew := map[string]int{
		"pay_status":    orderDB.PayStatus,
		"notify_status": orderDB.NotifyStatus,
	}

	dbGame, _ := game.GetGameById(orderNotifyParams.GameID)
	services.AddOperation(c, dbGame.ProjectID, "order", orderDB.OrderId, funcName, "update", orderNotifyParams, orderDBOld, orderDBNew)
	db := ac.GetDB(c)
	err = services.Notify(c.Request.Context(), db, orderDB, services.NotifyOptions{})
	if err != nil {
		ac.APIError(c, err)
		return
	}
	ac.APISuccess(c, "")
}

// GoogleOrderQuery
// @Summary 查询google订单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  OrderGoogleForm	true	"查询google订单"
// @Success 200 {object} controllers.APISuccessResponse{result=OrderGoogleQueryResult}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/google_query [post]
func (ac APIV1Controller) GoogleOrderQuery(c *gin.Context) {
	var params OrderGoogleForm
	err := c.ShouldBindJSON(&params)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	var order pay.GooglePurchaseResponse
	order, err = pay.CheckPurchaseInfo(
		c.Request.Context(),
		params.GameID,
		params.ProductId,
		params.PurchaseToken)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	orderID := order.ObfuscatedExternalAccountId
	orderDB, err := services.GetOrderByOrderID(c, orderID)
	if err != nil {
		ac.APIError(c, errors.New("未查询到订单"))
		return
	}
	result := OrderGoogleQueryResult{
		RoleId:           orderDB.RoleId,
		RoleName:         orderDB.RoleName,
		ServerId:         orderDB.ServerId,
		ServerName:       orderDB.ServerName,
		PayStatus:        orderDB.PayStatus,
		NotifyStatus:     orderDB.NotifyStatus,
		PurchaseState:    order.PurchaseState,
		AckState:         order.AcknowledgementState,
		ConsumptionState: order.ConsumptionState,
		PayId:            order.OrderID,
		OrderId:          orderDB.OrderId,
	}
	ac.APISuccess(c, result)
}

// GoogleOrderFix
// @Summary	修复google订单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  OrderGoogleForm	true	"修复google订单"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/google_fix [post]
func (ac APIV1Controller) GoogleOrderFix(c *gin.Context) {
	var form OrderGoogleForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	var order pay.GooglePurchaseResponse
	order, err = pay.CheckPurchaseInfo(c.Request.Context(), form.GameID,
		form.ProductId,
		form.PurchaseToken)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	orderID := order.ObfuscatedExternalAccountId
	orderDB, err := services.GetOrderByOrderID(c, orderID)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	orderDBOld := map[string]int{
		"pay_status":    orderDB.PayStatus,
		"notify_status": orderDB.NotifyStatus,
	}
	orderDB.PayId = order.OrderID
	orderDB.Receipt = order.ToString()
	orderDB.PayWay = constants.PayWayGoogle
	orderDB.PayStatus = constants.PaymentSuccess
	orderDB.PayProductId = form.ProductId
	orderDB.PayTime = time.Now()
	orderDB.VerifyStatus = constants.VerifyPassed

	if err := services.SaveOrder(c, orderDB); err != nil {
		ac.APIError(c, errors.New("失败"))
		return
	}
	orderDBNew := map[string]interface{}{
		"pay_id":        order.OrderID,
		"pay_way":       constants.PayWayGoogle,
		"pay_status":    orderDB.PayStatus,
		"notify_status": orderDB.NotifyStatus,
	}
	dbGame, _ := game.GetGameById(form.GameID)
	services.AddOperation(c, dbGame.ProjectID, "order", orderDB.OrderId, "修复Google订单", "update", form, orderDBOld, orderDBNew)
	db := ac.GetDB(c)
	err = services.Notify(c.Request.Context(), db, orderDB, services.NotifyOptions{})
	if err != nil {
		ac.APIError(c, err)
		return
	}
	ac.APISuccess(c, "")
}

// OrderVerify
// @Summary	验证订单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  VerifyForm	true	"验证订单"
// @Success 200 {object} controllers.APISuccessResponse{result=int}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/verify [post]
func (ac APIV1Controller) OrderVerify(c *gin.Context) {
	var form VerifyForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	orderDB, err := services.GetOrderByOrderID(c, form.OrderId)
	if err != nil {
		ac.APIError(c, errors.New("未查询到订单"))
		return
	}
	if orderDB.PayStatus == constants.PaymentUnpaid {
		ac.APIError(c, errors.New("订单未支付"))
		return
	}

	if orderDB.VerifyStatus != constants.NotVerify && !form.Force {
		ac.APISuccess(c, orderDB.VerifyStatus)
		return
	}

	var isSandBox bool

	if orderDB.OrderAttribute == constants.TestAccount {
		isSandBox = true
	}

	verifyStatus := constants.VerifyPassed

	var errorMessage string
	if orderDB.ProductId != orderDB.PayProductId {
		verifyStatus = constants.VerifyFailed
		errorMessage = "订单内商品ID与实际支付ID不符"
	} else {
		var pay payment.Payment
		converter.QueryByGameId(&pay, form.GameID)

		switch orderDB.PayWay {
		case constants.PayWayApple:
			var config payment.ApplePay
			converter.QueryByGameId(&config, form.GameID)

			if !strings.Contains(orderDB.Receipt, *config.AppleBundleId) {
				verifyStatus = constants.VerifyFailed
			} else {
				ac := apple.AppleClient{
					ISS:    *config.AppleISS,
					KeyID:  *config.AppleKeyID,
					BID:    *config.AppleBundleId,
					Secret: *config.AppleSecret,
				}

				transactionInfo, err := ac.VerifyOrderWithOriginTransactionID(c.Request.Context(), orderDB.PayId, isSandBox)
				if err != nil {
					verifyStatus = constants.VerifyFailed
					errorMessage = "未在Apple后台查询到交易订单"
				}

				if transactionInfo.ProductId != "" && transactionInfo.ProductId != orderDB.ProductId {
					verifyStatus = constants.VerifySuspected
					errorMessage = "票据内商品ID与订单商品ID不符"
				}

			}
		case constants.PayWayGoogle:
			var config payment.GooglePay
			converter.QueryByGameId(&config, form.GameID)

			if !strings.Contains(orderDB.Receipt, *config.GooglePayPackageName) {
				verifyStatus = constants.VerifyFailed
			}
		}
	}

	if orderDB.VerifyStatus != verifyStatus {
		orderDB.VerifyStatus = verifyStatus
		if err := services.SaveOrder(c, orderDB); err != nil {
			ac.APIError(c, errors.New(errorMessage))
			return
		}
	}

	ac.APISuccess(c, orderDB.VerifyStatus)
}

// OrderStatistics
// @Summary	获取订单统计数据
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  schema.OrderStatisticsParams	true	"获取订单统计数据"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/statistics [post]
func (ac APIV1Controller) OrderStatistics(c *gin.Context) {
	var form schema.OrderStatisticsParams
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	orderDB, total := services.GetOrderStatisticsByDimension(c, form)
	var orderStatisticsResponse schema.APIResponse
	orderStatisticsResponse.Total = total
	orderStatisticsResponse.PageSize = form.PageSize
	orderStatisticsResponse.PageNum = form.PageNumber
	orderStatisticsResponse.Data = orderDB
	if total == 0 {
		orderStatisticsResponse.Data = []schema.StatisticsData{}
	}
	ac.APISuccess(c, orderStatisticsResponse)
}

// UpdateOrderStatistics
// @Summary	更新订单统计
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  schema.UpdateOrderStatisticsForm	true	"更新订单统计"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/statistics_reload [post]
func (ac APIV1Controller) UpdateOrderStatistics(c *gin.Context) {
	var form schema.UpdateOrderStatisticsForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	if form.StartDate != "" {
		_, err := time.Parse("2006-01-02", form.StartDate)
		if err != nil {
			ac.APIError(c, errors.New("参数 StartDate 格式错误, 示例：2006-01-02"))
			return
		}
	}
	if form.EndDate != "" {
		_, err := time.Parse("2006-01-02", form.EndDate)
		if err != nil {
			ac.APIError(c, errors.New("参数 EndDate 格式错误, 示例：2006-01-02"))
			return
		}
	}

	services.OrderStatisticsTask(c.Request.Context(), form.GameID, form.StartDate, form.EndDate)

	ac.APISuccess(c, "")
}

// GetOrderRefund
// @Summary	更新订单退款数据
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  schema.UpdateOrderStatisticsForm	true	"更新订单退款数据"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/refund [put]
func (ac APIV1Controller) GetOrderRefund(c *gin.Context) {
	var form schema.UpdateOrderStatisticsForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	if form.StartDate != "" {
		_, err := time.Parse("2006-01-02", form.StartDate)
		if err != nil {
			ac.APIError(c, errors.New("参数 StartDate 格式错误, 示例：2006-01-02"))
			return
		}
	}
	if form.EndDate != "" {
		_, err := time.Parse("2006-01-02", form.EndDate)
		if err != nil {
			ac.APIError(c, errors.New("参数 EndDate 格式错误, 示例：2006-01-02"))
			return
		}
	}
	refund.OrderRefundTask(c.Request.Context(), form.GameID, form.StartDate, form.EndDate)
	ac.APISuccess(c, "")
}

type RefundParams struct {
	GameID     int    `form:"game_id" binding:"required"`
	RoleID     string `form:"role_id"`
	RoleName   string `form:"role_name"`
	ServerID   string `form:"server_id"`
	Field      string `form:"field"`
	Order      string `form:"order"`
	PageSize   int    `form:"pageSize"`
	PageNumber int    `form:"pageNumber"`
}

// GetRefundOrder
// @Summary	获取退款订单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  RefundParams	true	"获取退款订单"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/refund [get]
func (ac APIV1Controller) GetRefundOrder(c *gin.Context) {
	var refundParams RefundParams
	err := c.ShouldBindQuery(&refundParams)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	gameID := refundParams.GameID
	roleID := refundParams.RoleID
	roleName := refundParams.RoleName
	ServerID := refundParams.ServerID
	sortColumn := refundParams.Field
	sortRule := refundParams.Order
	pageSize := refundParams.PageSize
	pageNum := refundParams.PageNumber

	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}

	orderData, total := services.GetRefundOrder(c, gameID, roleID, roleName, ServerID, sortColumn, sortRule, pageNum, pageSize)
	var refundOrderResponse schema.APIResponse
	refundOrderResponse.Total = total
	refundOrderResponse.PageSize = pageSize
	refundOrderResponse.PageNum = pageNum
	refundOrderResponse.Data = orderData
	if total == 0 {
		refundOrderResponse.Data = []schema.RefundOrder{}
	}
	ac.APISuccess(c, refundOrderResponse)
}

// GetRefundOrderInfo
// @Summary	获取退款订单详情
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param   game_id    query  string  true  "游戏id"
// @Param   role_id    query  string  false  "角色id"
// @Param   server_id    query  string  false  "区服id"
// @Param   pageSize    query  string  false  "每一页展示的数量后端默认：10"
// @Param   pageNumber    query  string  false  "页"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/refund-info [get]
func (ac APIV1Controller) GetRefundOrderInfo(c *gin.Context) {
	gameID, err := strconv.Atoi(c.Query("game_id"))
	var pageSize, pageNum int
	if c.Query("pageSize") != "" {
		pageSize, err = strconv.Atoi(c.Query("pageSize"))
	}
	if c.Query("pageNumber") != "" {
		pageNum, err = strconv.Atoi(c.Query("pageNumber"))
	}
	if err != nil {
		ac.APIError(c, err)
		return
	}
	roleID := c.Query("role_id")
	ServerID := c.Query("server_id")

	refundOrderInfo, total := services.GetRefundOrderInfo(c, gameID, roleID, ServerID, pageNum, pageSize)
	var refundOrderInfoResponse schema.APIResponse
	refundOrderInfoResponse.Total = total
	refundOrderInfoResponse.PageSize = pageSize
	refundOrderInfoResponse.PageNum = pageNum
	refundOrderInfoResponse.Data = refundOrderInfo
	if total == 0 {
		refundOrderInfoResponse.Data = []schema.RefundOrderInfo{}
	}
	ac.APISuccess(c, refundOrderInfoResponse)
}

// IOSOrderQuery
// @Summary	根据票据获取ios订单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  GetIOSOrderForm	true	"根据票据获取ios订单"
// @Success 200 {object} controllers.APISuccessResponse{result=[]apple.TransactionOrder}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/ios_query [post]
func (ac APIV1Controller) IOSOrderQuery(c *gin.Context) {
	var form GetIOSOrderForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	var config payment.ApplePay
	converter.QueryByGameId(&config, form.GameID)

	appleClient := apple.AppleClient{
		ISS:    *config.AppleISS,
		KeyID:  *config.AppleKeyID,
		BID:    *config.AppleBundleId,
		Secret: *config.AppleSecret,
	}

	transactionInfo, err := apple.GetOrderByOrderID(c.Request.Context(), appleClient, form.PurchaseOrderID)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	order := apple.GetOrderByTransactionInfo(c, transactionInfo, form.PurchaseOrderID, form.RoleID)

	ac.APISuccess(c, order)
}

// IOSOrderFix
// @Summary	根据票据补单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  FixIOSOrderForm	true	"根据票据补单"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/ios_fix [post]
func (ac APIV1Controller) IOSOrderFix(c *gin.Context) {
	var form FixIOSOrderForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	orderDB, err := services.GetOrderByOrderID(c, form.OrderID)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	dbGame, _ := game.GetGameById(form.GameID)

	var pay payment.Payment
	converter.QueryByGameId(&pay, form.GameID)

	var config payment.ApplePay
	converter.QueryByGameId(&config, form.GameID)

	if orderDB.PayId == "" && orderDB.PayStatus != constants.PaymentSuccess {
		// 补发
		if *config.AppleISS == "" {
			ac.APIError(c, errors.New("当前项目缺少请求苹果服务器的认证信息"))
			return
		}
		act := apple.AppleClient{
			ISS:    *config.AppleISS,
			KeyID:  *config.AppleKeyID,
			BID:    *config.AppleBundleId,
			Secret: *config.AppleSecret,
		}
		isSandBox := false
		transactionInfo, err := act.VerifyOrderWithOriginTransactionID(c.Request.Context(), form.TransactionID, isSandBox)
		if transactionInfo.TransactionId == "" {
			isSandBox = true
			transactionInfo, err = act.VerifyOrderWithOriginTransactionID(c.Request.Context(), form.TransactionID, isSandBox)
		}
		if err != nil {
			orderDB.VerifyStatus = constants.VerifyFailed
			ac.APIError(c, errors.New("未在Apple后台查询到交易订单"))
			return
		}

		if transactionInfo.ProductId != "" && transactionInfo.ProductId != orderDB.ProductId {
			ac.APIError(c, errors.New("票据内商品ID与订单商品ID不符"))
			return
		}

		orderDBOld := map[string]interface{}{
			"pay_id":        orderDB.PayId,
			"pay_status":    orderDB.PayStatus,
			"notify_status": orderDB.NotifyStatus,
			"receipt":       "",
		}

		if transactionInfo.Environment == "Sandbox" {
			orderDB.OrderAttribute = constants.TestAccount
		}
		transactionInfoJson, err := json.Marshal(transactionInfo)
		orderDB.PayId = form.TransactionID
		orderDB.Receipt = string(transactionInfoJson)
		orderDB.PayWay = constants.PayWayApple
		orderDB.PayStatus = constants.PaymentSuccess
		orderDB.PayProductId = transactionInfo.ProductId
		orderDB.PayTime = time.Now()

		if err := services.SaveOrder(c, orderDB); err != nil {
			ac.APIError(c, errors.New("失败"))
			return
		}
		orderDBNew := map[string]interface{}{
			"pay_id":        form.TransactionID,
			"pay_way":       constants.PayWayApple,
			"pay_status":    orderDB.PayStatus,
			"notify_status": orderDB.NotifyStatus,
			"receipt":       string(transactionInfoJson),
		}
		services.AddOperation(c, dbGame.ProjectID, "order", orderDB.OrderId, "IOS票据补发", "update", form, orderDBOld, orderDBNew)
	} else if orderDB.PayStatus == constants.PaymentSuccess {
		// 补单
		orderDBOld := map[string]interface{}{
			"pay_status":    orderDB.PayStatus,
			"notify_status": orderDB.NotifyStatus,
		}
		services.AddOperation(c, dbGame.ProjectID, "order", orderDB.OrderId, "IOS票据补单", "update", form, orderDBOld, orderDBOld)
	}

	db := ac.GetDB(c)
	err = services.Notify(c.Request.Context(), db, orderDB, services.NotifyOptions{})
	if err != nil {
		ac.APIError(c, err)
		return
	}

	ac.APISuccess(c, "")
}

// OrdersDownload
// @Summary	订单下载
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  schema.OrderQueryParams	true	"订单下载"
// @Success 200
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/download [post]
func (ac APIV1Controller) OrdersDownload(c *gin.Context) {
	var orderQueryParams schema.OrderQueryParams
	err := c.ShouldBindJSON(&orderQueryParams)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	count := services.GetDownloadOrderCount(c, orderQueryParams)
	if count == 0 {
		ac.APIError(c, errors.New("未查询到数据！"))
		return
	}
	if count > constants.MaxDownloadCount {
		ac.APIError(c, errors.New("导出失败，单次导出数据上限为10万条，可添加限制条件或调整时间范围后重试！"))
		return
	}
	s := download.NewService(fmt.Sprintf("order-%d.xlsx", time.Now().Unix()), "order", orderQueryParams)
	filePath, err := s.Download(c)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	ac.File(c, filePath)
}

// OrderStatisticsDownload
// @Summary	订单统计下载
// @Tags	Order
// @Accept	json
// @Produce json
// @Success 200
// @Router /api/v1/orders/statistics_download [post]
func (ac APIV1Controller) OrderStatisticsDownload(c *gin.Context) {
	var orderQueryParams schema.OrderStatisticsParams
	err := c.ShouldBindJSON(&orderQueryParams)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	s := download.NewService(fmt.Sprintf("order_statistics-%d.xlsx", time.Now().Unix()), "order_statistics", orderQueryParams)
	filePath, err := s.Download(c)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	ac.File(c, filePath)
}

// HuaweiOrderQuery
// @Summary 查询huawei订单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  OrderHuaweiForm	true	"查询huawei订单"
// @Success 200 {object} controllers.APISuccessResponse{result=OrderHuaweiQueryResult}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/huawei_query [post]
func (ac APIV1Controller) HuaweiOrderQuery(c *gin.Context) {
	var form OrderHuaweiForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	dbGame, _ := game.GetGameById(form.GameID)
	var config payment.HuaweiPay
	converter.QueryByGameId(&config, form.GameID)

	huaweiPayClient := pay.HuaweiPayClient{
		ClientID:     *config.HuaweiClientId,
		ClientSecret: *config.HuaweiClientSecret,
		PublicKey:    *config.HuaweiPublicKey,
	}

	var purchaseData pay.HuaweiPurchaseData

	// 校验订单支付token
	purchaseData, err = huaweiPayClient.VerifyHuaweiOrder(c.Request.Context(), dbGame, form.PurchaseToken, form.ProductId)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	orderID := purchaseData.DeveloperPayload
	orderDB, err := services.GetOrderByOrderID(c, orderID)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	result := OrderHuaweiQueryResult{
		RoleId:           orderDB.RoleId,
		RoleName:         orderDB.RoleName,
		ServerId:         orderDB.ServerId,
		ServerName:       orderDB.ServerName,
		PayStatus:        orderDB.PayStatus,
		NotifyStatus:     orderDB.NotifyStatus,
		PurchaseState:    *purchaseData.PurchaseState,
		AckState:         purchaseData.Confirmed,
		ConsumptionState: purchaseData.ConsumptionState,
		PayId:            purchaseData.OrderId,
		OrderId:          orderDB.OrderId,
	}
	ac.APISuccess(c, result)
}

// HuaweiOrderFix
// @Summary	修复huawei订单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body  OrderHuaweiForm	true	"修复huawei订单"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/huawei_fix [post]
func (ac APIV1Controller) HuaweiOrderFix(c *gin.Context) {
	var form OrderHuaweiForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	dbGame, _ := game.GetGameById(form.GameID)
	var config payment.HuaweiPay
	converter.QueryByGameId(&config, form.GameID)

	huaweiPayClient := pay.HuaweiPayClient{
		ClientID:     *config.HuaweiClientId,
		ClientSecret: *config.HuaweiClientSecret,
		PublicKey:    *config.HuaweiPublicKey,
	}

	var purchaseData pay.HuaweiPurchaseData

	// 校验订单支付token
	purchaseData, err = huaweiPayClient.VerifyHuaweiOrder(c.Request.Context(), dbGame, form.PurchaseToken, form.ProductId)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	orderID := purchaseData.DeveloperPayload
	orderDB, err := services.GetOrderByOrderID(c, orderID)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	orderDBOld := map[string]int{
		"pay_status":    orderDB.PayStatus,
		"notify_status": orderDB.NotifyStatus,
	}
	orderDB.PayId = purchaseData.OrderId
	orderDB.Receipt = purchaseData.ToString()
	orderDB.PayWay = constants.PayWayHuawei
	orderDB.PayStatus = constants.PaymentSuccess
	orderDB.PayProductId = form.ProductId
	orderDB.PayTime = time.Now()
	orderDB.VerifyStatus = constants.VerifyPassed

	if err := services.SaveOrder(c, orderDB); err != nil {
		ac.APIError(c, errors.New("失败"))
		return
	}
	orderDBNew := map[string]interface{}{
		"pay_id":        purchaseData.OrderId,
		"pay_way":       constants.PayWayHuawei,
		"pay_status":    orderDB.PayStatus,
		"notify_status": orderDB.NotifyStatus,
	}
	services.AddOperation(c, dbGame.ProjectID, "order", orderDB.OrderId, "Huawei订单票据补单", "update", form, orderDBOld, orderDBNew)
	db := ac.GetDB(c)
	err = services.Notify(c.Request.Context(), db, orderDB, services.NotifyOptions{})
	if err != nil {
		ac.APIError(c, err)
		return
	}

	ac.APISuccess(c, "")
}
