package v1

import (
	"reflect"
	"strconv"

	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/field"
	"zeus/services/settings/payment"
	"zeus/services/settings/store"

	"github.com/gin-gonic/gin"
)

type GameAccountStatus struct {
	Label       string `json:"label"`
	Name        string `json:"name"`
	IsLoginOpen int    `json:"is_login_open"`
}

type GamePaymentStatus struct {
	Label         string `json:"label"`
	Name          string `json:"name"`
	IsPaymentOpen int    `json:"is_payment_open"`
}

type GameInfoFieldConfig struct {
	Label       string      `json:"label"`
	Name        string      `json:"name"`
	Value       interface{} `json:"value"`
	Type        string      `json:"type"`
	Placeholder string      `json:"placeholder"`
	Required    bool        `json:"required"`
}

type PaymentModuleInfo struct {
	PaymentModule  GamePaymentStatus     `json:"paymentModule"`
	PaymentConfigs []GameInfoFieldConfig `json:"paymentConfigs"`
}

type PaymentModuleConfigs struct {
	PaymentModules    []PaymentModuleInfo `json:"paymentModules"`
	IsRechargeOpen    int                 `json:"is_recharge_open"`
	PayNotifyUrl      string              `json:"pay_notify_url"`
	PayNotifyKey      string              `json:"pay_notify_key"`
	IsSetPayNotifyUrl bool                `json:"is_set_pay_notify_url"`
	DingTalkToken     string              `json:"ding_talk_token"`
}

type PaymentModule struct {
	Items []field.ConfigField `json:"items"`
	Title struct {
		OpenStatus   int    `json:"open_status"`
		Name         string `json:"name"`
		Label        string `json:"label"`
		AllowChecked bool   `json:"allow_checked"`
	} `json:"title"`
}

// GetGameSetting
// @Summary	获取游戏配置
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param	person	body	schema.GameSummary	true	"获取游戏配置"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Game}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Success 200
// @Router /api/v1/game/payments [get]
func (gc APIV1Controller) GetGamePayments(c *gin.Context) {
	gameID, _ := strconv.Atoi(c.Query("game_id"))

	var result struct {
		payment.Payment
		PaymentModules []PaymentModule `json:"modules"`
	}
	var base payment.Payment
	converter.QueryByGameId(&base, gameID)
	converter.StructConverter(base, &result)

	dbGame, _ := game.GetGameById(gameID)

	for _, pay := range store.PaySettings {
		config := reflect.New(reflect.TypeOf(pay)).Interface().(field.PaymentConfiger)
		if dbGame.Platform != "all" && config.GetPlatform() != "all" && dbGame.Platform != config.GetPlatform() {
			continue
		}
		converter.QueryByGameId(config.(field.BaseConfiger), gameID)
		var paymentModule PaymentModule
		paymentModule.Items = converter.ConvertToConfig(config)
		paymentModule.Title.Name = config.GetType()
		paymentModule.Title.Label = config.GetLabel()
		paymentModule.Title.OpenStatus = config.GetOpenStatus()
		paymentModule.Title.AllowChecked = config.AllowChecked()
		result.PaymentModules = append(result.PaymentModules, paymentModule)
	}
	gc.APISuccess(c, result)
}
