package v1

import (
	"github.com/gin-gonic/gin"
	"github.com/mohae/deepcopy"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"strconv"
	"time"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
)

type PointStrategyFrom struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Points      int    `json:"points" binding:"required"`
	ProductId   string `json:"product_id" binding:"required"`
}

// CreateWebPayPointStrategy
// @Summary	创建积分策略
// @Tags	API WebPayPointStrategy
// @Accept	json
// @Produce json
// @Param   point_strategy  body    PointStrategyRecordFrom  true  "创建积分策略"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/point_strategy [post]
func (ac APIV1Controller) CreateWebPayPointStrategy(c *gin.Context) {
	var form PointStrategyFrom
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	db := c.MustGet("db").(*gorm.DB)

	pointStrategyInfo := &models.WebPayPointStrategy{
		Name:        form.Name,
		Description: form.Description,
		Points:      form.Points,
		ProductId:   form.ProductId,
	}

	err = models.CreateWebPayPointStrategy(db, pointStrategyInfo)
	if err != nil {
		ac.APIError(c, errors.Errorf("策略创建失败， %s", err))
		return
	}

	gameID := c.MustGet("gameID").(int)
	gameDB, _ := game.GetGameInfoById(gameID)
	if gameDB.ProjectID != 0 {
		services.AddOperation(c, gameDB.ProjectID, "point_strategy", pointStrategyInfo.Name, "创建累计充值策略", "create", form, "", pointStrategyInfo)
	}
	ac.APISuccess(c, "")
	return
}

// UpdateWebPayPointStrategy
// @Summary	修改积分策略
// @Tags	API WebPayPointStrategy
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "id"
// @Param   body    PointStrategyFrom  true  "修改积分策略"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/point_strategy/{id} [put]
func (ac APIV1Controller) UpdateWebPayPointStrategy(c *gin.Context) {
	var form PointStrategyFrom
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	strategyId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		ac.APIError(c, err)
		return
	}
	strategyInfo, err := models.GetWebPayPointStrategyById(db, strategyId)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	oldStrategy := deepcopy.Copy(strategyInfo)

	strategyInfo.Name = form.Name
	strategyInfo.Description = form.Description
	strategyInfo.ProductId = form.ProductId
	strategyInfo.UpdateTime = time.Now()
	strategyInfo.Points = form.Points
	err = models.SaveWebPayPointStrategy(db, strategyInfo)
	if err != nil {
		ac.APIError(c, errors.Errorf("策略保存失败， %s", err))
		return
	}

	gameID := c.MustGet("gameID").(int)
	gameDB, _ := game.GetGameInfoById(gameID)
	if gameDB.ProjectID != 0 {
		services.AddOperation(c, gameDB.ProjectID, "point_strategy", strategyInfo.Name, "修改累计充值策略", "update", form, oldStrategy, strategyInfo)
	}

	ac.APISuccess(c, "")
	return
}

// GetWebPayPointStrategy
// @Summary	获取积分策略
// @Tags	API WebPayPointStrategy
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "id"
// @Success 200 {object} controllers.APISuccessResponse{result=ActivityResp}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/point_strategy [get]
func (ac APIV1Controller) GetWebPayPointStrategy(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	strategies, total, err := models.GetWebPayPointStrategy(db)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	resp := schema.APIResponse{
		Total: total,
		Data:  strategies,
	}

	ac.APISuccess(c, resp)
	return
}

// DeleteWebPayPointStrategy
// @Summary	删除商品
// @Tags	API Product
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "商品id"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/point_strategy/{id} [delete]
func (ac APIV1Controller) DeleteWebPayPointStrategy(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	strategyId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		ac.APIError(c, err)
		return
	}

	strategy, err := models.GetWebPayPointStrategyById(db, strategyId)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	err = models.DeleteWebPayPointStrategy(db, strategy)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	if strategy.Id != 0 {
		gameID := c.MustGet("gameID").(int)
		gameDB, _ := game.GetGameInfoById(gameID)
		if gameDB.ProjectID != 0 {
			services.AddOperation(c, gameDB.ProjectID, "point_strategy", strategy.Name, "删除累计充值策略", "delete", strategyId, strategy, "")
		}
	}

	ac.APISuccess(c, "删除成功")
}
