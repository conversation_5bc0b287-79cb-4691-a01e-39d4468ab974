package v1

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/mohae/deepcopy"
	"gorm.io/gorm"
	"strconv"
	"time"
	"zeus/common/constants"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
)

type PointStrategyRecordFrom struct {
	Name        string                     `json:"name" binding:"required"`
	Type        int                        `json:"type" binding:"required"`
	Description string                     `json:"description"`
	Products    []models.PointsAndProducts `json:"products" binding:"required"`
	RangeTime   []string                   `json:"range_time"`
	Picture     string                     `json:"picture"`
}

type recordInfo struct {
	Id          int                        `json:"id"`
	Name        string                     `json:"name"`
	Description string                     `json:"description"`
	Products    []models.PointsAndProducts `json:"products"`
	RangeTime   []string                   `json:"range_time"`
	Picture     string                     `json:"picture"`
	Type        int                        `json:"type"`
}

// CreateWebPayPointStrategyRecord
// @Summary	创建积分策略
// @Tags	API WebPayPointStrategyRecord
// @Accept	json
// @Produce json
// @Param   point_strategy_Record  body    PointStrategyRecordFrom  true  "创建积分策略"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/point_strategy_record [post]
func (ac APIV1Controller) CreateWebPayPointStrategyRecord(c *gin.Context) {
	var form PointStrategyRecordFrom
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	db := c.MustGet("db").(*gorm.DB)

	startTime, endTime, err := services.GetPointStrategyRecordRangeTimeByType(db, 0, form.Type, form.RangeTime)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	err = services.CheckStrategyRecord(db, 0, startTime, endTime)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	products, err := json.Marshal(form.Products)

	pointStrategyRecordInfo := &models.WebPayPointStrategyRecord{
		Name:        form.Name,
		Description: form.Description,
		Products:    string(products),
		StartTime:   startTime,
		EndTime:     endTime,
		Picture:     form.Picture,
		Type:        form.Type,
	}

	err = models.CreateWebPayPointStrategyRecord(db, pointStrategyRecordInfo)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	gameID := c.MustGet("gameID").(int)
	gameDB, _ := game.GetGameInfoById(gameID)
	if gameDB.ProjectID != 0 {
		services.AddOperation(c, gameDB.ProjectID, "point_strategy_record", pointStrategyRecordInfo.Name, "创建累计充值记录", "create", form, "", pointStrategyRecordInfo)
	}
	ac.APISuccess(c, "")
	return
}

// UpdateWebPayPointStrategyRecord
// @Summary	修改积分策略
// @Tags	API WebPayPointStrategyRecord
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "id"
// @Param   body    PointStrategyRecordFrom  true  "修改积分策略"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/point_strategy_record/{id} [put]
func (ac APIV1Controller) UpdateWebPayPointStrategyRecord(c *gin.Context) {
	var form PointStrategyRecordFrom
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	recordId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		ac.APIError(c, err)
		return
	}

	recordInfo, err := models.GetWebPayPointStrategyRecordById(db, recordId)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	startTime, endTime, err := services.GetPointStrategyRecordRangeTimeByType(db, recordId, form.Type, form.RangeTime)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	err = services.CheckStrategyRecord(db, recordId, startTime, endTime)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	oldRecord := deepcopy.Copy(recordInfo)

	products, err := json.Marshal(form.Products)
	recordInfo.Name = form.Name
	recordInfo.Description = form.Description
	recordInfo.Products = string(products)
	recordInfo.StartTime = startTime
	recordInfo.EndTime = endTime
	recordInfo.UpdateTime = time.Now()
	recordInfo.Picture = form.Picture
	recordInfo.Type = form.Type
	err = models.SaveWebPayPointStrategyRecord(db, recordInfo)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	gameID := c.MustGet("gameID").(int)
	gameDB, _ := game.GetGameInfoById(gameID)
	if gameDB.ProjectID != 0 {
		services.AddOperation(c, gameDB.ProjectID, "point_strategy_record", recordInfo.Name, "修改累计充值记录", "update", form, oldRecord, recordInfo)
	}

	ac.APISuccess(c, "")
	return
}

// GetWebPayPointStrategyRecord
// @Summary	获取积分策略
// @Tags	API WebPayPointStrategyRecord
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "id"
// @Success 200 {object} controllers.APISuccessResponse{result=ActivityResp}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/point_strategy_record [get]
func (ac APIV1Controller) GetWebPayPointStrategyRecord(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)

	page, err := strconv.Atoi(c.Query("pageNumber"))
	if err != nil {
		page = 1
	}
	pageSize, err := strconv.Atoi(c.Query("pageSize"))
	if err != nil {
		pageSize = 10
	}

	args := make(map[string]interface{})
	records, total, err := models.GetWebPayPointStrategyRecords(
		db,
		pageSize,
		page,
		args,
	)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	strategiesResp := make([]recordInfo, 0)

	for _, v := range records {
		var products = make([]models.PointsAndProducts, 0)
		json.Unmarshal([]byte(v.Products), &products)
		strategiesResp = append(strategiesResp, recordInfo{
			Id:          v.Id,
			Name:        v.Name,
			Description: v.Description,
			Products:    products,
			Picture:     v.Picture,
			Type:        v.Type,
			RangeTime: []string{
				v.StartTime.Format(constants.DateTimeMinuteOnly),
				v.EndTime.Format(constants.DateTimeMinuteOnly),
			},
		})
	}

	resp := schema.APIResponse{
		PageSize: pageSize,
		PageNum:  page,
		Total:    total,
		Data:     strategiesResp,
	}

	ac.APISuccess(c, resp)
	return
}
