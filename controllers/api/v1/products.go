package v1

import (
	"errors"
	"strconv"
	"strings"
	"time"

	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"

	"github.com/mohae/deepcopy"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProductFrom struct {
	Name          string            `json:"name" binding:"required"`
	GameProductId string            `json:"game_product_id" binding:"required"`
	Attribute     int               `json:"attribute"`
	Prices        models.PriceItems `json:"prices" binding:"required"`
	Picture       string            `json:"picture" binding:"required"`
	Description   string            `json:"description" binding:"required"`
	Points        int               `json:"points"`
}

type ProductResp struct {
	ID            int               `json:"id"`
	Name          string            `json:"name"`
	GameProductId string            `json:"game_product_id"`
	Attribute     int               `json:"attribute"`
	InActivitys   []string          `json:"in_activitys"`
	Prices        models.PriceItems `json:"prices"`
	Picture       string            `json:"picture"`
	Description   string            `json:"description"`
	Points        int               `json:"points"`
}

// CreateProduct
// @Summary	创建商品
// @Tags	API Product
// @Accept	json
// @Produce json
// @Param   product  body    ProductFrom  true  "创建商品"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/products [post]
func (ac APIV1Controller) CreateProduct(c *gin.Context) {
	var form ProductFrom
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	db := c.MustGet("db").(*gorm.DB)
	var product models.Product
	product.Name = form.Name
	product.GameProductId = form.GameProductId
	product.Attribute = form.Attribute
	product.Prices = form.Prices.ToMap()
	product.Picture = form.Picture
	product.Description = form.Description
	product.Points = form.Points
	err = models.CreateProduct(db, &product)
	if err != nil {
		if strings.Contains(err.Error(), "Error 1062") {
			ac.APIError(c, errors.New("商品重复，请修改商品名称或使用已有商品"))
			return
		}
		ac.APIError(c, err)
		return
	}

	gameID := c.MustGet("gameID").(int)
	gameDB, _ := game.GetGameInfoById(gameID)
	if gameDB.ProjectID != 0 {
		services.AddOperation(c, gameDB.ProjectID, "product", product.Name, "创建商品", "create", form, "", product)
	}

	ac.APISuccess(c, "创建成功")
}

// UpdateProducts
// @Summary	更新商品
// @Tags	API Product
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "商品id"
// @Param   product  body    ProductFrom  true  "更新商品"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/products/{id} [put]
func (ac APIV1Controller) UpdateProducts(c *gin.Context) {
	var form ProductFrom
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	productId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		ac.APIError(c, err)
		return
	}

	db := c.MustGet("db").(*gorm.DB)

	product, err := models.GetProductById(db, productId)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	oldProduct := deepcopy.Copy(product)

	product.Name = form.Name
	product.Picture = form.Picture
	product.Description = form.Description
	product.Points = form.Points
	product.GameProductId = form.GameProductId
	prices := make(models.Prices)
	for _, price := range form.Prices {
		prices.SetPrice(price.Currency, price.Price)
	}
	product.Prices = prices
	err = models.SaveProduct(db, product)
	if err != nil {
		if strings.Contains(err.Error(), "Error 1062") {
			ac.APIError(c, errors.New("商品重复，请修改商品名称或使用已有商品"))
			return
		}
		ac.APIError(c, err)
		return
	}

	gameID := c.MustGet("gameID").(int)
	gameDB, _ := game.GetGameInfoById(gameID)
	if gameDB.ProjectID != 0 {
		services.AddOperation(c, gameDB.ProjectID, "product", product.Name, "更新商品", "create", form, oldProduct, product)
	}

	ac.APISuccess(c, "更新成功")
}

// GetProducts
// @Summary	获取商品列表
// @Tags	API Product
// @Accept	json
// @Produce json
// @Param   pageNumber    query  int  true  "页码"
// @Param   pageSize    query  int  true  "每页数量"
// @Param   name    query  string  false  "商品名称"
// @Param   game_product_id    query  string  false  "商品属性"
// @Param   attribute    query  int  false  "商品属性"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/products [get]
func (ac APIV1Controller) GetProducts(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	page, err := strconv.Atoi(c.Query("pageNumber"))
	if err != nil {
		page = 1
	}
	pageSize, err := strconv.Atoi(c.Query("pageSize"))
	if err != nil {
		pageSize = 10
	}
	args := make(map[string]interface{})
	if c.Query("attribute") != "" {
		args["attribute"] = c.Query("attribute")
	}

	products, total, err := models.GetProducts(
		db,
		pageSize,
		page,
		c.Query("name"),
		c.Query("game_product_id"),
		args,
	)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	activity, _ := models.GetActivityByEndTimeAndType(db, time.Now(), 1)

	productsResp := make([]ProductResp, 0)
	for _, v := range products {
		activityNames := make([]string, 0)
		for _, a := range activity {
			if ok := a.ActivityProducts.GetProduct(v.Id); ok != nil {
				activityNames = append(activityNames, a.Name)
			}
		}

		productsResp = append(productsResp, ProductResp{
			ID:            v.Id,
			Name:          v.Name,
			GameProductId: v.GameProductId,
			Attribute:     v.Attribute,
			InActivitys:   activityNames,
			Prices:        v.Prices.Items(),
			Picture:       v.Picture,
			Description:   v.Description,
			Points:        v.Points,
		})
	}

	resp := schema.APIResponse{
		PageSize: pageSize,
		PageNum:  page,
		Total:    total,
		Data:     productsResp,
	}

	ac.APISuccess(c, resp)
}

// DeleteProducts
// @Summary	删除商品
// @Tags	API Product
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "商品id"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/products/{id} [delete]
func (ac APIV1Controller) DeleteProducts(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	productId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		ac.APIError(c, err)
		return
	}

	product, _ := models.GetProductById(db, productId)

	err = models.DeleteProductById(db, productId)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	if product.Id != 0 {
		gameID := c.MustGet("gameID").(int)
		gameDB, _ := game.GetGameInfoById(gameID)
		if gameDB.ProjectID != 0 {
			services.AddOperation(c, gameDB.ProjectID, "product", product.Name, "删除商品", "delete", productId, product, "")
		}
	}

	ac.APISuccess(c, "删除成功")
}
