package v1

import (
	"database/sql"
	"encoding/json"
	"sort"
	"strconv"

	"zeus/common/conf"
	"zeus/middlewares/checkJWT"
	"zeus/models"
	"zeus/schema"
	"zeus/services"

	"github.com/gin-gonic/gin"
)

// GetProjects
// @Summary	获取所有游戏
// @Tags	API Project
// @Accept	json
// @Produce json
// @Success 200 {object} controllers.APISuccessResponse{result=[]models.Project}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/project [get]
func (gc APIV1Controller) GetProjects(c *gin.Context) {
	var projects []*models.Project

	user, _ := c.Get("user")
	JWTUser := user.(*checkJWT.JWTUser)

	if JWTUser.IsAdmin {
		projects, _ = services.GetProjects()
	} else {
		for _, project := range JWTUser.AllowGames {
			project, err := services.GetProjectByAlias(project.Alias)
			if err == nil {
				projects = append(projects, project)
			}
		}
	}
	// 按代号的字母排序返回
	sort.Slice(projects, func(i, j int) bool {
		a := projects[i].Alias
		b := projects[j].Alias
		return a < b
	})
	gc.APISuccess(c, projects)
}

// CreateProject
// @Summary	添加游戏
// @Tags	API Project
// @Accept	json
// @Produce json
// @Param	person	body	schema.ProjectCreateForm	true	"添加游戏"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Project}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/project [post]
func (gc APIV1Controller) CreateProject(c *gin.Context) {
	var projectForm schema.ProjectCreateForm
	err := c.ShouldBindJSON(&projectForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	dbProject, err := services.CreateProject(projectForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	services.AddOperation(c, dbProject.ID, "project", dbProject.Name, "游戏配置", "create", projectForm, "", dbProject)
	gc.APISuccess(c, dbProject)
}

// UpdateProject
// @Summary	更新游戏
// @Tags	API Project
// @Accept	json
// @Produce json
// @Param	person	body	schema.ProjectSummary	true	"更新游戏"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Project}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/project/id [put]
func (gc APIV1Controller) UpdateProject(c *gin.Context) {
	var projectForm schema.ProjectSummary
	err := c.ShouldBindJSON(&projectForm)
	projectID, err := strconv.Atoi(c.Param("id"))

	dbProject, err := services.GetProjectById(projectID)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	dbProjectJson, _ := json.Marshal(dbProject)
	var dbProjectOld models.Project
	json.Unmarshal(dbProjectJson, &dbProjectOld)
	dbProjectNew, err := services.UpdateProject(dbProject, &projectForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	services.AddOperation(c, dbProjectNew.ID, "project", dbProjectOld.Name, "游戏配置", "update", projectForm, dbProjectOld, dbProjectNew)
	gc.APISuccess(c, dbProject)
}

// CheckMysqlConnection
// @Summary	检查mysql链接
// @Tags	API Project
// @Accept	json
// @Produce json
// @Param	person	body	schema.ProjectCreateForm	true	"检查mysql链接"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/project/check_connection [post]
func (gc APIV1Controller) CheckMysqlConnection(c *gin.Context) {
	var projectForm schema.ProjectCreateForm
	err := c.ShouldBindJSON(&projectForm)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	dataSource := conf.Conf.DB.Username + ":" + conf.Conf.DB.Password +
		"@tcp(" + conf.Conf.DB.Host + ":" + conf.Conf.DB.Port + ")/" + projectForm.MysqlDatabase +
		"?charset=" + conf.Conf.DB.Charset + "&parseTime=True&loc=Local&timeout=3s"
	Db, err := sql.Open("mysql", dataSource)
	if err != nil {
		gc.APISuccess(c, "数据库链接错误")
		return
	}
	err = Db.Ping()
	if err != nil {
		gc.APISuccess(c, "数据库链接错误")
		return
	}
	gc.APISuccess(c, "数据库链接正常")
}
