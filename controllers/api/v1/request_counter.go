package v1

import (
	"zeus/schema"
	"zeus/services"

	"github.com/gin-gonic/gin"
)

// GetRequestCount
// @Summary	获取项目接口请求次数
// @Tags	API Game
// @Accept	json
// @Produce json
// @Success 200
// @Router /api/v1/request_count [get]
func (ac APIV1Controller) GetRequestCount(c *gin.Context) {
	requestData := services.GetRequestStatistics()
	ac.APISuccess(c, requestData)
}

// GetRequestStatistics
// @Summary	获取项目接口请求次数
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param	person	body	schema.RequestStatisticsForm	true	"获取项目接口请求次数"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/request_statistics [get]
func (ac APIV1Controller) GetRequestStatistics(c *gin.Context) {
	var form schema.RequestStatisticsForm
	err := c.ShouldBindQuery(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	dbRequestStatistics, total, err := services.GetRequestStatisticsData(form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	var requestCountResponse schema.APIResponse
	requestCountResponse.Total = total
	requestCountResponse.PageSize = form.PageSize
	requestCountResponse.PageNum = form.PageNumber
	requestCountResponse.Data = dbRequestStatistics
	if total == 0 {
		requestCountResponse.Data = []schema.RequestStatistics{}
	}
	ac.APISuccess(c, requestCountResponse)
}

// GetRequestPercentage
// @Summary	获取所有项目接口请求次数
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param	person	body	schema.QueryRequestPercentageForm	true	"获取所有项目接口请求次数"
// @Success 200 {object} controllers.APISuccessResponse{result=[]schema.RequestPercentage}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/request_statistics/percentage [get]
func (ac APIV1Controller) GetRequestPercentage(c *gin.Context) {
	var form schema.QueryRequestPercentageForm
	err := c.ShouldBindQuery(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	dbRequestStatistics := services.GetRequestPercentageData(form)
	ac.APISuccess(c, dbRequestStatistics)
}
