package v1

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"

	"zeus/pkg/redis"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/settings/converter"
	"zeus/services/settings/field"
	"zeus/services/settings/store"

	"github.com/gin-gonic/gin"
	"golang.org/x/exp/slices"
)

// UpdateGame
// @Summary	获取游戏配置
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param	person	body	schema.GameSummary	true	"更新游戏配置"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Game}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Success 200
// @Router /api/v1/settings/:setting_type [get]
func (gc APIV1Controller) GetSettings(c *gin.Context) {
	settingType := c.Param("type")
	gameID, _ := strconv.Atoi(c.Query("game_id"))
	if settingSchema, ok := store.Settings[settingType]; ok {
		config := reflect.New(reflect.TypeOf(settingSchema)).Interface()
		if slices.Contains([]string{"ComplaintWarning", "Antispam"}, settingType) {
			dbGame, _ := game.GetGameById(gameID)
			converter.QueryByProjectId(config.(field.BaseConfiger), dbGame.ProjectID)
		} else {
			converter.QueryByGameId(config.(field.BaseConfiger), gameID)
		}
		gc.APISuccess(c, config)
		return

	} else {
		gc.APIError(c, errors.New("无效的配置类型"))
		return
	}
}

// UpdateGame
// @Summary	获取游戏配置详情
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param	person	body	schema.GameSummary	true	"更新游戏配置"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Game}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Success 200
// @Router /api/v1/settings/:setting_type/fields [get]
func (gc APIV1Controller) GetSettingsFields(c *gin.Context) {
	settingType := c.Param("type")
	gameID, _ := strconv.Atoi(c.Query("game_id"))

	if settingSchema, ok := store.Settings[settingType]; ok {
		config := reflect.New(reflect.TypeOf(settingSchema)).Interface()
		if slices.Contains([]string{"ComplaintWarning", "Antispam"}, settingType) {
			dbGame, _ := game.GetGameById(gameID)
			converter.QueryByProjectId(config.(field.BaseConfiger), dbGame.ProjectID)
		} else {
			converter.QueryByGameId(config.(field.BaseConfiger), gameID)
		}
		fields := converter.ConvertToConfig(config.(field.BaseConfiger))
		gc.APISuccess(c, fields)
		return
	} else {
		gc.APIError(c, errors.New("无效的配置类型"))
		return
	}
}

// UpdateGame
// @Summary	更新游戏配置
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param	person	body	schema.GameSummary	true	"更新游戏配置"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Game}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Success 200
// @Router /api/v1/settings/:setting_type [put]
func (gc APIV1Controller) UpdateGameSettings(c *gin.Context) {
	settingType := c.Param("type")
	gameID, _ := strconv.Atoi(c.Query("game_id"))

	gameDB, err := game.GetGameInfoById(gameID)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	if settingSchema, ok := store.Settings[settingType]; ok {
		var filter converter.Filter

		if slices.Contains([]string{"ComplaintWarning", "Antispam"}, settingType) {
			filter = converter.Filter{ProjectId: gameDB.ProjectID}
		} else {
			filter = converter.Filter{Appid: gameDB.Appid, ProjectId: gameDB.ProjectID, GameId: gameDB.Id}
		}

		originConfig := reflect.New(reflect.TypeOf(settingSchema)).Interface()
		converter.Query(originConfig.(field.BaseConfiger), filter)

		newConfig := reflect.New(reflect.TypeOf(settingSchema)).Interface()
		err := c.ShouldBindJSON(&newConfig)
		if err != nil {
			gc.APIError(c, err)
			return
		}
		converter.Save(newConfig.(field.BaseConfiger), filter)

		redis.Del(fmt.Sprintf("game_info_%s", gameDB.Appid))
		redis.Del(fmt.Sprintf("user_third_list_%s", gameDB.Appid))

		services.AddOperation(c, gameDB.ProjectID, "配置", gameDB.Name,
			originConfig.(field.BaseConfiger).GetLabel(),
			"update", "", originConfig, newConfig)
		gc.APISuccess(c, newConfig)
		return

	} else {
		gc.APIError(c, errors.New("无效的配置类型"))
		return
	}
}

// CheckGooglePaySettings
// @Summary	校验游戏配置参数
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param	person	body	schema.GameSummary	true	"校验游戏配置参数"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Game}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Success 200
// @Router /api/v1/settings/check/:setting_type [post]
func (gc APIV1Controller) CheckGooglePaySettings(c *gin.Context) {
	gameID, _ := strconv.Atoi(c.Query("game_id"))
	_, err := game.GetGameInfoById(gameID)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	settingType := c.Param("type")

	if settingSchema, ok := store.Settings[settingType]; ok {
		config := reflect.New(reflect.TypeOf(settingSchema)).Interface()
		err := c.ShouldBindJSON(&config)
		if err != nil {
			gc.APIError(c, err)
			return
		}

		configByte, _ := json.Marshal(config)
		configParams := string(configByte)

		paymentConfig := config.(field.PaymentConfiger)
		if paymentConfig.AllowChecked() {
			var err error
			switch settingType {
			case "PaymentGooglePay":
				err = pay.CheckSettingParams(c, configParams)
			default:
				gc.APIError(c, errors.New("无效的配置类型"))
				return
			}
			if err != nil {
				gc.APIError(c, errors.New("参数不可用，请检查参数是否有误！"))
				return
			}

		}
	} else {
		gc.APIError(c, errors.New("无效的配置类型"))
		return
	}

	gc.APISuccess(c, "")
	return
}
