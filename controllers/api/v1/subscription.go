package v1

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"zeus/models"

	"github.com/gin-gonic/gin"

	"zeus/common/constants"
	"zeus/schema"
	"zeus/services"
	"zeus/services/download"
	"zeus/services/game"
)

type SubscriptionNotifyParams struct {
	GameID  int    `json:"game_id"`
	OrderID string `json:"order_id"`
}

type SubscriptionQueryFields struct {
	GameID         int    `form:"game_id" binding:"required"`
	OrderID        string `form:"order_id"`
	PayID          string `form:"pay_id"`
	PayStatus      string `form:"pay_status"`
	NotifyStatus   string `form:"notify_status"`
	RoleID         string `form:"role_id"`
	ProductId      string `form:"product_id"`
	Account        string `form:"account"`
	OrderAttribute string `form:"order_attribute"`
	PageSize       int    `form:"pageSize"`
	PageNumber     int    `form:"pageNumber"`
	StartTime      string `form:"start_time"`
	EndTime        string `form:"end_time"`
}

// GetSubscriptionsInfo
// @Summary	获取订阅
// @Tags	API Subscription
// @Accept	json
// @Produce json
// @Param	person	body	SubscriptionQueryFields	true	"获取订阅信息"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/subscriptions [get]
func (ac APIV1Controller) GetSubscriptionsInfo(c *gin.Context) {
	queryForm := SubscriptionQueryFields{}
	err := c.ShouldBindQuery(&queryForm)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	pageSize := 10
	pageNum := 1
	if queryForm.PageSize != 0 {
		pageSize = queryForm.PageSize
	}
	if queryForm.PageNumber != 0 {
		pageNum = queryForm.PageNumber
	}

	filter := map[string]interface{}{
		"game_id": queryForm.GameID,
	}
	if queryForm.OrderID != "" {
		filter["order_id"] = queryForm.OrderID
	}
	if queryForm.PayStatus != "" {
		payStatusInt, _ := strconv.Atoi(queryForm.PayStatus)
		filter["pay_status"] = payStatusInt
	}
	if queryForm.PayID != "" {
		filter["pay_id"] = queryForm.PayID
	}
	if queryForm.RoleID != "" {
		filter["role_id"] = queryForm.RoleID
	}
	if queryForm.Account != "" {
		filter["account"] = queryForm.Account
	}
	if queryForm.NotifyStatus != "" {
		notifyStatus, _ := strconv.Atoi(queryForm.NotifyStatus)
		filter["notify_status"] = notifyStatus
	}
	if queryForm.ProductId != "" {
		filter["product_id"] = queryForm.ProductId
	}
	if queryForm.OrderAttribute != "" {
		orderAttribute, _ := strconv.Atoi(queryForm.OrderAttribute)
		filter["order_attribute"] = orderAttribute
	}

	subscriptionData, total, _ := services.GetSubscriptionByPage(c, filter, pageSize, pageNum, queryForm.StartTime, queryForm.EndTime)

	var subscriptionResponse schema.APIResponse
	subscriptionResponse.PageSize = pageSize
	subscriptionResponse.PageNum = pageNum
	subscriptionResponse.Total = total
	subscriptionResponse.Data = subscriptionData
	ac.APISuccess(c, subscriptionResponse)
}

// SubscriptionNotify
// @Summary	补单
// @Tags	API Subscription
// @Accept	json
// @Produce json
// @Param	person	body  SubscriptionNotifyParams	true	"补单"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/subscriptions/notify [post]
func (ac APIV1Controller) SubscriptionNotify(c *gin.Context) {
	var subscriptionNotifyParams SubscriptionNotifyParams
	err := c.ShouldBindJSON(&subscriptionNotifyParams)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	filter := map[string]interface{}{"order_id": subscriptionNotifyParams.OrderID}
	orderDB, err := services.QuerySubscription(c, filter)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	orderDBOld := map[string]int{
		"pay_status":    orderDB.PayStatus,
		"notify_status": orderDB.NotifyStatus,
		"expires_date":  orderDB.ExpiresDate,
	}

	db := ac.GetDB(c)

	renewSubscription := new(models.RenewSubscription)
	filter = map[string]interface{}{
		"game_id":  subscriptionNotifyParams.GameID,
		"order_id": subscriptionNotifyParams.OrderID,
	}
	renewSubscription, err = models.GetLastedRenewSubscriptionByFilter(db, filter)
	if err == nil && renewSubscription.OrderId != "" {
		if orderDB.ExpiresDate != renewSubscription.ExpiresDate {
			orderDB.ExpiresDate = renewSubscription.ExpiresDate
		}
	}
	funcName := "订阅补单"
	if orderDB.PayStatus != constants.PaymentSuccess {
		funcName = "订阅补发"
		orderDB.PayStatus = constants.PaymentSuccess
	}
	if err := models.UpdateSubscriptionColumns(db, orderDB); err != nil {
		ac.APIError(c, errors.New("失败"))
		return
	}
	orderDBNew := map[string]int{
		"pay_status":    orderDB.PayStatus,
		"notify_status": orderDB.NotifyStatus,
		"expires_date":  orderDB.ExpiresDate,
	}
	dbGame, _ := game.GetGameById(orderDB.GameId)
	services.AddOperation(c, dbGame.ProjectID, "subscription", orderDB.OrderId, funcName, "update", subscriptionNotifyParams, orderDBOld, orderDBNew)

	err = services.NotifySubscription(c, db, orderDB, false, renewSubscription)
	if err != nil {
		ac.APIError(c, errors.New("通知Game Server失败"))
		return
	}
	ac.APISuccess(c, "")
}

// SubscriptionsDownload
// @Summary	订阅订单下载
// @Tags	Order
// @Accept	json
// @Produce json
// @Success 200
// @Router /api/v1/subscriptions/download [post]
func (ac APIV1Controller) SubscriptionsDownload(c *gin.Context) {
	var orderQueryParams schema.SubscriptionQueryParams
	err := c.ShouldBindJSON(&orderQueryParams)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	count := services.GetDownloadSubscriptionCount(c, orderQueryParams)
	if count == 0 {
		ac.APIError(c, errors.New("未查询到数据！"))
		return
	}
	if count > constants.MaxDownloadCount {
		ac.APIError(c, errors.New("导出失败，单次导出数据上限为10万条，可添加限制条件或调整时间范围后重试！"))
		return
	}
	s := download.NewService(fmt.Sprintf("subscription-%d.xlsx", time.Now().Unix()), "subscription", orderQueryParams)
	filePath, err := s.Download(c)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	ac.File(c, filePath)
}

type RenewSubscriptionQuery struct {
	GameID  int    `form:"game_id" binding:"required"`
	OrderID string `form:"order_id" binding:"required"`
}

// GetRenewSubscriptions
// @Summary	获取续订信息
// @Tags	API Subscription
// @Accept	json
// @Produce json
// @Param	person	body	RenewSubscriptionQuery	true	"获取续订信息"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/subscriptions/renew [get]
func (ac APIV1Controller) GetRenewSubscriptions(c *gin.Context) {
	queryForm := RenewSubscriptionQuery{}
	err := c.ShouldBindQuery(&queryForm)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	db := ac.GetDB(c)
	filter := map[string]interface{}{
		"game_id":  queryForm.GameID,
		"order_id": queryForm.OrderID,
	}
	renewSubscriptions, err := models.GetRenewSubscriptionByFilter(db, filter)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	ac.APISuccess(c, renewSubscriptions)
}
