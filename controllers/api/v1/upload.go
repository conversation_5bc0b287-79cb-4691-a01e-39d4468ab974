package v1

import (
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"github.com/gin-gonic/gin"
	"os"
	"strings"
	"zeus/services"
)

type UploadFileForm struct {
	File     string `json:"file"`
	FileType string `json:"file_type"`
}

// WebPayUpload
// @Summary	上传文件到oss
// @Tags	API Upload
// @Accept	json
// @Produce json
// @Param   upload  body    UploadParams  true  "文件"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/upload [post]
func (ac APIV1Controller) WebPayUpload(c *gin.Context) {
	var form UploadFileForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	base64Image := form.File
	data := strings.Split(base64Image, ",")[1]
	decoded, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	hash := md5.Sum(decoded)
	fileName := fmt.Sprintf("%x.%s", hash, form.FileType)

	// 保存为临时文件
	tmpFile, err := os.CreateTemp("", fileName)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	tmpFilePath := tmpFile.Name()
	defer os.RemoveAll(tmpFilePath)

	_, err = tmpFile.Write(decoded)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	baseUrl, err := services.UploadFileToOSS(tmpFilePath, fileName)

	ac.APISuccess(c, baseUrl)
}
