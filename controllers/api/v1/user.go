package v1

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/account"
	"zeus/services/game"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type userInfoData struct {
	UserDB       models.User       `json:"user_db"`
	UserBindList map[string]string `json:"user_bind_list"`
}

type GetUserQueryParams struct {
	GameId            int    `form:"game_id"`
	PageSize          int    `form:"pageSize,default=10"`
	PageNum           int    `form:"pageNumber,default=1"`
	RoleId            string `form:"role_id"`
	RoleName          string `form:"role_name"`
	Account           string `form:"account"`
	RegisterDevice    string `form:"register_device"`
	UserId            int    `form:"user_id"`
	BindType          int    `form:"bind_type"`
	LoginStartTime    string `form:"login_start_time"`
	LoginEndTime      string `form:"login_end_time"`
	RegisterStartTime string `form:"register_start_time"`
	RegisterEndTime   string `form:"register_end_time"`
}

func handleBindType(c *gin.Context, bindType int, roles []models.Role) []int {
	var userIDs []int
	filter := map[string]interface{}{
		"type":   bindType,
		"status": constants.BoundStatus,
	}
	userBindData, err := services.GetBindThirdByArgs(c, filter)
	if err == nil && len(userBindData) != 0 {
		for _, userBind := range userBindData {
			for _, r := range roles {
				if r.UserID == userBind.UserID {
					userIDs = append(userIDs, userBind.UserID)
				}
			}
		}
	}
	return userIDs
}

// GetUser
// @Summary	获取用户信息
// @Tags	API-User
// @Accept	json
// @Produce json
// @Param   game_id    query  string  true  "游戏id"
// @Param   pageSize    query  string  false  "每一页展示的数量后端默认：10"
// @Param   pageNumber    query  string  false  "页"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/user/info [get]
func (gc APIV1Controller) GetUser(c *gin.Context) {
	var queryParams GetUserQueryParams
	if err := c.BindQuery(&queryParams); err != nil {
		gc.APIError(c, err)
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var options []models.QueryUserOption

	if queryParams.GameId != 0 {
		options = append(options, models.QueryUserWithGameId(queryParams.GameId))
	}

	if queryParams.RoleName != "" {
		roles, _ := services.GetRoleByRoleName(c, queryParams.RoleName)
		if len(roles) != 0 {
			var userIds []int
			for _, role := range roles {
				userIds = append(userIds, role.UserID)
			}
			options = append(options, models.QueryUserWithIds(userIds))

		} else {
			gc.APISuccess(c, schema.APIResponse{Total: 0, Data: []userInfoData{}})
			return
		}
	}

	if queryParams.RoleId != "" {
		roles, _ := services.GetRoleByRoleId(c, queryParams.RoleId)
		if len(roles) != 0 {
			var userIds []int
			for _, role := range roles {
				userIds = append(userIds, role.UserID)
			}
			options = append(options, models.QueryUserWithIds(userIds))
		} else {
			gc.APISuccess(c, schema.APIResponse{Total: 0, Data: []userInfoData{}})
			return
		}
	}

	if queryParams.BindType != 0 {
		userIds := models.GetUserIdsByBindType(db, queryParams.BindType, 1)
		fmt.Println("userIds: ", userIds)
		options = append(options, models.QueryUserWithIds(userIds))
	}

	if queryParams.UserId != 0 {
		options = append(options, models.QueryUserWithId(queryParams.UserId))
	}

	if queryParams.Account != "" {
		options = append(options, models.QueryUserWithAccount(queryParams.Account))
	}

	if queryParams.LoginStartTime != "" {
		options = append(options, models.QueryUserWithLoginStartTime(queryParams.LoginStartTime))
	}

	if queryParams.LoginEndTime != "" {
		options = append(options, models.QueryUserWithLoginEndTime(queryParams.LoginEndTime))
	}

	if queryParams.RegisterStartTime != "" {
		options = append(options, models.QueryUserWithRegisterStartTime(queryParams.RegisterStartTime))
	}

	if queryParams.RegisterEndTime != "" {
		options = append(options, models.QueryUserWithRegisterEndTime(queryParams.RegisterEndTime))
	}

	if queryParams.RegisterDevice != "" {
		options = append(options, models.QueryUserWithRegisterDevice(queryParams.RegisterDevice))
	}

	options = append(options, models.QueryUserWithPage(queryParams.PageSize, queryParams.PageNum))

	userData, total, err := models.QueryUsers(db, options...)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	userInfoDataList := make([]userInfoData, 0)
	for _, userDb := range userData {
		userBind := services.GetBindInfoByUserID(c, strconv.Itoa(userDb.ID))
		userInfoData := userInfoData{UserDB: userDb, UserBindList: userBind}
		userInfoDataList = append(userInfoDataList, userInfoData)
	}

	userResponse := schema.APIResponse{
		PageSize: queryParams.PageSize,
		PageNum:  queryParams.PageNum,
		Total:    total,
		Data:     userInfoDataList,
	}
	gc.APISuccess(c, userResponse)
}

// GetUserBind
// @Summary	获取用户信息
// @Tags	API-User
// @Accept	json
// @Produce json
// @Param   user_id    query  string  true  "用户id"
// @Success 200 {object} controllers.APISuccessResponse{result=[]models.UserBindThird}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/user/bind/:user_id [get]
func (gc APIV1Controller) GetUserBind(c *gin.Context) {
	userID, _ := strconv.Atoi(c.Query("user_id"))

	userBindThirdAll := services.QueryAllBindThirdWithAreaCode(c, int64(userID))
	gc.APISuccess(c, userBindThirdAll)
}

type UpdateUserBindForm struct {
	NewAccount      string `json:"new_account" binding:"required"`
	UnionId         string `json:"union_id" binding:"required"`
	Type            int    `json:"type" binding:"required"`
	UserId          int    `json:"user_id" binding:"required"`
	GameId          int    `json:"game_id" binding:"required"`
	AreaCode        string `json:"area_code"`
	CurrentAreaCode string `json:"current_area_code"`
}

// UpdateUserBind
// @Summary	更新用户绑定信息
// @Tags	API-User
// @Accept	json
// @Produce json
// @Param   user_id    query  string  true  "用户id"
// @Param	person	body	UpdateUserBindForm	true	"更新用户绑定信息"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/user/bind [put]
func (gc APIV1Controller) UpdateUserBind(c *gin.Context) {
	var form UpdateUserBindForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	dbGame, err := game.GetGameInfoById(form.GameId)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	project, _ := services.GetProjectById(dbGame.ProjectID)
	if project.ID == 0 {
		gc.APIError(c, fmt.Errorf("游戏项目: %s 不存在！", project.Alias))
		return
	}

	filter := map[string]interface{}{
		"user_id":  form.UserId,
		"union_id": form.UnionId,
	}
	_, err = services.QueryBindThird(c, filter)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	oldAccount := ""
	accountServer := account.NewServer(c)
	dbAccount, err := accountServer.FindByUnionId(form.UnionId)
	if err == nil {
		if form.Type == constants.Email {
			oldAccount = *dbAccount.Email
			dbAccount.Email = &form.NewAccount
		} else if form.Type == constants.Mobile {
			oldAccount = *dbAccount.Phone
			dbAccount.Phone = &form.NewAccount
			dbAccount.AreaCode = &form.AreaCode
		}
		err = accountServer.SaveAccount(dbAccount)
		if err != nil {
			gc.APIError(c, err)
			return
		}
		accountServer.UpdateThirdUserName(form.UnionId, form.Type, form.NewAccount)
	} else {
		gc.APIError(c, err)
		return
	}
	dbUser, _ := services.GetUserByUserID(c, form.UserId)
	if dbUser.ID == 0 {
		gc.APIError(c, errors.New("用户不存在"))
		return
	}
	userBindOld := map[string]string{
		"account":   oldAccount,
		"area_code": form.CurrentAreaCode,
	}
	userBindNew := map[string]string{
		"account":   form.NewAccount,
		"area_code": form.AreaCode,
	}

	services.AddOperation(c, project.ID, "user", dbUser.Account, "账号换绑", "update", form, userBindOld, userBindNew)

	gc.APISuccess(c, "")
}

type UpdateUserUnBindForm struct {
	UnionId string `json:"union_id" binding:"required"`
	Type    int    `json:"type" binding:"required"`
	UserId  int    `json:"user_id" binding:"required"`
	GameId  int    `json:"game_id" binding:"required"`
}

// UpdateUserUnbind
// @Summary	用户解绑
// @Tags	API-User
// @Accept	json
// @Produce json
// @Param   user_id    query  string  true  "用户id"
// @Param	person	body	UpdateUserBindForm	true	"用户解绑"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/user/unbind [put]
func (gc APIV1Controller) UpdateUserUnbind(c *gin.Context) {
	var form UpdateUserUnBindForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	dbGame, err := game.GetGameInfoById(form.GameId)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	project, _ := services.GetProjectById(dbGame.ProjectID)
	if project.ID == 0 {
		gc.APIError(c, fmt.Errorf("游戏项目: %s 不存在！", project.Alias))
		return
	}
	filter := map[string]interface{}{
		"user_id":  form.UserId,
		"union_id": form.UnionId,
		"type":     form.Type,
		"status":   constants.BoundStatus,
	}
	userBindThird, _ := services.QueryBindThird(c, filter)

	if userBindThird.ID == 0 {
		gc.APIError(c, fmt.Errorf("当前用户绑定关系不存在！"))
		return
	}

	dbUser, _ := services.GetUserByUserID(c, form.UserId)
	if dbUser.ID == 0 {
		gc.APIError(c, errors.New("用户不存在"))
		return
	}
	userBindThird.Status = constants.UnBoundStatus
	userBindThird.UpdateTime = time.Now()
	_ = services.SaveBindThird(c, userBindThird)
	userBindOld := map[string]interface{}{
		"user_id":  form.UserId,
		"type":     form.Type,
		"union_id": form.UnionId,
		"status":   constants.BoundStatus,
	}
	userBindNew := map[string]interface{}{
		"user_id":  form.UserId,
		"type":     form.Type,
		"union_id": form.UnionId,
		"status":   constants.UnBoundStatus,
	}

	services.AddOperation(c, project.ID, "user", dbUser.Account, "账号解绑", "update", form, userBindOld, userBindNew)

	gc.APISuccess(c, "")
}

// GetUserRoles
// @Summary	获取用户信息
// @Tags	API-User
// @Accept	json
// @Produce json
// @Param   user_id    query  string  true  "用户id"
// @Success 200 {object} controllers.APISuccessResponse{result=[]models.Role}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/user/roles [get]
func (gc APIV1Controller) GetUserRoles(c *gin.Context) {
	userID, _ := strconv.Atoi(c.Query("user_id"))

	filter := map[string]interface{}{"user_id": userID}
	userRoleAll, _ := services.QueryAllRole(c, filter)
	gc.APISuccess(c, userRoleAll)
}

// GetUserAccount
// @Summary	获取用户绑定账户信息
// @Tags	API-User
// @Accept	json
// @Produce json
// @Param   union_id    query  string  true  "union id"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Account}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/user/account [get]
func (gc APIV1Controller) GetUserAccount(c *gin.Context) {
	unionID := c.Query("union_id")

	accountServer := account.NewServer(c)
	dbAccount, err := accountServer.FindByUnionId(unionID)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	gc.APISuccess(c, dbAccount)
}

type QueryUserStatusForm struct {
	Accounts []string `json:"accounts" binding:"required"`
}

type UserInfo struct {
	UserId   int    `json:"id"`
	Account  string `json:"account"`
	RoleName string `json:"role_name"`
	RoleId   string `json:"role_id"`
	ServerId string `json:"server_id"`
	Status   int    `json:"status"`
}

// QueryUserStatus
// @Summary	获取用户状态
// @Tags	API-User
// @Accept	json
// @Produce json
// @Param	person	body	QueryUserStatusForm	true	"获取用户状态"
// @Success 200 {object} controllers.APISuccessResponse{result=models.Account}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/user/query_status [post]
func (gc APIV1Controller) QueryUserStatus(c *gin.Context) {
	var form QueryUserStatusForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		gc.APIError(c, err)
		return
	}

	accounts := form.Accounts
	if len(accounts) > 100 {
		gc.APIError(c, errors.New("单次处理上限为100条数据"))
		return
	}

	const userNotExistStatus = -1
	var users []UserInfo

	for _, a := range accounts {
		accountAndRole := strings.Split(a, ",")
		if len(accountAndRole) != 3 {
			gc.APIError(c, errors.New("数据格式有误. 请按照账号、角色ID、区服ID的顺序并且使用英文逗号分隔；例：'1203,,' 或 ',2202,1002'"))
			return
		}
		userAccount := accountAndRole[0]
		roleId := accountAndRole[1]
		serverId := accountAndRole[2]

		var userInfo UserInfo
		userInfo.RoleId = roleId
		userInfo.ServerId = serverId

		db := c.MustGet("db").(*gorm.DB)
		dbUser, dbRole, err := models.GetUserByAccountAndRole(db, userAccount, roleId, serverId)
		if err != nil || dbUser.ID == 0 {
			userInfo.Status = userNotExistStatus
			userInfo.UserId = 0
			userInfo.RoleName = dbRole.RoleName
		} else {
			userInfo.Status = dbUser.Status
			userInfo.UserId = dbUser.ID
			userInfo.Account = userAccount
		}
		users = append(users, userInfo)
	}

	gc.APISuccess(c, users)
}

type UpdateUserStatusForm struct {
	UserIds []int `json:"user_ids" binding:"required"`
	Status  *int  `json:"status" binding:"required"`
	GameId  int   `json:"game_id" binding:"required"`
}

// UpdateUserStatus
// @Summary	修改账号状态
// @Tags	API-User
// @Accept	json
// @Produce json
// @Param	person	body	UpdateUserStatusForm	true	"修改账号状态"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /api/v1/user/status [put]
func (gc APIV1Controller) UpdateUserStatus(c *gin.Context) {
	var form UpdateUserStatusForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		gc.APIError(c, err)
		return
	}
	db := c.MustGet("db").(*gorm.DB)

	dbGame, err := game.GetGameInfoById(form.GameId)
	if err != nil {
		log.Warn(c.Request.Context(), fmt.Sprintf("获取游戏信息失败，game_id: %d ", form.GameId), "error", err.Error())
		gc.APIError(c, err)
		return
	}

	if *form.Status == constants.UserStatusDeleting || *form.Status == constants.UserStatusDeleted {
		dbUsers, err := models.GetUserByIDs(db, form.UserIds)
		if err != nil {
			gc.APIError(c, err)
			return
		}
		for _, user := range dbUsers {
			acs := services.InitUnregisterServices()
			err = acs.CreateOrUpdateUnregister(c, form.GameId, user.Account, *form.Status)
			if err != nil {
				gc.APIError(c, errors.New("系统异常，删除账号操作失败。"))
				return
			}
			if *form.Status == constants.UserStatusDeleted {
				acs.UnregisterNotifyWithAccount(c, form.GameId, user.Account)
			}
		}
	} else {
		if len(form.UserIds) != 0 {
			err = models.UpdateUserStatusByUserIds(db, form.UserIds, *form.Status)
			if err != nil {
				gc.APIError(c, err)
				return
			}
		}
	}

	userOld := map[string]int{}
	services.AddOperation(c, dbGame.ProjectID, "game", dbGame.Name, "修改User状态", "update", form, userOld, form)
	gc.APISuccess(c, "")
}
