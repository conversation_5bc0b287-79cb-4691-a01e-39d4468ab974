package v1

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"zeus/models"
	"zeus/schema"
)

type WebPayOrderQueryFields struct {
	OrderId       string `form:"order_id"`
	RoleId        string `form:"role_id"`
	ServerId      string `form:"server_id"`
	GameProductId string `form:"game_product_id"`
	ActivityId    int    `form:"activity_id"`
	PayStatus     *int   `form:"pay_status"`
	Points        *int   `form:"points"`
	StartTime     string `form:"start_time"`
	EndTime       string `form:"end_time"`
	PageSize      int    `form:"pageSize,default=10"`
	PageNumber    int    `form:"pageNumber,default=1"`
}

type PointOrderQueryFields struct {
	OrderId    string `form:"order_id"`
	RoleId     string `form:"role_id"`
	ServerId   string `form:"server_id"`
	ProductId  string `form:"product_id"`
	Points     int    `form:"points"`
	StartTime  string `form:"start_time"`
	EndTime    string `form:"end_time"`
	PageSize   int    `form:"pageSize,default=10"`
	PageNumber int    `form:"pageNumber,default=1"`
}

// SearchWebPayOrders
// @Summary	获取网页支付订单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body	WebPayOrderQueryFields	true	"获取订单信息"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/orders/web_pay [get]
func (ac APIV1Controller) SearchWebPayOrders(c *gin.Context) {
	queryForm := WebPayOrderQueryFields{}
	err := c.ShouldBindQuery(&queryForm)
	if err != nil {
		ac.APIError(c, err)
		return
	}
	db := c.MustGet("db").(*gorm.DB)
	var options []models.QueryRecordOptions

	if queryForm.OrderId != "" {
		options = append(options, models.QueryRecordWithOrderId(queryForm.OrderId))
	}
	if queryForm.RoleId != "" {
		options = append(options, models.QueryRecordWithRoleId(queryForm.RoleId))
	}
	if queryForm.ServerId != "" {
		options = append(options, models.QueryRecordWithServerId(queryForm.ServerId))
	}
	if queryForm.GameProductId != "" {
		product, err := models.GetProductByGameProductId(db, queryForm.GameProductId)
		if err == nil {
			options = append(options, models.QueryRecordWithProductId(product.Id))
		}
	}
	if queryForm.ActivityId != 0 {
		options = append(options, models.QueryRecordWithActivityId(queryForm.ActivityId))
	}
	if queryForm.PayStatus != nil {
		options = append(options, models.QueryRecordWithPayStatus(*queryForm.PayStatus))
	}
	if queryForm.Points != nil {
		options = append(options, models.QueryRecordWithPoints(*queryForm.Points))
	}
	if queryForm.StartTime != "" {
		options = append(options, models.QueryPurchaseRecordWithStartTime(queryForm.StartTime))
	}
	if queryForm.EndTime != "" {
		options = append(options, models.QueryPurchaseRecordWithEndTime(queryForm.EndTime))
	}
	options = append(options, models.QueryRecordWithJoinProduct())
	options = append(options, models.QueryRecordWithPage(queryForm.PageSize, queryForm.PageNumber))
	options = append(options, models.QueryRecordWithCreateTimeSort())

	orderData, total, err := models.GetPurchaseRecordByPage(db, options...)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	var orderResponse schema.APIResponse
	orderResponse.PageSize = queryForm.PageSize
	orderResponse.PageNum = queryForm.PageNumber
	orderResponse.Total = total
	orderResponse.Data = orderData
	ac.APISuccess(c, orderResponse)
}

// GetPointOrders
// @Summary	获取网页支付积分兑换订单
// @Tags	API Order
// @Accept	json
// @Produce json
// @Param	person	body	PointOrderQueryFields	true	"获取订单信息"
// @Success 200 {object} controllers.APISuccessResponse{result=schema.APIResponse}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/point_orders [get]
func (ac APIV1Controller) GetPointOrders(c *gin.Context) {
	queryForm := PointOrderQueryFields{}
	if err := c.ShouldBindQuery(&queryForm); err != nil {
		ac.APIError(c, err)
		return
	}
	db := c.MustGet("db").(*gorm.DB)

	var options []models.QueryRecordOptions

	if queryForm.OrderId != "" {
		options = append(options, models.QueryRecordWithOrderId(queryForm.OrderId))
	}
	if queryForm.RoleId != "" {
		options = append(options, models.QueryRecordWithRoleId(queryForm.RoleId))
	}
	if queryForm.ServerId != "" {
		options = append(options, models.QueryRecordWithServerId(queryForm.ServerId))
	}
	if queryForm.ProductId != "" {
		options = append(options, models.QueryRecordWithProductId(queryForm.ProductId))
	}
	if queryForm.Points != 0 {
		options = append(options, models.QueryRecordWithPoints(queryForm.Points))
	}
	if queryForm.StartTime != "" {
		options = append(options, models.QueryRecordWithStartTime(queryForm.StartTime))
	}
	if queryForm.EndTime != "" {
		options = append(options, models.QueryRecordWithEndTime(queryForm.EndTime))
	}
	options = append(options, models.QueryRecordWithPage(queryForm.PageSize, queryForm.PageNumber))

	options = append(options, models.QueryRecordWithCreateTimeSort())

	orderData, total, err := models.GetPointsExchangeRecordsByPage(db, options...)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	var orderResponse schema.APIResponse
	orderResponse.PageSize = queryForm.PageSize
	orderResponse.PageNum = queryForm.PageNumber
	orderResponse.Total = total
	orderResponse.Data = orderData
	ac.APISuccess(c, orderResponse)
}
