package v1

import (
	"github.com/gin-gonic/gin"
	"github.com/mohae/deepcopy"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"strconv"
	"time"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
)

type WhiteListFrom struct {
	RoleId            string `json:"role_id" binding:"required"`
	ServerId          string `json:"server_id" binding:"required"`
	SandboxPayEnabled *bool  `json:"sandbox_pay_enabled" binding:"required"`
	PreviewEnabled    *bool  `json:"preview_enabled" binding:"required"`
}

// CreateWebPayWhiteList
// @Summary	创建用户白名单
// @Tags	API WhiteList
// @Accept	json
// @Produce json
// @Param   whitelist  body    WhiteListFrom  true  "创建用户白名单"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/whitelist [post]
func (ac APIV1Controller) CreateWebPayWhiteList(c *gin.Context) {
	var form WhiteListFrom
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	whiteListInfo := &models.WebPayWhiteList{
		RoleId:            form.RoleId,
		ServerId:          form.ServerId,
		SandboxPayEnabled: *form.SandboxPayEnabled,
		PreviewEnabled:    *form.PreviewEnabled,
	}
	err = models.CreateWebPayWhiteList(db, whiteListInfo)
	if err != nil {
		ac.APIError(c, errors.Errorf("白名单创建失败， %s", err))
		return
	}

	gameID := c.MustGet("gameID").(int)
	gameDB, _ := game.GetGameInfoById(gameID)
	if gameDB.ProjectID != 0 {
		services.AddOperation(c, gameDB.ProjectID, "web_pay_whitelist", whiteListInfo.RoleId, "添加网页支付白名单", "create", form, "", whiteListInfo)
	}
	ac.APISuccess(c, "")
	return
}

// UpdateWebPayWhiteList
// @Summary	修改用户白名单
// @Tags	API WhiteList
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "id"
// @Param   body    WhiteListFrom  true  "修改用户白名单"
// @Success 200 {object} controllers.APISuccessResponse{result=string}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/whitelist/{id} [put]
func (ac APIV1Controller) UpdateWebPayWhiteList(c *gin.Context) {
	var form WhiteListFrom
	err := c.ShouldBindJSON(&form)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	whiteListId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		ac.APIError(c, err)
		return
	}

	whiteListInfo, err := models.GetWebPayWhiteListById(db, whiteListId)
	if err != nil {
		ac.APIError(c, errors.Errorf("白名单保存失败， %s", err))
		return
	}

	oldWhiteList := deepcopy.Copy(whiteListInfo)

	whiteListInfo.RoleId = form.RoleId
	whiteListInfo.ServerId = form.ServerId
	whiteListInfo.SandboxPayEnabled = *form.SandboxPayEnabled
	whiteListInfo.PreviewEnabled = *form.PreviewEnabled
	whiteListInfo.UpdateTime = time.Now()
	err = models.SaveWebPayWhiteList(db, whiteListInfo)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	gameID := c.MustGet("gameID").(int)
	gameDB, _ := game.GetGameInfoById(gameID)
	if gameDB.ProjectID != 0 {
		services.AddOperation(c, gameDB.ProjectID, "web_pay_whitelist", whiteListInfo.RoleId, "修改网页支付白名单", "update", form, oldWhiteList, whiteListInfo)
	}

	ac.APISuccess(c, "")
	return
}

// GetWebPayWhiteList
// @Summary	获取用户白名单
// @Tags	API WhiteList
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "id"
// @Success 200 {object} controllers.APISuccessResponse{result=ActivityResp}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/whitelist [get]
func (ac APIV1Controller) GetWebPayWhiteList(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	var arg map[string]interface{}
	whiteList, total, err := models.GetWebPayWhiteListByArgs(db, arg)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	resp := schema.APIResponse{
		Total: total,
		Data:  whiteList,
	}

	ac.APISuccess(c, resp)
	return
}

// DeleteWebPayWhiteList
// @Summary	删除用户白名单
// @Tags	API Product
// @Accept	json
// @Produce json
// @Param   id    query  int  true  "用户白名单id"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /api/v1/web_pay/whitelist/{id} [delete]
func (ac APIV1Controller) DeleteWebPayWhiteList(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	whiteListId, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		ac.APIError(c, err)
		return
	}

	whiteList, err := models.GetWebPayWhiteListById(db, whiteListId)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	err = models.DeleteWebPayWhiteList(db, whiteList)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	if whiteList.ID != 0 {
		gameID := c.MustGet("gameID").(int)
		gameDB, _ := game.GetGameInfoById(gameID)
		if gameDB.ProjectID != 0 {
			services.AddOperation(c, gameDB.ProjectID, "web_pay_whitelist", whiteList.RoleId, "删除网页支付白名单", "delete", whiteListId, whiteList, "")
		}
	}

	ac.APISuccess(c, "删除成功")
}
