package applovin

import (
	"crypto/sha1"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"zeus/common/log"
	"zeus/pkg/redis"
	"zeus/services/settings/converter"
	"zeus/services/settings/other"

	"github.com/gin-gonic/gin"
)

type ApplovinRequestParams struct {
	CustomData    string `form:"custom_data"`
	EventId       string `form:"event_id"`
	EventToken    string `form:"event_token"`
	EventTokenAll string `form:"event_token_all"`
	Idfa          string `form:"idfa"`
	Platform      string `form:"platform"`
	Timestamp     string `form:"timestamp"`
	UserId        string `form:"user_id"`
}

type CustomData struct {
	Appid string `json:"appid"`
	Code  string `json:"code"`
}

func (o Controller) Notify(c *gin.Context) {
	var queryParams ApplovinRequestParams
	c.BindQuery(&queryParams)
	c.Set("request", queryParams)

	timestamp, _ := strconv.ParseInt(queryParams.Timestamp, 10, 64)

	// Note: 根据观察，Applovin通知内包含的时间戳，与回调时间差非常不稳定。保守设置有效期为一天
	if time.Unix(timestamp, 0).Before(time.Now().Add(-time.Hour * 24)) {
		o.Error(c, 70003, errors.New("invalid timestamp"))
		return
	}

	var customData CustomData
	json.Unmarshal([]byte(queryParams.CustomData), &customData)

	if customData.Appid == "" {
		o.Error(c, 1000, errors.New("invalid appid"))
		return
	}

	var applovinConfig other.ApplovinConfig
	converter.QueryByAppid(&applovinConfig, customData.Appid)

	// 根据Applovin 定义的宏名称排序后，将值传入签名算法中计算EventTokenAll
	if GenerateEventTokenAll(applovinConfig.EventKey, queryParams.CustomData,
		queryParams.EventId, queryParams.Idfa,
		queryParams.Platform, queryParams.Timestamp,
		queryParams.UserId) != queryParams.EventTokenAll {
		log.Error(c.Request.Context(), "Event token not match")
		o.Error(c, 1000, errors.New("bad event token"))
		return
	}

	redis.Set(customData.Code, customData.Appid, time.Hour*1)
	o.Success(c, "success")
}

func GenerateEventToken(eventId string, eventKey string) string {
	key := fmt.Sprintf("%s%s", eventId, eventKey)
	hasher := sha1.New()
	hasher.Write([]byte(key))
	token := string(hasher.Sum(nil))
	return fmt.Sprintf("%x", token)
}

func GenerateEventTokenAll(eventKey string, event ...string) string {
	data := strings.Join(event, "")
	key := fmt.Sprintf("%s%s", data, eventKey)
	hash := sha256.Sum256([]byte(key))
	hashHex := hex.EncodeToString(hash[:])
	return hashHex
}
