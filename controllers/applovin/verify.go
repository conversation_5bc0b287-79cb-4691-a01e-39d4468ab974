package applovin

import (
	"errors"
	"strconv"
	"time"

	"zeus/pkg/redis"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/utils"

	"github.com/avast/retry-go"
	"github.com/gin-gonic/gin"
)

type VerifyForm struct {
	Appid     string `json:"appid"`
	Code      string `json:"code"`
	Timestamp string `json:"timestamp"`
	Sign      string `json:"sign"`
}

func (o Controller) Verify(c *gin.Context) {
	var form VerifyForm
	c.BindJSON(&form)

	c.Set("request", form)

	var paymentConfig payment.Payment
	converter.QueryByAppid(&paymentConfig, form.Appid)

	sign := utils.MD5(form.Appid + form.Code + form.Timestamp + *paymentConfig.PayNotifyKey)

	if sign != form.Sign {
		o.Error(c, 70002, errors.New("invalid signature"))
		return
	}

	timestamp, _ := strconv.ParseInt(form.Timestamp, 10, 64)

	if time.Unix(timestamp, 0).Before(time.Now().Add(-time.Second * 300)) {
		o.Error(c, 70003, errors.New("invalid timestamp"))
		return
	}

	ctx := c.Request.Context()
	err := retry.Do(
		func() error {
			select {
			case <-ctx.Done():
				o.Cannel(c)
				return nil
			default:
				data := redis.Get(form.Code)
				if data == form.Appid {
					redis.Del(form.Code)
					o.Success(c, "成功")
					return nil
				}
				return errors.New("")
			}
		},
		retry.DelayType(func(n uint, err error, config *retry.Config) time.Duration {
			return retry.BackOffDelay(n, err, config)
		}),
		retry.Attempts(20),
		retry.Delay(10*time.Millisecond),
		retry.MaxDelay(1000*time.Millisecond),
	)
	if err != nil {
		o.Error(c, 70001, errors.New("无效Code"))
		return
	}
}
