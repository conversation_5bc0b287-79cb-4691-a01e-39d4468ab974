package controllers

import (
	"net/http"
	"path"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BaseController struct{}

type SuccessResponse struct {
	DefaultResponse
	Result interface{} `json:"result,omitempty" swaggertype:"object"` // 接口返回的业务数据
}

type DefaultResponse struct {
	ErrorNo int    `json:"error_no"` // code 为0表示正常 其它表示业务请求错误
	Message string `json:"message"`  // 错误提示信息
}

type NotifyResponse struct {
	ErrCode int    `json:"ErrCode"`
	ErrMsg  string `json:"ErrMsg"`
}

type APISuccessResponse struct {
	APIDefaultResponse
	Result interface{} `json:"result,omitempty" swaggertype:"object"` // 接口返回的业务数据
}

type APIDefaultResponse struct {
	Code    int    `json:"code"`    // code 为0表示正常 其它表示业务请求错误
	Message string `json:"message"` // 错误提示信息
}

func (Base BaseController) Success(c *gin.Context, obj interface{}) {
	var res SuccessResponse
	res.ErrorNo = Success
	switch obj.(type) {
	case string:
		res.Message = obj.(string)
	default:
		res.Message = "成功"
		res.Result = obj
	}

	c.Set("error_no", strconv.Itoa(res.ErrorNo))
	c.JSON(http.StatusOK, res)
}

func (Base BaseController) Error(c *gin.Context, code int, err error) {
	var res DefaultResponse
	res.ErrorNo = code
	res.Message = err.Error()

	c.Set("error_no", strconv.Itoa(res.ErrorNo))
	c.JSON(http.StatusOK, res)
}

func (Base BaseController) Cannel(c *gin.Context) {
	var res DefaultResponse
	res.ErrorNo = -1
	res.Message = "Client Closed Request"
	c.Set("error_no", strconv.Itoa(res.ErrorNo))
	c.JSON(499, res)
}

func (Base BaseController) APISuccess(c *gin.Context, obj interface{}) {
	var res APISuccessResponse
	res.Code = Success
	res.Message = "成功"
	res.Result = obj

	c.Set("error_no", strconv.Itoa(res.Code))
	c.JSON(http.StatusOK, res)
}

func (Base *BaseController) File(c *gin.Context, fPath string) {
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", "attachment; filename="+path.Base(fPath))
	c.Header("Access-Control-Expose-Headers", "Content-Disposition")
	c.Header("Content-Transfer-Encoding", "binary")
	c.File(fPath)
}

func (Base BaseController) APIError(c *gin.Context, err error) {
	var res APIDefaultResponse
	res.Code = Fail
	res.Message = err.Error()

	c.Set("error_no", strconv.Itoa(res.Code))
	c.JSON(http.StatusBadRequest, res)
}

func (Base BaseController) JWTError(c *gin.Context, err error) {
	var res APIDefaultResponse
	res.Code = ErrorJWT
	res.Message = err.Error()

	c.Set("error_no", strconv.Itoa(res.Code))
	c.JSON(http.StatusBadRequest, res)
}

func (Base BaseController) CallbackNotifySuccess(c *gin.Context) {
	var res NotifyResponse
	res.ErrCode = Success
	res.ErrMsg = "Success"

	c.JSON(http.StatusOK, res)
}

func (Base BaseController) CallbackNotifyError(c *gin.Context, errorCode int, err error) {
	var res NotifyResponse
	res.ErrCode = errorCode
	res.ErrMsg = err.Error()

	c.JSON(http.StatusBadRequest, res)
}
