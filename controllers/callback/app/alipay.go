package callback

import (
	"zeus/services/pay/wechat/minigame/callback"

	"github.com/gin-gonic/gin"
)

// CallbackMiniGameWeChatNotify
// @Summary	支付宝回调支付结果
// @Tags	SDK AliPay
// @Accept	json
// @Produce json
// @Param	person	body  true  "支付宝回调支付结果"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /callback/app/alipay/:appid [post]
func (o Controller) CallbackAlipayNotify(c *gin.Context) {
	appid := c.Param("appid")

	alipayCallback := callback.AlipayCallback{
		Appid: appid,
	}

	errorCode, err := callback.PaymentCallbackHandler(c, &alipayCallback)
	if err != nil {
		o.CallbackNotifyError(c, errorCode, err)
		return
	}

	o.Success(c, "success")
}
