package callback

import (
	"zeus/services/pay/wechat/minigame/callback"

	"github.com/gin-gonic/gin"
)

// CallbackMiniGameWeChatNotify
// @Summary	验证微信支付结果
// @Tags	SDK WeChatPay
// @Accept	json
// @Produce json
// @Param	person	body  true  "验证微信支付结果"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /callback/app/wechat/:appid [post]
func (o Controller) CallbackWeChatNotify(c *gin.Context) {
	appid := c.Param("appid")
	sdkCallback := callback.SDKPayCallback{
		Appid: appid,
	}
	errorCode, err := callback.PaymentCallbackHandler(c, &sdkCallback)
	if err != nil {
		o.CallbackNotifyError(c, errorCode, err)
		return
	}

	o.Success(c, "success")
}
