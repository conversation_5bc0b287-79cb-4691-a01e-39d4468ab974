package callback

import (
	"zeus/controllers"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	controllers.BaseController
}

type VerifyForm struct {
	OrderId string `json:"out_trade_no" form:"out_trade_no"`
}

var c = Controller{}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.POST("/minigame/wechat/notify/:appid", c.CallbackMiniGameWeChatNotify)
	routerGroup.GET("/minigame/wechat/notify/virtual-pay/:appid", c.Callback<PERSON>iniGameWeChatVirtualPayVerify)
	routerGroup.POST("/minigame/wechat/notify/virtual-pay/:appid", c.Callback<PERSON>iniGameWeChatVirtualPayNotify)
	routerGroup.POST("/minigame/douyin/notify/virtual-pay/:appid", c.CallbackMiniGamedouyinVirtualPayNotify)
	routerGroup.GET("/minigame/douyin/notify/virtual-pay/:appid", c.<PERSON>back<PERSON>iniGamedouyinVirtualPayVerify)
}
