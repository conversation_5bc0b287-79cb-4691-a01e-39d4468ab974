package callback

import (
	"encoding/json"
	"errors"
	"net/http"

	"zeus/common/constants"
	"zeus/pkg/douyin"
	"zeus/services"
	"zeus/services/pay/wechat/minigame/callback"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin"
)

type DouyinUserBalance struct {
	AppID  string `json:"app_id"`
	UserId string `json:"user_id"`
}

// CallbackMiniGamedouyinVirtualPayNotify
// @Summary	微信虚拟支付通知回调
// @Tags	SDK douyinPay
// @Accept	json
// @Produce json
// @Param	person	body  schema.VirtualPaymentNotify true  "微信虚拟支付通知回调"
// @Success 200 {object} controllers.NotifyResponse
// @Failure 400 {object} controllers.NotifyResponse
// @Router /callback/minigame/douyin/notify/virtual-pay/:appid [post]
func (o Controller) CallbackMiniGamedouyinVirtualPayNotify(c *gin.Context) {
	appid := c.Param("appid")
	douyinPayCallback := callback.MiniGameDouyinCallback{
		Appid: appid,
	}
	errorCode, err := callback.PaymentCallbackHandler(c, &douyinPayCallback)
	if err != nil {
		o.CallbackNotifyError(c, errorCode, err)
		return
	}

	o.CallbackNotifySuccess(c)
}

// CallbackMiniGamedouyinVirtualPayVerify
// @Summary	抖音虚拟支付通知回调验证
// @Tags	SDK douyinPay
// @Accept	json
// @Produce json
// @Param	person	body  true  "抖音虚拟支付通知回调验证"
// @Success 200 {string}
// @Router /callback/minigame/douyin/notify/virtual-pay/:appid [get]
func (o Controller) CallbackMiniGamedouyinVirtualPayVerify(c *gin.Context) {
	appid := c.Param("appid")
	_, err := services.GetGameDBByAppID(appid)
	if err != nil {
		c.String(http.StatusBadRequest, "")
		return
	}

	echoStr := c.Query("echostr")
	if echoStr != "" {
		c.String(http.StatusOK, echoStr)
		return
	}

	c.String(http.StatusOK, "")
	return
}

// OrderStatisticsDownload
// @Summary	订单统计下载
// @Tags	Order
// @Accept	json
// @Produce json
// @Success 200
// @Router /minigame/order/douyin/get_balance [post]
func (ac Controller) GetBalance(c *gin.Context) {
	var douyinUserBalance DouyinUserBalance
	err := c.ShouldBindJSON(&douyinUserBalance)
	if err != nil {
		ac.APIError(c, err)
		return
	}

	var config payment.MiniGameDouyinPay
	converter.QueryByAppid(&config, douyinUserBalance.AppID)

	if *config.DouyinAppId == "" || *config.DouyinSecret == "" || *config.DouyinSignKey == "" || *config.OpenStatus != constants.IsRechargeOpen {
		ac.APIError(c, errors.New("抖音小游戏支付参数配置不完整或支付已关闭"))
		return
	}
	bindThird, _ := services.QueryBindThird(c, map[string]interface{}{"user_id": douyinUserBalance.UserId, "type": constants.Douyin})
	if bindThird == nil {
		ac.APIError(c, errors.New("user has not bind to douyin"))
		return
	}
	detail := make(map[string]interface{})
	_ = json.Unmarshal([]byte(bindThird.Detail), &detail)

	client := douyin.Client{Appid: *config.DouyinAppId, Secret: *config.DouyinSecret, SignKey: *config.DouyinSignKey}
	client.GetBalance(c.Request.Context(), detail["openid"].(string), "1")

}
