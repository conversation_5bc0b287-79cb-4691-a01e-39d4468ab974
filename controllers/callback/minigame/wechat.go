package callback

import (
	"net/http"

	"zeus/services"
	"zeus/services/pay/wechat/minigame/callback"

	"github.com/gin-gonic/gin"
)

// CallbackMiniGameWeChatNotify
// @Summary	验证微信支付结果
// @Tags	SDK WeChatPay
// @Accept	json
// @Produce json
// @Param	person	body  true  "验证微信支付结果"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /callback/minigame/wechat/notify/:appid [post]
func (o Controller) CallbackMiniGameWeChatNotify(c *gin.Context) {
	appid := c.Param("appid")
	miniGameCallback := callback.MiniGamePayCallback{
		Appid: appid,
	}
	errorCode, err := callback.PaymentCallbackHandler(c, &miniGameCallback)
	if err != nil {
		o.CallbackNotifyError(c, errorCode, err)
		return
	}

	o.Success(c, "success")
}

// CallbackMiniGameWeChatVirtualPayNotify
// @Summary	微信虚拟支付通知回调
// @Tags	SDK WeChatPay
// @Accept	json
// @Produce json
// @Param	person	body  schema.VirtualPaymentNotify true  "微信虚拟支付通知回调"
// @Success 200 {object} controllers.NotifyResponse
// @Failure 400 {object} controllers.NotifyResponse
// @Router /callback/minigame/wechat/notify/virtual-pay/:appid [post]
func (o Controller) CallbackMiniGameWeChatVirtualPayNotify(c *gin.Context) {
	appid := c.Param("appid")
	miniGameVirtualPayCallback := callback.MiniGameVirtualPayCallback{
		Appid: appid,
	}
	errorCode, err := callback.PaymentCallbackHandler(c, &miniGameVirtualPayCallback)
	if err != nil {
		o.CallbackNotifyError(c, errorCode, err)
		return
	}

	o.CallbackNotifySuccess(c)
}

// CallbackMiniGameWeChatVirtualPayVerify
// @Summary	微信虚拟支付通知回调验证
// @Tags	SDK WeChatPay
// @Accept	json
// @Produce json
// @Param	person	body  true  "微信虚拟支付通知回调验证"
// @Success 200 {string}
// @Router /callback/minigame/wechat/notify/virtual-pay/:appid [get]
func (o Controller) CallbackMiniGameWeChatVirtualPayVerify(c *gin.Context) {
	appid := c.Param("appid")
	_, err := services.GetGameDBByAppID(appid)
	if err != nil {
		c.String(http.StatusBadRequest, "")
		return
	}

	echoStr := c.Query("echostr")
	if echoStr != "" {
		c.String(http.StatusOK, echoStr)
		return
	}

	c.String(http.StatusOK, "")
	return
}
