package minigame

import (
	"zeus/controllers"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	controllers.BaseController
}

var MiniGame = Controller{}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.POST("/user/login", MiniGame.Login)
	routerGroup.POST("/user/third-login", MiniGame.ThirdLogin)
	routerGroup.POST("/user/query", MiniGame.UserQueryByOpenId)
	routerGroup.POST("/order/wechat-exchange", MiniGame.WechatMiniGamePay)
	routerGroup.POST("/order/unpaid", MiniGame.WeChatMiniGameUnpaidOrder)
	routerGroup.POST("/order/info", MiniGame.GetOrderInfo)
	routerGroup.POST("/order/douyin-exchange", MiniGame.DouyinMiniGamePay)
	routerGroup.POST("/order/douyin-verify", MiniGame.DouyinOrderVerify)
}
