package minigame

import (
	"fmt"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/service"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/pay/douyin"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

// DouyinMiniGamePay
// @Summary	创建抖音小游戏订单
// @Tags	SDK MiniGameWeChatPay
// @Accept	json
// @Produce json
// @Param	person 	body	schema.WechatMiniGameExchange	true	"支付信息"
// @Success 200 {object} controllers.SuccessResponse{result=schema.PrepayOrderResult}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /minigame/order/douyin-exchange [post]
func (o Controller) DouyinMiniGamePay(c *gin.Context) {
	var order schema.DouyinMiniGameExchange
	err := c.ShouldBind<PERSON>(&order)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, err := game.GetGameByAppid(order.AppID)
	if err != nil {
		o.Error(c, controllers.ErrorParamInvalid, err)
		return
	}

	// 校验支付配置
	var baseConfig payment.Payment
	converter.QueryByAppid(&baseConfig, order.AppID)

	var config payment.MiniGameDouyinPay
	converter.QueryByAppid(&config, order.AppID)

	if *baseConfig.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		o.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}

	// 查询用户
	dbUser, _ := services.GetUserByUserID(c, order.UserID)
	if dbUser.ID == 0 {
		o.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	switch order.PayType {
	case constants.MiniGameDouyinVirtualPay:
		VirtualPayResult, err := douyin.CreateDouyinOrder(c, *dbUser, *dbGame, order)
		if err != nil {
			o.Error(c, controllers.ErrorOrderCreateFail, err)
			return
		}
		o.Success(c, VirtualPayResult)
	default:
		o.Error(c, controllers.ErrorOrderCreateFail, errors.New("不支持的支付类型"))
		return
	}
}

// WeChatOrderVerify
// @Summary	验证微信支付结果
// @Tags	SDK WeChatPay
// @Accept	json
// @Produce json
// @Param	person	body  schema.WeChatOrderVerify true  "验证微信支付结果"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /minigame/order/douyin-verify [post]
func (o Controller) DouyinOrderVerify(c *gin.Context) {
	orderForm := schema.OrderVerify{}
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbOrder, err := services.CheckOrder(c, orderForm.OrderID)
	if err != nil {
		o.Error(c, controllers.ErrorOrderNotExists, err)
		return
	}

	if dbOrder.PayStatus == constants.PaymentSuccess {
		o.Success(c, constants.PaySuccessMessage)
		return
	}

	lockKey := fmt.Sprintf("transcation_%s_%s", orderForm.AppID, dbOrder.OrderId)
	err = redis.Lock(lockKey, time.Second*constants.LockTimeout)
	if err != nil {
		o.Error(c, controllers.ErrorRequestHasBeenProcessed, errors.New("订单正在被处理"))
		return
	}
	defer redis.UnLock(lockKey)

	douyinOrder, _ := pay.GetOrder(constants.PayWayMiniGameDouyin, orderForm.AppID)
	OrderPayResult, err := douyinOrder.VerifyPayOrder(c, &dbOrder)
	if err != nil {
		o.Error(c, controllers.ErrorOrderSign, err)
		return
	}
	err = services.UpdatePayOrder(c, OrderPayResult, &dbOrder, constants.PayWayWeChat)
	if err != nil {
		o.Error(c, controllers.ErrorDBError, err)
		return
	}

	if OrderPayResult.Status == constants.PaymentUnpaid {
		o.Error(c, controllers.ErrorOrderSign, errors.New(constants.PayUnpaidMessage))
		return
	}
	db := service.GetDB(c)
	_ = services.Notify(c.Request.Context(), db, &dbOrder, services.NotifyOptions{})

	o.Success(c, constants.PaySuccessMessage)
}
