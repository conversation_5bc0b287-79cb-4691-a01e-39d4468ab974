package minigame

import (
	"encoding/json"
	"errors"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/schema/request"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/third_account"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
)

// Login
// @Summary 用户登录
// @Tags minigame
// @Accept json
// @Produce json
// @Param Login body request.MiniGameLoginForm true "登录信息"
// @Success 200
// @Router /minigame/user/login [post]
func (m Controller) Login(c *gin.Context) {
	form := request.MiniGameLoginForm{}
	err := c.ShouldBindJSON(&form)
	if err != nil {
		m.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	service := new(services.DeviceLoginServices)
	_ = service.Map(form)
	dbUser, err := service.Login(c)
	if dbUser.Status == constants.UserStatusBlocked {
		m.Error(c, controllers.ErrorAccountBlocked, err)
		return
	}
	if err != nil {
		m.Error(c, controllers.ErrorLoginFail, err)
		return
	}

	bindThird, err := services.QueryBindThird(c, map[string]interface{}{"user_id": dbUser.ID})
	if err != nil {
		m.Error(c, controllers.ErrorLoginFail, err)
		return
	}

	detail := make(map[string]interface{})
	_ = json.Unmarshal([]byte(bindThird.Detail), &detail)

	resp := request.MiniGameLoginResponse{
		Id:        dbUser.ID,
		Account:   dbUser.Account,
		Token:     dbUser.Token,
		LoginTime: dbUser.LoginTime.Format("2006-01-02 15:04:05"),
		Detail:    detail,
	}
	m.Success(c, resp)
}

// ThirdLogin
// @Summary 第三方登录
// @Tags minigame
// @Accept json
// @Produce json
// @Param Login body request.MiniGameThirdLoginForm true "第三方登录信息"
// @Success 200
// @Router /minigame/user/third-login [post]
func (m Controller) ThirdLogin(c *gin.Context) {
	form := request.MiniGameThirdLoginForm{}
	err := c.ShouldBindJSON(&form)
	if err != nil {
		m.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	detail, code, err := third_account.GetMiniGameThirdInfo(c, form.APPID, form)
	if err != nil {
		m.Error(c, code, err)
		return
	}

	detailInfo, _ := json.Marshal(detail)

	unionId := detail["unionid"].(string)
	// TODO：如果小程序为在微信开放平台绑定，则unionId为空，使用openid临时替代。
	if unionId == "" {
		unionId = detail["openid"].(string)
	}
	openId := detail["openid"].(string)

	gameDB, _ := game.GetGameByAppid(form.APPID)
	users, _ := services.GetUserWithThirdBindAccount(c, unionId, form.Type, gameDB.Id)
	var user *models.User
	if len(users) != 0 {
		user = &users[0]
		user.LoginTime = time.Now()
		user.Token = services.GenerateToken()
		err = services.SaveUser(c, user)
		if err != nil {
			m.Error(c, controllers.ErrorDBError, errors.New("用户更新失败"))
			return
		}

		db := c.MustGet("db").(*gorm.DB)
		err = models.UpdateThirdDetail(db, unionId, form.Type, constants.BoundStatus, string(detailInfo))
		if err != nil {
			m.Error(c, controllers.ErrorDBError, errors.New("更新绑定信息失败"))
			return
		}
		services.RecordLoginDevice(c, user, form.Device)
	} else {
		us := new(services.UserServices)
		us.Map(form)
		user, err = us.CreateUser(c)
		if err != nil {
			m.Error(c, controllers.ErrorDBError, errors.New("创建新用户失败"))
			return
		}

		userBindThird := &models.UserBindThird{
			UnionID:    unionId,
			OpenID:     openId,
			UserID:     user.ID,
			Type:       form.Type,
			Status:     constants.BoundStatus,
			Detail:     string(detailInfo),
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		}
		err = services.SaveBindThird(c, userBindThird)
		if err != nil {
			m.Error(c, controllers.ErrorDBError, errors.New("创建绑定信息失败"))
			return
		}
	}

	resp := request.MiniGameLoginResponse{
		Id:        user.ID,
		Account:   user.Account,
		Token:     user.Token,
		LoginTime: user.LoginTime.Format("2006-01-02 15:04:05"),
		Detail:    detail,
	}
	m.Success(c, resp)
}

// ThirdLogin
// @Summary 第三方登录
// @Tags minigame
// @Accept json
// @Produce json
// @Param Login body request.MiniGameThirdLoginForm true "第三方登录信息"
// @Success 200
// @Router /minigame/user/query [post]
func (m Controller) UserQueryByOpenId(c *gin.Context) {
	form := request.MiniGameUserQueryRequestForm{}
	err := c.ShouldBindJSON(&form)
	if err != nil {
		m.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	gameDB, _ := game.GetGameByAppid(form.APPID)

	users, _ := services.GetUserWithThirdBindAccountByOpenId(c, form.OpenId, form.Type, gameDB.Id)
	var user *models.User
	if len(users) > 0 {
		user = &users[0]
	} else {
		m.Error(c, controllers.ErrorThirdLogin, errors.New("Can't find any user by open_id: "+form.OpenId))
		return
	}

	account := request.MiniGameUserQueryResponse{
		Account: user.Account,
	}
	m.Success(c, account)
}
