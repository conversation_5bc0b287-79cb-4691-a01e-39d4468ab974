package minigame

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/validator"
	"zeus/schema/request"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/third_account"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestLoginInterface(t *testing.T) {
	r := gin.Default()
	group := r.Group("/minigame")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	Convey("TestMiniGameLogin", t, func() {
		Convey("success", func() {
			server := services.DeviceLoginServices{}
			mock1 := ApplyMethod(reflect.TypeOf(&server), "Map", func(_ *services.DeviceLoginServices, _ interface{}) error {
				return nil
			})
			defer mock1.Reset()

			mock2 := ApplyMethod(reflect.TypeOf(&server), "Login", func(_ *services.DeviceLoginServices, _ *gin.Context) (*models.User, error) {
				return &models.User{
					ID:      111,
					Account: "test_account",
					Token:   "test_token",
				}, nil
			})
			defer mock2.Reset()

			mock3 := ApplyFunc(services.QueryBindThird, func(_ *gin.Context, _ map[string]interface{}) (*models.UserBindThird, error) {
				return &models.UserBindThird{
					Detail: `{"test": "test"}`,
				}, nil
			})
			defer mock3.Reset()

			args := request.MiniGameLoginForm{
				APPID:   "tes_appid",
				Account: "test_account",
				Device:  "test_device",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/minigame/user/login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("登陆失败", func() {
			server := services.DeviceLoginServices{}
			mock1 := ApplyMethod(reflect.TypeOf(&server), "Map", func(_ *services.DeviceLoginServices, _ interface{}) error {
				return nil
			})
			defer mock1.Reset()

			mock2 := ApplyMethod(reflect.TypeOf(&server), "Login", func(_ *services.DeviceLoginServices, _ *gin.Context) (*models.User, error) {
				return &models.User{}, errors.New("登陆失败")
			})
			defer mock2.Reset()

			args := request.MiniGameLoginForm{
				APPID:   "tes_appid",
				Account: "test_account",
				Device:  "test_device",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/minigame/user/login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.ErrorLoginFail)
			So(output, ShouldEqual, outputExpect)
		})
	})

	Convey("TestMiniGameThirdLogin", t, func() {
		mock1 := ApplyFunc(third_account.GetMiniGameThirdInfo, func(c *gin.Context, appid string, form request.MiniGameThirdLoginForm) (map[string]interface{}, int, error) {
			return map[string]interface{}{
				"unionid": "test_unionid",
				"openid":  "test_openid",
			}, 0, nil
		})
		defer mock1.Reset()
		mock2 := ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
			return &models.Game{
				Id: 1,
			}, nil
		})
		defer mock2.Reset()

		Convey("三方账号绑定过zeus账号", func() {
			mock3 := ApplyFunc(services.GetUserWithThirdBindAccount, func(c *gin.Context, _ string, _, _ int) (models.Users, error) {
				return models.Users{
					{
						ID:      111,
						Account: "test_account",
						Token:   "test_token",
					},
				}, nil
			})
			defer mock3.Reset()

			mock4 := ApplyFunc(services.SaveUser, func(c *gin.Context, user *models.User) error {
				return nil
			})
			defer mock4.Reset()

			c := &gin.Context{}
			mock5 := ApplyMethod(reflect.TypeOf(c), "MustGet", func(_ *gin.Context, _ string) any {
				return &gorm.DB{}
			})
			defer mock5.Reset()

			mock6 := ApplyFunc(models.UpdateThirdDetail, func(db *gorm.DB, unionID string, _type int, status int, detail string) error {
				return nil
			})
			defer mock6.Reset()

			mock7 := ApplyFunc(services.RecordLoginDevice, func(_ *gin.Context, _ *models.User, _ string) {
			})
			defer mock7.Reset()

			args := request.MiniGameThirdLoginForm{
				APPID:      "tes_appid",
				Device:     "test_device",
				Type:       1,
				Code:       "test_code",
				Platform:   "test_platform",
				SdkVersion: "test_sdk_version",
				MobileInfo: "test_mobile_info",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/minigame/user/third-login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("三方账号没有绑zeus账号", func() {
			mock3 := ApplyFunc(services.GetUserWithThirdBindAccount, func(c *gin.Context, _ string, _, _ int) (models.Users, error) {
				return models.Users{}, nil
			})
			defer mock3.Reset()

			server := services.UserServices{}

			mock5 := ApplyMethod(reflect.TypeOf(&server), "CreateUser", func(_ *services.UserServices, _ *gin.Context) (*models.User, error) {
				return &models.User{
					ID:      111,
					Account: "test_account",
					Token:   "test_token",
				}, nil
			})
			defer mock5.Reset()

			mock6 := ApplyFunc(services.SaveBindThird, func(_ *gin.Context, _ *models.UserBindThird) error {
				return nil
			})
			defer mock6.Reset()

			args := request.MiniGameThirdLoginForm{
				APPID:      "tes_appid",
				Device:     "test_device",
				Type:       1,
				Code:       "test_code",
				Platform:   "test_platform",
				SdkVersion: "test_sdk_version",
				MobileInfo: "test_mobile_info",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/minigame/user/third-login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("用户更新失败", func() {
			mock3 := ApplyFunc(services.GetUserWithThirdBindAccount, func(c *gin.Context, _ string, _, _ int) (models.Users, error) {
				return models.Users{
					{
						ID:      111,
						Account: "test_account",
						Token:   "test_token",
					},
				}, nil
			})
			defer mock3.Reset()

			mock4 := ApplyFunc(services.SaveUser, func(c *gin.Context, user *models.User) error {
				return errors.New("用户更新失败")
			})
			defer mock4.Reset()

			c := &gin.Context{}
			mock5 := ApplyMethod(reflect.TypeOf(c), "MustGet", func(_ *gin.Context, _ string) any {
				return &gorm.DB{}
			})
			defer mock5.Reset()

			mock6 := ApplyFunc(models.UpdateThirdDetail, func(db *gorm.DB, unionID string, _type int, status int, detail string) error {
				return nil
			})
			defer mock6.Reset()

			args := request.MiniGameThirdLoginForm{
				APPID:      "tes_appid",
				Device:     "test_device",
				Type:       1,
				Code:       "test_code",
				Platform:   "test_platform",
				SdkVersion: "test_sdk_version",
				MobileInfo: "test_mobile_info",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/minigame/user/third-login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.ErrorDBError)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("更新绑定信息失败", func() {
			mock3 := ApplyFunc(services.GetUserWithThirdBindAccount, func(c *gin.Context, _ string, _, _ int) (models.Users, error) {
				return models.Users{
					{
						ID:      111,
						Account: "test_account",
						Token:   "test_token",
					},
				}, nil
			})
			defer mock3.Reset()

			mock4 := ApplyFunc(services.SaveUser, func(c *gin.Context, user *models.User) error {
				return nil
			})
			defer mock4.Reset()

			c := &gin.Context{}
			mock5 := ApplyMethod(reflect.TypeOf(c), "MustGet", func(_ *gin.Context, _ string) any {
				return &gorm.DB{}
			})
			defer mock5.Reset()

			mock6 := ApplyFunc(models.UpdateThirdDetail, func(db *gorm.DB, unionID string, _type int, status int, detail string) error {
				return errors.New("更新绑定信息失败")
			})
			defer mock6.Reset()

			args := request.MiniGameThirdLoginForm{
				APPID:      "tes_appid",
				Device:     "test_device",
				Type:       1,
				Code:       "test_code",
				Platform:   "test_platform",
				SdkVersion: "test_sdk_version",
				MobileInfo: "test_mobile_info",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/minigame/user/third-login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.ErrorDBError)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("创建新用户失败", func() {
			mock3 := ApplyFunc(services.GetUserWithThirdBindAccount, func(c *gin.Context, _ string, _, _ int) (models.Users, error) {
				return models.Users{}, nil
			})
			defer mock3.Reset()

			server := services.UserServices{}

			mock5 := ApplyMethod(reflect.TypeOf(&server), "CreateUser", func(_ *services.UserServices, _ *gin.Context) (*models.User, error) {
				return &models.User{}, errors.New("创建新用户失败")
			})
			defer mock5.Reset()

			mock6 := ApplyFunc(services.SaveBindThird, func(_ *gin.Context, _ *models.UserBindThird) error {
				return nil
			})
			defer mock6.Reset()

			args := request.MiniGameThirdLoginForm{
				APPID:      "tes_appid",
				Device:     "test_device",
				Type:       1,
				Code:       "test_code",
				Platform:   "test_platform",
				SdkVersion: "test_sdk_version",
				MobileInfo: "test_mobile_info",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/minigame/user/third-login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.ErrorDBError)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("创建绑定信息失败", func() {
			mock3 := ApplyFunc(services.GetUserWithThirdBindAccount, func(c *gin.Context, _ string, _, _ int) (models.Users, error) {
				return models.Users{}, nil
			})
			defer mock3.Reset()

			server := services.UserServices{}

			mock5 := ApplyMethod(reflect.TypeOf(&server), "CreateUser", func(_ *services.UserServices, _ *gin.Context) (*models.User, error) {
				return &models.User{
					ID:      111,
					Account: "test_account",
					Token:   "test_token",
				}, nil
			})
			defer mock5.Reset()

			mock6 := ApplyFunc(services.SaveBindThird, func(_ *gin.Context, _ *models.UserBindThird) error {
				return errors.New("创建绑定信息失败")
			})
			defer mock6.Reset()

			args := request.MiniGameThirdLoginForm{
				APPID:      "tes_appid",
				Device:     "test_device",
				Type:       1,
				Code:       "test_code",
				Platform:   "test_platform",
				SdkVersion: "test_sdk_version",
				MobileInfo: "test_mobile_info",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/minigame/user/third-login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.ErrorDBError)
			So(output, ShouldEqual, outputExpect)
		})
	})
}
