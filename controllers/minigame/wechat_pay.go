package minigame

import (
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay/wechat/minigame"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

// WechatMiniGamePay
// @Summary	创建微信小游戏订单
// @Tags	SDK MiniGameWeChatPay
// @Accept	json
// @Produce json
// @Param	person 	body	schema.WechatMiniGameExchange	true	"支付信息"
// @Success 200 {object} controllers.SuccessResponse{result=schema.PrepayOrderResult}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /minigame/order/wechat-exchange [post]
func (o Controller) WechatMiniGamePay(c *gin.Context) {
	var order schema.WechatMiniGameExchange
	err := c.ShouldBindJSON(&order)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, err := game.GetGameByAppid(order.AppID)
	if err != nil {
		o.Error(c, controllers.ErrorParamInvalid, err)
		return
	}

	// 校验支付配置
	var baseConfig payment.Payment
	converter.QueryByAppid(&baseConfig, order.AppID)

	var config payment.MiniGameWeChatPay
	converter.QueryByAppid(&config, order.AppID)

	if *baseConfig.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		o.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}

	// 查询用户
	dbUser, _ := services.GetUserByUserID(c, order.UserID)
	if dbUser.ID == 0 {
		o.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	switch order.PayType {
	case constants.MiniGameWechatJsapiPay:
		prepayResult, err := minigame.CreateJsapiOrder(c, config, *dbUser, *dbGame, order)
		if err != nil {
			o.Error(c, controllers.ErrorOrderCreateFail, err)
			return
		}

		o.Success(c, prepayResult)
	case constants.MiniGameWechatVirtualPay:

		VirtualPayResult, err := minigame.CreateVirtualPayOrder(c, config, *dbUser, *dbGame, order)
		if err != nil {
			o.Error(c, controllers.ErrorOrderCreateFail, err)
			return
		}

		o.Success(c, VirtualPayResult)
	default:
		o.Error(c, controllers.ErrorOrderCreateFail, errors.New("不支持的支付类型"))
		return
	}
}

// WeChatMiniGameUnpaidOrder
// @Summary	获取最近一笔未支付的小游戏订单
// @Tags	SDK MiniGameWeChatPay
// @Accept	json
// @Produce json
// @Success 200 {object} controllers.SuccessResponse{result=schema.OrderInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /minigame/order/unpaid [post]
func (o Controller) WeChatMiniGameUnpaidOrder(c *gin.Context) {
	var form schema.GetUnpaidOrderForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		o.Error(c, controllers.ErrorParamInvalid, err)
		return
	}

	db, err := services.GetGameDBByAppID(form.Appid)
	if err != nil {
		o.Error(c, controllers.ErrorDBError, err)
		return
	}

	orderInfo := schema.OrderInfo{}
	order, err := services.GetOrderByOpenId(db, form.OpenId)
	if err != nil {
		o.Error(c, controllers.ErrorDBError, err)
		return
	}
	if order.OrderId != "" && order.PayStatus == constants.PaymentUnpaid {
		timeDiff := time.Now().Unix() - order.CreateTime.Unix()
		if timeDiff <= constants.MiniGameOrderExpireMinute*60 {
			orderInfo.OrderId = order.OrderId
			orderInfo.ProductId = order.ProductId
			orderInfo.Price = order.Price
			orderInfo.UserID = order.UserId
			orderInfo.RoleName = order.RoleName
			orderInfo.PayStatus = order.PayStatus

			o.Success(c, orderInfo)
			return
		}
	}

	o.Error(c, controllers.ErrorOrderNotExists, errors.New("订单不存在"))
	return
}

// GetOrderInfo
// @Summary	获取小游戏订单信息
// @Tags	SDK MiniGameWeChatPay
// @Accept	json
// @Produce json
// @Success 200 {object} controllers.SuccessResponse{result=schema.OrderInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /minigame/order/info [post]
func (o Controller) GetOrderInfo(c *gin.Context) {
	var form schema.GetOrderInfoForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		o.Error(c, controllers.ErrorParamInvalid, err)
		return
	}

	if form.OrderId == "" {
		o.Error(c, controllers.ErrorEmptyParam, errors.New("order_id为空，查询失败"))
		return
	}

	orderInfo := schema.OrderInfo{}
	order, err := services.GetOrderByOrderID(c, form.OrderId)
	if order != nil {
		orderInfo.OrderId = order.OrderId
		orderInfo.PrepayId = order.PrepayId
		orderInfo.ProductId = order.ProductId
		orderInfo.Price = order.Price
		orderInfo.UserID = order.UserId
		orderInfo.RoleName = order.RoleName
		orderInfo.PayStatus = order.PayStatus
	}
	o.Success(c, orderInfo)
}
