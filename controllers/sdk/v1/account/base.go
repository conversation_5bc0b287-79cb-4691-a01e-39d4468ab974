package account

import (
	"zeus/controllers"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	controllers.BaseController
}

var Account = Controller{}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.POST("/account/login-with-email", Account.LoginWithEmail)
	routerGroup.POST("/account/login-with-phone", Account.LoginWithPhone)
	routerGroup.POST("/account/login-with-number-verify", Account.LoginWithNumberVerify)
	routerGroup.POST("/account/register-with-email", Account.RegisterWithEmail)
	routerGroup.POST("/account/send-email-captcha", Account.SendEmailCaptcha)
	routerGroup.POST("/account/send-sms-captcha", Account.SendSMSCaptcha)
	routerGroup.POST("/account/get-area-codes", Account.GetAreaCodes)
	routerGroup.POST("/account/modify-password", Account.ModifyPassword)
	routerGroup.POST("/account/reset-password", Account.ResetPassword)
}
