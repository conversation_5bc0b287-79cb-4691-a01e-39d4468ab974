package account

import (
	"zeus/controllers"
	"zeus/schema/request"
	"zeus/services/account"
	"zeus/services/settings/converter"
	"zeus/services/settings/thirdlogin"

	"github.com/gin-gonic/gin"
)

type Form struct {
	AppID string
}

// GetAreaCodes
// @Summary	获取支持的区号
// @Tags	SDK Account
// @Accept	json
// @Produce json
// @Param	person	body	request.InfoForm	  true	  "info"
// @Success 200 {object} controllers.SuccessResponse{result=[]string}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /account/get-area-codes [post]
func (a Controller) GetAreaCodes(c *gin.Context) {
	var form Form
	c.ShouldBindJSON(&form)

	var config thirdlogin.Mobile
	converter.QueryByAppid(&config, form.AppID)

	a.Success(c, config.AreaCodes)
}

// SendEmailCaptcha
// @Summary	发送邮箱验证码
// @Tags	SDK Account
// @Accept	json
// @Produce json
// @Param	person	body	request.SendEmailCaptcha	  true	  "发送验证码"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /account/send-email-captcha [post]
func (a Controller) SendEmailCaptcha(c *gin.Context) {
	var req request.SendEmailCaptcha
	err := c.ShouldBindJSON(&req)
	if err != nil {
		a.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	err = account.SendEmailCaptcha(c.Request.Context(), req.AppId, req.Language, req.Email)
	if err != nil {
		a.Error(c, controllers.ErrorSendCaptcha, err)
		return
	}
	a.Success(c, "success")
}

// SendSMSCaptcha
// @Summary	发送短信验证码
// @Tags	SDK Account
// @Accept	json
// @Produce json
// @Param	person	body	request.SendSMSCaptcha	  true	  "发送验证码"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /account/send-sms-captcha [post]
func (a Controller) SendSMSCaptcha(c *gin.Context) {
	var req request.SendSMSCaptcha
	err := c.ShouldBindJSON(&req)
	if err != nil {
		a.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	err = account.SendSMSCaptcha(c.Request.Context(), req.AppId, req.AreaCode, req.Phone)
	if err != nil {
		a.Error(c, controllers.ErrorSendCaptcha, err)
		return
	}
	a.Success(c, "success")
}
