package account

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"zeus/controllers"
	"zeus/pkg/validator"
	"zeus/schema/request"
	"zeus/services/account"
	"zeus/services/settings/converter"
	settings_schema "zeus/services/settings/field"
	"zeus/services/settings/thirdlogin"

	"github.com/gin-gonic/gin/binding"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
)

func TestAccountCaptcha(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	mockNewServerFunc := ApplyFunc(account.NewServer, func(c *gin.Context) *account.Server {
		return &account.Server{}
	})
	defer mockNewServerFunc.Reset()

	Convey("TestSendEmailCaptcha", t, func() {
		<PERSON><PERSON>("正常请求", func() {
			mackFunc := ApplyFunc(account.SendEmailCaptcha, func(ctx context.Context, appId string, language string, emailAddress string) error {
				return nil
			})
			defer mackFunc.Reset()
			args := request.SendEmailCaptcha{
				AppId:    "test_appid",
				Language: "zh-CN",
				Email:    "<EMAIL>",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/send-email-captcha", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("未填写邮箱", func() {
			mackFunc := ApplyFunc(account.SendEmailCaptcha, func(ctx context.Context, appId string, language string, emailAddress string) error {
				return nil
			})
			defer mackFunc.Reset()
			args := request.SendEmailCaptcha{
				AppId:    "test_appid",
				Language: "zh-CN",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/send-email-captcha", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorEmptyParam)
		})

		Convey("发送失败", func() {
			mackFunc := ApplyFunc(account.SendEmailCaptcha, func(ctx context.Context, appId string, language string, emailAddress string) error {
				return errors.New("Some error")
			})
			defer mackFunc.Reset()
			args := request.SendEmailCaptcha{
				AppId:    "test_appid",
				Language: "zh-CN",
				Email:    "<EMAIL>",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/send-email-captcha", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorSendCaptcha)
		})
	})

	Convey("TestSendSMSCaptcha", t, func() {
		Convey("正常请求", func() {
			mockFunc := ApplyFunc(account.SendSMSCaptcha, func(ctx context.Context, appId string, areaCode string, phoneNumber string) error {
				return nil
			})
			defer mockFunc.Reset()
			args := request.SendSMSCaptcha{
				AppId:    "test_appid",
				AreaCode: "+86",
				Phone:    "***********",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/send-sms-captcha", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("未填写区号", func() {
			args := request.SendSMSCaptcha{
				AppId: "test_appid",
				Phone: "***********",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/send-sms-captcha", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorEmptyParam)
		})

		Convey("发送短信失败", func() {
			mockFunc := ApplyFunc(account.SendSMSCaptcha, func(ctx context.Context, appId string, areaCode string, phoneNumber string) error {
				return errors.New("Some Error")
			})
			defer mockFunc.Reset()
			args := request.SendSMSCaptcha{
				AppId:    "test_appid",
				AreaCode: "+86",
				Phone:    "***********",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/send-sms-captcha", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorSendCaptcha)
		})
	})

	Convey("TestGetAreaCodes", t, func() {
		ApplyFunc(converter.QueryByAppid, func(config settings_schema.BaseConfiger, _ string) {
			switch config.GetType() {
			case "ThirdLoginMobile":
				thirdLoginMobile := config.(*thirdlogin.Mobile)
				thirdLoginMobile.AreaCodes = &thirdlogin.AreaCodes{"+86", "+886"}
			}
		})
		Convey("正常请求", func() {
			v, _ := json.Marshal(struct{}{})
			req, _ := http.NewRequest("POST", "/account/get-area-codes", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["result"]).([]interface{})
			So(output[0], ShouldEqual, "+86")
			So(output[1], ShouldEqual, "+886")
		})
	})
}
