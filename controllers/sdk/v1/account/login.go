package account

import (
	"errors"
	"fmt"
	"strings"

	"zeus/controllers"
	"zeus/pkg/yidun"
	"zeus/schema/request"
	"zeus/schema/response"
	"zeus/services/account"
	"zeus/services/settings/converter"
	"zeus/services/settings/thirdlogin"

	"github.com/gin-gonic/gin"
)

// LoginWithEmail
// @Summary 使用电子邮箱登录
// @Tags	SDK Account
// @Accept	json
// @Produce json
// @Param	person	body	request.LoginWithEmail	true	"账号登录"
// @Success 200 {object} controllers.SuccessResponse{result=response.AccountLogin}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /account/login-with-email [post]
func (a Controller) LoginWithEmail(c *gin.Context) {
	var req request.LoginWithEmail
	err := c.ShouldBindJSON(&req)
	if err != nil {
		a.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	server := account.NewServer(c)
	accountInfo, err := server.LoginWithEmail(&req)
	if err != nil {
		a.Error(c, controllers.ErrorLoginFail, err)
		return
	}
	resp := response.AccountLogin{
		UnionId: accountInfo.UnionId,
		Token:   accountInfo.Token,
	}
	a.Success(c, resp)
}

// LoginWithPhone
// @Summary 使用手机号登录
// @Tags	SDK Account
// @Accept	json
// @Produce json
// @Param	person	body	request.LoginWithPhone	true	"账号登录"
// @Success 200 {object} controllers.SuccessResponse{result=response.AccountLogin}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /account/login-with-phone [post]
func (a Controller) LoginWithPhone(c *gin.Context) {
	var req request.LoginWithPhone
	err := c.ShouldBindJSON(&req)
	if err != nil {
		a.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	phoneNumber := fmt.Sprintf("%s%s", strings.Replace(req.AreaCode, "+", "", 1), req.Phone)
	if !account.CheckCaptchaValid(req.AppId, phoneNumber, req.Captcha) {
		a.Error(c, controllers.ErrorMobileCaptchaInvalid, errors.New("无效的验证码"))
		return
	}

	server := account.NewServer(c)
	accountInfo, err := server.LoginWithPhone(&req)
	if err != nil {
		a.Error(c, controllers.ErrorLoginFail, err)
		return
	}
	resp := response.AccountLogin{
		UnionId: accountInfo.UnionId,
		Token:   accountInfo.Token,
	}
	a.Success(c, resp)
}

// LoginWithNumberVerify
// @Summary 使用手机号号码认证
// @Tags	SDK Account
// @Accept	json
// @Produce json
// @Param	person	body	request.LoginWithPhone	true	"账号登录"
// @Success 200 {object} controllers.SuccessResponse{result=response.AccountLogin}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /account/login-with-number-verify [post]
func (a Controller) LoginWithNumberVerify(c *gin.Context) {
	var req request.LoginWithNumberVerify
	err := c.ShouldBindJSON(&req)
	if err != nil {
		a.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	var numberVerifyConfig thirdlogin.NumberVerify
	converter.QueryByAppid(&numberVerifyConfig, req.AppId)

	numberVerifyClient := yidun.NewNumberVerifyClient(*numberVerifyConfig.NumberVerifyBusinessID, *numberVerifyConfig.NumberVerifySecretID, *numberVerifyConfig.NumberVerifySecretKey)

	phoneNumber, err := numberVerifyClient.GetNumber(c.Request.Context(), req.Token, req.AccessToken)
	if err != nil {
		a.Error(c, controllers.ErrorLoginFail, err)
		return
	}

	server := account.NewServer(c)
	accountInfo, err := server.LoginWithNumberVerify("+86", phoneNumber)
	if err != nil {
		a.Error(c, controllers.ErrorLoginFail, err)
		return
	}
	resp := response.AccountLogin{
		UnionId: accountInfo.UnionId,
		Token:   accountInfo.Token,
	}
	a.Success(c, resp)
}
