package account

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/validator"
	"zeus/schema/request"
	"zeus/services/account"

	"github.com/gin-gonic/gin/binding"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
)

func TestAccountLogin(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	mockNewServerFunc := ApplyFunc(account.NewServer, func(c *gin.Context) *account.Server {
		return &account.Server{}
	})
	defer mockNewServerFunc.Reset()
	server := account.Server{}

	Convey("TestLoginWithEmail", t, func() {
		<PERSON><PERSON>("正常请求", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "LoginWithEmail", func(_ *account.Server,
				v *request.LoginWithEmail,
			) (*models.Account, error) {
				return &models.Account{
					UnionId: "<EMAIL>",
					Token:   "token",
				}, nil
			})

			defer mockMethod.Reset()
			args := request.LoginWithEmail{
				AppId:    "test_appid",
				Email:    "<EMAIL>",
				Password: "password",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/login-with-email", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["result"]).(map[string]interface{})
			outputExpect := "<EMAIL>"
			So(output["union_id"].(string), ShouldEqual, outputExpect)
		})

		Convey("参数缺失", func() {
			args := request.LoginWithEmail{
				AppId:    "test_appid",
				Password: "password",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/login-with-email", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("登录失败", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "LoginWithEmail", func(_ *account.Server,
				v *request.LoginWithEmail,
			) (*models.Account, error) {
				return &models.Account{}, errors.New("LoginWithEmail Failed")
			})

			defer mockMethod.Reset()
			args := request.LoginWithEmail{
				AppId:    "test_appid",
				Email:    "<EMAIL>",
				Password: "password",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/login-with-email", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorLoginFail))
		})
	})

	Convey("TestLoginWithPhone", t, func() {
		Convey("正常请求", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "LoginWithPhone",
				func(_ *account.Server, v *request.LoginWithPhone) (*models.Account, error) {
					return &models.Account{
						UnionId: "***********",
						Token:   "token",
					}, nil
				})
			defer mockMethod.Reset()

			mockFunc2 := ApplyFunc(account.CheckCaptchaValid, func(appid, k, v string) bool {
				return true
			})
			defer mockFunc2.Reset()

			args := request.LoginWithPhone{
				AppId:    "test_appid",
				Phone:    "***********",
				AreaCode: "+86",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/login-with-phone", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["result"]).(map[string]interface{})
			outputExpect := "***********"
			So(output["union_id"].(string), ShouldEqual, outputExpect)
		})

		Convey("参数缺失", func() {
			args := request.LoginWithPhone{
				AppId:    "test_appid",
				AreaCode: "+86",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/login-with-phone", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("验证码无效", func() {
			mockFunc := ApplyFunc(account.CheckCaptchaValid, func(appid, k, v string) bool {
				return false
			})
			defer mockFunc.Reset()

			args := request.LoginWithPhone{
				AppId:    "test_appid",
				Phone:    "***********",
				AreaCode: "+86",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/login-with-phone", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorMobileCaptchaInvalid))
		})

		Convey("手机登录验证失败", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "LoginWithPhone",
				func(_ *account.Server, v *request.LoginWithPhone) (*models.Account, error) {
					return &models.Account{}, errors.New("LoginWithPhone Failed")
				})
			defer mockMethod.Reset()

			mockFunc2 := ApplyFunc(account.CheckCaptchaValid, func(appid, k, v string) bool {
				return true
			})
			defer mockFunc2.Reset()

			args := request.LoginWithPhone{
				AppId:    "test_appid",
				Phone:    "***********",
				AreaCode: "+86",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/login-with-phone", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorLoginFail))
		})
	})
}
