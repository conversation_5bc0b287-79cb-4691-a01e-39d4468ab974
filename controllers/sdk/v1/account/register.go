package account

import (
	"errors"

	"zeus/controllers"
	"zeus/schema/request"
	"zeus/schema/response"
	"zeus/services/account"

	"github.com/gin-gonic/gin"
)

// RegisterWithEmail
// @Summary 使用电子邮箱注册账号
// @Tags	SDK Account
// @Accept	json
// @Produce json
// @Param	person	body	request.RegisterWithEmail	true	"账号注册"
// @Success 200 {object} controllers.SuccessResponse{result=response.AccountLogin}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /account/register-with-email [post]
func (a Controller) RegisterWithEmail(c *gin.Context) {
	var req request.RegisterWithEmail
	err := c.ShouldBindJSON(&req)
	if err != nil {
		a.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	// TODO 注册时只有当输入验证码时才进行校验验证码。
	// TODO 为了满足注册时不需要验证邮箱合法性，绑定时需要验证合法性的需求。
	if req.Captcha != "" && !account.CheckCaptchaValid(req.AppId, req.Email, req.Captcha) {
		a.<PERSON>rror(c, controllers.ErrorMobileCaptchaInvalid, errors.New("无效的验证码"))
		return
	}

	server := account.NewServer(c)
	accountInfo, err := server.CreateAccountWithEmail(&req)
	if err != nil {
		a.Error(c, controllers.ErrorRegisterFail, err)
		return
	}
	resp := response.AccountLogin{
		UnionId: accountInfo.UnionId,
		Token:   accountInfo.Token,
	}
	a.Success(c, resp)
}

// ModifyPassword
// @Summary 修改密码
// @Tags	SDK Account
// @Accept	json
// @Produce json
// @Param	person	body	request.ModifyPassword	true	"修改密码"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /account/modify-password [post]
func (a Controller) ModifyPassword(c *gin.Context) {
	var req request.ModifyPassword
	err := c.ShouldBindJSON(&req)
	if err != nil {
		a.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	if !account.CheckCaptchaValid(req.AppId, req.Email, req.Captcha) {
		a.Error(c, controllers.ErrorMobileCaptchaInvalid, errors.New("无效的验证码"))
		return
	}

	server := account.NewServer(c)
	if _, err = server.GetAccountByEmail(req.Email); err != nil {
		a.Error(c, controllers.ErrorAccountNotExists, err)
		return
	}
	if err = server.ModifyPassword(&req); err != nil {
		a.Error(c, controllers.ErrorDBError, err)
		return
	}

	a.Success(c, "success")
}

// ResetPassword
// @Summary 找回密码
// @Tags	SDK Account
// @Accept	json
// @Produce json
// @Param	person	body	request.ResetPassword	true   "找回密码"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /account/reset-password [post]
func (a Controller) ResetPassword(c *gin.Context) {
	var req request.ResetPassword
	err := c.ShouldBindJSON(&req)
	if err != nil {
		a.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	server := account.NewServer(c)
	if _, err = server.GetAccountByEmail(req.Email); err != nil {
		a.Error(c, controllers.ErrorAccountNotExists, err)
		return
	}

	err = account.SendEmailCaptcha(c.Request.Context(), req.AppId, req.Language, req.Email)
	if err != nil {
		a.Error(c, controllers.ErrorSendCaptcha, err)
		return
	}
	a.Success(c, "success")
}
