package account

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/validator"
	"zeus/schema/request"
	"zeus/services/account"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	. "github.com/smartystreets/goconvey/convey"
)

func TestRegister(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	mockNewServerFunc := ApplyFunc(account.NewServer, func(c *gin.Context) *account.Server {
		return &account.Server{}
	})
	defer mockNewServerFunc.Reset()
	server := account.Server{}

	Convey("TestRegisterWithEmail", t, func() {
		<PERSON>vey("Register with error form", func() {
			args := request.RegisterWithEmail{
				AppId:    "test_appid",
				Password: "password",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/register-with-email", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorEmptyParam)
		})

		Convey("Register with error captcha", func() {
			mackFunc2 := ApplyFunc(account.CheckCaptchaValid, func(appid, k, v string) bool {
				return false
			})
			defer mackFunc2.Reset()

			args := request.RegisterWithEmail{
				AppId:    "test_appid",
				Password: "password",
				Email:    "<EMAIL>",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/register-with-email", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorMobileCaptchaInvalid)
		})

		Convey("Register with Create Account Error", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "CreateAccountWithEmail", func(_ *account.Server,
				v *request.RegisterWithEmail,
			) (*models.Account, error) {
				return nil, errors.New("error")
			})
			defer mockMethod.Reset()
			mackFunc2 := ApplyFunc(account.CheckCaptchaValid, func(appid, k, v string) bool {
				return true
			})
			defer mackFunc2.Reset()

			args := request.RegisterWithEmail{
				AppId:    "test_appid",
				Password: "password",
				Email:    "<EMAIL>",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/register-with-email", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorRegisterFail)
		})

		Convey("正常请求", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "CreateAccountWithEmail", func(_ *account.Server,
				v *request.RegisterWithEmail,
			) (*models.Account, error) {
				return &models.Account{
					UnionId: "<EMAIL>",
					Token:   "token",
				}, nil
			})
			defer mockMethod.Reset()

			mackFunc2 := ApplyFunc(account.CheckCaptchaValid, func(appid, k, v string) bool {
				return true
			})
			defer mackFunc2.Reset()

			args := request.RegisterWithEmail{
				AppId:    "test_appid",
				Email:    "<EMAIL>",
				Password: "password",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/register-with-email", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["result"]).(map[string]interface{})
			outputExpect := "<EMAIL>"
			So(output["union_id"].(string), ShouldEqual, outputExpect)
		})
	})
}

func TestModifyPassword(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	mockNewServerFunc := ApplyFunc(account.NewServer, func(c *gin.Context) *account.Server {
		return &account.Server{}
	})
	defer mockNewServerFunc.Reset()
	server := account.Server{}
	Convey("TestSendEmailCaptcha", t, func() {
		Convey("正常请求", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "GetAccountByEmail", func(_ *account.Server,
				_ string,
			) (*models.Account, error) {
				return &models.Account{
					UnionId: "<EMAIL>",
					Token:   "token",
				}, nil
			})
			defer mockMethod.Reset()

			mockMethod2 := ApplyMethod(reflect.TypeOf(&server), "ModifyPassword", func(_ *account.Server,
				_ *request.ModifyPassword,
			) error {
				return nil
			})
			defer mockMethod2.Reset()

			mackFunc2 := ApplyFunc(account.CheckCaptchaValid, func(appid, k, v string) bool {
				return true
			})
			defer mackFunc2.Reset()

			args := request.ModifyPassword{
				AppId:    "test_appid",
				Email:    "<EMAIL>",
				Password: "password",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/modify-password", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, 0)
			So(resp["message"].(string), ShouldEqual, "success")
		})

		Convey("one func with error params", func() {
			args := request.ModifyPassword{
				AppId:    "test_appid",
				Password: "password",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/modify-password", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorEmptyParam)
		})

		Convey("one func with error captcha", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "GetAccountByEmail", func(_ *account.Server,
				_ string,
			) (*models.Account, error) {
				return &models.Account{
					UnionId: "<EMAIL>",
					Token:   "token",
				}, nil
			})
			defer mockMethod.Reset()

			mockMethod2 := ApplyMethod(reflect.TypeOf(&server), "ModifyPassword", func(_ *account.Server,
				_ *request.ModifyPassword,
			) error {
				return nil
			})
			defer mockMethod2.Reset()

			mackFunc2 := ApplyFunc(account.CheckCaptchaValid, func(appid, k, v string) bool {
				return false
			})
			defer mackFunc2.Reset()

			args := request.ModifyPassword{
				AppId:    "test_appid",
				Email:    "<EMAIL>",
				Password: "password",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/modify-password", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorMobileCaptchaInvalid)
		})

		Convey("one func with error account", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "GetAccountByEmail", func(_ *account.Server,
				_ string,
			) (*models.Account, error) {
				return nil, errors.New("error account")
			})
			defer mockMethod.Reset()

			mockMethod2 := ApplyMethod(reflect.TypeOf(&server), "ModifyPassword", func(_ *account.Server,
				_ *request.ModifyPassword,
			) error {
				return nil
			})
			defer mockMethod2.Reset()

			mackFunc2 := ApplyFunc(account.CheckCaptchaValid, func(appid, k, v string) bool {
				return true
			})
			defer mackFunc2.Reset()

			args := request.ModifyPassword{
				AppId:    "test_appid",
				Email:    "<EMAIL>",
				Password: "password",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/modify-password", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorAccountNotExists)
		})

		Convey("one func with modify password error", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "GetAccountByEmail", func(_ *account.Server,
				_ string,
			) (*models.Account, error) {
				return &models.Account{
					UnionId: "<EMAIL>",
					Token:   "token",
				}, nil
			})
			defer mockMethod.Reset()

			mockMethod2 := ApplyMethod(reflect.TypeOf(&server), "ModifyPassword", func(_ *account.Server,
				_ *request.ModifyPassword,
			) error {
				return errors.New("ModifyPassword Error")
			})
			defer mockMethod2.Reset()

			mackFunc2 := ApplyFunc(account.CheckCaptchaValid, func(appid, k, v string) bool {
				return true
			})
			defer mackFunc2.Reset()

			args := request.ModifyPassword{
				AppId:    "test_appid",
				Email:    "<EMAIL>",
				Password: "password",
				Captcha:  "4444",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/modify-password", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorDBError)
		})
	})
}

func TestResetPassword(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	mockNewServerFunc := ApplyFunc(account.NewServer, func(c *gin.Context) *account.Server {
		return &account.Server{}
	})
	defer mockNewServerFunc.Reset()
	server := account.Server{}
	Convey("TestResetPassword", t, func() {
		Convey("正常请求", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "GetAccountByEmail", func(_ *account.Server,
				_ string,
			) (*models.Account, error) {
				return &models.Account{
					UnionId: "<EMAIL>",
					Token:   "token",
				}, nil
			})
			defer mockMethod.Reset()

			mackFunc2 := ApplyFunc(account.SendEmailCaptcha, func(ctx context.Context, appId string, language string, emailAddress string) error {
				return nil
			})
			defer mackFunc2.Reset()

			args := request.ResetPassword{
				SendEmailCaptcha: request.SendEmailCaptcha{
					AppId:    "test_appid",
					Email:    "<EMAIL>",
					Language: "en",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/reset-password", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, 0)
			So(resp["message"].(string), ShouldEqual, "success")
		})

		Convey("Failed with empty params", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "GetAccountByEmail", func(_ *account.Server,
				_ string,
			) (*models.Account, error) {
				return &models.Account{
					UnionId: "<EMAIL>",
					Token:   "token",
				}, nil
			})
			defer mockMethod.Reset()

			mackFunc2 := ApplyFunc(account.SendEmailCaptcha, func(ctx context.Context, appId string, language string, emailAddress string) error {
				return nil
			})
			defer mackFunc2.Reset()

			args := request.ResetPassword{
				SendEmailCaptcha: request.SendEmailCaptcha{
					AppId:    "test_appid",
					Language: "en",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/reset-password", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorEmptyParam)
		})

		Convey("Failed with account is not exists", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "GetAccountByEmail", func(_ *account.Server,
				_ string,
			) (*models.Account, error) {
				return nil, errors.New("Account is not exists")
			})
			defer mockMethod.Reset()

			mackFunc2 := ApplyFunc(account.SendEmailCaptcha, func(ctx context.Context, appId string, language string, emailAddress string) error {
				return nil
			})
			defer mackFunc2.Reset()

			args := request.ResetPassword{
				SendEmailCaptcha: request.SendEmailCaptcha{
					AppId:    "test_appid",
					Email:    "<EMAIL>",
					Language: "en",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/reset-password", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorAccountNotExists)
		})
		Convey("Failed with SendEmailCaptcha", func() {
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "GetAccountByEmail", func(_ *account.Server,
				_ string,
			) (*models.Account, error) {
				return &models.Account{
					UnionId: "<EMAIL>",
					Token:   "token",
				}, nil
			})
			defer mockMethod.Reset()

			mackFunc2 := ApplyFunc(account.SendEmailCaptcha, func(ctx context.Context, appId string, language string, emailAddress string) error {
				return errors.New("SendEmailCaptcha Error")
			})
			defer mackFunc2.Reset()

			args := request.ResetPassword{
				SendEmailCaptcha: request.SendEmailCaptcha{
					AppId:    "test_appid",
					Email:    "<EMAIL>",
					Language: "en",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/account/reset-password", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorSendCaptcha)
		})
	})
}
