package anti_addiction

import (
	"github.com/gin-gonic/gin"

	"zeus/controllers"
)

type Controller struct {
	controllers.BaseController
}

var AntiAddiction = Controller{}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.POST("/anti-addiction/identity-check", AntiAddiction.IdentityCheck)
	routerGroup.POST("/anti-addiction/pay-limit", AntiAddiction.PayLimit)
	routerGroup.POST("/anti-addiction/pay-limit-information", AntiAddiction.PayLimitInformation)
	routerGroup.POST("/anti-addiction/playable", AntiAddiction.Playable)
	routerGroup.POST("/anti-addiction/logout", AntiAddiction.Logout)
}
