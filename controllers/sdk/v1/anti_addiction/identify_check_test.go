package anti_addiction

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"zeus/common/constants"

	"zeus/controllers"
	"zeus/schema/request"
	"zeus/services"

	"gorm.io/gorm"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
)

type TestBehavior struct {
	result bool
}

func (t *TestBehavior) Check(c context.Context, idNo, name string) (bool, string) {
	return t.result, "test"
}

func (t TestBehavior) LogOut(ctx context.Context, db *gorm.DB, account string, status int) {
}

func TestAntiAddictionCheck(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)

	ApplyFunc(services.BingAccount, func(_ *gorm.DB, _ string, _ string, _ string) error {
		return nil
	})
	c := &gin.Context{}
	mock5 := ApplyMethod(reflect.TypeOf(c), "MustGet", func(_ *gin.Context, _ string) any {
		return &gorm.DB{}
	})
	defer mock5.Reset()

	Convey("TestIdentityCheck", t, func() {
		Convey("one func for succ", func() {
			mock1 := ApplyFunc(services.CreateAntiAddictionBehavior, func(_ string) services.AntiAddiction {
				return &TestBehavior{true}
			})
			defer mock1.Reset()

			args := request.IdentityCheck{
				AppID:   "test_appid",
				Account: "117",
				IDNo:    "510126197202086449",
				Name:    "张三",
				Region:  constants.China,
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/anti-addiction/identity-check", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("参数缺失", func() {
			args := request.IdentityCheck{
				AppID:   "test_appid",
				Account: "117",
				Name:    "李四",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/anti-addiction/identity-check", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("姓名不合法", func() {
			mock1 := ApplyFunc(services.CreateAntiAddictionBehavior, func(_ string) services.AntiAddiction {
				return &TestBehavior{false}
			})
			defer mock1.Reset()

			args := request.IdentityCheck{
				AppID:   "test_appid",
				Account: "117",
				IDNo:    "50011219980801xxxx",
				Name:    "童",
				Region:  constants.China,
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/anti-addiction/identity-check", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorAuthenticationFailed))
		})
	})
}
