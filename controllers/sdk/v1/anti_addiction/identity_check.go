package anti_addiction

import (
	"errors"
	"zeus/common/constants"

	"zeus/controllers"
	"zeus/schema/request"
	"zeus/schema/response"
	"zeus/services"

	"gorm.io/gorm"

	idvalidator "github.com/guanguans/id-validator"

	"github.com/gin-gonic/gin"
)

// IdentityCheck
// @Summary
// @Tags	SDK AntiAddiction
// @Accept	json
// @Produce json
// @Param	person	body  request.IdentityCheck	 true "实名认证"
// @Success 200 {object} controllers.SuccessResponse{result=response.IdentityCheck}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /anti-addiction/identity-check [post]
func (aa Controller) IdentityCheck(c *gin.Context) {
	var req = request.NewIdentityCheck()
	err := c.ShouldBindJSON(&req)
	if err != nil {
		aa.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	var region = constants.China
	if req.Region != "" {
		region = req.Region
	}
	if region == constants.Japan {
		db := c.MustGet("db").(*gorm.DB)
		if err := services.BingAccount(db, req.Account, req.IDNo[6:14], req.IDNo); err != nil {
			aa.Error(c, controllers.ErrorAuthenticationFailed, err)
			return
		}
		resp := response.IdentityCheck{IDNo: req.IDNo, Name: req.Name, Res: true}

		aa.Success(c, resp)
		return
	}

	if req.Name == "" || !services.CheckName(req.Name) {
		aa.Error(c, controllers.ErrorAuthenticationFailed, errors.New("姓名为空或非法"))
		return
	}

	// 非严格模式验证大陆居民身份证18位
	validityID := idvalidator.IsValid(req.IDNo, false)
	if !validityID {
		aa.Error(c, controllers.ErrorAuthenticationFailed, errors.New("身份证号为空或非法"))
		return
	}

	behavior := services.CreateAntiAddictionBehavior(req.AppID)
	if behavior == nil {
		aa.Error(c, controllers.ErrorUnAvailableAuthenticationMethod, errors.New("认证类型不可用"))
		return
	}
	result, uniqueIdentity := behavior.Check(c.Request.Context(), req.Name, req.IDNo)

	if result {
		db := c.MustGet("db").(*gorm.DB)
		if err := services.BingAccount(db, req.Account, services.GetBirthDayYMD(req.IDNo), uniqueIdentity); err != nil {
			aa.Error(c, controllers.ErrorAuthenticationFailed, err)
			return
		}
	}

	resp := response.IdentityCheck{IDNo: req.IDNo, Name: req.Name, Res: result}

	aa.Success(c, resp)
}
