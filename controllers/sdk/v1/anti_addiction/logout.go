package anti_addiction

import (
	"zeus/controllers"
	"zeus/services"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
)

type LogoutForm struct {
	AppID   string `json:"appid" binding:"required"`
	Account string `json:"account" binding:"required"`
	Status  int    `json:"status,string" binding:"required"`
}

// Logout
// @Summary
// @Tags	SDK AntiAddiction
// @Accept	json
// @Produce json
// @Param	person	body Logout	true "上报用户上下线行为数据"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /anti-addiction/logout [post]
func (aa Controller) Logout(c *gin.Context) {
	var req LogoutForm
	err := c.ShouldBindJSON(&req)
	if err != nil {
		aa.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	behavior := services.CreateAntiAddictionBehavior(req.AppID)
	behavior.LogOut(c.Request.Context(), c.Must<PERSON>("db").(*gorm.DB), req.Account, req.Status)

	aa.Success(c, "成功")
}
