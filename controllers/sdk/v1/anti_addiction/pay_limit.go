package anti_addiction

import (
	"errors"
	"zeus/common/conf"
	"zeus/controllers"
	"zeus/schema/domain"
	"zeus/schema/request"
	"zeus/schema/response"
	"zeus/services"

	"github.com/gin-gonic/gin"
)

// PayLimit
// @Summary
// @Tags	SDK AntiAddiction
// @Accept	json
// @Produce json
// @Param	person	body  request.PayLimit	 true "支付金额限制"
// @Success 200 {object} controllers.SuccessResponse{result=response.PayLimit}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /anti-addiction/pay-limit [post]
func (aa Controller) PayLimit(c *gin.Context) {
	req := request.NewPayLimit()
	err := c.ShouldBindJSON(req)
	if err != nil {
		aa.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	limit := domain.PayLimit{}
	limit.Map(req)
	os := new(services.AntiAddictionServices)
	os.InitAntiAddictionServices(c, limit)
	PaidAmount := os.GetPaidAmount()

	PayMaxCap, PayOneCap, err := os.GetPayCap()
	if err != nil {
		aa.Error(c, controllers.ErrorAccountNotExists, errors.New("账号不存在"))
		return
	}

	resp := response.NewPayLimit(
		PayMaxCap,
		PayOneCap,
		PaidAmount,
		limit.StartTime,
		limit.EndTime,
	)

	aa.Success(c, resp)

	return
}

// PayLimitInformation
// @Summary
// @Tags	SDK AntiAddiction
// @Accept	json
// @Produce json
// @Param	person	body  request.PayLimitDescription	 true "支付金额限制"
// @Success 200 {object} controllers.SuccessResponse{result=response.PayLimit}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /anti-addiction/pay-limit-information [post]
func (aa Controller) PayLimitInformation(c *gin.Context) {
	param := new(request.PayLimitInformation)
	err := c.ShouldBindJSON(param)
	if err != nil {
		aa.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	payLimit := conf.Conf.PayLimit
	resp := response.PayLimitInformation{}
	for _, p := range payLimit {
		if p.Region == param.Region {
			resp.PayLimitInformation.LimitContent = p.Information.LimitContent
			resp.PayLimitInformation.TextTitle = p.Information.TextTitle
			resp.PayLimitInformation.TextContent = p.Information.TextContent
		}
	}

	aa.Success(c, resp)
}
