package anti_addiction

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"zeus/common/constants"

	"zeus/controllers"
	"zeus/schema/request"
	"zeus/services"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
)

func TestAntiAddictionPayLimit(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)

	var os *services.AntiAddictionServices
	ApplyMethod(reflect.TypeOf(os), "InitAntiAddictionServices", func(_ *services.AntiAddictionServices, c *gin.Context, v interface{}) {
		return
	})
	Convey("TestPayLimit", t, func() {
		Convey("one func for succ", func() {
			ApplyMethod(reflect.TypeOf(os), "GetPaidAmount", func(_ *services.AntiAddictionServices) float64 {
				return 13
			})
			ApplyMethod(reflect.TypeOf(os), "GetPayCap", func(_ *services.AntiAddictionServices) (PayMaxCap, PayOneCap float64, err error) {
				return 200, 50, nil
			})

			args := request.PayLimit{
				GameAccount: request.GameAccount{AppID: "1111", Account: "117"},
				Region:      constants.China,
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/anti-addiction/pay-limit", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("参数缺失", func() {
			ApplyMethod(reflect.TypeOf(os), "GetPaidAmount", func(_ *services.AntiAddictionServices) float64 {
				return 13
			})
			ApplyMethod(reflect.TypeOf(os), "GetPayCap", func(_ *services.AntiAddictionServices) (PayMaxCap, PayOneCap float64, err error) {
				return 200, 50, nil
			})

			args := request.PayLimit{
				GameAccount: request.GameAccount{AppID: "1111"},
				Region:      constants.China,
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/anti-addiction/pay-limit", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("账户不存在", func() {
			ApplyMethod(reflect.TypeOf(os), "GetPaidAmount", func(_ *services.AntiAddictionServices) float64 {
				return 13
			})
			ApplyMethod(reflect.TypeOf(os), "GetPayCap", func(_ *services.AntiAddictionServices) (PayMaxCap, PayOneCap float64, err error) {
				return 0, 0, errors.New("Invalid")
			})

			args := request.PayLimit{
				GameAccount: request.GameAccount{AppID: "1111", Account: "117"},
				Region:      constants.China,
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/anti-addiction/pay-limit", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorAccountNotExists))
		})
	})
}
