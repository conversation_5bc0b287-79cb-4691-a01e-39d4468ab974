package anti_addiction

import (
	"errors"

	"zeus/controllers"
	"zeus/schema/domain"
	"zeus/schema/request"
	"zeus/schema/response"
	"zeus/services"

	"github.com/gin-gonic/gin"
)

// Playable
// @Summary
// @Tags	SDK AntiAddiction
// @Accept	json
// @Produce json
// @Param	person	body  request.Playable	 true "检查玩家当前能否游戏"
// @Success 200 {object} controllers.SuccessResponse{result=response.Playable}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /anti-addiction/playable [post]
func (aa Controller) Playable(c *gin.Context) {
	req := new(request.Playable)
	err := c.ShouldBindJSON(req)
	if err != nil {
		aa.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	gt := domain.Playable{}
	_ = gt.Map(req)

	os := new(services.AntiAddictionServices)
	os.InitAntiAddictionServices(c, gt)

	age, err := os.GetUserAge()
	if err != nil {
		aa.Error(c, controllers.ErrorAccountNotExists, errors.New("账号不存在"))
		return
	}
	remainTime, _ := os.GetPlayTime(age)

	resp := response.NewPlayable(remainTime, age)
	aa.Success(c, resp)
}
