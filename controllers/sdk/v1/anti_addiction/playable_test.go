package anti_addiction

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"zeus/controllers"
	"zeus/schema/request"
	"zeus/services"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
)

func TestAntiAddictionPlayable(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)

	var os *services.AntiAddictionServices
	ApplyMethod(reflect.TypeOf(os), "InitAntiAddictionServices", func(_ *services.AntiAddictionServices, c *gin.Context, v interface{}) {
		return
	})
	Convey("TestPlayable", t, func() {
		Convey("one func for succ", func() {
			ApplyMethod(reflect.TypeOf(os), "GetUserAge", func(_ *services.AntiAddictionServices) (age int, err error) {
				return 13, nil
			})
			ApplyMethod(reflect.TypeOf(os), "GetPlayTime", func(_ *services.AntiAddictionServices, age int) (int, error) {
				return 100, nil
			})

			args := request.PayLimit{
				GameAccount: request.GameAccount{AppID: "1111", Account: "117"},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/anti-addiction/playable", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("参数缺失", func() {
			ApplyMethod(reflect.TypeOf(os), "GetUserAge", func(_ *services.AntiAddictionServices) (age int, err error) {
				return 13, nil
			})
			ApplyMethod(reflect.TypeOf(os), "GetPlayTime", func(_ *services.AntiAddictionServices, age int) (int, error) {
				return 100, nil
			})

			args := request.PayLimit{
				GameAccount: request.GameAccount{AppID: "1111"},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/anti-addiction/playable", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("账户不存在", func() {
			ApplyMethod(reflect.TypeOf(os), "GetUserAge", func(_ *services.AntiAddictionServices) (age int, err error) {
				return 13, errors.New("GetUserAge Failed")
			})
			ApplyMethod(reflect.TypeOf(os), "GetPlayTime", func(_ *services.AntiAddictionServices, age int) (int, error) {
				return 100, nil
			})

			args := request.PayLimit{
				GameAccount: request.GameAccount{AppID: "1111", Account: "117"},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/anti-addiction/playable", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorAccountNotExists))
		})
	})
}
