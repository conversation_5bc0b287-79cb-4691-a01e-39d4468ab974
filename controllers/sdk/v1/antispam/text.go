package antispam

import (
	"encoding/json"
	"fmt"
	"time"

	"zeus/common/conf"
	"zeus/common/log"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/services/antispam"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/other"
	"zeus/utils"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type TextCheck struct {
	AppID   string `json:"appid" binding:"required"`
	DataID  string `json:"data_id" binding:"required"`
	Data    string `json:"data" binding:"required"`
	Account string `json:"account"`
}

// TextCheck
// @Summary
// @Tags	SDK Antispam
// @Accept	json
// @Produce json
// @Param	person	body  TextCheck	 true "检测文本"
// @Success 200 {object} controllers.SuccessResponse{result=yidun.CheckTextResult}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /antispam/text-check [post]
func (an Controller) TextCheck(c *gin.Context) {
	var req TextCheck
	if err := c.ShouldBindJSON(&req); err != nil {
		an.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, _ := game.GetGameByAppid(req.AppID)
	var antispamConfig other.AntispamConfig
	converter.QueryByProjectId(&antispamConfig, dbGame.ProjectID)

	var resp antispam.CheckTextResult
	cacheKey := fmt.Sprintf("antispam_%v_%s", antispamConfig.AntispamType, utils.MD5(req.Data))
	data := redis.Get(cacheKey)
	if data != "" {
		json.Unmarshal([]byte(data), &resp)
		log.Debug(c.Request.Context(), "屏蔽字服务", "Data", req.Data, "Result", resp, "HitCache", true)
		an.Success(c, resp)
		return
	}

	var checker antispam.TextChecker
	switch *antispamConfig.AntispamType {
	case 0:
		checker = antispam.NewYiDunChecker(conf.Conf.AntispamInfo.ApiURL,
			*antispamConfig.AntispamSecretID,
			*antispamConfig.AntispamSecretKey,
			*antispamConfig.AntispamBusinessID)
	case 1:
		if req.Account == "" {
			an.Error(c, controllers.ErrorEmptyParam, errors.New("Account is required"))
			return
		}

		cacheKey := fmt.Sprintf("%s_%s", req.AppID, req.Account)

		openid := redis.Get(cacheKey)
		if openid == "" {
			db := c.MustGet("db").(*gorm.DB)
			user, _ := models.GetUserByAccount(db, req.Account)
			if user.ID == 0 {
				an.Error(c, controllers.ErrorEmptyParam, errors.New("Account is not a valid user"))
				return
			}

			bindInfo, _ := models.GetOneBindThird(db, map[string]interface{}{"user_id": user.ID})
			if bindInfo.UnionID == "" {
				an.Error(c, controllers.ErrorEmptyParam, errors.New("Account has no valid bind"))
				return
			}
			bindDetail := make(map[string]string)

			json.Unmarshal([]byte(bindInfo.Detail), &bindDetail)

			openid = bindDetail["openid"]
			if openid == "" {
				an.Error(c, controllers.ErrorEmptyParam, errors.New("Can not get openid from bindinfo"))
				return
			}

			redis.Set(cacheKey, openid, time.Hour*24)
		}

		checker = antispam.NewWechatChecker(
			*antispamConfig.AntispamWeChatAppId,
			*antispamConfig.AntispamWeChatSecret,
			openid)
	}

	var err error
	resp, err = checker.Check(c.Request.Context(), req.DataID, req.Data)
	if err != nil {
		an.Error(c, controllers.ErrorAntispamCheckFailed, err)
	} else {
		respByte, _ := json.Marshal(resp)
		redis.Set(cacheKey, string(respByte), 30*24*time.Hour)
		log.Debug(c.Request.Context(), "网易易盾屏蔽字服务", "Data", req.Data, "Result", resp, "HitCache", false)
		an.Success(c, resp)
	}
}
