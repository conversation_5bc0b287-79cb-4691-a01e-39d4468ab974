package v1

import (
	"zeus/controllers/sdk/v1/account"
	"zeus/controllers/sdk/v1/anti_addiction"
	"zeus/controllers/sdk/v1/antispam"
	"zeus/controllers/sdk/v1/game"
	"zeus/controllers/sdk/v1/order"
	"zeus/controllers/sdk/v1/robust_config"
	"zeus/controllers/sdk/v1/role"
	"zeus/controllers/sdk/v1/subscription"
	"zeus/controllers/sdk/v1/translate"
	"zeus/controllers/sdk/v1/user"

	"github.com/gin-gonic/gin"
)

func SetupRouter(routerGroup *gin.RouterGroup) {
	game.SetupRouter(routerGroup)
	order.SetupRouter(routerGroup)
	role.SetupRouter(routerGroup)
	subscription.SetupRouter(routerGroup)
	user.SetupRouter(routerGroup)
	anti_addiction.SetupRouter(routerGroup)
	antispam.SetupRouter(routerGroup)
	account.SetupRouter(routerGroup)
	robust_config.SetupRouter(routerGroup)
	translate.SetupRouter(routerGroup)
}
