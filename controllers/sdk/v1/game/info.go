package game

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"zeus/common/constants"
	"zeus/pkg/redis"
	"zeus/services/game"

	"github.com/gin-gonic/gin"

	"zeus/controllers"
	"zeus/services/settings/converter"
)

type InfoForm struct {
	Appid string `json:"appid" binding:"required"`
}

type GameBaseInfo struct {
	Id          int    `json:"id"`
	Name        string `json:"name"`
	ServiceMail string `json:"service_mail"`
	Game        string `json:"game"`
	Platform    string `json:"platform"`
	Timestamp   int64  `json:"timestamp"`
}
type GameDetailInfo struct {
	SamplingRate           float64 `json:"sampling_rate"`
	FreshdeskAppkey        string  `json:"freshdesk_appkey,omitempty"`
	FreshdeskDomain        string  `json:"freshdesk_domain,omitempty"`
	FreshdeskAppid         string  `json:"freshdesk_appid,omitempty"`
	TwitterKey             string  `json:"twitter_key,omitempty"`
	TwitterSecret          string  `json:"twitter_secret,omitempty"`
	GoogleClientId         string  `json:"google_client_id,omitempty"`
	LineAppid              string  `json:"line_appid,omitempty"`
	WechatAppid            string  `json:"wechat_appid,omitempty"`
	FbAppid                string  `json:"fb_appid,omitempty"`
	FbSecret               string  `json:"fb_secret,omitempty"`
	FbClientToken          string  `json:"fb_client_token,omitempty"`
	QQAppid                string  `json:"qq_appid,omitempty"`
	NumberVerifyBusinessID string  `json:"number_verify_business_id,omitempty"`
	IsRechargeOpen         int     `json:"is_recharge_open"`
}

func (i GameDetailInfo) GetLabel() string {
	return "any"
}

func (i GameDetailInfo) GetType() string {
	return "any"
}

type GameInfo struct {
	GameBaseInfo
	GameDetailInfo
}

// GameInfo
// @Summary	获取游戏信息
// @Tags	SDK Game
// @Accept	json
// @Produce json
// @Param	person	body	InfoForm	true	"获取游戏信息"
// @Success 200 {object} controllers.SuccessResponse{result=GameInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /game/info [post]
func (g Controller) GameInfo(c *gin.Context) {
	var form InfoForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		g.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	cacheKey := fmt.Sprintf("game_info_%s", form.Appid)
	cacheContent := redis.Get(cacheKey)
	var gameInfo GameInfo

	if cacheContent == "" {
		dbGame, err := game.GetGameByAppid(form.Appid)
		if err != nil {
			g.Error(c, controllers.ErrorParamInvalid, errors.New("无效的appid"))
			return
		}

		var detailInfo GameDetailInfo
		converter.QueryByAppid(&detailInfo, form.Appid)

		gameInfo = GameInfo{
			GameBaseInfo: GameBaseInfo{
				Id:          dbGame.Id,
				Name:        dbGame.Name,
				Game:        dbGame.Name,
				Platform:    dbGame.Platform,
				ServiceMail: dbGame.ServiceMail,
			},
			GameDetailInfo: detailInfo,
		}
		data, _ := json.Marshal(gameInfo)
		redis.Set(cacheKey, string(data), time.Second*constants.GameInfoTimeout)
	} else {
		json.Unmarshal([]byte(cacheContent), &gameInfo)
	}

	gameInfo.Timestamp = time.Now().UnixMilli()
	g.Success(c, gameInfo)
}
