package game

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"zeus/controllers"
	"zeus/pkg/redis"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/field"

	"github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	. "github.com/smartystreets/goconvey/convey"

	"zeus/models"
)

// TestGameInfo 测试游戏获取接口
func TestGameInfo(t *testing.T) {
	gomonkey.ApplyFunc(redis.Set, func(key string, data interface{}, duration time.Duration) error {
		return nil
	})

	QueryMockFunc := gomonkey.ApplyFunc(converter.Query, func(config field.BaseConfiger, _ converter.Filter) {
	})

	defer QueryMockFunc.Reset()

	Convey("VerifySandBox", t, func() {
		Convey("one func for succ", func() {
			gomonkey.ApplyFunc(redis.Get, func(key string) string {
				return ""
			})

			gomonkey.ApplyFunc(game.GetGameByAppid, func(appID string) (*models.Game, error) {
				g := models.Game{}
				return &g, nil
			})

			r := gin.Default()
			g := r.Group("/")
			SetupRouter(g)

			args := InfoForm{
				Appid: "test_appid",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/game/info", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			assert.Equal(t, output, outputExpect)
		})

		Convey("参数缺失", func() {
			r := gin.Default()
			g := r.Group("/")
			SetupRouter(g)

			args := InfoForm{}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/game/info", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("未命中缓存", func() {
			gomonkey.ApplyFunc(redis.Get, func(key string) string {
				return `{"test_key": "test_content"}`
			})

			r := gin.Default()
			g := r.Group("/")
			SetupRouter(g)

			args := InfoForm{
				Appid: "test_appid",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/game/info", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			assert.Equal(t, output, outputExpect)
		})

		Convey("获取Game信息失败", func() {
			gomonkey.ApplyFunc(redis.Get, func(key string) string {
				return ""
			})

			gomonkey.ApplyFunc(game.GetGameByAppid, func(appID string) (*models.Game, error) {
				return nil, errors.New("game not found")
			})

			r := gin.Default()
			g := r.Group("/")
			SetupRouter(g)

			args := InfoForm{
				Appid: "test_appid",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/game/info", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorParamInvalid))
		})
	})
}
