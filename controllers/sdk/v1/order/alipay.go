package order

import (
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
)

type StrInfo struct {
	OrderStr string `json:"order_str"`
	OrderID  string `json:"order_id"`
}

// AliPay
// @Summary	支付宝支付
// @Tags	SDK AliPay
// @Accept	json
// @Produce json
// @Param	order	body	schema.AliExchangeForm	true	"Ali支付"
// @Success 200 {object} controllers.SuccessResponse{result=StrInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /order/ali-exchange [post]
func (o Controller) AliPay(c *gin.Context) {
	var order schema.AliExchangeForm
	err := c.ShouldBindJSON(&order)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, err := game.GetGameByAppid(order.AppID)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	var baseConfig payment.Payment
	converter.QueryByAppid(&baseConfig, order.AppID)

	var config payment.AliPay
	converter.QueryByAppid(&config, order.AppID)

	if *baseConfig.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		o.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}

	dbUser, _ := services.GetUserByUserID(c, order.UserID)
	if dbUser.ID == 0 {
		o.Error(c, controllers.ErrorAccountNotExists, errors.New("用户不存在"))
		return
	}

	var orderResult *models.Order
	orderResult, orderStr, err := pay.CreateAliOrder(c, *dbUser, *dbGame, order)
	if err != nil {
		o.Error(c, controllers.ErrorOrderSign, err)
		return
	}

	o.Success(c, StrInfo{orderStr, orderResult.OrderId})
}

// AliOrderVerify
// @Summary	验证Ali支付结果
// @Tags	SDK AliPay
// @Accept	json
// @Produce json
// @Param	order	body  schema.AliVerify	true	"支付验证"
// @Success 200 {object} controllers.SuccessResponse{result=string}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /order/ali-verify [post]
func (o Controller) AliOrderVerify(c *gin.Context) {
	var orderForm schema.AliVerify
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbOrder, err := services.CheckOrder(c, orderForm.OrderID)
	if err != nil {
		o.Error(c, controllers.ErrorOrderNotExists, err)
		return
	}

	if dbOrder.PayStatus == constants.PaymentSuccess {
		o.Success(c, constants.PaySuccessMessage)
		return
	}

	lockKey := fmt.Sprintf("transcation_%s_%s", orderForm.AppID, dbOrder.OrderId)
	err = redis.Lock(lockKey, time.Second*constants.LockTimeout)
	if err != nil {
		o.Error(c, controllers.ErrorRequestHasBeenProcessed, errors.New("订单正在被处理"))
		return
	}
	defer redis.UnLock(lockKey)

	aliOrder, _ := pay.GetOrder(constants.PayWayAli, orderForm.AppID)

	OrderPayResult, err := aliOrder.VerifyPayOrder(c, &dbOrder)
	if err != nil {
		o.Error(c, controllers.ErrorOrderSign, err)
		return
	}
	err = services.UpdatePayOrder(c, OrderPayResult, &dbOrder, constants.PayWayAli)
	if err != nil {
		o.Error(c, controllers.ErrorDBError, err)
		return
	}

	if OrderPayResult.Status == constants.PaymentUnpaid {
		o.Error(c, controllers.ErrorOrderSign, errors.New(constants.PayFailMessage))
		return
	}

	db := o.GetDB(c)
	_ = services.Notify(c.Request.Context(), db, &dbOrder, services.NotifyOptions{})

	o.Success(c, constants.PaySuccessMessage)
}
