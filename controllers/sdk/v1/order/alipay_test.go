package order

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/pkg/validator"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin/binding"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestAlipayOrderExchange(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{
			Id: 1,
		}, nil
	})

	ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
		return &models.User{
			ID: 1111,
		}, nil
	})
	ApplyFunc(services.GenerateOrderID, func(_ int, _ int, _ ...string) string {
		return "test_order_id"
	})
	ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
		return nil
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentAliPay":
			aliInfo := config.(*payment.AliPay)
			status := 1
			aliInfo.OpenStatus = &status
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	Convey("TestAlipayOrder", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(pay.CreateAliOrder, func(_ *gin.Context, _ models.User, _ models.Game, _ schema.AliExchangeForm) (*models.Order, string, error) {
				return &models.Order{}, "orderStr", nil
			})

			args := schema.AliExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("参数缺失", func() {
			args := schema.AliExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("无效的AppID", func() {
			func1 := ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{
					Id: 1,
				}, errors.New("AppID is invalid")
			})
			defer func1.Reset()

			args := schema.AliExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("支付关闭", func() {
			QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
				switch config.GetType() {
				case "PaymentAliPay":
					aliInfo := config.(*payment.AliPay)
					status := 0
					aliInfo.OpenStatus = &status
				case "Payment":
					payConfig := config.(*payment.Payment)
					status := constants.IsRechargeOpen
					payConfig.IsRechargeOpen = &status
				}
			})
			defer QueryMockFunc.Reset()

			args := schema.AliExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorRechargeClose))
		})

		Convey("用户不存在", func() {
			func1 := ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{}, errors.New("User Not Found")
			})

			defer func1.Reset()

			args := schema.AliExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorAccountNotExists))
		})
		Convey("创建订单失败", func() {
			ApplyFunc(pay.CreateAliOrder, func(_ *gin.Context, _ models.User, _ models.Game, _ schema.AliExchangeForm) (*models.Order, string, error) {
				return &models.Order{}, "orderStr", errors.New("Create Order Failed")
			})

			args := schema.AliExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorOrderSign))
		})
	})
}

func TestAlipayOrderVerify(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(services.CheckOrder, func(_ *gin.Context, _ string) (models.Order, error) {
		return models.Order{}, nil
	})
	ApplyFunc(services.Notify, func(_ context.Context, _ *gorm.DB, _ *models.Order, _ services.NotifyOptions) error {
		return nil
	})
	var c *Controller
	ApplyMethod(reflect.TypeOf(c), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
		return &gorm.DB{}
	})

	ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
		return nil
	})

	ApplyFunc(redis.UnLock, func(key string) int64 {
		return 0
	})

	Convey("TestAliOrderVerify", t, func() {
		Convey("one func for succ", func() {
			var ali *pay.AliOrder
			ApplyMethod(reflect.TypeOf(ali), "VerifyPayOrder", func(_ *pay.AliOrder, _ *gin.Context, _ *models.Order) (schema.OrderPayResult, error) {
				return schema.OrderPayResult{Status: 1}, nil
			})
			updatePayOrderFunc := ApplyFunc(services.UpdatePayOrder, func(_ *gin.Context, _ schema.OrderPayResult, _ *models.Order, _ int) error {
				return nil
			})
			defer updatePayOrderFunc.Reset()

			GetGameByAppidMockFunc := ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			defer GetGameByAppidMockFunc.Reset()
			args := schema.AliVerify{
				AppID:   "test_appid",
				OrderID: "test_order_id",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("同时请求订单验证", func() {
			apply1 := ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
				return errors.New("Get Lock failed")
			})
			defer apply1.Reset()

			var ali *pay.AliOrder
			ApplyMethod(reflect.TypeOf(ali), "VerifyPayOrder", func(_ *pay.AliOrder, _ *gin.Context, _ *models.Order) (schema.OrderPayResult, error) {
				return schema.OrderPayResult{Status: 1}, nil
			})

			GetGameByAppidMockFunc := ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			defer GetGameByAppidMockFunc.Reset()
			args := schema.AliVerify{
				AppID:   "test_appid",
				OrderID: "test_order_id",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorRequestHasBeenProcessed)
		})

		Convey("订单已支付", func() {
			func1 := ApplyFunc(services.CheckOrder, func(_ *gin.Context, _ string) (models.Order, error) {
				return models.Order{PayStatus: int(constants.PaymentSuccess)}, nil
			})
			defer func1.Reset()

			args := schema.AliVerify{
				AppID:   "test_appid",
				OrderID: "test_order_id",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("参数缺失", func() {
			args := schema.AliVerify{
				AppID: "test_appid",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("订单不存在", func() {
			func1 := ApplyFunc(services.CheckOrder, func(_ *gin.Context, _ string) (models.Order, error) {
				return models.Order{}, errors.New("Check Order Failed")
			})

			defer func1.Reset()

			args := schema.AliVerify{
				AppID:   "test_appid",
				OrderID: "test_order_id",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorOrderNotExists))
		})

		Convey("更新订单失败", func() {
			var ali *pay.AliOrder
			ApplyMethod(reflect.TypeOf(ali), "VerifyPayOrder", func(_ *pay.AliOrder, _ *gin.Context, _ *models.Order) (schema.OrderPayResult, error) {
				return schema.OrderPayResult{Status: 1}, nil
			})

			func1 := ApplyFunc(services.CheckOrder, func(_ *gin.Context, _ string) (models.Order, error) {
				return models.Order{}, nil
			})

			defer func1.Reset()

			func2 := ApplyFunc(services.UpdatePayOrder, func(_ *gin.Context, _ schema.OrderPayResult, _ *models.Order, _ int) error {
				return errors.New("UpdatePayOrder Failed")
			})
			defer func2.Reset()

			args := schema.AliVerify{
				AppID:   "test_appid",
				OrderID: "test_order_id",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorDBError))
		})

		Convey("订单未支付", func() {
			var ali *pay.AliOrder
			ApplyMethod(reflect.TypeOf(ali), "VerifyPayOrder", func(_ *pay.AliOrder, _ *gin.Context, _ *models.Order) (schema.OrderPayResult, error) {
				return schema.OrderPayResult{Status: 0}, nil
			})
			func1 := ApplyFunc(services.UpdatePayOrder, func(_ *gin.Context, _ schema.OrderPayResult, _ *models.Order, _ int) error {
				return nil
			})
			defer func1.Reset()

			args := schema.AliVerify{
				AppID:   "test_appid",
				OrderID: "test_order_id",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ali-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorOrderSign))
		})
	})
}
