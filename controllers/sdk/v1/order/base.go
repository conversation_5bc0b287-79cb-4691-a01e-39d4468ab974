package order

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"zeus/controllers"
)

type Controller struct {
	controllers.BaseController
}

var Order = Controller{}

func (o *Controller) GetDB(c *gin.Context) *gorm.DB {
	db := c.MustGet("db")
	return db.(*gorm.DB)
}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.POST("/order/wechat-exchange", Order.WeChatPay)
	routerGroup.POST("/order/wechat-verify", Order.WeChatOrderVerify)
	routerGroup.POST("/order/ali-exchange", Order.AliPay)
	routerGroup.POST("/order/ali-verify", Order.AliOrderVerify)
	routerGroup.POST("/order/ios-exchange", Order.IOSExchangeOrder)
	routerGroup.POST("/order/ios-verify", Order.IOSVerifyOrder)
	routerGroup.POST("/order/google-exchange", Order.GoogleExchangeOrder)
	routerGroup.POST("/order/huawei-exchange", Order.HuaweiExchangeOrder)
	routerGroup.POST("/order/huawei-verify", Order.HuaweiVerify)
	routerGroup.POST("/order/mockpay-exchange", Order.MockPayExchangeOrder)
}
