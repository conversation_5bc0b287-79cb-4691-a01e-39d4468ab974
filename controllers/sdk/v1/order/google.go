package order

import (
	"errors"
	"time"

	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/utils"

	"github.com/gin-gonic/gin"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
)

type GoogleOrderInfo struct {
	GooglePayKey string `json:"google_pay_key"`
	OrderID      string `json:"order_id"`
}

// GoogleExchangeOrder
// @Summary 创建google订单
// @Tags	SDK GooglePay
// @Accept	json
// @Produce json
// @Param	person 	body  schema.GoogleExchangeForm	true	"Google支付"
// @Success 200 {object} controllers.SuccessResponse{result=GoogleOrderInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /order/google-exchange [post]
func (o Controller) GoogleExchangeOrder(c *gin.Context) {
	var orderForm schema.GoogleExchangeForm
	err := c.Should<PERSON>ind<PERSON>(&orderForm)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, _ := game.GetGameByAppid(orderForm.AppID)

	var baseConfig payment.Payment
	converter.QueryByAppid(&baseConfig, orderForm.AppID)

	var config payment.GooglePay
	converter.QueryByAppid(&config, orderForm.AppID)

	if *config.GooglePayKey == "" {
		o.Error(c, controllers.ErrorEmptyParam, errors.New("未配置google加密key"))
		return
	}
	if *baseConfig.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		o.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}

	dbUser, _ := services.GetUserByUserID(c, orderForm.UserID)
	if dbUser.ID == 0 {
		o.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	orderID := services.GenerateOrderID(constants.PayWayGoogle, constants.OrderIDTokenSize)
	dbOrder := &models.Order{}

	dbOrder.GameId = dbGame.Id
	dbOrder.OrderId = orderID
	dbOrder.Account = dbUser.Account
	dbOrder.UserId = dbUser.ID
	dbOrder.RoleId = orderForm.RoleID
	dbOrder.ProductId = orderForm.ProductID
	dbOrder.Price = orderForm.Price
	dbOrder.PayWay = constants.PayWayGoogle
	dbOrder.Extend = orderForm.Extend
	dbOrder.Device = orderForm.Device
	dbOrder.RoleName = orderForm.RoleName
	dbOrder.PayNotifyUrl = orderForm.PayNotifyURL
	dbOrder.CreateTime = time.Now()
	dbOrder.Platform = constants.PlatformAndroid
	dbOrder.ServerId = orderForm.ServerID
	dbOrder.ServerName = orderForm.ServerName
	dbOrder.Vip = utils.ConvertStringToInt(orderForm.Vip)
	dbOrder.Level = utils.ConvertStringToInt(orderForm.Level)

	if err := services.CreateOrder(c, dbOrder); err != nil {
		o.Error(c, controllers.ErrorDBError, errors.New("创建google订单失败"))
		return
	}
	o.Success(c, GoogleOrderInfo{*config.GooglePayKey, dbOrder.OrderId})
}
