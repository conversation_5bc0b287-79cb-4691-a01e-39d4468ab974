package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/utils"
)

type HuaweiOrderInfo struct {
	OrderID string `json:"order_id"`
}

// HuaweiExchangeOrder
// @Summary 创建huawei订单
// @Tags	SDK HuaweiPay
// @Accept	json
// @Produce json
// @Param	person 	body  schema.HuaweiExchangeForm	true	"Huawei支付"
// @Success 200 {object} controllers.SuccessResponse{result=HuaweiOrderInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /order/huawei-exchange [post]
func (o Controller) HuaweiExchangeOrder(c *gin.Context) {
	var orderForm schema.HuaweiExchangeForm
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, _ := game.GetGameByAppid(orderForm.AppID)

	var pay payment.Payment
	converter.QueryByAppid(&pay, orderForm.AppID)

	var config payment.HuaweiPay
	converter.QueryByAppid(&config, orderForm.AppID)

	if *pay.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		o.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}

	dbUser, _ := services.GetUserByUserID(c, orderForm.UserID)
	if dbUser.ID == 0 {
		o.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	orderID := services.GenerateOrderID(constants.PayWayHuawei, constants.OrderIDTokenSize)
	dbOrder := &models.Order{}

	dbOrder.GameId = dbGame.Id
	dbOrder.OrderId = orderID
	dbOrder.Account = dbUser.Account
	dbOrder.UserId = dbUser.ID
	dbOrder.RoleId = orderForm.RoleID
	dbOrder.ProductId = orderForm.ProductID
	dbOrder.Price = orderForm.Price
	dbOrder.PayWay = constants.PayWayHuawei
	dbOrder.Extend = orderForm.Extend
	dbOrder.Device = orderForm.Device
	dbOrder.RoleName = orderForm.RoleName
	dbOrder.PayNotifyUrl = orderForm.PayNotifyURL
	dbOrder.CreateTime = time.Now()
	dbOrder.Platform = orderForm.PlatForm
	dbOrder.ServerId = orderForm.ServerID
	dbOrder.ServerName = orderForm.ServerName
	dbOrder.Vip = utils.ConvertStringToInt(orderForm.Vip)
	dbOrder.Level = utils.ConvertStringToInt(orderForm.Level)

	if err := services.CreateOrder(c, dbOrder); err != nil {
		o.Error(c, controllers.ErrorDBError, errors.New("创建华为订单失败"))
		return
	}

	o.Success(c, HuaweiOrderInfo{dbOrder.OrderId})
}

// HuaweiVerify
// @Summary 验证华为订单，验证成功通知CP
// @Tags	SDK-Order
// @Accept	json
// @Produce json
// @Param	order verify	body	schema.HuaweiVerifyForm	true	"华为支付验证"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /order/huawei-verify [post]
func (o Controller) HuaweiVerify(c *gin.Context) {
	db := o.GetDB(c)
	var orderForm schema.HuaweiVerifyForm
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, _ := game.GetGameByAppid(orderForm.AppID)
	var config payment.HuaweiPay
	converter.QueryByAppid(&config, orderForm.AppID)

	if *config.HuaweiClientId == "" || *config.HuaweiClientSecret == "" || *config.HuaweiPublicKey == "" {
		o.Error(c, controllers.ErrorOrderSign, errors.New("华为支付缺少配置参数，无法完成支付"))
		return
	}

	huaweiPayClient := pay.HuaweiPayClient{
		ClientID:     *config.HuaweiClientId,
		ClientSecret: *config.HuaweiClientSecret,
		PublicKey:    *config.HuaweiPublicKey,
	}

	// 校验订单数据签名
	err = huaweiPayClient.VerifyOrderSignature(orderForm.PurchaseData, orderForm.DataSignature)
	if err != nil {
		o.Error(c, controllers.ErrorOrderSign, errors.New("华为支付订单数据验签失败"))
		return
	}

	// 被支付订单不存在.
	filter := map[string]interface{}{"order_id": orderForm.DeveloperPayload}
	dbOrder, _ := services.QueryOrder(c, filter)
	if dbOrder.Id == 0 {
		o.Error(c, controllers.ErrorOrderNotExists, fmt.Errorf("订单%s不存在", orderForm.DeveloperPayload))
		return
	}

	// 订单已被支付.
	if dbOrder.PayStatus == constants.PaymentSuccess {
		o.Success(c, "success")
		return
	}

	lockKey := fmt.Sprintf("transcation_%s_%s", orderForm.AppID, dbOrder.OrderId)
	err = redis.Lock(lockKey, time.Second*constants.LockTimeout)
	if err != nil {
		o.Error(c, controllers.ErrorRequestHasBeenProcessed, errors.New("订单正在被处理"))
		return
	}
	defer redis.UnLock(lockKey)

	// 校验订单支付token
	purchaseData, err := huaweiPayClient.VerifyHuaweiOrder(c.Request.Context(), dbGame, orderForm.PurchaseToken, orderForm.ProductID)
	if err != nil {
		errorMessage := fmt.Sprintf("报错信息: %s, 华为支付订单认证失败导致无法完成支付操作", err)
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			o.Error(c, controllers.ErrorDBError, err)
			return
		}
		o.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}
	log.Debug(c.Request.Context(), "华为支付数据校验结果", "purchaseData", purchaseData, "order_id", orderForm.DeveloperPayload)

	if purchaseData.OrderId == "" {
		errorMessage := fmt.Sprintf("报错信息: 订单%s, 华为支付订单认证失败, 认证数据中缺少华为orderId字段", orderForm.DeveloperPayload)
		o.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}

	// 校验order_id是否一致
	if orderForm.DeveloperPayload != purchaseData.DeveloperPayload {
		errorMessage := fmt.Sprintf("报错信息: 华为支付订单认证失败, 订单ID：%s与验证数据中的订单ID%s不一致", orderForm.DeveloperPayload, purchaseData.DeveloperPayload)
		o.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}

	// 验证订单交易状态
	if purchaseData.PurchaseState != nil {
		if int64(*purchaseData.PurchaseState) != constants.PaySuccessStatus {
			o.Error(c, controllers.ErrorOrderUnpaid, errors.New("华为支付订单校验失败, 请确认此笔订单玩家是否付款成功"))
			return
		}
	}

	if purchaseData.PackageName != *config.HuaweiPackageName {
		o.Error(c, controllers.ErrorOrderSign, fmt.Errorf("华为支付订单校验失败，验证包名:%s与配置的%s不一致", purchaseData.PackageName, *config.HuaweiPackageName))
		return
	}

	// 使用华为服务的订单ID验证重复订单
	filter = map[string]interface{}{
		"pay_id":     purchaseData.OrderId,
		"pay_status": constants.PaymentSuccess,
	}

	dbOrder2, _ := services.QueryOrder(c, filter)
	if dbOrder2.OrderId != "" {
		errorMessage := "华为支付订单重复, 无法完成支付操作"
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			o.Error(c, controllers.ErrorDBError, err)
			return
		}
		o.Error(c, controllers.ErrorOrderRepeated, errors.New(errorMessage))
		return
	}

	// 校验ProductId
	if dbOrder.ProductId != orderForm.ProductID || dbOrder.ProductId != purchaseData.ProductId {
		errorMessage := fmt.Sprintf("华为支付实际支付的商品为%s, 与订单商品%s不一致", purchaseData.ProductId, dbOrder.ProductId)
		o.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}

	// 校验购买类型
	if purchaseData.PurchaseType != nil {
		if *purchaseData.PurchaseType == 0 {
			dbOrder.OrderAttribute = constants.TestAccount
		}
	} else {
		dbOrder.OrderAttribute = constants.NormalOrder
	}

	// 修改订单状态.
	dbOrder.PayId = purchaseData.PayOrderId
	dbOrder.PayTime = time.Now()
	dbOrder.PayIp = utils.GetClientIP(c)
	dbOrder.PayAmount = float64(purchaseData.Price) // 商品实际价格*100以后的值
	dbOrder.PayCurrency = purchaseData.Currency
	dbOrder.PayStatus = constants.PaymentSuccess
	dbOrder.PayProductId = orderForm.ProductID
	purchaseDataJson, _ := json.Marshal(purchaseData)
	dbOrder.Receipt = string(purchaseDataJson)
	dbOrder.VerifyStatus = constants.VerifyPassed

	err = services.SaveOrder(c, dbOrder)
	if err != nil {
		o.Error(c, controllers.ErrorDBError, err)
		return
	}

	services.Notify(c.Request.Context(), db, dbOrder, services.NotifyOptions{})

	o.Success(c, "success")
}
