package order

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/pkg/validator"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin/binding"
	"gorm.io/gorm"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
)

func TestHuaweiPayOrderExchange(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{
			Id: 1,
		}, nil
	})

	ApplyFunc(services.UpdateErrorMessage, func(_ *gin.Context, _ string, _ models.Order) error {
		return nil
	})

	var c *gin.Context
	ApplyMethod(reflect.TypeOf(c), "MustGet", func(_ *gin.Context, key string) any {
		return &gorm.DB{}
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentHuaweiPay":
			info := config.(*payment.HuaweiPay)
			status := 1
			info.OpenStatus = &status
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
		return nil
	})

	ApplyFunc(redis.UnLock, func(key string) int64 {
		return 0
	})

	Convey("TestHuaweiPayOrderExchange", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			args := schema.GoogleExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("参数缺失", func() {
			args := schema.GoogleExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:    "test_appid",
					UserID:   1111,
					Price:    10,
					Extend:   "test_Extend",
					Device:   "test_device",
					RoleName: "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("用户不存在", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{ID: 0}, errors.New("User not found")
			})
			args := schema.GoogleExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorUserNotExists))
		})

		Convey("创建订单失败", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return errors.New("Create Order failed")
			})

			args := schema.GoogleExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorDBError))
		})
	})
}

func TestHuaweiPayOrderVerify(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{
			Id: 1,
		}, nil
	})

	ApplyFunc(services.Notify, func(_ context.Context, _ *gorm.DB, _ *models.Order, _ services.NotifyOptions) error {
		return nil
	})

	var c *gin.Context
	ApplyMethod(reflect.TypeOf(c), "MustGet", func(_ *gin.Context, key string) any {
		return &gorm.DB{}
	})

	huaweiPayClient := &pay.HuaweiPayClient{}

	ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyOrderSignature", func(_ *pay.HuaweiPayClient, _ string, _ string) error {
		return nil
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentHuaweiPay":
			info := config.(*payment.HuaweiPay)
			status := 1
			info.OpenStatus = &status
			huaweiClientId := "test_huawei_cliend_id"
			info.HuaweiClientId = &huaweiClientId
			huaweiClientSecret := "test_huawei_client_secret"
			info.HuaweiClientSecret = &huaweiClientSecret
			huaweiPublicKey := "test_huawei_public_key"
			info.HuaweiPublicKey = &huaweiPublicKey
			huaweiPackageName := "test_package_name"
			info.HuaweiPackageName = &huaweiPackageName

		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	Convey("TestHuaweiPayOrderVerify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{Id: 1, OrderId: "", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 0
			testOrder := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "test_product_id",
					PurchaseType:     &testOrder,
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("Normal Order Success", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{Id: 1, OrderId: "", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "test_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("表单异常", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{Id: 1, OrderId: "", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "test_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:         "test_appid",
				PurchaseToken: "test_token",
				ProductID:     "test_product_id",
				PayCurrency:   "test_pay_currency",
				PayAmount:     "10",
				PurchaseData:  "test_purchase_data",
				DataSignature: "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("订单不存在", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				return &models.Order{}, errors.New("Order not found")
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "test_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorOrderNotExists))
		})

		Convey("订单已被支付", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				return &models.Order{Id: 1, PayStatus: constants.PaymentSuccess}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "test_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.Success))
		})

		Convey("校验票据信息失败", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{Id: 1, OrderId: "", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return errors.New("Save Order Error")
			})

			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{}, errors.New("VerifyHuaweiOrder Error")
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorOrderSign))
		})

		Convey("票据中缺少订单ID", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{Id: 1, OrderId: "", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "test_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorOrderSign))
		})
		Convey("票据中DeveloperPayload数据异常", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{Id: 1, OrderId: "", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "other_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "test_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorOrderSign))
		})

		Convey("票据中的支付状态异常", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{Id: 1, OrderId: "", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 1
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "test_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorOrderUnpaid))
		})

		Convey("票据中的包名不匹配", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{Id: 1, OrderId: "", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "other_package_name",
					ProductId:        "test_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorOrderSign))
		})

		Convey("票据信息重复支付", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				return &models.Order{Id: 1, OrderId: "repeated_order", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "test_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorOrderRepeated))
		})

		Convey("支付商品不匹配", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{Id: 1, OrderId: "", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			status := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "other_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorOrderSign))
		})

		Convey("保存订单失败", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{Id: 1, OrderId: "", ProductId: "test_product_id", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return errors.New("Save Order Error")
			})

			status := 0
			ApplyMethod(reflect.TypeOf(huaweiPayClient), "VerifyHuaweiOrder", func(_ *pay.HuaweiPayClient, _ context.Context, _ *models.Game, _ string, _ string) (pay.HuaweiPurchaseData, error) {
				return pay.HuaweiPurchaseData{
					OrderId:          "test_developer_payload",
					DeveloperPayload: "test_developer_payload",
					PurchaseState:    &status,
					PackageName:      "test_package_name",
					ProductId:        "test_product_id",
				}, nil
			})

			args := schema.HuaweiVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "test_token",
				ProductID:        "test_product_id",
				DeveloperPayload: "test_developer_payload",
				PackageName:      "test_package_name",
				PayCurrency:      "test_pay_currency",
				PayAmount:        "10",
				PurchaseData:     "test_purchase_data",
				DataSignature:    "test_data_signature",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/huawei-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorDBError))
		})
	})
}
