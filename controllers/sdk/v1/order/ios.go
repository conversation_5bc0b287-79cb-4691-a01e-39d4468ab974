package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/notify"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay/apple"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/utils"

	"zeus/common/log"

	"github.com/gin-gonic/gin"
)

type IOSOrderInfo struct {
	OrderID string `json:"order_id"`
}

const messageTemplate = "## ❗ Zeus疑似异常订单：\n " +
	"* 应用： %s (%s) \n " +
	"* 订单ID: %s \n " +
	"* 渠道: %s \n " +
	"* 角色名称: %s \n " +
	"* 角色ID: %s \n " +
	"* 区服ID: %s \n " +
	"* 商品ID: %s \n " +
	"* 创建时间: %s \n " +
	"* 异常信息: %s"

// IOSExchangeOrder
// @Summary	生成ios订单
// @Tags	SDK iOSPay
// @Accept	json
// @Produce json
// @Param	person	body	schema.IOSExchangeForm	true	"生成ios订单"
// @Success 200 {object} controllers.SuccessResponse{result=IOSOrderInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /order/ios-exchange [post]
func (o Controller) IOSExchangeOrder(c *gin.Context) {
	order := schema.IOSExchangeForm{}
	err := c.ShouldBindJSON(&order)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, err := game.GetGameByAppid(order.AppID)
	if err != nil {
		o.Error(c, controllers.ErrorParamInvalid, err)
		return
	}

	var baseConfig payment.Payment
	converter.QueryByAppid(&baseConfig, order.AppID)

	var config payment.ApplePay
	converter.QueryByAppid(&config, order.AppID)

	if *baseConfig.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		o.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}

	// 账号查询
	dbUser, _ := services.GetProjectUserByUserID(c, order.UserID)
	if dbUser.ID == 0 {
		o.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	orderID := services.GenerateOrderID(constants.PayWayApple, constants.OrderIDTokenSize)
	dbOrder := &models.Order{}

	dbOrder.GameId = dbGame.Id
	dbOrder.OrderId = orderID
	dbOrder.Account = dbUser.Account
	dbOrder.UserId = dbUser.ID
	dbOrder.RoleId = order.RoleID
	dbOrder.ProductId = order.ProductID
	dbOrder.Price = order.Price
	dbOrder.Extend = order.Extend
	dbOrder.Device = order.Device
	dbOrder.RoleName = order.RoleName
	dbOrder.CreateTime = time.Now()
	dbOrder.Platform = order.PlatForm
	dbOrder.PayWay = constants.PayWayApple
	dbOrder.ServerId = order.ServerID
	dbOrder.ServerName = order.ServerName
	dbOrder.Vip = utils.ConvertStringToInt(order.Vip)
	dbOrder.Level = utils.ConvertStringToInt(order.Level)
	dbOrder.PayNotifyUrl = order.PayNotifyURL

	if order.PlatForm == "" {
		dbOrder.Platform = constants.PlatformIOS
	}

	if err := services.CreateOrder(c, dbOrder); err != nil {
		o.Error(c, controllers.ErrorOrderCreateFail, errors.New("创建ios订单失败"))
		return
	}
	o.Success(c, IOSOrderInfo{dbOrder.OrderId})
}

// IOSVerifyOrder
// @Summary	验证ios订单
// @Tags	SDK iOSPay
// @Accept	json
// @Produce json
// @Param	person	body	schema.IOSOrderVerify	true	"验证ios订单"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /order/ios-verify [post]
func (o Controller) IOSVerifyOrder(c *gin.Context) {
	db := o.GetDB(c)
	orderForm := schema.IOSOrderVerify{}
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	// 被支付订单不存在
	filter := map[string]interface{}{
		"order_id": orderForm.OrderID,
	}
	dbOrder, _ := services.QueryOrder(c, filter)
	if dbOrder.OrderId == "" {
		o.Error(c, controllers.ErrorOrderNotExists, errors.New("被支付订单不存在"))
		return
	}
	// 订单已被支付
	if dbOrder.PayStatus == constants.PaymentSuccess {
		o.Success(c, "success")
		return
	}

	lockKey := fmt.Sprintf("transcation_%s_%s", orderForm.AppID, dbOrder.OrderId)
	err = redis.Lock(lockKey, time.Second*constants.LockTimeout)
	if err != nil {
		o.Error(c, controllers.ErrorRequestHasBeenProcessed, errors.New("订单正在被处理"))
		return
	}
	defer redis.UnLock(lockKey)

	paper := orderForm.Paper

	var isSandBox bool

	// 线上验证
	info, err := services.VerifyOnline(c.Request.Context(), paper, "order", "")
	if err != nil {
		errorMessage := fmt.Sprintf("报错信息：%s, 苹果支付订单线上验证失败导致无法完成支付操作, 请确认苹果支付配置是否正确", err)
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			o.Error(c, controllers.ErrorDBError, err)
			return
		}
		o.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}
	log.Debug(c.Request.Context(), "IOS支付线上验证结果", "data", info, "order_id", orderForm.OrderID)

	// 沙盒验证
	status, _ := info["status"].(float64)
	if int64(status) == constants.IOSVerifyStatus {
		isSandBox = true
		info, err = services.VerifySandBox(c.Request.Context(), paper, "order", "")
		dbOrder.OrderAttribute = constants.TestAccount
		if err != nil {
			errorMessage := fmt.Sprintf("报错信息：%s, 苹果支付订单沙盒验证失败导致无法完成支付操作, 请确认苹果支付配置是否正确", err)
			err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
			if err != nil {
				o.Error(c, controllers.ErrorDBError, err)
				return
			}
			o.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
			return
		}
		log.Debug(c.Request.Context(), "IOS支付沙盒验证结果", "data", info, "order_id", orderForm.OrderID)
	}

	// 订单验证失败
	if int64(info["status"].(float64)) != constants.PaySuccessStatus || info["receipt"] == nil {
		errorMessage := "苹果支付订单未支付成功导致校验失败, 请确认此笔订单玩家是否付款成功"
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			o.Error(c, controllers.ErrorDBError, err)
			return
		}
		o.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}
	data := info["receipt"].(map[string]interface{})

	// 临时修复跨App攻击问题
	dbGame, _ := game.GetGameByAppid(orderForm.AppID)
	var pay payment.Payment
	converter.QueryByAppid(&pay, orderForm.AppID)

	var config payment.ApplePay
	converter.QueryByAppid(&config, orderForm.AppID)

	bid := data["bid"].(string)

	if bid != *config.AppleBundleId {
		errorMessage := fmt.Sprintf("当前订单的BID为%s, 与项目%s不一致", bid, *config.AppleBundleId)
		o.Error(c, controllers.ErrorOrderRepeated, errors.New(errorMessage))
		return
	}

	// 重复订单验证
	filter = map[string]interface{}{
		"pay_id":     data["transaction_id"],
		"pay_status": constants.PaymentSuccess,
	}

	dbOrder2, _ := services.QueryOrder(c, filter)
	if dbOrder2.OrderId != "" {
		errorMessage := "苹果支付订单重复, 无法完成支付操作"
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			o.Error(c, controllers.ErrorDBError, err)
		}
		o.Error(c, controllers.ErrorOrderRepeated, errors.New(errorMessage))
		return
	}

	pay_product_id := data["product_id"].(string)

	// 临时修复, 添加订单实际支付的商品校验
	if pay_product_id != dbOrder.ProductId {
		errorMessage := fmt.Sprintf("实际支付的商品为%s, 与订单商品%s不一致", pay_product_id, dbOrder.ProductId)
		o.Error(c, controllers.ErrorOrderRepeated, errors.New(errorMessage))
		return
	}

	transcation_id := data["transaction_id"].(string)
	log.Debug(c.Request.Context(), "开始向Apple验证订单信息", "transcation_id", transcation_id)

	ac := apple.AppleClient{
		ISS:    *config.AppleISS,
		KeyID:  *config.AppleKeyID,
		BID:    *config.AppleBundleId,
		Secret: *config.AppleSecret,
	}

	transactionInfo, err := ac.VerifyOrderWithOriginTransactionID(c.Request.Context(), transcation_id, isSandBox)
	if err != nil {
		errorMessage := "与Apple校验订单异常"
		o.Error(c, controllers.ErrorOrderRepeated, errors.New(errorMessage))
		return
	}

	if transactionInfo.ProductId != data["product_id"].(string) {
		errorMessage := fmt.Sprintf("支付票据信息中商品信息%s与苹果后台的%s不一致", data["product_id"].(string), transactionInfo.ProductId)
		log.Warn(c.Request.Context(), fmt.Sprintf("Zeus订单%s: %s", orderForm.OrderID, errorMessage))
		dingTalkMessage := fmt.Sprintf(messageTemplate, dbGame.Name, dbOrder.Platform, dbOrder.OrderId,
			"苹果支付", dbOrder.RoleName, dbOrder.RoleId, dbOrder.ServerId, dbOrder.ProductId,
			dbOrder.CreateTime, errorMessage)
		if *pay.DingTalkToken != "" {
			d := notify.DingTalk{AccessToken: *pay.DingTalkToken}
			d.SendMarkDown(c, "苹果支付订单异常", dingTalkMessage)
		}
		o.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}
	if transactionInfo.OriginalPurchaseDate < dbOrder.CreateTime.UnixMilli() {
		errorMessage := fmt.Sprintf("支付票据内支付时间 %s, 比订单创建时间 %s早",
			time.Unix(transactionInfo.OriginalPurchaseDate/1000, 0).In(time.FixedZone("CST", 8*3600)),
			dbOrder.CreateTime)
		log.Warn(c.Request.Context(), fmt.Sprintf("Zeus订单 %s: %s", orderForm.OrderID, errorMessage))
		dingTalkMessage := fmt.Sprintf(messageTemplate, dbGame.Name, dbOrder.Platform, dbOrder.OrderId,
			"苹果支付", dbOrder.RoleName, dbOrder.RoleId, dbOrder.ServerId, dbOrder.ProductId,
			dbOrder.CreateTime, errorMessage)
		if *pay.DingTalkToken != "" {
			d := notify.DingTalk{AccessToken: *pay.DingTalkToken}
			d.SendMarkDown(c, "苹果支付订单异常", dingTalkMessage)
		}
	}

	if transactionInfo.RevocationDate > 0 {
		errorMessage := fmt.Sprintf("支付票据状态为已退款 , 退款时间为:%s",
			time.Unix(transactionInfo.RevocationDate/1000, 0).In(time.FixedZone("CST", 8*3600)))
		log.Warn(c.Request.Context(), fmt.Sprintf("Zeus订单%s: %s", orderForm.OrderID, errorMessage))
		dingTalkMessage := fmt.Sprintf(messageTemplate, dbGame.Name, dbOrder.Platform, dbOrder.OrderId,
			"苹果支付", dbOrder.RoleName, dbOrder.RoleId, dbOrder.ServerId, dbOrder.ProductId,
			dbOrder.CreateTime, errorMessage)
		if *pay.DingTalkToken != "" {
			d := notify.DingTalk{AccessToken: *pay.DingTalkToken}
			d.SendMarkDown(c, "苹果支付订单异常", dingTalkMessage)
		}
		o.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}

	payAmount, _ := strconv.ParseFloat(orderForm.PayAmount, 64)

	dbReceipt, _ := json.Marshal(info)
	// 修改订单状态
	dbOrder.PayId = data["transaction_id"].(string)
	dbOrder.PayTime = time.Now()
	dbOrder.PayIp = utils.GetClientIP(c)
	dbOrder.PayStatus = constants.PaymentSuccess
	dbOrder.PayCurrency = orderForm.PayCurrency
	dbOrder.PayAmount = payAmount
	dbOrder.PayProductId = data["product_id"].(string)
	dbOrder.Receipt = string(dbReceipt)
	dbOrder.VerifyStatus = constants.VerifyPassed

	if err := services.SaveOrder(c, dbOrder); err != nil {
		o.Error(c, controllers.ErrorDBError, err)
		return
	}
	services.Notify(c.Request.Context(), db, dbOrder, services.NotifyOptions{})
	o.Success(c, "success")
}
