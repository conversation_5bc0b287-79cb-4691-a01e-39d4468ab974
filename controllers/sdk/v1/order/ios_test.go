package order

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/pkg/validator"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay/apple"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin/binding"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestOrderIosExchangeInterface(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{
			Id: 1,
		}, nil
	})

	ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
		return &models.User{
			ID: 1111,
		}, nil
	})
	ApplyFunc(services.GetProjectUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
		return &models.User{
			ID: 1111,
		}, nil
	})
	ApplyFunc(services.GenerateOrderID, func(_ int, _ int, _ ...string) string {
		return "test_order_id"
	})
	ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
		return nil
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentAliPay":
			aliInfo := config.(*payment.AliPay)
			status := 1
			aliInfo.OpenStatus = &status
		case "PaymentWeChatPay":
			wechatInfo := config.(*payment.WeChatPay)
			status := 1
			wechatInfo.OpenStatus = &status
		case "PaymentApplePay":
			appleInfo := config.(*payment.ApplePay)
			status := 1
			appleInfo.OpenStatus = &status
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
		return nil
	})

	ApplyFunc(redis.UnLock, func(key string) int64 {
		return 0
	})

	Convey("TestIOSOrder", t, func() {
		Convey("one func for succ", func() {
			args := schema.IOSExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ios-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestIOSOrderVerify(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	var c *Controller
	ApplyMethod(reflect.TypeOf(c), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
		return &gorm.DB{}
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentApplePay":
			applePay := config.(*payment.ApplePay)
			applePassword := "ApplePassword"
			applePay.ApplePassword = &applePassword
			status := 1
			applePay.OpenStatus = &status
			appleISS := "AppleISS"
			applePay.AppleISS = &appleISS
			appleKeyID := "AppleKeyID"
			applePay.AppleKeyID = &appleKeyID
			appleBundleId := "com.topjoy.test"
			applePay.AppleBundleId = &appleBundleId
			appleSecret := "AppleSecret"
			applePay.AppleSecret = &appleSecret
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
			dingTalkToken := ""
			payConfig.DingTalkToken = &dingTalkToken
		}
	})
	defer QueryMockFunc.Reset()

	Convey("TestIOSOrderVerify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.CheckOrder, func(_ *gin.Context, _ string) (models.Order, error) {
				return models.Order{}, nil
			})
			ApplyFunc(services.Notify, func(_ context.Context, _ *gorm.DB, _ *models.Order, _ services.NotifyOptions) error {
				return nil
			})
			var c *Controller
			ApplyMethod(reflect.TypeOf(c), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
				return &gorm.DB{}
			})

			GetGameByAppidMockFunc := ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			defer GetGameByAppidMockFunc.Reset()

			ac := apple.AppleClient{}
			VerifyOrderWithOriginTransactionIDMock := ApplyMethod(reflect.TypeOf(&ac), "VerifyOrderWithOriginTransactionID",
				func(_ *apple.AppleClient, _ context.Context, _ string, _ bool) (schema.SignedTransactionInfo, error) {
					return schema.SignedTransactionInfo{
						BundleId:  "com.topjoy.test",
						ProductId: "com.topjoy.test.goods1",
					}, nil
				})
			defer VerifyOrderWithOriginTransactionIDMock.Reset()

			ApplyFunc(services.VerifyOnline, func(_ context.Context, _ string, _ string, _ string) (map[string]interface{}, error) {
				data := make(map[string]interface{})
				receipt := make(map[string]interface{})
				data["status"] = float64(0)
				data["receipt"] = receipt
				receipt["bid"] = "com.topjoy.test"
				receipt["product_id"] = "com.topjoy.test.goods1"
				receipt["transaction_id"] = "111777"
				return data, nil
			})

			ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{OrderId: "test_order_id", ProductId: "com.topjoy.test.goods1", CreateTime: time.Unix(1686219067, 0)}, nil
			})

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})

			args := schema.IOSOrderVerify{
				AppID:       "test_appid",
				OrderID:     "test_order_id",
				Paper:       "test_paper",
				PayCurrency: "test_pay_currency",
				PayAmount:   "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("未支付", func() {
			ApplyFunc(services.CheckOrder, func(_ *gin.Context, _ string) (models.Order, error) {
				return models.Order{}, nil
			})
			ApplyFunc(services.Notify, func(_ context.Context, _ *gorm.DB, _ *models.Order, _ services.NotifyOptions) error {
				return nil
			})
			var c *Controller
			ApplyMethod(reflect.TypeOf(c), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
				return &gorm.DB{}
			})

			GetGameByAppidMockFunc := ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			defer GetGameByAppidMockFunc.Reset()

			ac := apple.AppleClient{}
			VerifyOrderWithOriginTransactionIDMock := ApplyMethod(reflect.TypeOf(&ac), "VerifyOrderWithOriginTransactionID",
				func(_ *apple.AppleClient, _ context.Context, _ string, _ bool) (schema.SignedTransactionInfo, error) {
					return schema.SignedTransactionInfo{
						BundleId:  "com.topjoy.test",
						ProductId: "com.topjoy.test.goods1",
					}, nil
				})
			defer VerifyOrderWithOriginTransactionIDMock.Reset()

			ApplyFunc(services.VerifyOnline, func(_ context.Context, _ string, _ string, _ string) (map[string]interface{}, error) {
				data := make(map[string]interface{})
				receipt := make(map[string]interface{})
				data["status"] = float64(1)
				data["receipt"] = receipt
				receipt["bid"] = "com.topjoy.test"
				receipt["product_id"] = "com.topjoy.test.goods1"
				receipt["transaction_id"] = "111777"
				return data, nil
			})

			ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{OrderId: "test_order_id", ProductId: "com.topjoy.test.goods1", CreateTime: time.Unix(1686219067, 0)}, nil
			})

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})

			args := schema.IOSOrderVerify{
				AppID:       "test_appid",
				OrderID:     "test_order_id",
				Paper:       "test_paper",
				PayCurrency: "test_pay_currency",
				PayAmount:   "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorOrderSign))
		})

		Convey("沙盒支付", func() {
			ApplyFunc(services.CheckOrder, func(_ *gin.Context, _ string) (models.Order, error) {
				return models.Order{}, nil
			})
			ApplyFunc(services.Notify, func(_ context.Context, _ *gorm.DB, _ *models.Order, _ services.NotifyOptions) error {
				return nil
			})
			var c *Controller
			ApplyMethod(reflect.TypeOf(c), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
				return &gorm.DB{}
			})

			GetGameByAppidMockFunc := ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			defer GetGameByAppidMockFunc.Reset()

			ac := apple.AppleClient{}
			VerifyOrderWithOriginTransactionIDMock := ApplyMethod(reflect.TypeOf(&ac), "VerifyOrderWithOriginTransactionID",
				func(_ *apple.AppleClient, _ context.Context, _ string, _ bool) (schema.SignedTransactionInfo, error) {
					return schema.SignedTransactionInfo{
						BundleId:  "com.topjoy.test",
						ProductId: "com.topjoy.test.goods1",
					}, nil
				})
			defer VerifyOrderWithOriginTransactionIDMock.Reset()

			ApplyFunc(services.VerifyOnline, func(_ context.Context, _ string, _ string, _ string) (map[string]interface{}, error) {
				data := make(map[string]interface{})
				receipt := make(map[string]interface{})
				data["status"] = float64(constants.IOSVerifyStatus)
				data["receipt"] = receipt
				receipt["bid"] = "com.topjoy.test"
				receipt["product_id"] = "com.topjoy.test.goods1"
				receipt["transaction_id"] = "111777"
				return data, nil
			})

			ApplyFunc(services.VerifySandBox, func(_ context.Context, _ string, _ string, _ string) (map[string]interface{}, error) {
				data := make(map[string]interface{})
				receipt := make(map[string]interface{})
				data["status"] = float64(0)
				data["receipt"] = receipt
				receipt["bid"] = "com.topjoy.test"
				receipt["product_id"] = "com.topjoy.test.goods1"
				receipt["transaction_id"] = "111777"
				return data, nil
			})

			ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{OrderId: "test_order_id", ProductId: "com.topjoy.test.goods1", CreateTime: time.Unix(1686219067, 0)}, nil
			})

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})

			args := schema.IOSOrderVerify{
				AppID:       "test_appid",
				OrderID:     "test_order_id",
				Paper:       "test_paper",
				PayCurrency: "test_pay_currency",
				PayAmount:   "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("沙盒支付异常", func() {
			ApplyFunc(services.CheckOrder, func(_ *gin.Context, _ string) (models.Order, error) {
				return models.Order{}, nil
			})
			ApplyFunc(services.Notify, func(_ context.Context, _ *gorm.DB, _ *models.Order, _ services.NotifyOptions) error {
				return nil
			})
			var c *Controller
			ApplyMethod(reflect.TypeOf(c), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
				return &gorm.DB{}
			})

			GetGameByAppidMockFunc := ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			defer GetGameByAppidMockFunc.Reset()

			ac := apple.AppleClient{}
			VerifyOrderWithOriginTransactionIDMock := ApplyMethod(reflect.TypeOf(&ac), "VerifyOrderWithOriginTransactionID",
				func(_ *apple.AppleClient, _ context.Context, _ string, _ bool) (schema.SignedTransactionInfo, error) {
					return schema.SignedTransactionInfo{
						BundleId:  "com.topjoy.test",
						ProductId: "com.topjoy.test.goods1",
					}, nil
				})
			defer VerifyOrderWithOriginTransactionIDMock.Reset()

			ApplyFunc(services.VerifyOnline, func(_ context.Context, _ string, _ string, _ string) (map[string]interface{}, error) {
				data := make(map[string]interface{})
				receipt := make(map[string]interface{})
				data["status"] = float64(constants.IOSVerifyStatus)
				data["receipt"] = receipt
				receipt["bid"] = "com.topjoy.test"
				receipt["product_id"] = "com.topjoy.test.goods1"
				receipt["transaction_id"] = "111777"
				return data, nil
			})

			ApplyFunc(services.VerifySandBox, func(_ context.Context, _ string, _ string, _ string) (map[string]interface{}, error) {
				data := make(map[string]interface{})
				receipt := make(map[string]interface{})
				data["status"] = float64(constants.IOSVerifyStatus)
				data["receipt"] = receipt
				receipt["bid"] = "com.topjoy.test"
				receipt["product_id"] = "com.topjoy.test.goods1"
				receipt["transaction_id"] = "111777"
				return data, errors.New("SandBox failed")
			})

			ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{OrderId: "test_order_id", ProductId: "com.topjoy.test.goods1", CreateTime: time.Unix(1686219067, 0)}, nil
			})

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})

			args := schema.IOSOrderVerify{
				AppID:       "test_appid",
				OrderID:     "test_order_id",
				Paper:       "test_paper",
				PayCurrency: "test_pay_currency",
				PayAmount:   "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, float64(controllers.ErrorOrderSign))
		})

		Convey("错误-1 被支付订单不存在", func() {
			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{OrderId: "", ProductId: "com.topjoy.test.goods1", CreateTime: time.Unix(1686219067, 0)}, nil
			})
			defer QueryOrderMockFunc.Reset()

			args := schema.IOSOrderVerify{
				AppID:       "test_appid",
				OrderID:     "test_order_id",
				Paper:       "test_paper",
				PayCurrency: "test_pay_currency",
				PayAmount:   "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.ErrorOrderNotExists)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("错误-2 订单已被支付", func() {
			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{
					OrderId:    "test_order_id",
					ProductId:  "com.topjoy.test.goods1",
					CreateTime: time.Unix(1686219067, 0),
					PayStatus:  constants.PaymentSuccess,
				}, nil
			})
			defer QueryOrderMockFunc.Reset()

			args := schema.IOSOrderVerify{
				AppID:       "test_appid",
				OrderID:     "test_order_id",
				Paper:       "test_paper",
				PayCurrency: "test_pay_currency",
				PayAmount:   "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.Success)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("错误-3 苹果支付订单线上验证失败", func() {
			VerifyOnlineMockFunc := ApplyFunc(services.VerifyOnline, func(_ context.Context, _ string, _ string, _ string) (map[string]interface{}, error) {
				return nil, errors.New("验证失败")
			})
			defer VerifyOnlineMockFunc.Reset()

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{
					OrderId:    "test_order_id",
					ProductId:  "com.topjoy.test.goods1",
					CreateTime: time.Unix(1686219067, 0),
				}, nil
			})
			defer QueryOrderMockFunc.Reset()

			ApplyFunc(services.UpdateErrorMessage, func(_ *gin.Context, _ string, _ models.Order) error {
				return nil
			})

			args := schema.IOSOrderVerify{
				AppID:       "test_appid",
				OrderID:     "test_order_id",
				Paper:       "test_paper",
				PayCurrency: "test_pay_currency",
				PayAmount:   "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.ErrorOrderSign)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("错误-4 苹果支付订单线上验证失败", func() {
			VerifyOnlineMockFunc := ApplyFunc(services.VerifyOnline, func(_ context.Context, _ string, _ string, _ string) (map[string]interface{}, error) {
				return nil, errors.New("验证失败")
			})
			defer VerifyOnlineMockFunc.Reset()

			QueryOrderMockFunc := ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				_, ok := filter["pay_id"]
				if ok {
					return &models.Order{}, nil
				}

				return &models.Order{
					OrderId:    "test_order_id",
					ProductId:  "com.topjoy.test.goods1",
					CreateTime: time.Unix(1686219067, 0),
				}, nil
			})
			defer QueryOrderMockFunc.Reset()

			UpdateErrorMessageMockFunc := ApplyFunc(services.UpdateErrorMessage, func(_ *gin.Context, _ string, _ models.Order) error {
				return nil
			})
			defer UpdateErrorMessageMockFunc.Reset()

			args := schema.IOSOrderVerify{
				AppID:       "test_appid",
				OrderID:     "test_order_id",
				Paper:       "test_paper",
				PayCurrency: "test_pay_currency",
				PayAmount:   "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.ErrorOrderSign)
			So(output, ShouldEqual, outputExpect)
		})
	})
}
