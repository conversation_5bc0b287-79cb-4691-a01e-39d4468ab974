package order

import (
	"errors"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/utils"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
)

type MockOrderInfo struct {
	OrderID string `json:"order_id"`
}

// MockPayExchangeOrder
// @Summary 创建模拟支付订单
// @Tags	SDK MockPay
// @Accept	json
// @Produce json
// @Param	person 	body  schema.MockPayExchangeForm	true	"MockPay支付"
// @Success 200 {object} controllers.SuccessResponse{result=MockOrderInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /order/mockpay-exchange [post]
func (o Controller) MockPayExchangeOrder(c *gin.Context) {
	var orderForm schema.MockPayExchangeForm
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, _ := game.GetGameByAppid(orderForm.AppID)

	var baseConfig payment.Payment
	converter.QueryByAppid(&baseConfig, orderForm.AppID)

	var config payment.MockPay
	converter.QueryByAppid(&config, orderForm.AppID)

	if *baseConfig.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		o.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}

	dbUser, _ := services.GetUserByUserID(c, orderForm.UserID)
	if dbUser.ID == 0 {
		o.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	orderID := services.GenerateOrderID(constants.PayWayMock, constants.OrderIDTokenSize)
	dbOrder := &models.Order{}

	dbOrder.GameId = dbGame.Id
	dbOrder.OrderId = orderID
	dbOrder.Account = dbUser.Account
	dbOrder.UserId = dbUser.ID
	dbOrder.RoleId = orderForm.RoleID
	dbOrder.ProductId = orderForm.ProductID
	dbOrder.Price = orderForm.Price
	dbOrder.PayWay = constants.PayWayMock
	dbOrder.Extend = orderForm.Extend
	dbOrder.Device = orderForm.Device
	dbOrder.RoleName = orderForm.RoleName
	dbOrder.PayNotifyUrl = orderForm.PayNotifyURL
	dbOrder.CreateTime = time.Now()
	dbOrder.Platform = constants.PlatformAndroid
	dbOrder.ServerId = orderForm.ServerID
	dbOrder.ServerName = orderForm.ServerName
	dbOrder.Vip = utils.ConvertStringToInt(orderForm.Vip)
	dbOrder.Level = utils.ConvertStringToInt(orderForm.Level)
	dbOrder.PayStatus = constants.PaymentSuccess
	dbOrder.PayAmount = orderForm.Price
	dbOrder.PayId = orderID
	dbOrder.PayCurrency = "CNY"
	dbOrder.PayTime = time.Now()
	dbOrder.PayIp = c.ClientIP()

	if err := services.CreateOrder(c, dbOrder); err != nil {
		o.Error(c, controllers.ErrorDBError, errors.New("创建模拟订单失败"))
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	services.Notify(c.Request.Context(), db, dbOrder, services.NotifyOptions{})

	o.Success(c, MockOrderInfo{dbOrder.OrderId})
}
