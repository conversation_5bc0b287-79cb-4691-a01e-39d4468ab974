package order

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/validator"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin/binding"
	"gorm.io/gorm"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
)

func TestMockPayOrderExchange(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{
			Id: 1,
		}, nil
	})

	ApplyFunc(services.GenerateOrderID, func(_ int, _ int, _ ...string) string {
		return "test_order_id"
	})

	ApplyFunc(services.Notify, func(_ context.Context, _ *gorm.DB, _ *models.Order, _ services.NotifyOptions) error {
		return nil
	})

	var c *gin.Context
	ApplyMethod(reflect.TypeOf(c), "MustGet", func(_ *gin.Context, key string) any {
		return &gorm.DB{}
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentMockPay":
			info := config.(*payment.MockPay)
			status := 1
			info.OpenStatus = &status
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	Convey("TestMockPayOrderExchange", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return nil
			})

			args := schema.GoogleExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/mockpay-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("参数缺失", func() {
			args := schema.GoogleExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:    "test_appid",
					UserID:   1111,
					Price:    10,
					Extend:   "test_Extend",
					Device:   "test_device",
					RoleName: "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/mockpay-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorEmptyParam))
		})

		Convey("用户不存在", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{ID: 0}, errors.New("User not found")
			})
			args := schema.GoogleExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/mockpay-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorUserNotExists))
		})

		Convey("创建订单失败", func() {
			ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
				return &models.User{
					ID: 1111,
				}, nil
			})

			ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
				return errors.New("Create Order failed")
			})

			args := schema.GoogleExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/mockpay-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			So(output, ShouldEqual, float64(controllers.ErrorDBError))
		})
	})
}
