package order

import (
	"errors"
	"fmt"
	"time"

	"zeus/services/pay/wechat"

	"github.com/gin-gonic/gin"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
)

// WeChatPay
// @Summary	创建微信支付订单
// @Tags	SDK WeChatPay
// @Accept	json
// @Produce json
// @Param	person 	body	schema.WeChatExchangeForm	true	"支付信息"
// @Success 200 {object} controllers.SuccessResponse{result=schema.WeChatOrder}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /order/wechat-exchange [post]
func (o Controller) WeChatPay(c *gin.Context) {
	var order schema.WeChatExchangeForm
	err := c.ShouldBindJSON(&order)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, _ := game.GetGameByAppid(order.AppID)

	var baseConfig payment.Payment
	converter.QueryByAppid(&baseConfig, order.AppID)

	var config payment.WeChatPay
	converter.QueryByAppid(&config, order.AppID)

	if *baseConfig.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		o.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}

	dbUser, _ := services.GetUserByUserID(c, order.UserID)
	if dbUser.ID == 0 {
		o.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	var orderResult *schema.WeChatOrder
	orderResult, err = wechat.CreateWeChatOrder(c, *dbUser, *dbGame, order)
	if err != nil {
		o.Error(c, controllers.ErrorOrderCreateFail, err)
		return
	}
	o.Success(c, orderResult)
}

// WeChatOrderVerify
// @Summary	验证微信支付结果
// @Tags	SDK WeChatPay
// @Accept	json
// @Produce json
// @Param	person	body  schema.WeChatOrderVerify true  "验证微信支付结果"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /order/wechat-verify [post]
func (o Controller) WeChatOrderVerify(c *gin.Context) {
	orderForm := schema.WeChatOrderVerify{}
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbOrder, err := services.CheckOrder(c, orderForm.OrderID)
	if err != nil {
		o.Error(c, controllers.ErrorOrderNotExists, err)
		return
	}

	if dbOrder.PayStatus == constants.PaymentSuccess {
		o.Success(c, constants.PaySuccessMessage)
		return
	}

	lockKey := fmt.Sprintf("transcation_%s_%s", orderForm.AppID, dbOrder.OrderId)
	err = redis.Lock(lockKey, time.Second*constants.LockTimeout)
	if err != nil {
		o.Error(c, controllers.ErrorRequestHasBeenProcessed, errors.New("订单正在被处理"))
		return
	}
	defer redis.UnLock(lockKey)

	wechatOrder, _ := pay.GetOrder(constants.PayWayWeChat, orderForm.AppID)
	OrderPayResult, err := wechatOrder.VerifyPayOrder(c, &dbOrder)
	if err != nil {

		o.Error(c, controllers.ErrorOrderSign, err)
		return
	}
	err = services.UpdatePayOrder(c, OrderPayResult, &dbOrder, constants.PayWayWeChat)
	if err != nil {
		o.Error(c, controllers.ErrorDBError, err)
		return
	}

	if OrderPayResult.Status == constants.PaymentUnpaid {
		o.Error(c, controllers.ErrorOrderSign, errors.New(constants.PayUnpaidMessage))
		return
	}
	db := o.GetDB(c)
	_ = services.Notify(c.Request.Context(), db, &dbOrder, services.NotifyOptions{})

	o.Success(c, constants.PaySuccessMessage)
}
