package order

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/pkg/validator"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay/apple"
	"zeus/services/pay/wechat"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin/binding"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestOrderWechatExchangeInterface(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{
			Id: 1,
		}, nil
	})

	ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
		return &models.User{
			ID: 1111,
		}, nil
	})
	ApplyFunc(services.GenerateOrderID, func(_ int, _ int, _ ...string) string {
		return "test_order_id"
	})
	ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
		return nil
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentWeChatPay":
			wechatInfo := config.(*payment.WeChatPay)
			status := 1
			wechatInfo.OpenStatus = &status
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	Convey("TestWechatOrder", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(wechat.CreateWeChatOrder, func(_ *gin.Context, _ models.User, _ models.Game, _ schema.WeChatExchangeForm) (*schema.WeChatOrder, error) {
				return &schema.WeChatOrder{}, nil
			})

			args := schema.WeChatExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/wechat-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestOrderWeChatVerifyInterface(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(services.CheckOrder, func(_ *gin.Context, _ string) (models.Order, error) {
		return models.Order{}, nil
	})
	ApplyFunc(services.Notify, func(_ context.Context, _ *gorm.DB, _ *models.Order, _ services.NotifyOptions) error {
		return nil
	})
	var c *Controller
	ApplyMethod(reflect.TypeOf(c), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
		return &gorm.DB{}
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
			dingTalkToken := ""
			payConfig.DingTalkToken = &dingTalkToken
		case "PaymentWeChatPay":
			wechatInfo := config.(*payment.WeChatPay)
			status := 1
			wechatInfo.OpenStatus = &status
		}
	})
	defer QueryMockFunc.Reset()

	GetGameByAppidMockFunc := ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{}, nil
	})
	defer GetGameByAppidMockFunc.Reset()

	ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
		return nil
	})

	ApplyFunc(redis.UnLock, func(key string) int64 {
		return 0
	})

	ac := apple.AppleClient{}
	VerifyOrderWithOriginTransactionIDMock := ApplyMethod(reflect.TypeOf(&ac), "VerifyOrderWithOriginTransactionID",
		func(_ *apple.AppleClient, _ context.Context, _ string, _ bool) (schema.SignedTransactionInfo, error) {
			return schema.SignedTransactionInfo{
				BundleId:  "com.topjoy.test",
				ProductId: "com.topjoy.test.goods1",
			}, nil
		})
	defer VerifyOrderWithOriginTransactionIDMock.Reset()

	Convey("TestWechatOrderVerify", t, func() {
		Convey("one func for succ", func() {
			var weChatOrder *wechat.WeChatOrder
			ApplyMethod(reflect.TypeOf(weChatOrder), "VerifyPayOrder", func(_ *wechat.WeChatOrder, _ *gin.Context, _ *models.Order) (schema.OrderPayResult, error) {
				return schema.OrderPayResult{Status: 1}, nil
			})
			updatePayOrderFunc := ApplyFunc(services.UpdatePayOrder, func(_ *gin.Context, _ schema.OrderPayResult, _ *models.Order, _ int) error {
				return nil
			})
			defer updatePayOrderFunc.Reset()

			args := schema.WeChatOrderVerify{
				AppID:   "test_appid",
				OrderID: "test_order_id",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/order/wechat-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}
