package robust_config

import (
	"strings"

	"github.com/gin-gonic/gin"

	"zeus/controllers"
	"zeus/models"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/other"
)

type DialTestingParam struct {
	UserID string `json:"user_id"`
	Appid  string `json:"appid" binding:"required"`
}

type DialTestingInfo struct {
	InWhiteList  bool                        `json:"in_white_list"`
	SamplingRate float64                     `json:"sampling_rate"`
	Targets      []*models.DialTestingTarget `json:"targets"`
}

type LogConfigInfo struct {
	InWhiteList  bool    `json:"in_white_list"`
	SamplingRate float64 `json:"sampling_rate"`
}

type ConfigResponse struct {
	DialTestingInfo DialTestingInfo `json:"dial_testing_info"`
	LogConfigInfo   LogConfigInfo   `json:"log_config_info"`
}

// GetRobustConfigInfo
// @Summary	获取拨测与日志收集配置
// @Tags	API Game
// @Accept	json
// @Produce json
// @Param	person	body	DialTestingParam	true	"获取拨测与日志收集配置"
// @Success 200 {object} controllers.SuccessResponse{result=ConfigResponse}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /robust-configs/info [post]
func (cn Controller) GetRobustConfigInfo(c *gin.Context) {
	var dialTestingParam DialTestingParam
	err := c.ShouldBindJSON(&dialTestingParam)
	if err != nil {
		cn.Error(c, controllers.ErrorUnknown, err)
		return
	}

	dbGame, err := game.GetGameByAppid(dialTestingParam.Appid)
	if err != nil {
		cn.APIError(c, err)
		return
	}

	targets, err := models.GetDialTestingTargetsByGameID(dbGame.Id)
	if err != nil {
		cn.APIError(c, err)
		return
	}

	var dialTestConfig other.DialTestingConfig
	converter.Query(&dialTestConfig, converter.Filter{ProjectId: dbGame.ProjectID, GameId: dbGame.Id})
	dialTestingInfo := DialTestingInfo{}
	if dialTestConfig.DialTestingSamplingRate != nil {
		dialTestingInfo.SamplingRate = *dialTestConfig.DialTestingSamplingRate
	} else {
		dialTestingInfo.SamplingRate = 0
	}
	dialTestingInfo.Targets = targets
	if dialTestingParam.UserID != "" {
		dialTestingInfo.InWhiteList = strings.Contains(*dialTestConfig.DialTestingWhiteList, dialTestingParam.UserID)
	} else {
		dialTestingInfo.InWhiteList = false
	}

	var logCollectionConfig other.LogCollectionConfig
	converter.Query(&logCollectionConfig, converter.Filter{ProjectId: dbGame.ProjectID, GameId: dbGame.Id})

	LogConfigInfo := LogConfigInfo{}
	if dialTestingParam.UserID != "" {
		LogConfigInfo.InWhiteList = strings.Contains(*logCollectionConfig.LogCollectionWhiteList, dialTestingParam.UserID)
	} else {
		LogConfigInfo.InWhiteList = false
	}

	if logCollectionConfig.LogCollectionSamplingRate != nil {
		LogConfigInfo.SamplingRate = *logCollectionConfig.LogCollectionSamplingRate
	} else {
		LogConfigInfo.SamplingRate = 0
	}

	ConfigResponse := ConfigResponse{}
	ConfigResponse.DialTestingInfo = dialTestingInfo
	ConfigResponse.LogConfigInfo = LogConfigInfo

	cn.Success(c, ConfigResponse)
}
