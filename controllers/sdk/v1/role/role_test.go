package role

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"zeus/services/game"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"

	"zeus/models"
	"zeus/services"
)

func TestRoleSave(t *testing.T) {
	r := gin.Default()
	g := r.Group("/")
	SetupRouter(g)

	<PERSON>vey("TestRoleSave", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.GetRoleByRoleIDAndServerID, func(c *gin.Context, gameID int, roleID string, serverID string) (*models.Role, error) {
				role := models.Role{}
				return &role, nil
			})
			ApplyFunc(services.SaveRole, func(c *gin.Context, role *models.Role) error {
				return nil
			})
			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})

			args := SaveForm{
				UserId:   "11112222",
				APPID:    "test_appid",
				RoleId:   "test_role_id",
				ServerID: "test_server_id",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/role/save", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}
