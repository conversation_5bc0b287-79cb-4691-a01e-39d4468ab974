package role

import (
	"strconv"
	"time"

	"zeus/services/game"

	"github.com/gin-gonic/gin"

	"zeus/controllers"
	"zeus/services"
)

type SaveForm struct {
	UserId     string `json:"user_id" binding:"required"`
	APPID      string `json:"appid" binding:"required"`
	RoleId     string `json:"role_id" binding:"required"`
	Device     string `json:"device"`
	Platform   string `json:"platform"`
	ServerID   string `json:"server_id"`
	ServerName string `json:"server_name"`
	RoleName   string `json:"role_name"`
	VIP        string `json:"vip"`
	Level      string `json:"level"`
	PlayTime   string `json:"play_time"`
	Extend     string `json:"extend"`
}

// RoleSave
// @Summary	上传用户信息
// @Tags	SDK Role
// @Accept	json
// @Produce json
// @Param	person	body	SaveForm	true	"获取用户信息"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /role/save [post]
func (r Controller) RoleSave(c *gin.Context) {
	role := SaveForm{}
	err := c.ShouldBindJSON(&role)
	if err != nil {
		r.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, _ := game.GetGameByAppid(role.APPID)
	dbRole, _ := services.GetRoleByRoleIDAndServerID(c, dbGame.Id, role.RoleId, role.ServerID)
	dbRole.GameID = dbGame.Id
	dbRole.UserID, _ = strconv.Atoi(role.UserId)
	dbRole.RoleId = role.RoleId
	dbRole.RoleName = role.RoleName
	dbRole.Device = role.Device
	dbRole.Level, _ = strconv.Atoi(role.Level)
	dbRole.Vip, _ = strconv.Atoi(role.VIP)
	dbRole.PlayIp = c.ClientIP()
	dbRole.ServerId = role.ServerID
	dbRole.Platform = role.Platform
	dbRole.ServerName = role.ServerName
	dbRole.Extend = role.Extend
	dbRole.PlayTime = time.Now()
	err = services.SaveRole(c, dbRole)
	if err != nil {
		r.Error(c, controllers.ErrorDBError, err)
		return
	}
	r.Success(c, "成功")
}
