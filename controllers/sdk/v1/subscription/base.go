package subscription

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"zeus/controllers"
)

type Controller struct {
	controllers.BaseController
}

func (s *Controller) GetDB(c *gin.Context) *gorm.DB {
	db := c.MustGet("db")
	return db.(*gorm.DB)
}

var Subscription = Controller{}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.POST("/subscription/google-exchange", Subscription.GoogleExchangeSubscription)
	routerGroup.POST("/subscription/google-verify", Subscription.GoogleVerifySubscription)
	routerGroup.POST("/callback/google-notify", Subscription.GoogleSubscriptionNotify)
	routerGroup.POST("/subscription/ios-exchange", Subscription.IosExchangeSubscription)
	routerGroup.POST("/subscription/ios-verify", Subscription.IosVerifySubscription)
	routerGroup.POST("/callback/ios-notify", Subscription.IOSSubscriptionNotify)
}
