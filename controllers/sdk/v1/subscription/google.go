package subscription

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/exp/slices"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/controllers"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/utils"
)

type GoogleSubscriptionInfo struct {
	GooglePayKey string `json:"google_pay_key"`
	OrderID      string `json:"order_id"`
}

type NotifyData struct {
	SubscriptionNotification struct {
		NotificationType int    `json:"notificationType"`
		SubscriptionId   string `json:"subscriptionId"`
		PurchaseToken    string `json:"purchaseToken"`
	} `json:"subscriptionNotification"`
	PackageName string `json:"packageName"`
}

// GoogleExchangeSubscription
// @Summary 创建Google订阅订单
// @Tags	SDK Subscription
// @Accept	json
// @Produce json
// @Param	order	body  schema.GoogleSubscription	true	"Google订阅"
// @Success 200
// @Router /subscription/google-exchange [post]
func (s Controller) GoogleExchangeSubscription(c *gin.Context) {
	var orderForm schema.GoogleSubscription
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, _ := game.GetGameByAppid(orderForm.AppID)

	var payConfig payment.Payment
	converter.QueryByAppid(&payConfig, orderForm.AppID)

	var config payment.GooglePay
	converter.QueryByAppid(&config, orderForm.AppID)

	if *config.GooglePayKey == "" {
		s.Error(c, controllers.ErrorEmptyParam, errors.New("未配置google加密key"))
		return
	}
	if *payConfig.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		s.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}

	dbUser, _ := services.GetUserByUserID(c, orderForm.UserID)
	if dbUser.ID == 0 {
		s.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	orderID := services.GenerateOrderID(constants.GoogleSubscription, constants.OrderIDTokenSize)
	dbSubscription := &models.Subscription{}

	dbSubscription.GameId = dbGame.Id
	dbSubscription.OrderId = orderID
	dbSubscription.Account = dbUser.Account
	dbSubscription.UserId = dbUser.ID
	dbSubscription.RoleId = orderForm.RoleID
	dbSubscription.ProductId = orderForm.ProductID
	dbSubscription.Price = orderForm.Price
	dbSubscription.Extend = orderForm.Extend
	dbSubscription.Device = orderForm.Device
	dbSubscription.RoleName = orderForm.RoleName
	dbSubscription.CreateTime = time.Now()
	dbSubscription.PayNotifyUrl = orderForm.PayNotifyURL
	dbSubscription.Platform = constants.PlatformAndroid
	dbSubscription.ServerId = orderForm.ServerID
	dbSubscription.ServerName = orderForm.ServerName
	dbSubscription.Vip = utils.ConvertStringToInt(orderForm.Vip)
	dbSubscription.Level = utils.ConvertStringToInt(orderForm.Level)

	if err := services.CreateSubscription(c, dbSubscription); err != nil {
		s.Error(c, controllers.ErrorDBError, errors.New("创建google订阅失败"))
		return
	}

	s.Success(c, GoogleSubscriptionInfo{*config.GooglePayKey, dbSubscription.OrderId})
}

// GoogleVerifySubscription
// @Summary	验证Google订阅订单
// @Tags	SDK Subscription
// @Accept	json
// @Produce json
// @Param	person	body	schema.GoogleSubscriptionVerify	true	"验证google订阅订单"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /subscription/google-verify [post]
func (s Controller) GoogleVerifySubscription(c *gin.Context) {
	db := s.GetDB(c)
	var orderForm schema.GoogleSubscriptionVerify
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	var data schema.PurchaseData
	json.Unmarshal([]byte(orderForm.PurchaseData), &data)

	if data.PurchaseState != 0 {
		s.Error(c, controllers.ErrorOrderUnpaid, errors.New("订单未支付"))
		return
	}

	dbGame, err := game.GetGameByAppid(orderForm.AppID)
	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	// 被支付订单不存在.
	filter := map[string]interface{}{
		"order_id": data.DeveloperPayload,
		"game_id":  dbGame.Id,
	}
	dbOrder, _ := services.QuerySubscription(c, filter)

	getPurchaseData := map[string]interface{}{
		"packageName":   data.PackageName,
		"productId":     data.ProductID,
		"purchaseToken": data.PurchaseToken,
	}

	// 订单支付状态验证
	info, _ := pay.GetPurchaseInfo(c.Request.Context(), orderForm.AppID, getPurchaseData, false, data.DeveloperPayload)
	if info == nil {
		s.Error(c, controllers.ErrorOrderSign, errors.New("支付订单认证失败"))
		return
	}

	_, ok := info["paymentState"]
	if !ok {
		s.Error(c, controllers.ErrorOrderSign, errors.New("支付订单认证失败"))
		return
	}
	// 订阅的付款状态 0:待处理付款  1:付款已收讫
	if info["paymentState"].(float64) != 1 {
		s.Error(c, controllers.ErrorOrderUnpaid, errors.New("订单未支付"))
		return
	}

	// 原始订单处理
	if originalTransactionId, ok := info["linkedPurchaseToken"]; ok {

		filter = map[string]interface{}{
			"original_transaction_id": originalTransactionId,
			"game_id":                 dbGame.Id,
		}
		dbOrderTwo, _ := services.QuerySubscription(c, filter)
		log.Debug(c.Request.Context(), "Google订阅查询到原始订单", "filter", filter, "dbOrderOriginal", dbOrderTwo)
		err = services.EditNewSubscription(c, dbOrder, data, info, orderForm)
		if err != nil {
			s.Error(c, controllers.ErrorOrderCreateFail, err)
			return
		}
		dbOrder = dbOrderTwo
	}
	if dbOrder.PayProductId != "" {
		pids := strings.Split(dbOrder.PayProductId, "->")
		length := len(pids)
		if pids[length-1] != data.ProductID {
			// 不是同一商品
			dbOrder.PayProductId = dbOrder.PayProductId + "->" + data.ProductID
		}
	}

	dataJson, _ := json.Marshal(data)
	infoJson, _ := json.Marshal(info)
	// 订单第一次
	if constants.PaymentSuccess != dbOrder.PayStatus {
		dbOrder.OriginReceipt = string(dataJson)
		dbOrder.OriginReceiptInfo = string(infoJson)
		dbOrder.PayProductId = data.ProductID
		dbOrder.PayTime = time.Now()
		dbOrder.PayStatus = constants.PaymentSuccess
		dbOrder.PayCurrency = orderForm.PayCurrency
		if orderForm.PayAmount != "" {
			payAmount, _ := strconv.ParseFloat(orderForm.PayAmount, 64)
			dbOrder.PayAmount = payAmount
		}
		dbOrder.OriginalTransactionId = data.PurchaseToken
	}

	googlePurchaseType, ok := info["purchaseType"]
	if ok {
		purchaseTypeS := strconv.FormatFloat(googlePurchaseType.(float64), 'f', 0, 64)
		purchaseType := constants.NormalOrder
		switch purchaseTypeS {
		case "0": // 从许可测试帐号中购买的服务
			purchaseType = constants.TestAccount
		case "1": // 使用促销代码购买
			purchaseType = constants.SalesPromotion
		}
		dbOrder.OrderAttribute = purchaseType
	}
	dbOrder.ProductId = data.ProductID
	dbOrder.TransactionId = data.PurchaseToken
	dbOrder.PayId = data.OrderID
	dbOrder.LatestReceipt = string(dataJson)
	dbOrder.LatestReceiptInfo = string(infoJson)
	expiresDate, _ := strconv.Atoi(info["expiryTimeMillis"].(string))
	dbOrder.ExpiresDate = expiresDate / 1000
	dbOrder.NotifyStatus = constants.SubscriptionOrderNormal

	if err := services.SaveSubscription(c, dbOrder); err != nil {
		s.Error(c, controllers.ErrorUnknown, errors.New("订单更新失败"))
		return
	}

	// 通知Game Server
	renewSubscription := new(models.RenewSubscription)
	_ = services.NotifySubscription(c.Request.Context(), db, dbOrder, false, renewSubscription)

	s.Success(c, "success")
}

// GoogleSubscriptionNotify
// @Summary	Google订阅通知回调
// @Tags	SDK Subscription
// @Accept	json
// @Produce json
// @Param	person	body	schema.GoogleSubscriptionNotify	true	"google订阅通知回调"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /callback/google-notify [post]
func (s Controller) GoogleSubscriptionNotify(c *gin.Context) {
	db := s.GetDB(c)
	var notifyParams schema.GoogleSubscriptionNotify
	err := c.ShouldBindJSON(&notifyParams)
	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	appID := c.Query("appid")
	if appID == "" {
		s.Error(c, controllers.ErrorEmptyParam, errors.New("缺少字段appid"))
		return
	}
	dbGame, err := game.GetGameByAppid(appID)
	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	var decodeNotifyParams []byte
	if notifyParams.Message.Data != "" {
		decodeNotifyParams, err = base64.StdEncoding.DecodeString(notifyParams.Message.Data)
	}

	var NotifyData NotifyData
	err = json.Unmarshal(decodeNotifyParams, &NotifyData)
	if err != nil {
		s.Error(c, controllers.ErrorUnknown, err)
		return
	}
	subData := NotifyData.SubscriptionNotification
	//* notificationType    int
	//* 通知的类型。它可以具有以下值：
	//* (1) SUBSCRIPTION_RECOVERED - 从帐号保留状态恢复了订阅。
	//* (2) SUBSCRIPTION_RENEWED - 续订了处于活动状态的订阅。
	//* (3) SUBSCRIPTION_CANCELED - 自愿或非自愿地取消了订阅。如果是自愿取消，在用户取消时发送。
	//* (4) SUBSCRIPTION_PURCHASED - 购买了新的订阅。
	//* (5) SUBSCRIPTION_ON_HOLD - 订阅已进入帐号保留状态（如已启用）。
	//* (6) SUBSCRIPTION_IN_GRACE_PERIOD - 订阅已进入宽限期（如已启用）。
	//* (7) SUBSCRIPTION_RESTARTED - 用户已通过“Play”>“帐号”>“订阅”重新激活其订阅（需要选择使用订阅恢复功能）。
	//* (8) SUBSCRIPTION_PRICE_CHANGE_CONFIRMED - 用户已成功确认订阅价格变动。
	//* (9) SUBSCRIPTION_DEFERRED - 订阅的续订时间点已延期。
	//* (10) SUBSCRIPTION_PAUSED - 订阅已暂停。
	//* (11) SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED - 订阅暂停计划已更改。
	//* (12) SUBSCRIPTION_REVOKED - 用户在有效时间结束前已撤消订阅。
	//* (13) SUBSCRIPTION_EXPIRED - 订阅已过期。
	notifyType := subData.NotificationType
	availableNotificationTypes := []int{constants.GoogleSubscriptionRecoveredType, constants.GoogleSubscriptionRenewedType, constants.GoogleSubscriptionPurchasedType, constants.GoogleSubscriptionRevokedType}
	if !slices.Contains(availableNotificationTypes, notifyType) {
		log.Debug(c.Request.Context(), fmt.Sprintf("非Google自动续订或订阅退款通知类型(1、2、4、12)"), "NotificationType", notifyType)
		s.Error(c, controllers.Fail, errors.New("sdk_callback, 非Google自动续订或订阅退款通知类型"))
		return
	}
	subscriptionId := subData.SubscriptionId
	purchaseToken := subData.PurchaseToken
	NotifyPurchaseData := make(map[string]interface{})
	NotifyPurchaseData["productId"] = subscriptionId
	NotifyPurchaseData["purchaseToken"] = purchaseToken
	NotifyPurchaseData["subscriptionNotification"] = subData
	NotifyPurchaseData["packageName"] = NotifyData.PackageName

	info, _ := pay.GetPurchaseInfo(c.Request.Context(), appID, NotifyPurchaseData, false, "subscriptionId: "+subscriptionId)
	infoJson, _ := json.Marshal(info)
	notificationInfo := new(schema.GoogleSubscriptionNotificationResponse)
	json.Unmarshal(infoJson, &notificationInfo)
	if info == nil {
		s.Error(c, controllers.ErrorOrderSign, errors.New("sdk_callback"))
		return
	}
	if notificationInfo.ObfuscatedExternalAccountId == "" {
		s.Error(c, controllers.ErrorOrderSign, errors.New("支付订单认证失败"))
		return
	}
	// 被支付订单不存在
	filter := map[string]interface{}{
		"order_id": notificationInfo.ObfuscatedExternalAccountId,
		"game_id":  dbGame.Id,
	}
	dbOrder, _ := services.QuerySubscription(c, filter)
	if dbOrder.OrderId == "" {
		s.Error(c, controllers.ErrorOrderNotExists, errors.New("订单不存在"))
		return
	}

	renewSubscription := new(schema.RenewSubscriptionCreate)
	renewSubscription.OrderId = dbOrder.OrderId
	renewSubscription.GameId = dbOrder.GameId
	renewSubscription.PayWay = constants.PayWayGoogle
	renewSubscription.OriginalTransactionId = notificationInfo.LinkedPurchaseToken
	renewSubscription.TransactionId = notificationInfo.OrderId    // 格式：GPA.3318-xxxx-xxxx-xxxx..6
	renewSubscription.NotificationType = strconv.Itoa(notifyType) // google 回调接口传参notifyType是数字
	NotifyDataJson, _ := json.Marshal(NotifyData)
	renewSubscription.NotificationInfo = string(NotifyDataJson)
	renewSubscription.NotificationUUID = notificationInfo.OrderId // google续订通知中没有NotificationUUID字段，使用谷歌订单ID
	if notificationInfo.ExpiryTimeMillis != "" {
		expiresDate, _ := strconv.Atoi(notificationInfo.ExpiryTimeMillis)
		renewSubscription.ExpiresDate = expiresDate / 1000
	} else {
		s.Error(c, controllers.Fail, errors.New("google续订通知缺少过期时间字段 expiryTimeMillis"))
		return
	}

	log.Debug(c.Request.Context(), "开始保存Google续订订单", "RenewSubscription", renewSubscription)
	dbRenewSubscription, err := services.CreateRenewSubscription(db, dbGame.Id, renewSubscription)
	if err != nil {
		log.Warn(c.Request.Context(), "保存Google续订订单失败", "error", err)
		s.Error(c, controllers.ErrorDBError, err)
		return
	}

	if notificationInfo.LinkedPurchaseToken != "" {
		log.Debug(c.Request.Context(), "Google续订原始订单处理", "original_transaction_id", notificationInfo.LinkedPurchaseToken)

		filter = map[string]interface{}{
			"original_transaction_id": notificationInfo.LinkedPurchaseToken,
			"game_id":                 dbGame.Id,
		}
		dbOrderTwo, _ := services.QuerySubscription(c, filter)
		dbOrder = dbOrderTwo
	}
	newExpiresDate, _ := strconv.Atoi(notificationInfo.ExpiryTimeMillis)
	newExpiresDate = newExpiresDate / 1000
	if newExpiresDate > dbOrder.ExpiresDate {
		dbOrder.NotifyCnt = 0
		dbOrder.NotifyStatus = constants.SubscriptionOrderNormal
		dbOrder.ExpiresDate = newExpiresDate
	}

	dbOrder.TransactionId = purchaseToken
	dbOrder.LatestReceipt = string(decodeNotifyParams)
	dbOrder.LatestReceiptInfo = string(infoJson)
	dbOrder.NotificationType = notifyType

	log.Debug(c.Request.Context(), "开始保存Google续订原始订单", "dbOrder", dbOrder)

	if err := services.SaveSubscription(c, dbOrder); err != nil {
		log.Warn(c.Request.Context(), "Google续订原始订单保存失败", "dbOrder", dbOrder)
		s.Error(c, controllers.ErrorUnknown, fmt.Errorf("google续订原始订单更新失败, err:%s", err))
		return
	}
	GameNotificationTypes := []int{constants.GoogleSubscriptionRecoveredType, constants.GoogleSubscriptionRenewedType, constants.GoogleSubscriptionPurchasedType}
	if slices.Contains(GameNotificationTypes, dbOrder.NotificationType) {
		// 通知Game Server
		_ = services.NotifySubscription(c.Request.Context(), db, dbOrder, false, dbRenewSubscription)
	}

	s.Success(c, "sdk_callback")
}
