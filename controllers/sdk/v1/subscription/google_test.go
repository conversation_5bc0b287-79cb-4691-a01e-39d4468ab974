package subscription

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/validator"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin/binding"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestSubscriptionGoogleExchangeInterface(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{}, nil
	})

	ApplyFunc(services.GetSubscriptionByExtendAndPayStatus, func(_ *gin.Context, _, _ interface{}) (*models.Subscription, error) {
		return &models.Subscription{}, nil
	})

	ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
		return &models.User{ID: 117}, nil
	})

	ApplyFunc(services.GenerateOrderID, func(orderType int, tokenNumber int, extra ...string) string {
		return "test_order_id"
	})

	ApplyFunc(services.CreateSubscription, func(_ *gin.Context, _ *models.Subscription) error {
		return nil
	})
	ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentGooglePay":
			googleConfig := config.(*payment.GooglePay)
			googlePayKey := "testGooglePayKey"
			googleConfig.GooglePayKey = &googlePayKey
			status := constants.IsRechargeOpen
			googleConfig.OpenStatus = &status
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})

	Convey("TestGoogleSubscription", t, func() {
		Convey("one func for succ", func() {
			args := schema.GoogleSubscription{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    99811,
					RoleID:    "117",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/subscription/google-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestSubscriptionGoogleVerifyInterface(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	var s *Controller
	ApplyMethod(reflect.TypeOf(s), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
		return &gorm.DB{}
	})

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{}, nil
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentApplePay":
			applePay := config.(*payment.ApplePay)
			applePassword := "ApplePassword"
			applePay.ApplePassword = &applePassword
		}
	})
	defer QueryMockFunc.Reset()

	Convey("TestGoogleVerifySubscription", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.QuerySubscription, func(_ *gin.Context, filter map[string]interface{}) (*models.Subscription, error) {
				return &models.Subscription{
					OrderId: "test_order_id",
				}, nil
			})

			ApplyFunc(pay.GetPurchaseInfo, func(ctx context.Context, _ string, _ map[string]interface{}, _ bool, _ string) (map[string]interface{}, error) {
				info := make(map[string]interface{})
				info["acknowledgementState"] = float64(1)
				info["expiryTimeMillis"] = "test"
				info["linkedPurchaseToken"] = ""
				info["paymentState"] = float64(1)
				return info, nil
			})

			ApplyFunc(services.EditNewSubscription, func(c *gin.Context, order *models.Subscription, data schema.PurchaseData, info map[string]interface{}, orderForm schema.GoogleSubscriptionVerify) error {
				return nil
			})

			ApplyFunc(services.SaveSubscription, func(c *gin.Context, order *models.Subscription) error {
				return nil
			})
			ApplyFunc(services.NotifySubscription, func(ctx context.Context, db *gorm.DB, order *models.Subscription, force bool, renewSubscription *models.RenewSubscription) error {
				return nil
			})

			args := schema.GoogleSubscriptionVerify{
				AppID:        "test_appid",
				PurchaseSign: "test_purchase_sign",
				PurchaseData: "test_purchase_data",
				PayCurrency:  "test_pay_currency",
				PayAmount:    "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/subscription/google-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestGoogleSubscriptionNotify(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)

	var s *Controller
	apply1 := ApplyMethod(reflect.TypeOf(s), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
		return &gorm.DB{}
	})
	defer apply1.Reset()

	c := &gin.Context{}
	apply2 := ApplyMethod(reflect.TypeOf(c), "Query", func(_ *gin.Context, key string) (value string) {
		return "appID"
	})
	defer apply2.Reset()

	apply3 := ApplyFunc(pay.GetPurchaseInfo, func(ctx context.Context, appid string, info map[string]interface{}, isNormal bool, orderID string) (map[string]interface{}, error) {
		return map[string]interface{}{
			"obfuscatedExternalAccountId": "obfuscatedExternalAccountId",
			"developerPayload":            "developerPayload",
			"linkedPurchaseToken":         "linkedPurchaseToken",
			"expiryTimeMillis":            "10000",
			"orderId":                     "test_order_id",
		}, nil
	})
	defer apply3.Reset()

	apply4 := ApplyFunc(services.QuerySubscription, func(c *gin.Context, filter map[string]interface{}) (*models.Subscription, error) {
		return &models.Subscription{
			OrderId: "test_order_id",
		}, nil
	})
	defer apply4.Reset()

	apply5 := ApplyFunc(services.SaveSubscription, func(c *gin.Context, order *models.Subscription) error {
		return nil
	})
	defer apply5.Reset()

	apply6 := ApplyFunc(services.NotifySubscription, func(ctx context.Context, db *gorm.DB, order *models.Subscription, force bool, renewSubscription *models.RenewSubscription) error {
		return nil
	})
	defer apply6.Reset()

	Convey("TestGoogleSubscriptionNotify", t, func() {
		Convey("one func for succ", func() {
			args := schema.GoogleSubscriptionNotify{
				Message: struct {
					Data string `json:"data" binding:"required"`
				}(struct{ Data string }{Data: "ewogICJzdWJzY3JpcHRpb25Ob3RpZmljYXRpb24iOiB7CiAgICAibm90aWZpY2F0aW9uVHlwZSI6IDEsCiAgICAic3Vic2NyaXB0aW9uSWQiOiAic3Vic2NyaXB0aW9uSWQiLAogICAgInB1cmNoYXNlVG9rZW4iOiAicHVyY2hhc2VUb2tlbiIKICB9LAogICJwYWNrYWdlTmFtZSI6ICJwYWNrYWdlTmFtZSIKfQ=="}),
				Subscription: "subscription",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/callback/google-notify?appid=test_appid", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := float64(1)
			if resp, ok := resp["error_no"]; ok {
				output = resp.(float64)
			}
			outputExpect := float64(1)
			So(output, ShouldEqual, outputExpect)
		})
	})
}
