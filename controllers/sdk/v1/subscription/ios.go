package subscription

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/controllers"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay/apple"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/utils"
)

type IosSubscriptionInfo struct {
	OrderID string `json:"order_id"`
}

// IosExchangeSubscription
// @Summary	生成iOS订阅订单
// @Tags	SDK Subscription
// @Accept	json
// @Produce json
// @Param	person	body	schema.IosSubscription	true	"生成ios订阅订单"
// @Success 200 {object} controllers.SuccessResponse{result=IosSubscriptionInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /subscription/ios-exchange [post]
func (s Controller) IosExchangeSubscription(c *gin.Context) {
	order := schema.IosSubscription{}
	err := c.Should<PERSON>ind<PERSON>(&order)
	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, err := game.GetGameByAppid(order.AppID)
	var payConfig payment.Payment
	converter.QueryByAppid(&payConfig, order.AppID)

	var config payment.ApplePay
	converter.QueryByAppid(&config, order.AppID)

	if err != nil {
		s.Error(c, controllers.ErrorDBError, errors.New("无效的appid"))
		return
	}
	// 支付关闭
	if *payConfig.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		s.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}
	// 账号查询
	dbUser, _ := services.GetUserByUserID(c, order.UserID)
	if dbUser.ID == 0 {
		s.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}
	orderID := services.GenerateOrderID(constants.IosSubscription, constants.OrderIDTokenSize)
	dbOrder := &models.Subscription{}

	dbOrder.GameId = dbGame.Id
	dbOrder.OrderId = orderID
	dbOrder.Account = dbUser.Account
	dbOrder.UserId = dbUser.ID
	dbOrder.RoleId = order.RoleID
	dbOrder.ProductId = order.ProductID
	dbOrder.Price = order.Price
	dbOrder.Extend = order.Extend
	dbOrder.Device = order.Device
	dbOrder.RoleName = order.RoleName
	dbOrder.CreateTime = time.Now()
	dbOrder.PayNotifyUrl = order.PayNotifyURL
	dbOrder.Platform = constants.PlatformIOS
	dbOrder.ServerId = order.ServerID
	dbOrder.ServerName = order.ServerName
	dbOrder.Vip = utils.ConvertStringToInt(order.Vip)
	dbOrder.Level = utils.ConvertStringToInt(order.Level)

	if err := services.CreateSubscription(c, dbOrder); err != nil {
		s.Error(c, controllers.ErrorDBError, errors.New("创建订阅订单失败"))
		return
	}
	s.Success(c, IosSubscriptionInfo{dbOrder.OrderId})
}

// IosVerifySubscription
// @Summary	验证iOS订阅订单
// @Tags	SDK Subscription
// @Accept	json
// @Produce json
// @Param	person	body	schema.IosSubscriptionVerify	true	"验证ios订阅订单"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /subscription/ios-verify [post]
func (s Controller) IosVerifySubscription(c *gin.Context) {
	db := s.GetDB(c)
	order := schema.IosSubscriptionVerify{}
	err := c.ShouldBindJSON(&order)
	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	paper := order.Paper
	dbGame, _ := game.GetGameByAppid(order.AppID)

	var config payment.ApplePay
	converter.QueryByAppid(&config, order.AppID)

	// 线上验证
	info, err := services.VerifyOnline(c.Request.Context(), paper, "subscription", *config.ApplePassword)
	if err != nil {
		s.Error(c, controllers.ErrorParamInvalid, err)
		return
	}

	// 沙盒验证
	status, ok := info["status"].(float64)
	if !ok {
		s.Error(c, controllers.ErrorOrderSign, errors.New("ios订阅订单校验接口异常"))
		return
	}
	var isSandBox bool
	if int64(status) == constants.IOSVerifyStatus {
		isSandBox = true
		info, err = services.VerifySandBox(c.Request.Context(), paper, "subscription", *config.ApplePassword)
		if err != nil {
			s.Error(c, controllers.ErrorParamInvalid, err)
			return
		}
	}

	// 订单验证失败
	status, ok = info["status"].(float64)
	if !ok {
		s.Error(c, controllers.ErrorOrderSign, errors.New("ios订阅订单校验接口异常"))
		return
	}

	if int64(status) != constants.PaySuccessStatus && !isSandBox {
		s.Error(c, controllers.ErrorOrderSign, errors.New(fmt.Sprintf("正式环境验证状态status=%f，支付订单认证失败", status)))
		return
	}
	if info["receipt"] == nil {
		s.Error(c, controllers.ErrorOrderSign, errors.New("支付订单认证失败, receipt字段为空"))
		return
	}

	data := map[string]interface{}{}
	if info["latest_receipt_info"] == nil {
		data = info["receipt"].(map[string]interface{})
	} else {
		data = info["latest_receipt_info"].(map[string]interface{})
	}

	if originalTransactionID, ok := data["original_transaction_id"]; ok {
		if originalTransactionID.(string) == "" {
			s.Error(c, controllers.ErrorOrderSign, errors.New("ios订阅订单认证失败, 缺少original_transaction_id字段"))
			return
		}
	} else {
		s.Error(c, controllers.ErrorOrderSign, errors.New("ios订阅订单认证失败, 缺少original_transaction_id字段"))
		return
	}

	var dbOrder *models.Subscription
	dbOrder = new(models.Subscription)

	filter := map[string]interface{}{
		"original_transaction_id": data["original_transaction_id"],
		"game_id":                 dbGame.Id,
	}
	dbOrderOriginal, _ := services.QuerySubscription(c, filter)
	log.Debug(c.Request.Context(), "根据original_transaction_id查询原始订单", "filter", filter, "original_dbOrder", dbOrderOriginal)
	// 查询到原始订单，且当前orderId不为空
	if dbOrderOriginal.OrderId != "" && order.OrderID != "" {
		dbOrderOriginal.OriginalTransactionId = dbOrderOriginal.OriginalTransactionId + "_" + time.Now().Format("20060102150405")
		log.Debug(c.Request.Context(), fmt.Sprintf("将原始订单%s修改original_transaction_id为%s归档保存", dbOrderOriginal.OrderId, dbOrderOriginal.OriginalTransactionId))

		if err := services.SaveSubscription(c, dbOrderOriginal); err != nil {
			s.Error(c, controllers.ErrorDBError, fmt.Errorf("ios订阅原始订单%s归档修改失败", dbOrderOriginal.OrderId))
			return
		}
	}

	if order.OrderID != "" {
		filter = map[string]interface{}{
			"order_id": order.OrderID,
			"game_id":  dbGame.Id,
		}
		dbOrder, err = services.QuerySubscription(c, filter)
		log.Debug(c.Request.Context(), "根据orderId查询当前订单", "filter", filter, "dbOrder", dbOrder)

		if err != nil {
			s.Error(c, controllers.ErrorOrderNotExists, fmt.Errorf("订单：%s 不存在, %s", order.OrderID, err.Error()))
			return
		}
	} else {
		// 在原始订单保存数据
		dbOrder = dbOrderOriginal
	}

	infoJson, _ := json.Marshal(info)
	if dbOrder.PayStatus != constants.PaymentSuccess {
		log.Debug(c.Request.Context(), "订单状态: 未支付, 表示首次订阅")

		dbOrder.OriginalTransactionId = data["original_transaction_id"].(string)
		transactionId, ok := data["transaction_id"]
		if ok {
			dbOrder.PayId = transactionId.(string)
		}
		dbOrder.OriginReceipt = order.Paper
		dbOrder.PayProductId = data["product_id"].(string)
		dbOrder.OriginReceiptInfo = string(infoJson)
		dbOrder.PayTime = time.Now()
		dbOrder.PayStatus = constants.PaymentSuccess
		dbOrder.PayCurrency = order.PayCurrency
		payAmount, _ := strconv.ParseFloat(order.PayAmount, 64)
		dbOrder.PayAmount = payAmount
	}

	// 修改订单状态
	dbOrder.NotifyCnt = 0
	dbOrder.NotifyStatus = constants.UnNotified

	var productID string
	_, ok = data["product_id"]
	if ok {
		productID = data["product_id"].(string)
	} else {
		s.Error(c, controllers.ErrorOrderSign, errors.New("IOS订阅验证返回数据中缺少product_id参数"))
		return
	}
	var newPayPid string
	if dbOrder.PayProductId != "" {
		// 非首次
		pids := strings.Split(dbOrder.PayProductId, "->")
		if pids[len(pids)-1] != productID {
			// 不是同一商品
			newPayPid = dbOrder.PayProductId + "->" + productID
			log.Debug(c.Request.Context(), "非首次订阅且不是同一商品", "order_id", dbOrder.OrderId, "PayProductId", dbOrder.PayProductId, "newPayPid", newPayPid)
		}
	}

	if newPayPid != "" {
		dbOrder.PayProductId = newPayPid
	}
	dbOrder.ProductId = productID
	if _, ok := data["expires_date"]; ok {
		purchaseDate, _ := strconv.Atoi(data["expires_date"].(string))
		dbOrder.ExpiresDate = purchaseDate / 1000
	} else {
		s.Error(c, controllers.ErrorOrderSign, errors.New("IOS订阅验证返回数据中缺少过期时间expires_date参数"))
		return
	}
	dbOrder.LatestReceipt = order.Paper
	dbOrder.LatestReceiptInfo = string(infoJson)
	dbOrder.NotificationType = services.GetSubscriptionStatus(data)

	notificationType := dbOrder.NotificationType
	if utils.InArray(strconv.Itoa(notificationType), []string{strconv.Itoa(constants.SubscriptionOrderIgnore)}) {
		dbOrder.NotifyStatus = constants.NotifySuccess
	}
	if err := services.SaveSubscription(c, dbOrder); err != nil {
		s.Error(c, controllers.ErrorDBError, errors.New("订阅修改失败"))
		return
	}
	renewSubscription := new(models.RenewSubscription)
	_ = services.NotifySubscription(c.Request.Context(), db, dbOrder, false, renewSubscription)

	s.Success(c, "success")
}

// IOSSubscriptionNotify
// @Summary	IOS订阅通知回调
// @Tags	SDK Subscription
// @Accept	json
// @Produce json
// @Param	person	body	apple.CallbackNotification	true	"IOS订阅通知回调"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /callback/ios-notify [post]
func (s Controller) IOSSubscriptionNotify(c *gin.Context) {
	var notifyParams apple.CallbackNotification
	err := c.ShouldBindJSON(&notifyParams)
	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	appID := c.Query("appid")
	dbGame, err := game.GetGameByAppid(appID)

	var pay payment.Payment
	converter.QueryByAppid(&pay, appID)

	var config payment.ApplePay
	converter.QueryByAppid(&config, appID)

	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	ac := apple.AppleClient{
		ISS:    *config.AppleISS,
		KeyID:  *config.AppleKeyID,
		BID:    *config.AppleBundleId,
		Secret: *config.AppleSecret,
	}
	notificationInfo, err := ac.ParseSubscriptionCallbackNotification(c.Request.Context(), notifyParams)
	if err != nil {
		s.Error(c, controllers.Fail, err)
		return
	}

	filter := map[string]interface{}{
		"original_transaction_id": notificationInfo.OriginalTransactionId,
		"game_id":                 dbGame.Id,
	}
	dbOrder, err := services.QuerySubscription(c, filter)
	if err != nil || dbOrder.OrderId == "" {
		s.Error(c, controllers.ErrorOrderNotExists, err)
		return
	}
	db := s.GetDB(c)
	RenewSubscriptionCreateInfo := new(schema.RenewSubscriptionCreate)
	RenewSubscriptionCreateInfo.OrderId = dbOrder.OrderId
	RenewSubscriptionCreateInfo.GameId = dbOrder.GameId
	RenewSubscriptionCreateInfo.PayWay = constants.PayWayApple
	RenewSubscriptionCreateInfo.OriginalTransactionId = notificationInfo.OriginalTransactionId
	RenewSubscriptionCreateInfo.TransactionId = notificationInfo.TransactionId
	notificationInfoJson, _ := json.Marshal(notificationInfo)
	RenewSubscriptionCreateInfo.NotificationInfo = string(notificationInfoJson)
	RenewSubscriptionCreateInfo.NotificationType = notificationInfo.NotificationType
	RenewSubscriptionCreateInfo.ExpiresDate = notificationInfo.ExpiresDate / 1000
	RenewSubscriptionCreateInfo.NotificationUUID = notificationInfo.NotificationUUID

	log.Debug(c.Request.Context(), "开始保存IOS续订订单", "RenewSubscription", RenewSubscriptionCreateInfo)
	dbRenewSubscription, err := services.CreateRenewSubscription(db, dbGame.Id, RenewSubscriptionCreateInfo)
	if err != nil {
		log.Warn(c.Request.Context(), "保存IOS续订订单失败", "error", err)
		s.Error(c, controllers.ErrorDBError, err)
		return
	}

	if dbOrder.ExpiresDate < notificationInfo.ExpiresDate {
		dbOrder.NotifyCnt = 0
		dbOrder.ExpiresDate = notificationInfo.ExpiresDate / 1000
		dbOrder.NotifyStatus = constants.SubscriptionOrderNormal
	}

	NotificationTypeMapping := map[string]int{
		"DID_FAIL_TO_RENEW":         constants.SubscriptionOrderExpire, // v2版本需要与subtype字段一起判断是否为续订失败
		"DID_CHANGE_RENEWAL_PREF":   constants.SubscriptionOrderIgnore,
		"DID_CHANGE_RENEWAL_STATUS": constants.SubscriptionOrderNormal,
		"DID_RENEW":                 constants.SubscriptionOrderNormal,
		"REFUND":                    constants.SubscriptionOrderRefund,
	}
	dbOrder.NotificationType = NotificationTypeMapping[notificationInfo.NotificationType]
	log.Debug(c.Request.Context(), "开始保存IOS续订原始订单", "dbOrder", dbOrder)
	if err := services.SaveSubscription(c, dbOrder); err != nil {
		log.Warn(c.Request.Context(), "IOS续订原始订单保存失败", "dbOrder", dbOrder)
		s.Error(c, controllers.ErrorUnknown, fmt.Errorf("IOS续订原始订单更新失败, err:%s", err))
		return
	}

	if dbOrder.NotificationType != constants.SubscriptionOrderRefund {
		_ = services.NotifySubscription(c.Request.Context(), db, dbOrder, false, dbRenewSubscription)
	}
	s.Success(c, "sdk_callback")
}
