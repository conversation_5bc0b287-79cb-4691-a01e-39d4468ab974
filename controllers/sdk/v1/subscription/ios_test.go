package subscription

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/validator"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/pay/apple"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin/binding"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestIosSubscriptionExchangeInterface(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{}, nil
	})

	ApplyFunc(services.GetSubscriptionByExtendAndPayStatus, func(_ *gin.Context, _, _ interface{}) (*models.Subscription, error) {
		return &models.Subscription{}, nil
	})

	ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
		return &models.User{ID: 117}, nil
	})

	ApplyFunc(services.GenerateOrderID, func(orderType int, tokenNumber int, extra ...string) string {
		return "test_order_id"
	})

	ApplyFunc(services.CreateSubscription, func(_ *gin.Context, _ *models.Subscription) error {
		return nil
	})
	ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		case "PaymentApplePay":
			ApplePayConfig := config.(*payment.ApplePay)
			status := constants.IsRechargeOpen
			ApplePayConfig.OpenStatus = &status
		}
	})

	Convey("TestIOSSubscription", t, func() {
		Convey("one func for succ", func() {
			args := schema.IosSubscription{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    99811,
					RoleID:    "117",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/subscription/ios-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestSubscriptionIosVerifyInterface(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	var s *Controller
	ApplyMethod(reflect.TypeOf(s), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
		return &gorm.DB{}
	})

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{}, nil
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentApplePay":
			applePay := config.(*payment.ApplePay)
			applePassword := "ApplePassword"
			applePay.ApplePassword = &applePassword
		}
	})
	defer QueryMockFunc.Reset()

	Convey("TestIOSVerifySubscription", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.VerifyOnline, func(ctx context.Context, paper string, payType string, applePassword string) (map[string]interface{}, error) {
				info := make(map[string]interface{})
				info["status"] = float64(constants.IOSVerifyStatus)
				return info, nil
			})

			ApplyFunc(services.VerifySandBox, func(ctx context.Context, paper string, payType string, applePassword string) (map[string]interface{}, error) {
				info := make(map[string]interface{})
				info["status"] = float64(0)
				info["receipt"] = "222"
				data := make(map[string]interface{})
				data["original_transaction_id"] = "2222"
				data["product_id"] = "2222"
				data["expires_date"] = "1675322334000"
				data["productId"] = "2222"
				info["latest_receipt_info"] = data
				return info, nil
			})

			ApplyFunc(services.QuerySubscription, func(_ *gin.Context, filter map[string]interface{}) (*models.Subscription, error) {
				return &models.Subscription{
					OrderId: "test_order_id",
				}, nil
			})

			ApplyFunc(services.EditNewSubscription, func(c *gin.Context, order *models.Subscription, data schema.PurchaseData, info map[string]interface{}, orderForm schema.GoogleSubscriptionVerify) error {
				return nil
			})

			ApplyFunc(services.SaveSubscription, func(c *gin.Context, order *models.Subscription) error {
				return nil
			})

			ApplyFunc(services.NotifySubscription, func(ctx context.Context, db *gorm.DB, order *models.Subscription, force bool, renewSubscription *models.RenewSubscription) error {
				return nil
			})

			args := schema.IosSubscriptionVerify{
				AppID:       "test_appid",
				OrderID:     "test_order_id",
				Paper:       "test_paper",
				PayCurrency: "test_pay_currency",
				PayAmount:   "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/subscription/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("one func for fail", func() {
			ApplyFunc(services.VerifyOnline, func(ctx context.Context, paper string, payType string, applePassword string) (map[string]interface{}, error) {
				info := make(map[string]interface{})
				return info, nil
			})

			args := schema.IosSubscriptionVerify{
				AppID:       "test_appid",
				OrderID:     "test_order_id",
				Paper:       "test_paper",
				PayCurrency: "test_pay_currency",
				PayAmount:   "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/subscription/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(10009)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestIosSubscriptionCallback(t *testing.T) {
	r := gin.Default()
	group := r.Group("/")
	SetupRouter(group)

	ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		case "PaymentApplePay":
			ApplePayConfig := config.(*payment.ApplePay)
			status := constants.IsRechargeOpen
			ApplePayConfig.OpenStatus = &status
			appleISS := "test_apple_iss"
			ApplePayConfig.AppleISS = &appleISS
			appleKeyID := "test_apple_key_id"
			ApplePayConfig.AppleKeyID = &appleKeyID
			appleBID := "test_apple_bid"
			ApplePayConfig.AppleBundleId = &appleBID
			appleSecret := "test_apple_secret"
			ApplePayConfig.AppleSecret = &appleSecret
		}
	})

	var s *Controller
	apply1 := ApplyMethod(reflect.TypeOf(s), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
		return &gorm.DB{}
	})
	defer apply1.Reset()

	c := &gin.Context{}
	apply2 := ApplyMethod(reflect.TypeOf(c), "Query", func(_ *gin.Context, key string) (value string) {
		return "appID"
	})
	defer apply2.Reset()

	apply3 := ApplyFunc(pay.GetPurchaseInfo, func(ctx context.Context, appid string, info map[string]interface{}, isNormal bool, orderID string) (map[string]interface{}, error) {
		return map[string]interface{}{
			"obfuscatedExternalAccountId": "obfuscatedExternalAccountId",
			"developerPayload":            "developerPayload",
			"linkedPurchaseToken":         "linkedPurchaseToken",
			"expiryTimeMillis":            "10000",
			"orderId":                     "test_order_id",
		}, nil
	})
	defer apply3.Reset()

	apply4 := ApplyFunc(services.QuerySubscription, func(c *gin.Context, filter map[string]interface{}) (*models.Subscription, error) {
		return &models.Subscription{
			OrderId: "test_order_id",
		}, nil
	})
	defer apply4.Reset()

	apply5 := ApplyFunc(services.SaveSubscription, func(c *gin.Context, order *models.Subscription) error {
		return nil
	})
	defer apply5.Reset()

	apply6 := ApplyFunc(services.NotifySubscription, func(ctx context.Context, db *gorm.DB, order *models.Subscription, force bool, renewSubscription *models.RenewSubscription) error {
		return nil
	})
	defer apply6.Reset()

	ac := &apple.AppleClient{}
	apply7 := ApplyMethod(reflect.TypeOf(ac), "ParseSubscriptionCallbackNotification", func(_ *apple.AppleClient, _ context.Context, _ apple.CallbackNotification) (*apple.CallbackNotificationInfo, error) {
		return &apple.CallbackNotificationInfo{OriginalTransactionId: "test_original_transaction_id"}, nil
	})
	defer apply7.Reset()

	Convey("TestIosSubscriptionNotify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.CreateRenewSubscription, func(db *gorm.DB, _ int, _ *schema.RenewSubscriptionCreate) (*models.RenewSubscription, error) {
				return &models.RenewSubscription{}, nil
			})

			args := apple.CallbackNotification{
				SignedPayload: "test",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/callback/ios-notify?appid=test_appid", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := float64(1)
			if resp, ok := resp["error_no"]; ok {
				output = resp.(float64)
			}
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}
