package translate

import (
	"zeus/controllers"
	"zeus/pkg/parrot"

	"github.com/gin-gonic/gin"
)

type GetSupportLanguagesForm struct {
	AppId string `json:"appid" binding:"required"`
}

type TranslateForm struct {
	AppId   string `json:"appid" binding:"required"`
	UserID  string `json:"user_id" binding:"required"`
	Target  string `json:"target"  binding:"required"`
	Content string `json:"content" binding:"required"`
}

type TranslateResp struct {
	Content string
}

// Translate
// @Summary
// @Tags	SDK Translate
// @Accept	json
// @Produce json
// @Param	person	body  TranslateForm	 true "翻译"
// @Success 200 {object} controllers.SuccessResponse{result=TranslateResp}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /translator/translate [post]
func (t Controller) Translate(c *gin.Context) {
	var form TranslateForm
	err := c.ShouldBindJSON(&form)
	if err != nil {
		t.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	client := parrot.NewClient()
	content, err := client.Translate(c.Request.Context(), form.UserID, form.Target,
		form.Content)
	if err != nil {
		t.Error(c, controllers.ErrorTranslateFailed, err)
		return
	}

	t.Success(c, TranslateResp{Content: content})
}

// GetSupportLanguages
// @Summary
// @Tags	SDK Translate
// @Accept	json
// @Produce json
// @Param	person	body  GetSupportLanguagesForm	 true "获取支持的语言"
// @Success 200 {object} controllers.SuccessResponse{result=parrot.SupportLanguages}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /translator/get_support_languages [post]
func (t Controller) GetSupportLanguages(c *gin.Context) {
	client := parrot.NewClient()
	t.Success(c, client.GetSupportLanguages(c.Request.Context()))
}
