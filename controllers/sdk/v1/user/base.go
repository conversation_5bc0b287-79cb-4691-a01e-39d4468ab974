package user

import (
	"github.com/gin-gonic/gin"

	"zeus/controllers"
)

type Controller struct {
	controllers.BaseController
}

var User = Controller{}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.POST("/user/info", User.UserInfo)
	routerGroup.POST("/user/login", User.UserLogin)
	routerGroup.POST("/login/verify", User.LoginVerify)
	routerGroup.POST("/user/register", User.UserRegister)
	routerGroup.POST("/user/update", User.UserUpdate)
	routerGroup.POST("/user/third-list", User.ThirdListInfo)
	routerGroup.POST("/user/bind-list", User.BindListInfo)
	routerGroup.POST("/user/third-login", User.UserThirdLogin)
	routerGroup.POST("/user/unbind-third", User.UserUnbindThird)
	routerGroup.POST("/user/bind-third", User.UserBindThird)
	routerGroup.POST("/user/unregister", User.UserUnregister)
	routerGroup.POST("/user/unregister/apple_token", User.RevokeAppleToken)
	routerGroup.POST("/user/third-user", User.GetUserInfoByUnionID)
}
