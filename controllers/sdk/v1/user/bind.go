package user

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"time"

	"zeus/pkg/redis"
	"zeus/schema/request"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/field"
	"zeus/services/settings/store"
	"zeus/services/settings/thirdlogin"
	"zeus/services/third_account"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/services"

	"github.com/gin-gonic/gin"
)

type UpdateForm struct {
	UserId string `json:"user_id" binding:"required"`
	Name   string `json:"name"`
}

type BindListForm struct {
	UserId string `json:"user_id" binding:"required"`
}

type ThirdUnionForm struct {
	Appid   string `json:"appid" binding:"required"`
	Type    string `json:"type" binding:"required"`
	UnionID string `json:"union_id" binding:"required"`
}

// UserBindThird
// @Summary	绑定第三方登录
// @Tags	SDK User
// @Accept	json
// @Produce json
// @Param	person	body	request.BindThirdForm	true	"绑定第三方登录"
// @Success 200 {object} controllers.SuccessResponse{result=request.BindThirdForm}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/bind-third [post]
func (u Controller) UserBindThird(c *gin.Context) {
	bindForm := request.BindThirdForm{}
	err := c.ShouldBindJSON(&bindForm)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	user, _ := services.GetUserByUserID(c, bindForm.UserId)
	if user.ID == 0 {
		u.Error(c, controllers.ErrorUserNotExists, errors.New("该用户不存在"))
		return
	}

	filter := map[string]interface{}{"user_id": bindForm.UserId, "type": bindForm.Type}
	userBindThird, _ := services.QueryBindThird(c, filter)
	if userBindThird.Status == constants.BoundStatus {
		u.Error(c, controllers.ErrorThirdUserBinded, errors.New("该用户已经绑定"))
		return
	}

	dbGame, _ := game.GetGameByAppid(bindForm.APPID)
	userInfo, statusCode, err := third_account.GetThirdUserInfo(c, bindForm.APPID, bindForm.ThirdForm)
	if err != nil {
		u.Error(c, statusCode, err)
		return
	}

	filter = map[string]interface{}{"union_id": userInfo.UnionID, "type": bindForm.Type, "status": constants.BoundStatus}
	userBindThirdAll, _ := services.QueryAllBindThird(c, filter)

	var userIDs []int
	for _, ubt := range userBindThirdAll {
		userIDs = append(userIDs, ubt.UserID)
	}
	users, _ := services.GetUserByIDs(c, userIDs)

	var config thirdlogin.ThirdLogin
	converter.QueryByAppid(&config, bindForm.APPID)

	userBindThirdMax := 0
	for _, q := range users {
		if q.GameID == dbGame.Id {
			userBindThirdMax++
		}
	}
	var maxCount int = 1
	if *config.BoundMaxCount > 1 {
		maxCount = *config.BoundMaxCount
	}

	if userBindThirdMax >= maxCount {
		u.Error(c, controllers.ErrorThirdAccountBinded, errors.New("该第三方账号绑定已经达到上限"))
		return
	}

	if userBindThird.ID == 0 {
		userBindThird = &models.UserBindThird{}
	}

	userBindThird.UnionID = userInfo.UnionID
	userBindThird.UserID = bindForm.UserId
	userBindThird.Type = bindForm.Type
	nowTime := time.Now()
	userBindThird.CreateTime = nowTime
	userBindThird.UpdateTime = nowTime
	userBindThird.Status = constants.BoundStatus
	userBindThird.UserName = userInfo.UserName
	err = services.SaveBindThird(c, userBindThird)
	if err != nil {
		u.Error(c, controllers.ErrorDBError, errors.New("数据库异常，绑定第三方账号失败"))
		return
	}
	u.Success(c, bindForm)
}

// UserUnbindThird
// @Summary	解绑第三方登录
// @Tags	SDK User
// @Accept	json
// @Produce json
// @Param	person	body	request.UnBindThirdForm	true	"解绑第三方登录"
// @Success 200 {object} controllers.SuccessResponse{result=request.UnBindThirdForm}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/unbind-third [post]
func (u Controller) UserUnbindThird(c *gin.Context) {
	unBindForm := request.UnBindThirdForm{}
	err := c.ShouldBindJSON(&unBindForm)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	userInfo, statusCode, err := third_account.GetThirdUserInfo(c, unBindForm.APPID, unBindForm.ThirdForm)
	if err != nil {
		u.Error(c, statusCode, err)
		return
	}

	filter := map[string]interface{}{
		"user_id":  unBindForm.UserId,
		"union_id": userInfo.UnionID,
		"type":     unBindForm.Type,
		"status":   constants.BoundStatus,
	}
	userBindThird, _ := services.QueryBindThird(c, filter)

	if userBindThird.ID == 0 {
		u.Error(c, controllers.ErrorParamInvalid, errors.New("参数无效"))
		return
	}

	userBindThird.Status = constants.UnBoundStatus
	userBindThird.UpdateTime = time.Now()
	_ = services.SaveBindThird(c, userBindThird)
	u.Success(c, map[string]string{"type": strconv.Itoa(unBindForm.Type)})
}

// BindListInfo
// @Summary	获取用户绑定列表
// @Tags	SDK User
// @Accept	json
// @Produce json
// @Param	person	body	BindListForm	true	"获取用户绑定列表"
// @Success 200 {object} controllers.SuccessResponse{result=services.BindList}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/bind-list [post]
func (u Controller) BindListInfo(c *gin.Context) {
	user := BindListForm{}
	err := c.ShouldBindJSON(&user)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	result := services.GetBindInfoByUserID(c, user.UserId)

	u.Success(c, result)
}

type GameBindInfo struct {
	GoogleClientId string `json:"google_client_id" label:"游戏google client"`
	LineAppid      string `json:"line_appid,omitempty" label:"是否开启Line appid登录"`
}

func (g GameBindInfo) GetLabel() string {
	return "any"
}

func (g GameBindInfo) GetType() string {
	return "any"
}

// ThirdListInfo
// @Summary	获取游戏第三方登录信息
// @Tags	SDK User
// @Accept	json
// @Produce json
// @Param	person	body	ThridInfoForm	true "获取游戏第三方登录信息"
// @Success 200 {object} controllers.SuccessResponse{result=schema.GameInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/third-list [post]
func (u Controller) ThirdListInfo(c *gin.Context) {
	form := ThridInfoForm{}
	err := c.ShouldBindJSON(&form)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	result := make(map[string]interface{})

	cacheKey := fmt.Sprintf("user_third_list_%s", form.AppId)
	cacheContent := redis.Get(cacheKey)

	if cacheContent != "" {
		json.Unmarshal([]byte(cacheContent), &result)
	} else {
		var gameBindInfo GameBindInfo
		converter.QueryByAppid(&gameBindInfo, form.AppId)

		for _, account := range store.ThirdLoginSettings {
			config := reflect.New(reflect.TypeOf(account)).Interface().(field.ThirdLoginConfiger)
			converter.QueryByAppid(config.(field.BaseConfiger), form.AppId)
			if config.GetOpenStatus() == constants.IsOpen {
				result[fmt.Sprintf("is_%s_login", config.GetAbbrName())] = config.GetOpenStatus()
			}
			switch config.GetType() {
			case "ThirdLoginGoogle":
				result["google_client_id"] = config.(*thirdlogin.ThirdLoginGoogle).GoogleClientId
			case "ThirdLoginLine":
				result["line_appid"] = config.(*thirdlogin.ThirdLoginLine).LineAppId
			}
		}
		data, _ := json.Marshal(result)
		redis.Set(cacheKey, string(data), time.Second*constants.GameInfoTimeout)
	}

	u.Success(c, result)
}

// GetUserInfoByUnionID
// @Summary	根据三方union_id获取用户account信息
// @Tags	SDK User
// @Accept	json
// @Produce json
// @Param	person	body	ThirdUnionForm	true  "根据三方union_id获取用户account信息"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/third-user [post]
func (u Controller) GetUserInfoByUnionID(c *gin.Context) {
	form := ThirdUnionForm{}
	err := c.ShouldBindJSON(&form)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	userBindData := services.GetAccountByUnionID(c, form.UnionID, form.Type)
	u.Success(c, userBindData)
}
