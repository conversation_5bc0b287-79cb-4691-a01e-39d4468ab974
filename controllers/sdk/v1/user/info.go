package user

import (
	"errors"

	"github.com/gin-gonic/gin"

	"zeus/controllers"
	"zeus/services"
)

type Info struct {
	Name    string `json:"name"`
	Account string `json:"account"`
}

type InfoForm struct {
	UserId string `json:"user_id" binding:"required"`
}

// UserInfo
// @Summary	获取用户信息
// @Tags	SDK User
// @Accept	json
// @Produce json
// @Param	person	body	InfoForm	true	"获取用户信息"
// @Success 200 {object} controllers.SuccessResponse{result=Info}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/info [post]
func (u Controller) UserInfo(c *gin.Context) {
	user := InfoForm{}
	err := c.ShouldBindJSON(&user)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbUser, _ := services.GetUserByUserID(c, user.UserId)
	if dbUser.ID == 0 {
		u.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	u.Success(c, Info{dbUser.Name, dbUser.Account})
}
