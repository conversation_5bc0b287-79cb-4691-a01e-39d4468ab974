package user

import (
	"errors"
	"strconv"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/idno"
	"zeus/schema/domain"
	"zeus/schema/request"
	"zeus/services"
	"zeus/services/game"

	"github.com/gin-gonic/gin"
)

type LoginInfo struct {
	Id                       int    `json:"id"`
	Account                  int    `json:"account"`
	Token                    string `json:"token"`
	IsRealNameAuthentication bool   `json:"is_real_name_authentication"`
	Adult                    bool   `json:"adult"`
}

func NewLoginInfo(user *models.User) *LoginInfo {
	var l LoginInfo
	l.Id = user.ID
	l.Account, _ = strconv.Atoi(user.Account)
	l.Token = user.Token
	if user.IsRealNameAuthentication == constants.Authorized {
		l.IsRealNameAuthentication = true
	}
	age := idno.GetAgeByBirthDayYMD(user.Birthday)
	if age >= 18 {
		l.Adult = true
	}
	return &l
}

type VerifyForm struct {
	APPID  string `json:"appid" binding:"required"`
	UserId string `json:"user_id" binding:"required"`
	Token  string `json:"token" binding:"required"`
}

type LoginResult struct {
	ID      int    `json:"id,string"`
	Account string `json:"account"`
	Token   string `json:"token"`
}

type ThirdLoginForm struct {
	APPID       string `json:"appid"`
	Device      string `json:"device" binding:"required"`
	Type        string `json:"type" binding:"required"`
	UnionId     string `json:"union_id" binding:"required"`
	Platform    string `json:"platform" binding:"required" `
	Token       string `json:"token"`
	SecretToken string `json:"secret_token"`
	MobileInfo  string `json:"mobile_info" `
}

type ThridInfoForm struct {
	AppId string `json:"appid" binding:"required"`
}

// UserLogin
// @Summary	用户登录
// @Tags	SDK User
// @Accept	json
// @Produce json
// @Param	person	body	request.Login	true	"用户登录"
// @Success 200 {object} controllers.SuccessResponse{result=user.LoginInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/login [post]
func (u Controller) UserLogin(c *gin.Context) {
	req := new(request.Login)
	err := c.ShouldBindJSON(&req)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	login := &domain.Login{}
	login.Map(req)

	ls := services.ChoiceLoginServices(login)
	dbUser, err := ls.Login(c)
	if err != nil {
		u.Error(c, services.GetLoginErrorCode(err), err)
		return
	}
	resp := NewLoginInfo(dbUser)
	u.Success(c, resp)
}

// UserThirdLogin Login
// @Summary	第三方登录（作废）
// @Tags	SDK-User
// @Accept	json
// @Produce json
// @Param	person	body	ThirdLoginForm	true	"第三方登录"
// @Success 200 {object} controllers.SuccessResponse{result=user.LoginResult}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/third-login [post]
func (u Controller) UserThirdLogin(c *gin.Context) {
	var thirdLoginForm ThirdLoginForm
	var err error

	err = c.ShouldBindJSON(&thirdLoginForm)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	thirdType, err := strconv.Atoi(thirdLoginForm.Type)

	var userBindThird *models.UserBindThird
	var user *models.User

	filter := map[string]interface{}{
		"union_id": thirdLoginForm.UnionId,
		"type":     thirdType,
		"status":   constants.BoundStatus,
	}
	userBindThird, _ = services.QueryBindThird(c, filter)

	if userBindThird.ID != 0 {
		user, _ = services.GetUserByUserID(c, userBindThird.UserID)
		user.LoginTime = time.Now()
		user.Token = services.GenerateToken()
		user.LoginIP = c.ClientIP()

		services.RecordLoginDevice(c, user, thirdLoginForm.Device)
		user.LoginCnt += 1
		services.SaveUser(c, user)
	} else {
		user = &models.User{}
		dbGame, _ := game.GetGameByAppid(thirdLoginForm.APPID)
		user.Account = services.GenerateAccountId(c)
		user.Platform = thirdLoginForm.Platform
		user.PwMd5 = constants.DefaultPassword
		user.GameID = dbGame.Id
		user.RegisterTime = time.Now()
		user.RegisterIP = c.ClientIP()
		user.LoginIP = c.ClientIP()
		user.Token = services.GenerateToken()
		user.LoginCnt += 1
		user.RegisterDevice = thirdLoginForm.Device
		user.MobileInfo = thirdLoginForm.MobileInfo
		err = services.CreateUser(c, user)
		if err != nil {
			u.Error(c, controllers.ErrorDBError, errors.New("创建新用户失败"))
			return
		}

		services.RecordLoginDevice(c, user, thirdLoginForm.Device)

		userBindThird.UnionID = thirdLoginForm.UnionId
		userBindThird.UserID = user.ID
		userBindThird.Type = thirdType
		userBindThird.Status = constants.BoundStatus
		nowTime := time.Now()
		userBindThird.CreateTime = nowTime
		userBindThird.UpdateTime = nowTime
		err = services.SaveBindThird(c, userBindThird)
		if err != nil {
			u.Error(c, controllers.ErrorDBError, errors.New("用户绑定三方失败"))
			return
		}
	}

	r := LoginResult{
		ID:      user.ID,
		Account: user.Account,
		Token:   user.Token,
	}
	u.Success(c, r)
}

// LoginVerify
// @Summary	验证登录状态
// @Tags	SDK-User
// @Accept	json
// @Produce json
// @Param	person	body	VerifyForm	true	"token登录验证"
// @Router /login/verify [post]
func (u Controller) LoginVerify(c *gin.Context) {
	var verifyForm VerifyForm
	err := c.ShouldBindJSON(&verifyForm)
	if err != nil {
		u.Error(c, controllers.ErrorUnknown, err)
		return
	}
	UserId, _ := strconv.Atoi(verifyForm.UserId)
	user, _ := services.GetProjectUserByUserID(c, UserId)
	if user.ID == 0 {
		u.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}
	if user.Token == verifyForm.Token && time.Now().Before(user.LoginTime.Add(constants.LoginTokenTimeout*time.Second)) {
		u.Success(c, map[string]interface{}{"user_id": user.ID, "account": user.Account})
	} else {
		u.Error(c, controllers.ErrorToken, errors.New("登录失效"))
	}
}
