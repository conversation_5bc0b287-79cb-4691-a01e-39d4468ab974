package user

import (
	"errors"

	"zeus/controllers"
	"zeus/schema/domain"
	"zeus/schema/request"
	"zeus/schema/response"
	"zeus/services"

	"github.com/gin-gonic/gin"
)

// UserRegister
// @Summary	用户注册
// @Tags	SDK User
// @Accept	json
// @Produce json
// @Param	person	body	request.Register	true	"用户注册"
// @Success 200 {object} controllers.SuccessResponse{result=response.Register}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/register [post]
func (u Controller) UserRegister(c *gin.Context) {
	req := request.Register{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	register := domain.Register{}
	err = register.Map(req)
	if err != nil {
		u.Error(c, controllers.ErrorParamInvalid, err)
		return
	}

	rs := services.ChoiceRegisterServices(&register)
	dbUser, err := rs.Register(c)
	if err != nil {
		u.Error(c, controllers.ErrorRegisterFail, err)
		return
	}

	u.Success(c, response.Register{ID: dbUser.ID, Account: dbUser.Account})
}

// UserUnregister
// @Summary	用户注销
// @Tags	SDK-User
// @Accept	json
// @Produce json
// @Param	person	body	request.Unregister	true	"用户注销"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/unregister [post]
func (u Controller) UserUnregister(c *gin.Context) {
	req := new(request.Unregister)
	err := c.ShouldBindJSON(&req)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	acs := services.InitUnregisterServices()
	err = acs.CreateOrUpdateUnregisterWithAppid(c, req.APPID, req.Account)
	if err != nil {
		u.Error(c, controllers.ErrorDBError, errors.New("系统异常，注销账号操作失败。"))
		return
	}
	u.Success(c, "账号注销成功")
}

// RevokeAppleToken
// @Summary 撤销苹果令牌
// @Tags	SDK-User
// @Accept	json
// @Produce json
// @Param	person	body	request.RevokeAppleToken	true	"撤销苹果令牌"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/unregister/apple_token [post]
func (u Controller) RevokeAppleToken(c *gin.Context) {
	req := new(request.RevokeAppleToken)
	err := c.ShouldBindJSON(&req)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	acs := services.InitUnregisterServices()
	err = acs.RevokeAppleToken(c.Request.Context(), req.APPID, req.Token)
	if err != nil {
		u.Error(c, controllers.ErrorUnknown, errors.New("系统异常"))
		return
	}
	u.Success(c, "apple token 撤销成功")
}
