package user

import (
	"errors"

	"zeus/controllers"
	"zeus/services"

	"github.com/gin-gonic/gin"
)

// UserUpdate
// @Summary	更新用户信息 [作废]
// @Tags	SDK User
// @Accept	json
// @Produce json
// @Param	person	body	InfoForm	true	"更新用户信息"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /user/update [post]
func (u Controller) UserUpdate(c *gin.Context) {
	user := UpdateForm{}
	err := c.ShouldBindJSON(&user)
	if err != nil {
		u.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbUser, _ := services.GetUserByUserID(c, user.UserId)
	if dbUser.ID == 0 {
		u.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	dbUser.Name = user.Name
	err = services.SaveUser(c, dbUser)
	if err != nil {
		u.Error(c, controllers.ErrorDBError, errors.New("系统异常，保存失败"))
		return
	}

	u.Success(c, "更新成功")
}
