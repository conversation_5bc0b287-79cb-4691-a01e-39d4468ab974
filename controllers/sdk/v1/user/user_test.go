package user

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/schema/domain"
	"zeus/schema/request"
	"zeus/services"
	"zeus/services/game"
	_ "zeus/services/settings"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/thirdlogin"

	"github.com/gin-gonic/gin"

	"github.com/agiledragon/gomonkey"
	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestUserInfo(t *testing.T) {
	r := gin.Default()
	r.POST("/user/info", User.UserInfo)

	Convey("TestUserInfo", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.GetUserByUserID, func(c *gin.Context, userID interface{}) (*models.User, error) {
				user := models.User{ID: 1}
				return &user, nil
			})

			args := InfoForm{
				UserId: "********",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/info", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestUserUpdate(t *testing.T) {
	r := gin.Default()
	r.POST("/user/update", User.UserUpdate)

	Convey("TestUserUpdate", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.GetUserByUserID, func(c *gin.Context, userID interface{}) (*models.User, error) {
				user := models.User{ID: 1}
				return &user, nil
			})
			ApplyFunc(services.SaveUser, func(c *gin.Context, role *models.User) error {
				return nil
			})

			args := UpdateForm{
				UserId: "********",
				Name:   "test_name",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/update", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestUserBindThird(t *testing.T) {
	r := gin.Default()
	r.POST("/user/bind-third", User.UserBindThird)

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "ThirdLogin":
			thirdLoginConfig := config.(*thirdlogin.ThirdLogin)
			boundMaxCount := 2
			thirdLoginConfig.BoundMaxCount = &boundMaxCount
		case "ThirdLoginFacebook":
			thirdLoginFacebookConfig := config.(*thirdlogin.ThirdLoginFacebook)
			openStatus := 1
			thirdLoginFacebookConfig.OpenStatus = &openStatus
		}
	})
	defer QueryMockFunc.Reset()

	gomonkey.ApplyFunc(redis.Get, func(key string) string {
		return ""
	})

	gomonkey.ApplyFunc(redis.Set, func(key string, data interface{}, duration time.Duration) error {
		return nil
	})

	Convey("TestUserBindThird", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.GetUserByUserID, func(c *gin.Context, userID interface{}) (*models.User, error) {
				user := models.User{ID: 1}
				return &user, nil
			})
			ApplyFunc(services.QueryBindThird, func(c *gin.Context, filter map[string]interface{}) (*models.UserBindThird, error) {
				UserBindThird := models.UserBindThird{}
				return &UserBindThird, nil
			})
			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				game := &models.Game{}
				return game, nil
			})
			ApplyFunc(services.QueryAllBindThird, func(c *gin.Context, filter map[string]interface{}) ([]models.UserBindThird, error) {
				var userBindThirdAll []models.UserBindThird
				return userBindThirdAll, nil
			})
			ApplyFunc(services.SaveBindThird, func(c *gin.Context, bindThird *models.UserBindThird) error {
				return nil
			})
			ApplyFunc(services.GetUserByIDs, func(c *gin.Context, ids []int) ([]models.User, error) {
				var users []models.User
				return users, nil
			})

			args := request.BindThirdForm{
				UserId: 1112222,
				ThirdForm: request.ThirdForm{
					Type:    1,
					UnionId: "test_union_id",
					APPID:   "Appid",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/bind-third", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("one func for fail", func() {
			ApplyFunc(services.GetUserByUserID, func(c *gin.Context, userID interface{}) (*models.User, error) {
				user := models.User{}
				return &user, nil
			})
			ApplyFunc(services.QueryBindThird, func(c *gin.Context, filter map[string]interface{}) (*models.UserBindThird, error) {
				UserBindThird := models.UserBindThird{}
				return &UserBindThird, nil
			})
			ApplyFunc(services.GetUserByIDs, func(c *gin.Context, ids []int) ([]models.User, error) {
				var users []models.User
				return users, nil
			})

			args := request.BindThirdForm{
				UserId: ********,
				ThirdForm: request.ThirdForm{
					Type:    1,
					UnionId: "test_union_id",
					APPID:   "Appid",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/bind-third", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			output1 := (resp["message"]).(string)
			outputExpect := float64(controllers.ErrorUserNotExists)
			outputExpect2 := "该用户不存在"
			So(output, ShouldEqual, outputExpect)
			So(output1, ShouldEqual, outputExpect2)
		})

		Convey("two func for fail", func() {
			ApplyFunc(services.GetUserByUserID, func(c *gin.Context, userID interface{}) (*models.User, error) {
				user := models.User{ID: 1}
				return &user, nil
			})
			ApplyFunc(services.QueryBindThird, func(c *gin.Context, filter map[string]interface{}) (*models.UserBindThird, error) {
				UserBindThird := models.UserBindThird{Status: 1}
				return &UserBindThird, nil
			})
			ApplyFunc(services.GetUserByIDs, func(c *gin.Context, ids []int) ([]models.User, error) {
				var users []models.User
				return users, nil
			})

			args := request.BindThirdForm{
				UserId: ********,
				ThirdForm: request.ThirdForm{
					Type:    1,
					UnionId: "test_union_id",
					APPID:   "Appid",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/bind-third", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			output1 := (resp["message"]).(string)
			outputExpect := float64(controllers.ErrorThirdUserBinded)
			outputExpect2 := "该用户已经绑定"
			So(output, ShouldEqual, outputExpect)
			So(output1, ShouldEqual, outputExpect2)
		})

		Convey("three func for fail", func() {
			ApplyFunc(services.GetUserByUserID, func(c *gin.Context, userID interface{}) (*models.User, error) {
				user := models.User{ID: 1}
				return &user, nil
			})
			ApplyFunc(services.QueryBindThird, func(c *gin.Context, filter map[string]interface{}) (*models.UserBindThird, error) {
				UserBindThird := models.UserBindThird{}
				return &UserBindThird, nil
			})
			ApplyFunc(services.QueryAllBindThird, func(c *gin.Context, filter map[string]interface{}) ([]models.UserBindThird, error) {
				var userBindThirdAll []models.UserBindThird
				userBindThirdAll = append(userBindThirdAll, models.UserBindThird{})
				return userBindThirdAll, nil
			})
			QueryMockFunc1 := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
				switch config.GetType() {
				case "ThirdLogin":
					thirdLoginConfig := config.(*thirdlogin.ThirdLogin)
					boundMaxCount := 1
					thirdLoginConfig.BoundMaxCount = &boundMaxCount
				}
			})
			defer QueryMockFunc1.Reset()
			ApplyFunc(services.GetUserByIDs, func(c *gin.Context, ids []int) ([]models.User, error) {
				var users []models.User
				users = append(users, models.User{})
				return users, nil
			})

			args := request.BindThirdForm{
				UserId: ********,
				ThirdForm: request.ThirdForm{
					Type:    1,
					UnionId: "test_union_id",
					APPID:   "Appid",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/bind-third", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			output1 := (resp["message"]).(string)
			outputExpect := float64(controllers.ErrorThirdAccountBinded)
			outputExpect2 := "该第三方账号绑定已经达到上限"
			So(output, ShouldEqual, outputExpect)
			So(output1, ShouldEqual, outputExpect2)
		})
	})
}

func TestUserUnBindThird(t *testing.T) {
	r := gin.Default()
	r.POST("/user/unbind-third", User.UserUnbindThird)

	ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		thirdLoginConfig := config.(*thirdlogin.ThirdLoginFacebook)
		openStatus := 0
		thirdLoginConfig.OpenStatus = &openStatus
	})

	Convey("TestUserUnBindThird", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.QueryBindThird, func(c *gin.Context, filter map[string]interface{}) (*models.UserBindThird, error) {
				UserBindThird := models.UserBindThird{ID: 1, UserID: ********, Type: 1}
				return &UserBindThird, nil
			})
			ApplyFunc(services.SaveBindThird, func(c *gin.Context, bindThird *models.UserBindThird) error {
				return nil
			})

			args := request.UnBindThirdForm{
				UserId: ********,
				ThirdForm: request.ThirdForm{
					Type:    1,
					UnionId: "test_union_id",
					APPID:   "Appid",
				},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/unbind-third", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestUserRegister(t *testing.T) {
	r := gin.Default()
	r.POST("/user/register", User.UserRegister)

	ApplyFunc(services.ChoiceRegisterServices, func(r *domain.Register) services.Registrar {
		return &services.DeviceRegisterServices{}
	})

	var drs *services.DeviceRegisterServices
	ApplyMethod(reflect.TypeOf(drs), "Register", func(_ *services.DeviceRegisterServices, _ *gin.Context) (*models.User, error) {
		return &models.User{}, nil
	})

	Convey("TestUserRegister", t, func() {
		Convey("one func for succ", func() {
			var us *services.UserServices
			ApplyMethod(reflect.TypeOf(us), "CreateUser", func(_ *services.UserServices, _ *gin.Context) (*models.User, error) {
				dbUser := models.User{}
				return &dbUser, nil
			})

			args := request.Register{
				Device:     "test_device",
				Platform:   "test_platform",
				ForceRenew: "true",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/register", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestUserLogin(t *testing.T) {
	r := gin.Default()
	r.POST("/user/login", User.UserLogin)

	ApplyFunc(services.ChoiceLoginServices, func(_ *domain.Login) services.Loginer {
		return &services.DeviceLoginServices{}
	})

	var dls *services.DeviceLoginServices
	ApplyMethod(reflect.TypeOf(dls), "Login", func(_ *services.DeviceLoginServices, _ *gin.Context) (*models.User, error) {
		return &models.User{}, nil
	})

	Convey("TestUserBindThird", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.QueryOneUser, func(c *gin.Context, args map[string]interface{}) (*models.User, error) {
				user := models.User{ID: 1}
				return &user, nil
			})
			ApplyFunc(services.SaveUser, func(c *gin.Context, user *models.User) error {
				return nil
			})

			args := request.Login{
				APPID:   "test_appid",
				Device:  "test_device",
				Account: "********",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestUserThirdLogin(t *testing.T) {
	r := gin.Default()
	g := r.Group("/")
	SetupRouter(g)

	Convey("TestUserThirdLogin", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.QueryBindThird, func(c *gin.Context, args map[string]interface{}) (*models.UserBindThird, error) {
				userBindThird := models.UserBindThird{ID: 1, Status: constants.BoundStatus}
				return &userBindThird, nil
			})
			ApplyFunc(services.GetUserByUserID, func(c *gin.Context, userID interface{}) (*models.User, error) {
				user := models.User{}
				return &user, nil
			})
			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			ApplyFunc(services.SaveUser, func(c *gin.Context, user *models.User) error {
				return nil
			})
			ApplyFunc(services.CreateUser, func(c *gin.Context, user *models.User) error {
				return nil
			})
			ApplyFunc(services.SaveBindThird, func(c *gin.Context, bindThird *models.UserBindThird) error {
				return nil
			})
			ApplyFunc(services.RecordLoginDevice, func(_ *gin.Context, _ *models.User, _ string) {
			})

			args := ThirdLoginForm{
				Device:   "test_device",
				Type:     "1",
				UnionId:  "********",
				Platform: "test_platform",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/third-login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
		Convey("twe func for succ", func() {
			ApplyFunc(services.QueryBindThird, func(c *gin.Context, args map[string]interface{}) (*models.UserBindThird, error) {
				userBindThird := models.UserBindThird{}
				return &userBindThird, nil
			})

			ApplyFunc(services.GenerateAccountId, func(c *gin.Context) string {
				return "********"
			})

			ApplyFunc(services.SaveUser, func(c *gin.Context, user *models.User) error {
				return nil
			})
			ApplyFunc(services.CreateUser, func(c *gin.Context, user *models.User) error {
				return nil
			})

			ApplyFunc(services.SaveBindThird, func(c *gin.Context, bindThird *models.UserBindThird) error {
				return nil
			})
			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})

			args := ThirdLoginForm{
				Device:   "test_device",
				Type:     "1",
				UnionId:  "********",
				Platform: "test_platform",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/third-login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestLoginVerify(t *testing.T) {
	r := gin.Default()
	g := r.Group("/")
	SetupRouter(g)

	Convey("TestLoginVerify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.GetProjectUserByUserID, func(c *gin.Context, userID interface{}) (*models.User, error) {
				user := models.User{ID: 111, Token: "Token"}
				return &user, nil
			})
			sj := time.Time{}
			ApplyMethod(reflect.TypeOf(sj), "Before", func(_ time.Time, _ time.Time) bool {
				return true
			})

			args := VerifyForm{
				APPID:  "APPID",
				UserId: "111",
				Token:  "Token",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/login/verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("one func for fail", func() {
			ApplyFunc(services.GetProjectUserByUserID, func(c *gin.Context, userID interface{}) (*models.User, error) {
				user := models.User{}
				return &user, nil
			})

			args := VerifyForm{
				APPID:  "APPID",
				UserId: "111",
				Token:  "Token",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/login/verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.ErrorUserNotExists)
			output2 := (resp["message"]).(string)
			outputExpect2 := "用户不存在"
			So(output, ShouldEqual, outputExpect)
			So(output2, ShouldEqual, outputExpect2)
		})
	})
}

func TestThirdListInfo(t *testing.T) {
	r := gin.Default()
	g := r.Group("/")
	SetupRouter(g)

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "any":
			thirdLoginConfig := config.(*GameBindInfo)
			thirdLoginConfig.GoogleClientId = "GoogleClientId"
			thirdLoginConfig.LineAppid = "LineAppid"
		}
	})
	defer QueryMockFunc.Reset()

	GetGameByAppidMockFunc := ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
		return &models.Game{}, nil
	})
	defer GetGameByAppidMockFunc.Reset()

	Convey("TestThirdListInfo", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(game.GetGameInfoByAppId, func(appId string) schema.GameInfo {
				return schema.GameInfo{}
			})

			args := ThridInfoForm{
				AppId: "AppId",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/third-list", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestBindListInfo(t *testing.T) {
	r := gin.Default()
	g := r.Group("/")
	SetupRouter(g)

	Convey("TestBindListInfo", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.GetBindInfoByUserID, func(c *gin.Context, userID string) map[string]string {
				data := make(map[string]string)
				return data
			})

			args := BindListForm{
				UserId: "UserId",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/bind-list", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestUserUnregister(t *testing.T) {
	r := gin.Default()
	g := r.Group("/")
	SetupRouter(g)

	apply1 := ApplyFunc(services.InitUnregisterServices, func() *services.UnregisterServices {
		return &services.UnregisterServices{}
	})
	defer apply1.Reset()

	Convey("TestUserUnregister", t, func() {
		Convey("one func for succ", func() {
			var acs *services.UnregisterServices
			func1 := ApplyMethod(reflect.TypeOf(acs), "CreateOrUpdateUnregisterWithAppid", func(
				_ *services.UnregisterServices, c *gin.Context, appid, account string,
			) error {
				return nil
			})
			defer func1.Reset()

			args := request.Unregister{
				APPID:   "7AVS2D5QH2TV",
				Account: "********",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/unregister", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("one func for fail", func() {
			var acs *services.UnregisterServices
			func1 := ApplyMethod(reflect.TypeOf(acs), "CreateOrUpdateUnregisterWithAppid", func(
				_ *services.UnregisterServices, c *gin.Context, appid, account string,
			) error {
				return errors.New("fail")
			})
			defer func1.Reset()

			args := request.Unregister{
				APPID:   "7AVS2D5QH2TV",
				Account: "********",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/unregister", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(controllers.ErrorDBError)
			So(output, ShouldEqual, outputExpect)
		})
	})
}

func TestRevokeAppleToken(t *testing.T) {
	r := gin.Default()
	g := r.Group("/")
	SetupRouter(g)

	apply1 := ApplyFunc(services.InitUnregisterServices, func() *services.UnregisterServices {
		return &services.UnregisterServices{}
	})
	defer apply1.Reset()

	Convey("TestRevokeAppleToken", t, func() {
		Convey("one func for succ", func() {
			var acs *services.UnregisterServices
			func1 := ApplyMethod(reflect.TypeOf(acs), "RevokeAppleToken", func(_ *services.UnregisterServices, _ context.Context, appid, token string) error {
				return nil
			})
			defer func1.Reset()

			args := request.RevokeAppleToken{
				APPID: "7AVS2D5QH2TV",
				Token: "********",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/user/unregister/apple_token", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}
