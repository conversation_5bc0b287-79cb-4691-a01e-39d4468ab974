package v2

import (
	"zeus/controllers"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	controllers.BaseController
}

var SV2C = Controller{}

func (tlc *Controller) GetDB(c *gin.Context) *gorm.DB {
	db := c.MustGet("db")
	return db.(*gorm.DB)
}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.POST("/user/third-login", SV2C.UserThirdLogin)
	routerGroup.POST("/order/google-verify", SV2C.GoogleVerify)
	routerGroup.POST("/order/ios-exchange", SV2C.IOSExchange)
	routerGroup.POST("/order/ios-verify", SV2C.IOSVerify)
	routerGroup.POST("/subscription/ios-exchange", SV2C.IosSubscriptionExchange)
	routerGroup.POST("/subscription/ios-verify", SV2C.IosSubscriptionVerify)
}
