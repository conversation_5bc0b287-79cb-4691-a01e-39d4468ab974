package v2

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services"
	"zeus/services/pay"
	"zeus/utils"

	"github.com/gin-gonic/gin"
)

// GoogleVerify
// @Summary 验证google订单，验证成功通知CP
// @Tags	SDK-Order
// @Accept	json
// @Produce json
// @Param	order verify	body	schema.GoogleVerifyForm	true	"Google支付验证"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /v2/order/google-verify [post]
func (tlc Controller) GoogleVerify(c *gin.Context) {
	db := tlc.GetDB(c)
	var orderForm schema.GoogleVerifyForm
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		tlc.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	// 被支付订单不存在.
	filter := map[string]interface{}{"order_id": orderForm.DeveloperPayload}
	dbOrder, _ := services.QueryOrder(c, filter)
	if dbOrder.Id == 0 {
		tlc.Error(c, controllers.ErrorOrderNotExists, errors.New("订单不存在"))
		return
	}

	// 订单已被支付.
	if dbOrder.PayStatus == constants.PaymentSuccess {
		tlc.Success(c, "success")
		return
	}
	lockKey := fmt.Sprintf("transcation_%s_%s", orderForm.AppID, dbOrder.OrderId)
	err = redis.Lock(lockKey, time.Second*constants.LockTimeout)
	if err != nil {
		tlc.Error(c, controllers.ErrorRequestHasBeenProcessed, errors.New("订单正在被处理"))
		return
	}
	defer redis.UnLock(lockKey)

	info := map[string]interface{}{
		"packageName":   orderForm.PackageName,
		"productId":     orderForm.ProductID,
		"purchaseToken": orderForm.PurchaseToken,
		"appid":         orderForm.AppID,
	}

	data, e := pay.GetPurchaseInfo(c.Request.Context(), orderForm.AppID, info, true, orderForm.DeveloperPayload)
	if e != nil {
		errorMessage := fmt.Sprintf("报错信息: %s, 支付订单认证失败导致无法完成支付操作, 请确认GoogleRefreshToken数据是否过期", e)
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			tlc.Error(c, controllers.ErrorDBError, err)
			return
		}
		tlc.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}

	_, ok := data["purchaseState"]
	if !ok {
		errorMessage := "支付订单校验未获取到purchaseState字段, 订单校验失败"
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			tlc.Error(c, controllers.ErrorDBError, err)
			return
		}
		tlc.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}
	purchaseStateF := strconv.FormatFloat(data["purchaseState"].(float64), 'f', 0, 64)
	if data["purchaseState"] == "" || purchaseStateF != "0" {
		tlc.Error(c, controllers.ErrorOrderUnpaid, errors.New("订单校验失败, 请确认此笔订单玩家是否付款成功"))
		return
	}
	// 重复订单验证
	filter = map[string]interface{}{
		"pay_id":     data["orderId"],
		"pay_status": constants.PaymentSuccess,
	}
	dbOrder2, _ := services.QueryOrder(c, filter)
	if dbOrder2.OrderId != "" {
		if dbOrder2.OrderId == dbOrder.OrderId {
			tlc.Success(c, "success")
			return
		}

		errorMessage := "Google订单重复, 无法完成支付操作"
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			tlc.Error(c, controllers.ErrorDBError, err)
			return
		}
		tlc.Error(c, controllers.ErrorOrderRepeated, errors.New(errorMessage))
		return
	}

	if dbOrder.ProductId != orderForm.ProductID {
		errorMessage := fmt.Sprintf("实际支付的商品为%s, 与订单商品%s不一致", orderForm.ProductID, dbOrder.ProductId)
		tlc.Error(c, controllers.ErrorOrderRepeated, errors.New(errorMessage))
		return
	}

	googlePurchaseType, ok := data["purchaseType"]
	if ok {
		purchaseTypeS := strconv.FormatFloat(googlePurchaseType.(float64), 'f', 0, 64)
		purchaseType := constants.NormalOrder
		switch purchaseTypeS {
		case "0": // 从许可测试帐号中购买的服务
			purchaseType = constants.TestAccount
		case "1": // 使用促销代码购买
			purchaseType = constants.SalesPromotion
		case "2": // 通过观看视频广告而不是付费
			purchaseType = constants.Advertising
		}
		dbOrder.OrderAttribute = purchaseType
	}
	// 修改订单状态.
	dbOrder.PayId = data["orderId"].(string)
	dbOrder.PayTime = time.Now()
	dbOrder.PayIp = utils.GetClientIP(c)
	dbOrder.PayStatus = constants.PaymentSuccess
	if orderForm.PayAmount != "" {
		payAmount, _ := strconv.ParseFloat(orderForm.PayAmount, 64)
		dbOrder.PayAmount = payAmount
	}
	dbOrder.PayCurrency = orderForm.PayCurrency
	dbOrder.PayProductId = orderForm.ProductID
	dbOrder.Receipt = orderForm.PurchaseData
	dbOrder.VerifyStatus = constants.VerifyPassed

	err = services.SaveOrder(c, dbOrder)
	if err != nil {
		tlc.Error(c, controllers.ErrorDBError, err)
		return
	}

	services.Notify(c.Request.Context(), db, dbOrder, services.NotifyOptions{})

	tlc.Success(c, "success")
}
