package v2

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/pkg/validator"
	"zeus/schema"
	"zeus/services"
	"zeus/services/pay"
	"zeus/utils"

	"github.com/gin-gonic/gin/binding"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestGoogleVerify(t *testing.T) {
	r := gin.Default()
	group := r.Group("/v2")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	var tlc *Controller
	ApplyMethod(reflect.TypeOf(tlc), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
		return &gorm.DB{}
	})

	ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
		return nil
	})

	ApplyFunc(redis.UnLock, func(key string) int64 {
		return 0
	})

	Convey("TestGoogleVerify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(pay.GetPurchaseInfo, func(ctx context.Context, appid string, info map[string]interface{}, isNormal bool, orderID string) (map[string]interface{}, error) {
				data := make(map[string]interface{})
				data["purchaseState"] = float64(0)
				data["orderId"] = "test_order_id"
				return data, nil
			})

			ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				order := models.Order{
					Id:        1,
					OrderId:   "",
					ProductId: "test_product_id",
				}
				return &order, nil
			})

			ApplyFunc(utils.InArray, func(_ string, _ []string) bool {
				return true
			})

			ApplyFunc(services.Notify, func(ctx context.Context, db *gorm.DB, order *models.Order, _ services.NotifyOptions) error {
				return nil
			})

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			args := schema.GoogleVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "PurchaseToken",
				PayCurrency:      "CNY",
				ProductID:        "test_product_id",
				PayAmount:        "10",
				DeveloperPayload: "DeveloperPayload",
				PackageName:      "PackageName",
				PurchaseData:     "PurchaseData",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/order/google-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("同时请求订单验证", func() {
			ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
				return errors.New("Get Lock failed")
			})

			ApplyFunc(pay.GetPurchaseInfo, func(ctx context.Context, appid string, info map[string]interface{}, isNormal bool, orderID string) (map[string]interface{}, error) {
				data := make(map[string]interface{})
				data["purchaseState"] = float64(0)
				data["orderId"] = "test_order_id"
				return data, nil
			})

			ApplyFunc(services.QueryOrder, func(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
				order := models.Order{
					Id:        1,
					OrderId:   "",
					ProductId: "test_product_id",
				}
				return &order, nil
			})

			ApplyFunc(utils.InArray, func(_ string, _ []string) bool {
				return true
			})

			ApplyFunc(services.Notify, func(ctx context.Context, db *gorm.DB, order *models.Order, _ services.NotifyOptions) error {
				return nil
			})

			ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			args := schema.GoogleVerifyForm{
				AppID:            "test_appid",
				PurchaseToken:    "PurchaseToken",
				PayCurrency:      "CNY",
				ProductID:        "test_product_id",
				PayAmount:        "10",
				DeveloperPayload: "DeveloperPayload",
				PackageName:      "PackageName",
				PurchaseData:     "PurchaseData",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/order/google-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorRequestHasBeenProcessed)
		})
	})
}
