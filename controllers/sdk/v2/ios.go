package v2

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/notify"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay/apple"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/utils"

	"github.com/gin-gonic/gin"
)

const messageTemplate = "## ❗ Zeus疑似异常订单：\n " +
	"* 应用： %s (%s) \n " +
	"* 订单ID: %s \n " +
	"* 渠道: %s \n " +
	"* 角色名称: %s \n " +
	"* 角色ID: %s \n " +
	"* 区服ID: %s \n " +
	"* 商品ID: %s \n " +
	"* 创建时间: %s \n " +
	"* 异常信息: %s"

const EnvironmentSandbox = "Sandbox"

type IOSSubscriptionInfo struct {
	OrderID string `json:"order_id"`
}

// IOSExchange
// @Summary	生成ios订单
// @Tags	SDK iOSPay
// @Accept	json
// @Produce json
// @Param	person	body	schema.IOSExchangeForm	true	"生成ios订单"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /v2/order/ios-exchange [post]
func (o Controller) IOSExchange(c *gin.Context) {
	exchangeInfo := schema.IOSExchangeForm{}
	err := c.ShouldBindJSON(&exchangeInfo)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, err := game.GetGameByAppid(exchangeInfo.AppID)
	if err != nil {
		o.Error(c, controllers.ErrorParamInvalid, err)
		return
	}

	var pay payment.Payment
	converter.QueryByAppid(&pay, exchangeInfo.AppID)

	var config payment.ApplePay
	converter.QueryByAppid(&config, exchangeInfo.AppID)

	// 支付关闭
	if *pay.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		o.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}
	// 账号查询
	dbUser, _ := services.GetUserByUserID(c, exchangeInfo.UserID)
	if dbUser.ID == 0 {
		o.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	dbOrder := &models.Order{}

	dbOrder.GameId = dbGame.Id
	dbOrder.OrderId = services.GenerateUUID()
	dbOrder.Account = dbUser.Account
	dbOrder.UserId = dbUser.ID
	dbOrder.RoleId = exchangeInfo.RoleID
	dbOrder.ProductId = exchangeInfo.ProductID
	dbOrder.Price = exchangeInfo.Price
	dbOrder.Extend = exchangeInfo.Extend
	dbOrder.Device = exchangeInfo.Device
	dbOrder.RoleName = exchangeInfo.RoleName
	dbOrder.CreateTime = time.Now()
	dbOrder.Platform = constants.PlatformIOS
	dbOrder.PayWay = constants.PayWayApple
	dbOrder.ServerId = exchangeInfo.ServerID
	dbOrder.ServerName = exchangeInfo.ServerName
	dbOrder.Vip = utils.ConvertStringToInt(exchangeInfo.Vip)
	dbOrder.Level = utils.ConvertStringToInt(exchangeInfo.Level)
	dbOrder.PayNotifyUrl = exchangeInfo.PayNotifyURL

	if err := services.CreateOrder(c, dbOrder); err != nil {
		o.Error(c, controllers.ErrorOrderCreateFail, errors.New("创建ios订单失败"))
		return
	}

	IOSOrderId := map[string]string{
		"order_id": dbOrder.OrderId,
	}

	o.Success(c, IOSOrderId)
}

// IOSVerify
// @Summary	验证ios订单
// @Tags	SDK iOSPay
// @Accept	json
// @Produce json
// @Param	person	body	schema.IOSVerifyV2	true	"验证ios订单"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /v2/order/ios-verify [post]
func (o Controller) IOSVerify(c *gin.Context) {
	orderForm := schema.IOSVerifyV2{}
	err := c.ShouldBindJSON(&orderForm)
	if err != nil {
		o.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, _ := game.GetGameByAppid(orderForm.AppID)
	var pay payment.Payment
	converter.QueryByAppid(&pay, orderForm.AppID)

	var config payment.ApplePay
	converter.QueryByAppid(&config, orderForm.AppID)

	transactionInfo, err := apple.VerifyIOSOrderWithTransactionID(c.Request.Context(),
		*config.AppleISS, *config.AppleKeyID, *config.AppleBundleId, *config.AppleSecret,
		orderForm.TransactionID)
	if err != nil {
		errorMessage := "与Apple校验订单异常"
		if orderForm.OrderID != "" {
			services.UpdatePayFailedMessage(c, orderForm.OrderID, errorMessage)
		}
		log.Warn(c.Request.Context(), errorMessage, "transaction_id", orderForm.TransactionID)
		o.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}

	orderId := transactionInfo.AppAccountToken
	var dbOrder *models.Order
	if orderId == "" {
		errorMessage := "IOS返回的AppAccountToken为空"
		if orderForm.OrderID != "" {
			services.UpdatePayFailedMessage(c, orderForm.OrderID, errorMessage)
		}
		o.Error(c, controllers.ErrorOrderNotExists, errors.New("被支付订单不存在"))
		return
	} else {
		// 被支付订单是否存在
		filter := map[string]interface{}{
			"order_id": orderId,
		}
		dbOrder, _ = services.QueryOrder(c, filter)
		if dbOrder.OrderId == "" {
			errorMessage := fmt.Sprintf("根据IOS返回的AppAccountToken: %s字段查询不到订单", orderId)
			if orderForm.OrderID != "" {
				services.UpdatePayFailedMessage(c, orderForm.OrderID, errorMessage)
			}
			o.Error(c, controllers.ErrorOrderNotExists, errors.New("被支付订单不存在"))
			return
		}
	}

	// 订单已被支付
	if dbOrder.PayStatus == constants.PaymentSuccess {
		o.Success(c, "success")
		return
	}

	lockKey := fmt.Sprintf("transcation_%s_%s", orderForm.AppID, dbOrder.OrderId)
	err = redis.Lock(lockKey, time.Second*constants.LockTimeout)
	if err != nil {
		o.Error(c, controllers.ErrorRequestHasBeenProcessed, errors.New("订单正在被处理"))
		return
	}
	defer redis.UnLock(lockKey)

	err = apple.CheckAppleOrder(transactionInfo, dbOrder, *config.AppleBundleId)
	if err != nil {
		_ = services.UpdateErrorMessage(c, err.Error(), *dbOrder)

		dingTalkMessage := fmt.Sprintf(messageTemplate, dbGame.Name, dbOrder.Platform, dbOrder.OrderId,
			"苹果支付", dbOrder.RoleName, dbOrder.RoleId, dbOrder.ServerId, dbOrder.ProductId, dbOrder.CreateTime, err)
		if *pay.DingTalkToken != "" {
			d := notify.DingTalk{AccessToken: *pay.DingTalkToken}
			d.SendMarkDown(c, "苹果支付订单异常", dingTalkMessage)
		}

		log.Warn(c.Request.Context(), fmt.Sprintf("Zeus订单%s: %s", dbOrder.OrderId, err))
		o.Error(c, controllers.ErrorOrderSign, err)
		return
	}

	dbReceipt, _ := json.Marshal(transactionInfo)
	// 修改订单状态
	dbOrder.PayId = orderForm.TransactionID
	dbOrder.PayTime = time.Now()
	dbOrder.PayIp = utils.GetClientIP(c)
	dbOrder.PayStatus = constants.PaymentSuccess
	dbOrder.PayCurrency = orderForm.PayCurrency
	payAmount, _ := strconv.ParseFloat(orderForm.PayAmount, 64)
	dbOrder.PayAmount = payAmount
	dbOrder.PayProductId = transactionInfo.ProductId
	dbOrder.Receipt = string(dbReceipt)
	dbOrder.VerifyStatus = constants.VerifyPassed
	if transactionInfo.Environment == EnvironmentSandbox {
		dbOrder.OrderAttribute = constants.TestAccount
	}

	if err := services.SaveOrder(c, dbOrder); err != nil {
		o.Error(c, controllers.ErrorDBError, err)
		return
	}

	db := o.GetDB(c)
	_ = services.Notify(c.Request.Context(), db, dbOrder, services.NotifyOptions{})

	o.Success(c, "success")
}

// IosSubscriptionExchange
// @Summary	生成ios订阅订单
// @Tags	SDK iOSPay
// @Accept	json
// @Produce json
// @Param	person	body	schema.IosSubscription	true	"生成ios订阅订单"
// @Success 200 {object} controllers.SuccessResponse{result=IOSSubscriptionInfo}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /v2/subscription/ios-exchange [post]
func (s Controller) IosSubscriptionExchange(c *gin.Context) {
	order := schema.IosSubscription{}
	err := c.ShouldBindJSON(&order)
	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}

	dbGame, err := game.GetGameByAppid(order.AppID)
	if err != nil {
		s.Error(c, controllers.ErrorParamInvalid, err)
		return
	}

	var payConfig payment.Payment
	converter.QueryByAppid(&payConfig, order.AppID)

	var config payment.ApplePay
	converter.QueryByAppid(&config, order.AppID)

	// 支付关闭
	if *payConfig.IsRechargeOpen != constants.IsRechargeOpen || *config.OpenStatus != constants.IsRechargeOpen {
		s.Error(c, controllers.ErrorRechargeClose, errors.New("支付关闭"))
		return
	}
	// 账号查询
	dbUser, _ := services.GetUserByUserID(c, order.UserID)
	if dbUser.ID == 0 {
		s.Error(c, controllers.ErrorUserNotExists, errors.New("用户不存在"))
		return
	}

	dbOrder := &models.Subscription{}

	dbOrder.GameId = dbGame.Id
	dbOrder.OrderId = services.GenerateUUID()
	dbOrder.Account = dbUser.Account
	dbOrder.UserId = dbUser.ID
	dbOrder.RoleId = order.RoleID
	dbOrder.ProductId = order.ProductID
	dbOrder.Price = order.Price
	dbOrder.Extend = order.Extend
	dbOrder.Device = order.Device
	dbOrder.RoleName = order.RoleName
	dbOrder.CreateTime = time.Now()
	dbOrder.PayNotifyUrl = order.PayNotifyURL
	dbOrder.Platform = constants.PlatformIOS
	dbOrder.ServerId = order.ServerID
	dbOrder.ServerName = order.ServerName
	dbOrder.Vip = utils.ConvertStringToInt(order.Vip)
	dbOrder.Level = utils.ConvertStringToInt(order.Level)

	if err := services.CreateSubscription(c, dbOrder); err != nil {
		s.Error(c, controllers.ErrorDBError, errors.New("创建订阅订单失败"))
		return
	}

	s.Success(c, IOSSubscriptionInfo{dbOrder.OrderId})
}

// IosSubscriptionVerify
// @Summary	验证ios订阅订单
// @Tags	SDK iOSPay
// @Accept	json
// @Produce json
// @Param	person	body	schema.IOSVerifyV2	true	"验证ios订单"
// @Success 200 {object} controllers.SuccessResponse
// @Failure 400 {object} controllers.DefaultResponse
// @Router /v2/subscription/ios-verify [post]
func (s Controller) IosSubscriptionVerify(c *gin.Context) {
	db := s.GetDB(c)
	orderVerifyForm := schema.IOSVerifyV2{}
	err := c.ShouldBindJSON(&orderVerifyForm)
	if err != nil {
		s.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	dbGame, _ := game.GetGameByAppid(orderVerifyForm.AppID)

	var config payment.ApplePay
	converter.QueryByAppid(&config, orderVerifyForm.AppID)

	transactionInfo, err := apple.VerifyIOSOrderWithTransactionID(c.Request.Context(),
		*config.AppleISS, *config.AppleKeyID, *config.AppleBundleId, *config.AppleSecret,
		orderVerifyForm.TransactionID)
	if err != nil {
		errorMessage := "与Apple校验订单异常"
		log.Warn(c.Request.Context(), errorMessage, "transaction_id", orderVerifyForm.TransactionID)
		s.Error(c, controllers.ErrorOrderSign, errors.New(errorMessage))
		return
	}

	originalTransactionID := transactionInfo.OriginalTransactionId
	if originalTransactionID == "" {
		s.Error(c, controllers.ErrorOrderSign, errors.New("ios订阅订单认证失败, 缺少original_transaction_id字段"))
		return
	}

	var dbOrder *models.Subscription
	dbOrder = new(models.Subscription)

	filter := map[string]interface{}{
		"original_transaction_id": originalTransactionID,
		"game_id":                 dbGame.Id,
	}
	dbOrderOriginal, _ := services.QuerySubscription(c, filter)
	log.Debug(c.Request.Context(), "根据original_transaction_id查询原始订单", "filter", filter, "original_dbOrder", dbOrderOriginal)
	// 查询到原始订单，且当前orderId不为空
	if dbOrderOriginal.OrderId != "" && orderVerifyForm.OrderID != "" {
		dbOrderOriginal.OriginalTransactionId = dbOrderOriginal.OriginalTransactionId + "_" + time.Now().Format("20060102150405")
		log.Debug(c.Request.Context(), fmt.Sprintf("将原始订单%s修改original_transaction_id为%s归档保存", dbOrderOriginal.OrderId, dbOrderOriginal.OriginalTransactionId))

		if err := services.SaveSubscription(c, dbOrderOriginal); err != nil {
			s.Error(c, controllers.ErrorDBError, fmt.Errorf("ios订阅原始订单%s归档修改失败", dbOrderOriginal.OrderId))
			return
		}
	}
	if orderVerifyForm.OrderID != "" {
		filter = map[string]interface{}{
			"order_id": orderVerifyForm.OrderID,
			"game_id":  dbGame.Id,
		}
		if transactionInfo.AppAccountToken != orderVerifyForm.OrderID {
			log.Debug(c.Request.Context(), "ios订阅Zeus表中OrderID与appAccountToken不同,使用appAccountToken查询zeus订单", "order_id", orderVerifyForm.OrderID, "appAccountToken", transactionInfo.AppAccountToken)

			filter["order_id"] = transactionInfo.AppAccountToken
		}

		dbOrder, err = services.QuerySubscription(c, filter)
		log.Debug(c.Request.Context(), "根据orderId查询当前订单", "filter", filter, "dbOrder", dbOrder)
		// 被支付订单不存在
		if err != nil {
			s.Error(c, controllers.ErrorOrderNotExists, fmt.Errorf("订单：%s 不存在, %s", orderVerifyForm.OrderID, err.Error()))
			return
		}
	} else {
		// 在原始订单保存数据
		dbOrder = dbOrderOriginal
	}

	transactionInfoJson, _ := json.Marshal(transactionInfo)

	if dbOrder.PayStatus != constants.PaymentSuccess {
		log.Debug(c.Request.Context(), "订单状态: 未支付, 表示首次订阅")

		dbOrder.OriginalTransactionId = transactionInfo.OriginalTransactionId
		dbOrder.PayId = transactionInfo.TransactionId
		dbOrder.OriginReceipt = transactionInfo.TransactionId
		dbOrder.PayProductId = transactionInfo.ProductId
		dbOrder.OriginReceiptInfo = string(transactionInfoJson)
		dbOrder.PayTime = time.Now()
		dbOrder.PayStatus = constants.PaymentSuccess
		dbOrder.PayCurrency = orderVerifyForm.PayCurrency
		payAmount, _ := strconv.ParseFloat(orderVerifyForm.PayAmount, 64)
		dbOrder.PayAmount = payAmount
	}

	if transactionInfo.ProductId == "" {
		s.Error(c, controllers.ErrorOrderSign, errors.New("IOS订阅验证返回数据中缺少product_id参数"))
		return
	}
	if transactionInfo.ExpiresDate != 0 {
		dbOrder.ExpiresDate = transactionInfo.ExpiresDate / 1000
	} else {
		s.Error(c, controllers.ErrorOrderSign, errors.New("IOS订阅验证返回数据中缺少过期时间expiresDate参数"))
		return
	}

	var newPayPid string
	if dbOrder.PayProductId != "" {
		// 非首次
		pids := strings.Split(dbOrder.PayProductId, "->")
		if pids[len(pids)-1] != transactionInfo.ProductId {
			// 不是同一商品
			newPayPid = dbOrder.PayProductId + "->" + transactionInfo.ProductId
			log.Debug(c.Request.Context(), "非首次订阅且不是同一商品", "order_id", dbOrder.OrderId, "PayProductId", dbOrder.PayProductId, "newPayPid", newPayPid)
		}
	}

	if newPayPid != "" {
		dbOrder.PayProductId = newPayPid
	}
	// 修改订单状态
	dbOrder.NotifyCnt = 0
	dbOrder.NotifyStatus = constants.UnNotified
	dbOrder.ProductId = transactionInfo.ProductId
	dbOrder.LatestReceipt = transactionInfo.TransactionId
	dbOrder.LatestReceiptInfo = string(transactionInfoJson)

	notificationType := dbOrder.NotificationType
	if utils.InArray(strconv.Itoa(notificationType), []string{strconv.Itoa(constants.SubscriptionOrderIgnore)}) {
		dbOrder.NotifyStatus = constants.NotifySuccess
	}
	if err := services.SaveSubscription(c, dbOrder); err != nil {
		s.Error(c, controllers.ErrorDBError, errors.New("订阅修改失败"))
		return
	}
	renewSubscription := new(models.RenewSubscription)
	_ = services.NotifySubscription(c.Request.Context(), db, dbOrder, false, renewSubscription)

	s.Success(c, "success")
}
