package v2

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/pkg/validator"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay/apple"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin/binding"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestOrderInterface(t *testing.T) {
	r := gin.Default()
	group := r.Group("/v2")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
		return &models.User{
			ID: 1111,
		}, nil
	})
	ApplyFunc(services.GenerateOrderID, func(_ int, _ int, _ ...string) string {
		return "test_order_id"
	})
	ApplyFunc(services.CreateOrder, func(_ *gin.Context, _ *models.Order) error {
		return nil
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentApplePay":
			applePay := config.(*payment.ApplePay)
			applePassword := "ApplePassword"
			applePay.ApplePassword = &applePassword
			status := 1
			applePay.OpenStatus = &status
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	Convey("TestIOSOrder", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			args := schema.IOSExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/order/ios-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("Failed with empty params", func() {
			args := schema.IOSExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/order/ios-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorEmptyParam)
		})

		Convey("Failed with get game by appid", func() {
			ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return nil, errors.New("Get Game By Appid Error")
			})
			args := schema.IOSExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/order/ios-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorParamInvalid)
		})

		Convey("Failed with reCharge is closed", func() {
			ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
				switch config.GetType() {
				case "PaymentApplePay":
					applePay := config.(*payment.ApplePay)
					applePassword := "ApplePassword"
					applePay.ApplePassword = &applePassword
					status := 0
					applePay.OpenStatus = &status
				case "Payment":
					payConfig := config.(*payment.Payment)
					status := constants.IsRechargeOpen
					payConfig.IsRechargeOpen = &status
				}
			})

			args := schema.IOSExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/order/ios-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorRechargeClose)
		})
	})
}

func TestIOSOrderVerify(t *testing.T) {
	r := gin.Default()
	group := r.Group("/v2")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	var c *Controller
	ApplyMethod(reflect.TypeOf(c), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
		return &gorm.DB{}
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentApplePay":
			applePay := config.(*payment.ApplePay)
			applePassword := "ApplePassword"
			applePay.ApplePassword = &applePassword
			status := 1
			applePay.OpenStatus = &status
			appleISS := "AppleISS"
			applePay.AppleISS = &appleISS
			appleKeyID := "AppleKeyID"
			applePay.AppleKeyID = &appleKeyID
			appleBundleId := "AppleBundleId"
			applePay.AppleBundleId = &appleBundleId
			appleSecret := "AppleSecret"
			applePay.AppleSecret = &appleSecret
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
		return nil
	})

	ApplyFunc(redis.UnLock, func(key string) int64 {
		return 0
	})

	Convey("TestIOSOrderVerify", t, func() {
		Convey("成功 订单验证通过", func() {
			mockGetGameByAppidFunc := ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			defer mockGetGameByAppidFunc.Reset()

			mockVerifyIOSOrderWithTransactionIDFunc := ApplyFunc(apple.VerifyIOSOrderWithTransactionID,
				func(_ context.Context, _ string, _ string, _ string, _ string, _ string) (schema.SignedTransactionInfo, error) {
					return schema.SignedTransactionInfo{AppAccountToken: "test_order_id"}, nil
				})
			defer mockVerifyIOSOrderWithTransactionIDFunc.Reset()

			mockQueryOrderFunc := ApplyFunc(services.QueryOrder, func(_ *gin.Context,
				_ map[string]interface{},
			) (*models.Order, error) {
				return &models.Order{
					OrderId:    "test_order_id",
					ProductId:  "com.topjoy.test.goods1",
					CreateTime: time.Unix(**********, 0),
				}, nil
			})
			defer mockQueryOrderFunc.Reset()

			mockCheckAppleOrderFunc := ApplyFunc(apple.CheckAppleOrder, func(_ schema.SignedTransactionInfo,
				_ *models.Order, _ string,
			) error {
				return nil
			})
			defer mockCheckAppleOrderFunc.Reset()

			mockSaveOrderFunc := ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})
			defer mockSaveOrderFunc.Reset()

			mockNotifyFunc := ApplyFunc(services.Notify, func(_ context.Context, _ *gorm.DB,
				_ *models.Order, _ services.NotifyOptions,
			) error {
				return nil
			})
			defer mockNotifyFunc.Reset()

			args := schema.IOSVerifyV2{
				AppID:         "test_appid",
				OrderID:       "test_order_id",
				TransactionID: "test_order_id",
				PayCurrency:   "test_pay_currency",
				PayAmount:     "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/order/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("同时请求订单验证", func() {
			mockGetGameByAppidFunc := ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			defer mockGetGameByAppidFunc.Reset()

			ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
				return errors.New("Get Lock Failed")
			})

			mockVerifyIOSOrderWithTransactionIDFunc := ApplyFunc(apple.VerifyIOSOrderWithTransactionID,
				func(_ context.Context, _ string, _ string, _ string, _ string, _ string) (schema.SignedTransactionInfo, error) {
					return schema.SignedTransactionInfo{AppAccountToken: "test_order_id"}, nil
				})
			defer mockVerifyIOSOrderWithTransactionIDFunc.Reset()

			mockQueryOrderFunc := ApplyFunc(services.QueryOrder, func(_ *gin.Context,
				_ map[string]interface{},
			) (*models.Order, error) {
				return &models.Order{
					OrderId:    "test_order_id",
					ProductId:  "com.topjoy.test.goods1",
					CreateTime: time.Unix(**********, 0),
				}, nil
			})
			defer mockQueryOrderFunc.Reset()

			mockCheckAppleOrderFunc := ApplyFunc(apple.CheckAppleOrder, func(_ schema.SignedTransactionInfo,
				_ *models.Order, _ string,
			) error {
				return nil
			})
			defer mockCheckAppleOrderFunc.Reset()

			mockSaveOrderFunc := ApplyFunc(services.SaveOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})
			defer mockSaveOrderFunc.Reset()

			mockNotifyFunc := ApplyFunc(services.Notify, func(_ context.Context, _ *gorm.DB,
				_ *models.Order, _ services.NotifyOptions,
			) error {
				return nil
			})
			defer mockNotifyFunc.Reset()

			args := schema.IOSVerifyV2{
				AppID:         "test_appid",
				OrderID:       "test_order_id",
				TransactionID: "test_order_id",
				PayCurrency:   "test_pay_currency",
				PayAmount:     "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/order/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorRequestHasBeenProcessed)
		})
	})
}

func TestIosSubscriptionExchange(t *testing.T) {
	r := gin.Default()
	group := r.Group("/v2")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	ApplyFunc(services.GetUserByUserID, func(_ *gin.Context, _ interface{}) (*models.User, error) {
		return &models.User{
			ID: 1111,
		}, nil
	})

	ApplyFunc(services.CreateSubscription, func(_ *gin.Context, _ *models.Subscription) error {
		return nil
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentApplePay":
			applePay := config.(*payment.ApplePay)
			applePassword := "ApplePassword"
			applePay.ApplePassword = &applePassword
			status := 1
			applePay.OpenStatus = &status
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	Convey("TestIOSOrder", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			args := schema.IOSExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/subscription/ios-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})

		Convey("Failed with empty params", func() {
			args := schema.IOSExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/subscription/ios-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorEmptyParam)
		})

		Convey("Failed with get game by appid", func() {
			ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return nil, errors.New("Get Game By Appid Error")
			})
			args := schema.IOSExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/subscription/ios-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorParamInvalid)
		})

		Convey("Failed with reCharge is closed", func() {
			ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
				switch config.GetType() {
				case "PaymentApplePay":
					applePay := config.(*payment.ApplePay)
					applePassword := "ApplePassword"
					applePay.ApplePassword = &applePassword
					status := 0
					applePay.OpenStatus = &status
				case "Payment":
					payConfig := config.(*payment.Payment)
					status := constants.IsRechargeOpen
					payConfig.IsRechargeOpen = &status
				}
			})

			args := schema.IOSExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					AppID:     "test_appid",
					UserID:    1111,
					RoleID:    "test_role_id",
					ProductID: "test_product_id",
					Price:     10,
					Extend:    "test_Extend",
					Device:    "test_device",
					RoleName:  "test_role_name",
				},
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/subscription/ios-exchange", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			So(resp["error_no"].(float64), ShouldEqual, controllers.ErrorRechargeClose)
		})
	})
}

func TestIOSSubscriptionVerify(t *testing.T) {
	r := gin.Default()
	group := r.Group("/v2")
	SetupRouter(group)
	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	var c *Controller
	ApplyMethod(reflect.TypeOf(c), "GetDB", func(_ *Controller, _ *gin.Context) *gorm.DB {
		return &gorm.DB{}
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentApplePay":
			applePay := config.(*payment.ApplePay)
			applePassword := "ApplePassword"
			applePay.ApplePassword = &applePassword
			status := 1
			applePay.OpenStatus = &status
			appleISS := "AppleISS"
			applePay.AppleISS = &appleISS
			appleKeyID := "AppleKeyID"
			applePay.AppleKeyID = &appleKeyID
			appleBundleId := "AppleBundleId"
			applePay.AppleBundleId = &appleBundleId
			appleSecret := "AppleSecret"
			applePay.AppleSecret = &appleSecret
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
		return nil
	})

	ApplyFunc(redis.UnLock, func(key string) int64 {
		return 0
	})

	Convey("TestIOSSubscriptionVerify", t, func() {
		Convey("成功 订单验证通过", func() {
			mockGetGameByAppidFunc := ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			defer mockGetGameByAppidFunc.Reset()

			mockVerifyIOSOrderWithTransactionIDFunc := ApplyFunc(apple.VerifyIOSOrderWithTransactionID,
				func(_ context.Context, _ string, _ string, _ string, _ string, _ string) (schema.SignedTransactionInfo, error) {
					return schema.SignedTransactionInfo{
						AppAccountToken: "test_order_id", ProductId: "test_subscription_product_id",
						ExpiresDate:           int(time.Now().UnixMicro()),
						OriginalTransactionId: "test_original_transaction_id",
					}, nil
				})
			defer mockVerifyIOSOrderWithTransactionIDFunc.Reset()

			mockQueryOrderFunc := ApplyFunc(services.QuerySubscription, func(_ *gin.Context,
				_ map[string]interface{},
			) (*models.Subscription, error) {
				return &models.Subscription{
					OrderId:    "test_order_id",
					ProductId:  "com.topjoy.test.goods1",
					CreateTime: time.Unix(**********, 0),
				}, nil
			})
			defer mockQueryOrderFunc.Reset()

			mockCheckAppleOrderFunc := ApplyFunc(apple.CheckAppleOrder, func(_ schema.SignedTransactionInfo,
				_ *models.Order, _ string,
			) error {
				return nil
			})
			defer mockCheckAppleOrderFunc.Reset()

			mockSaveSubscriptionFunc := ApplyFunc(services.SaveSubscription, func(c *gin.Context, order *models.Subscription) error {
				return nil
			})
			defer mockSaveSubscriptionFunc.Reset()

			mockNotifyFunc := ApplyFunc(services.NotifySubscription, func(_ context.Context, _ *gorm.DB,
				_ *models.Subscription, _ bool, _ *models.RenewSubscription,
			) error {
				return nil
			})
			defer mockNotifyFunc.Reset()

			args := schema.IOSVerifyV2{
				AppID:         "test_appid",
				OrderID:       "test_order_id",
				TransactionID: "test_order_id",
				PayCurrency:   "test_pay_currency",
				PayAmount:     "10",
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/subscription/ios-verify", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}
