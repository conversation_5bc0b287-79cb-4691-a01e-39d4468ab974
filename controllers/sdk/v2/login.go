package v2

import (
	"zeus/controllers"
	"zeus/schema/request"
	"zeus/services/game"
	"zeus/services/third_account"

	"github.com/gin-gonic/gin"
)

type ThirdLoginController struct {
	Controller
}

type VerifyForm struct {
	APPID  string `json:"appid" binding:"required"`
	UserId string `json:"user_id" binding:"required"`
	Token  string `json:"token" binding:"required"`
}

// UserThirdLogin
// @Summary	第三方登录
// @Tags	SDK User
// @Accept	json
// @Produce json
// @Param	person	body	request.ThirdLogin	true	"第三方登录"
// @Success 200 {object} controllers.SuccessResponse{result=response.ThirdLogin}
// @Failure 400 {object} controllers.DefaultResponse
// @Router /v2/user/third-login [post]
func (tlc Controller) UserThirdLogin(c *gin.Context) {
	var req request.ThirdLogin
	err := c.ShouldBind<PERSON>(&req)
	if err != nil {
		tlc.Error(c, controllers.ErrorEmptyParam, err)
		return
	}
	gameDB, _ := game.GetGameByAppid(req.APPID)
	userInfo, statusCode, err := third_account.GetThirdUserInfo(c, req.APPID, req.ThirdForm)
	if err != nil {
		tlc.Error(c, statusCode, err)
		return
	}

	resp, err := third_account.ThirdLogin(c, gameDB.Id, userInfo, req)
	if err != nil {
		tlc.Error(c, controllers.ErrorDBError, err)
		return
	}
	if len(resp) == 1 {
		tlc.Success(c, resp[0])
	} else {
		tlc.Success(c, resp)
	}
}
