package v2

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"zeus/models"
	"zeus/pkg/thirdlogin"
	"zeus/schema/request"
	"zeus/schema/response"
	"zeus/services/game"
	"zeus/services/third_account"

	_ "zeus/services/settings"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
)

func TestUserThirdLogin(t *testing.T) {
	r := gin.Default()
	group := r.Group("/v2")
	SetupRouter(group)

	ThirdLoginMockFunc := ApplyFunc(third_account.ThirdLogin, func(ctx *gin.Context, gameId int, userInfo thirdlogin.UserInfo, req request.ThirdLogin) (response.ThirdLogins, error) {
		return response.ThirdLogins{}, nil
	})
	defer ThirdLoginMockFunc.Reset()

	GetGameByAppidMockFunc := ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
		return &models.Game{}, nil
	})
	defer GetGameByAppidMockFunc.Reset()

	Convey("TestUserThirdLogin", t, func() {
		Convey("one func for succ", func() {
			args := request.ThirdLogin{
				ThirdForm: request.ThirdForm{
					APPID:       "test_appid",
					Type:        1,
					UnionId:     "test_union_id",
					Token:       "test_token",
					SecretToken: "test_secret_token",
				},
				Device:     "test_device",
				Platform:   "test_platform",
				MobileInfo: "test_mobile_info",
			}

			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/v2/user/third-login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp map[string]interface{}
			_ = json.Unmarshal(w.Body.Bytes(), &resp)
			output := (resp["error_no"]).(float64)
			outputExpect := float64(0)
			So(output, ShouldEqual, outputExpect)
		})
	})
}
