package third_pay

import (
	"sort"
	"time"
	"zeus/common/constants"
	"zeus/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Activity struct {
	Id        int                `json:"id"`
	Name      string             `json:"name"`
	StartTime string             `json:"start_time"`
	EndTime   string             `json:"end_time"`
	Products  []*ActivityProduct `json:"products"`
}

type ActivityInfo struct {
	Id            int    `json:"id"`
	Name          string `json:"name"`
	Description   string `json:"description"`
	IsTimeLimited bool   `json:"is_time_limited"`
	StartTime     string `json:"start_time"`
	EndTime       string `json:"end_time"`
	NextPeriod    bool   `json:"next_period"`
}

// GetActivity
// @Summary	获取活动
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param   currency    query  string  true  "货币类型"
// @Param   role_id    query  string  false  "角色id"
// @Param   server_id    query  string  false  "服务器id"
// @Param   project_id    query  string  true  "project_id"
// @Success 200 {object} controllers.APISuccessResponse{result=Activity}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /third-pay/activity [get]
func (ct *Controller) GetActivity(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)

	nowTime := time.Now()
	activity, err := models.GetCurrentActivity(db, nowTime)
	if err != nil {
		ct.APISuccess(c, map[string]interface{}{})
		return
	}
	products, err := models.GetProductByIds(db, activity.ActivityProducts.GetProductIds())
	if err != nil {
		ct.APISuccess(c, map[string]interface{}{})
		return
	}

	currency := c.Query("currency")
	if currency == "" && len(products) > 0 {
		for k := range products[0].Prices {
			currency = string(k)
			break
		}
	}

	activityProducts := make([]*ActivityProduct, 0)
	for _, product := range products {
		activityProducts = append(activityProducts, &ActivityProduct{
			Product: Product{
				ProductId:     product.Id,
				GameProductId: product.GameProductId,
				Name:          product.Name,
				Currency:      currency,
				Price:         product.Prices.GetPrice(currency),
			},
			BuyCount: 0,
			Limit:    activity.ActivityProducts.GetProduct(product.Id).Limit,
			index:    activity.ActivityProducts.GetProduct(product.Id).Index,
		})
	}
	sort.Slice(activityProducts, func(i, j int) bool {
		return activityProducts[i].index < activityProducts[j].index
	})

	resp := &Activity{
		Id:        activity.Id,
		Name:      activity.Name,
		StartTime: activity.StartTime.Format(constants.DateTimeMinuteOnly),
		EndTime:   activity.EndTime.Format(constants.DateTimeMinuteOnly),
		Products:  activityProducts,
	}

	roleId := c.Query("role_id")
	serverId := c.Query("server_id")
	if roleId == "" || serverId == "" {
		ct.APISuccess(c, resp)
		return
	}
	purchaseRecords, err := models.GetRoleActivityPurchaseRecords(db, roleId, serverId, activity.Id)
	if err != nil {
		ct.APISuccess(c, resp)
		return
	}
	userBuyCounts := make(map[int]int)
	for _, purchaseRecord := range purchaseRecords {
		if _, ok := userBuyCounts[purchaseRecord.ProductId]; ok {
			userBuyCounts[purchaseRecord.ProductId]++
		} else {
			userBuyCounts[purchaseRecord.ProductId] = 1
		}
	}

	for _, product := range activityProducts {
		if buyCount, ok := userBuyCounts[product.ProductId]; ok {
			product.BuyCount = buyCount
		}
	}
	ct.APISuccess(c, resp)
}

// GetActivities
// @Summary	获取活动
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Success 200 {object} controllers.APISuccessResponse{result=Activity}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /third-pay/v2/activities [get]
func (ct *Controller) GetActivities(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	roleId := c.Query("role_id")
	serverId := c.Query("server_id")
	inWhiteList := false
	userInWhiteList, _ := models.GetWebPayWhiteListByRoleId(db, roleId, serverId)
	if userInWhiteList.ID != 0 {
		inWhiteList = userInWhiteList.PreviewEnabled
	}

	var activities []models.Activity
	if inWhiteList {
		activities, _ = models.GetActivitiesBeforeDeadline(db, time.Now())
	} else {
		activities, _ = models.GetCurrentActivities(db)
	}

	var ActivityInfos []ActivityInfo
	for _, a := range activities {
		isTimeLimited := false
		startTime := ""
		endTime := ""
		if a.Type == constants.TimeLimitedActivity {
			isTimeLimited = true
			startTime = a.StartTime.Format(constants.DateTimeMinuteOnly)
			endTime = a.EndTime.Format(constants.DateTimeMinuteOnly)
		}

		if a.StartTime.Before(time.Now()) && a.EndTime.After(time.Now()) {
			ActivityInfos = append(ActivityInfos, ActivityInfo{
				Id:            a.Id,
				Name:          a.Name,
				Description:   a.Description,
				IsTimeLimited: isTimeLimited,
				StartTime:     startTime,
				EndTime:       endTime,
				NextPeriod:    false,
			})
		} else {
			ActivityInfos = append(ActivityInfos, ActivityInfo{
				Id:            a.Id,
				Name:          a.Name,
				Description:   a.Description,
				IsTimeLimited: isTimeLimited,
				StartTime:     startTime,
				EndTime:       endTime,
				NextPeriod:    true,
			})
		}
	}

	ct.APISuccess(c, ActivityInfos)
}
