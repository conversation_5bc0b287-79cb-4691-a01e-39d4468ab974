package third_pay

import (
	"zeus/controllers"

	"github.com/gin-gonic/gin"
)

type Controller struct {
	controllers.BaseController
}

var controller = Controller{}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.POST("/login", controller.Login)
	routerGroup.POST("/order", controller.Order)
	routerGroup.POST("/notify", controller.Notify)
	routerGroup.POST("/query", controller.Query)
	routerGroup.POST("/v2/order", controller.OrderV2)
	routerGroup.POST("/v2/notify", controller.NotifyV2)
	routerGroup.GET("/products", controller.GetProducts)
	routerGroup.GET("/activity", controller.GetActivity)
	routerGroup.GET("/v2/activities", controller.GetActivities)
	routerGroup.GET("/v2/products", controller.GetActivityProducts)
	routerGroup.GET("/v2/cumulative-points", controller.GetCumulativePoints)
	routerGroup.POST("/v2/points-exchange", controller.CreatePointsExchangeRecord)
}
