package third_pay

import (
	"zeus/schema/request"
	"zeus/services/third_pay"

	"github.com/gin-gonic/gin"
)

// Login
// @Summary	ThirdPay 登录
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param	person	body  request.ThirdPayLogin	true     "ThirdPay 登录"
// @Success 200 {object} controllers.APISuccessResponse{result=[]third_pay.RoleInfo}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /third-pay/login [post]
func (ct *Controller) Login(c *gin.Context) {
	req := new(request.ThirdPayLogin)
	err := c.ShouldBind(req)
	if err != nil {
		ct.APIError(c, err)
		return
	}

	service := third_pay.NewServices(c, req.ProjectID)
	roleInfos, err := service.GetRoleInfos(req.RoleID)
	if err != nil {
		ct.APIError(c, err)
		return
	}
	ct.APISuccess(c, roleInfos)
}
