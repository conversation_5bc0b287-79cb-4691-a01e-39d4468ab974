package third_pay

import (
	"strconv"

	"zeus/services/third_pay"

	"github.com/gin-gonic/gin"
)

// Notify
// @Summary	ThirdPay 回调通知
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param   merchant_id  query  string   true   "商户id"
// @Param   pay_way  query  int   true   "支付方式"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /third-pay/notify [post]
func (ct *Controller) Notify(c *gin.Context) {
	payWay, _ := strconv.Atoi(c.Query("pay_way"))

	values, _ := c.Get("request")

	service := third_pay.NewServices(c, c.Query("project_id"))
	err := service.Notify(c.Request.Context(), payWay, values)
	if err != nil {
		ct.APIError(c, err)
		return
	}

	ct.APISuccess(c, "success")
}

// NotifyV2
// @Summary	ThirdPay 回调通知
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param   merchant_id  query  string   true   "商户id"
// @Param   pay_way  query  int   true   "支付方式"
// @Success 200 {object} controllers.APISuccessResponse
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /third-pay/v2/notify [post]
func (ct *Controller) NotifyV2(c *gin.Context) {
	payWay, _ := strconv.Atoi(c.Query("pay_way"))

	values, _ := c.Get("request")

	service := third_pay.NewServices(c, c.Query("project_id"))
	err := service.NotifyV2(c.Request.Context(), payWay, values)
	if err != nil {
		ct.APIError(c, err)
		return
	}

	ct.APISuccess(c, "success")
}
