package third_pay

import (
	"errors"
	"fmt"

	"zeus/pkg/redis"
	"zeus/schema/request"
	"zeus/services/third_pay"

	"github.com/gin-gonic/gin"
)

// Order
// @Summary	ThirdPay 订单
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param	person	body  request.ThirdPayOrder	true     "三方支付"
// @Success 200
// @Router /third-pay/order [post]
func (ct *Controller) Order(c *gin.Context) {
	req := new(request.ThirdPayOrder)
	err := c.ShouldBind(req)
	if err != nil {
		ct.APIError(c, err)
		return
	}

	service := third_pay.NewServices(c, req.ProjectID)
	resp, err := service.CreateOrder(c, req)
	if err != nil {
		ct.APIError(c, err)
		return
	}

	ct.APISuccess(c, resp)
}

// OrderV2
// @Summary	ThirdPay 订单
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param	person	body  request.ThirdPayOrder	true     "三方支付"
// @Success 200
// @Router /third-pay/v2/order [post]
func (ct *Controller) OrderV2(c *gin.Context) {
	req := new(request.ThirdPayOrderV2)
	err := c.ShouldBind(req)
	if err != nil {
		ct.APIError(c, err)
		return
	}
	key := fmt.Sprintf("webPay_%s_%s_%s_%v_%v", req.ProjectId, req.ServerId, req.RoleId, req.ActivityId, req.ProductId)
	err = redis.Lock(key, 300)
	defer redis.UnLock(key)
	if err != nil {
		ct.APIError(c, errors.New("操作過於頻繁，請稍後再試"))
		return
	}
	service := third_pay.NewServices(c, req.ProjectId)
	resp, err := service.CreateOrderV2(c, req)
	if err != nil {
		ct.APIError(c, err)
		return
	}

	ct.APISuccess(c, resp)
}
