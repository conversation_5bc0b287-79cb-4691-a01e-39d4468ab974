package third_pay

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"time"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/services"
	"zeus/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CumulativePointsAndStrategyInfo struct {
	StrategyPicture          string `json:"strategy_picture"`
	CumulativePoints         int    `json:"cumulative_points"`
	IsPointsExchangeable     bool   `json:"is_points_exchangeable"`
	RemainingPointsForReward int    `json:"remaining_points_for_reward"`
	EndTime                  string `json:"end_time"`
	Description              string `json:"description"`
}

type PointsExchangeParams struct {
	RoleId       string `json:"role_id" binding:"required"`
	ServerId     string `json:"server_id" binding:"required"`
	PayNotifyUrl string `json:"pay_notify_url" binding:"required"`
}

// GetCumulativePoints
// @Summary	获取充值积分
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param   role_id    query  string  false  "角色id"
// @Param   server_id    query  string  false  "服务器id"
// @Success 200 {object} controllers.APISuccessResponse{result=cumulativePointsAndStrategyInfo}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /third-pay/v2/cumulative-points [get]
func (ct *Controller) GetCumulativePoints(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)
	cumulativePointsAndStrategyInfo := CumulativePointsAndStrategyInfo{}

	roleId := c.Query("role_id")
	serverId := c.Query("server_id")
	if roleId == "" || serverId == "" {
		ct.APISuccess(c, cumulativePointsAndStrategyInfo)
		return
	}

	currentPointStrategyRecord, err := models.GetCurrentWebPayPointStrategyRecord(db)
	if err != nil {
		ct.APISuccess(c, cumulativePointsAndStrategyInfo)
		return
	}
	cumulativePointsAndStrategyInfo.EndTime = currentPointStrategyRecord.EndTime.Format(constants.DateTimeMinuteOnly)

	cumulativePoints := models.GetPurchaseTotalPoints(db, roleId, serverId, currentPointStrategyRecord.StartTime, currentPointStrategyRecord.EndTime)
	cumulativePointsAndStrategyInfo.CumulativePoints = cumulativePoints
	cumulativePointsAndStrategyInfo.StrategyPicture = currentPointStrategyRecord.Picture
	cumulativePointsAndStrategyInfo.Description = currentPointStrategyRecord.Description

	pointsAndProducts := make([]models.PointsAndProducts, 0)
	json.Unmarshal([]byte(currentPointStrategyRecord.Products), &pointsAndProducts)
	sort.Slice(pointsAndProducts, func(i, j int) bool {
		return pointsAndProducts[i].Points < pointsAndProducts[j].Points
	})

	receivedPoints, err := services.GetReceivedPoints(db, roleId, serverId, currentPointStrategyRecord.Id)
	if err != nil {
		ct.APISuccess(c, cumulativePointsAndStrategyInfo)
		return
	}
	for index := range pointsAndProducts {
		// 跳过已经领取过的奖品
		if utils.InArray(string(rune(pointsAndProducts[index].Points)), receivedPoints) {
			continue
		}
		if cumulativePoints < pointsAndProducts[index].Points { // 当积分不够兑换当前商品时，计算还需充值多少才能兑换并跳出循环
			cumulativePointsAndStrategyInfo.RemainingPointsForReward = pointsAndProducts[index].Points - cumulativePoints
			break
		} else { // 当积分可以兑换任意商品时，标记状态为可兑换
			cumulativePointsAndStrategyInfo.IsPointsExchangeable = true
		}
	}

	ct.APISuccess(c, cumulativePointsAndStrategyInfo)
}

// CreatePointsExchangeRecord
// @Summary	领取累计积分礼品
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param   role_id    query  string  false  "角色id"
// @Param   server_id    query  string  false  "服务器id"
// @Success 200 {object} controllers.APISuccessResponse{result=activityProducts}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /third-pay/v2/points-exchange [post]
func (ct *Controller) CreatePointsExchangeRecord(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)

	var pointsExchangeParams PointsExchangeParams
	err := c.ShouldBindJSON(&pointsExchangeParams)
	if err != nil {
		ct.APIError(c, err)
		return
	}

	lockKey := fmt.Sprintf("points_exchange_%s_%s", pointsExchangeParams.RoleId, pointsExchangeParams.ServerId)
	err = redis.Lock(lockKey, time.Second*30)
	if err != nil {
		ct.APIError(c, errors.New("请求正在被处理"))
		return
	}
	defer redis.UnLock(lockKey)

	role, err := models.GetRoleByServerIdAndRoleId(db, pointsExchangeParams.RoleId, pointsExchangeParams.ServerId)
	if err != nil {
		ct.APIError(c, err)
		return
	}
	user, err := models.GetUserByID(db, role.UserID)
	if err != nil {
		ct.APIError(c, err)
		return
	}

	currentPointStrategyRecord, err := models.GetCurrentWebPayPointStrategyRecord(db)
	if err != nil {
		ct.APIError(c, err)
		return
	}
	pointsAndProducts := make([]models.PointsAndProducts, 0)
	json.Unmarshal([]byte(currentPointStrategyRecord.Products), &pointsAndProducts)

	newPointsExchangeRecords := make([]models.WebPayPointsExchangeRecord, 0)

	receivedPoints, err := services.GetReceivedPoints(db, pointsExchangeParams.RoleId, pointsExchangeParams.ServerId, currentPointStrategyRecord.Id)
	if err != nil {
		ct.APIError(c, err)
		return
	}
	cumulativePoints := models.GetPurchaseTotalPoints(db, pointsExchangeParams.RoleId, pointsExchangeParams.ServerId, currentPointStrategyRecord.StartTime, currentPointStrategyRecord.EndTime)

	payWay := constants.PayWayWebPoints
	newOrders := make([]models.Order, 0)
	for _, p := range pointsAndProducts {
		if !utils.InArray(string(rune(p.Points)), receivedPoints) && p.Points <= cumulativePoints {
			orderId := services.GenerateOrderID(payWay, constants.OrderIDTokenSize)
			extend := map[string]string{
				"order_id":   orderId,
				"product_id": p.Product,
				"role_id":    pointsExchangeParams.RoleId,
				"server_id":  pointsExchangeParams.ServerId,
				"account":    user.Account,
				"pay_way":    constants.PointsPayName,
				"region":     "",
			}
			extendString, _ := json.Marshal(extend)
			newOrders = append(newOrders, models.Order{
				OrderId:        orderId,
				GameId:         role.GameID,
				Level:          role.Level,
				RoleName:       role.RoleName,
				RoleId:         role.RoleId,
				ServerId:       role.ServerId,
				ServerName:     role.ServerName,
				UserId:         role.UserID,
				Platform:       role.Platform,
				Vip:            role.Vip,
				Account:        user.Account,
				PayIp:          c.ClientIP(),
				PayWay:         payWay,
				PayStatus:      constants.PaymentSuccess,
				CreateTime:     time.Now(),
				ProductId:      p.Product,
				PayProductId:   p.Product,
				PayAmount:      0,
				PayNotifyUrl:   pointsExchangeParams.PayNotifyUrl,
				Price:          0,
				OrderAttribute: constants.SalesPromotion,
				Extend:         string(extendString),
			})

			newPointsExchangeRecords = append(newPointsExchangeRecords, models.WebPayPointsExchangeRecord{
				OrderId:               orderId,
				RoleId:                pointsExchangeParams.RoleId,
				ServerId:              pointsExchangeParams.ServerId,
				PointStrategyRecordId: currentPointStrategyRecord.Id,
				Points:                p.Points,
				ProductId:             p.Product,
				CreateTime:            time.Now(),
				UpdateTime:            time.Now(),
			})
		}
	}

	if len(newPointsExchangeRecords) > 0 {
		if err = models.CreateOrders(db, newOrders); err != nil {
			ct.APIError(c, err)
			return
		}

		err = models.CreatePointsExchangeRecords(db, newPointsExchangeRecords)
		if err != nil {
			ct.APIError(c, err)
			return
		}

		for _, order := range newOrders {
			_ = services.Notify(c, db, &order, services.NotifyOptions{})
		}

	} else {
		ct.APIError(c, errors.New("暂无可领取礼包！"))
		return
	}

	ct.APISuccess(c, "")
}
