package third_pay

import (
	"sort"
	"strconv"
	"time"

	"zeus/common/constants"
	"zeus/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Product struct {
	ProductId     int     `json:"product_id"`
	GameProductId string  `json:"game_product_id"`
	Name          string  `json:"name"`
	Currency      string  `json:"currency"`
	Price         float64 `json:"price"`
	Description   string  `json:"description"`
	Picture       string  `json:"picture"`
	Points        int     `json:"points"`
}

type ActivityProduct struct {
	Product
	BuyCount int `json:"buy_count"`
	Limit    int `json:"limit"`
	index    int
}

// GetProducts
// @Summary	获取产品列表
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param   currency    query  string  true  "货币类型"
// @Param   project_id    query  string  true  "project_id"
// @Success 200 {object} controllers.APISuccessResponse{result=Product}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /third-pay/products [get]
func (ct *Controller) GetProducts(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)

	args := make(map[string]interface{})
	args["attribute"] = constants.NormalProduct
	products, _, err := models.GetProducts(db, 200, 1, "", "", args)
	if err != nil {
		ct.APIError(c, err)
		return
	}
	currency := c.Query("currency")
	if currency == "" && len(products) > 0 {
		for k := range products[0].Prices {
			currency = string(k)
			break
		}
	}

	resp := make([]*Product, 0)
	for _, product := range products {
		resp = append(resp, &Product{
			ProductId:     product.Id,
			GameProductId: product.GameProductId,
			Name:          product.Name,
			Currency:      currency,
			Price:         product.Prices.GetPrice(currency),
		})
	}

	sort.Slice(resp, func(i, j int) bool {
		return resp[i].Price < resp[j].Price
	})
	ct.APISuccess(c, resp)
}

// GetActivityProducts
// @Summary	获取商品
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param   currency    query  string  true  "货币类型"
// @Param   role_id    query  string  false  "角色id"
// @Param   server_id    query  string  false  "服务器id"
// @Param   project_id    query  string  true  "project_id"
// @Success 200 {object} controllers.APISuccessResponse{result=activityProducts}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /third-pay/v2/products [get]
func (ct *Controller) GetActivityProducts(c *gin.Context) {
	db := c.MustGet("db").(*gorm.DB)

	activityProducts := make([]*ActivityProduct, 0)

	activityId, _ := strconv.Atoi(c.Query("activity_id"))
	activity, err := models.GetActivityById(db, activityId)
	if err != nil {
		ct.APISuccess(c, activityProducts)
		return
	}
	roleId := c.Query("role_id")
	serverId := c.Query("server_id")

	if activity.StartTime.After(time.Now()) {
		userWhiteList, _ := models.GetWebPayWhiteListByRoleId(db, roleId, serverId)
		if userWhiteList.ID == 0 || !userWhiteList.PreviewEnabled {
			ct.APISuccess(c, activityProducts)
			return
		}
	}

	products, err := models.GetProductByIds(db, activity.ActivityProducts.GetProductIds())
	if err != nil {
		ct.APISuccess(c, activityProducts)
		return
	}

	currency := c.Query("currency")
	if currency == "" && len(products) > 0 {
		for k := range products[0].Prices {
			currency = string(k)
			break
		}
	}

	userBuyCounts := make(map[int]int)
	if roleId != "" && serverId != "" {
		purchaseRecords, err := models.GetRoleActivityPurchaseRecords(db, roleId, serverId, activity.Id)
		if err != nil {
			ct.APISuccess(c, activityProducts)
			return
		}

		for _, purchaseRecord := range purchaseRecords {
			userBuyCounts[purchaseRecord.ProductId]++
		}
	}

	var hasPointExchangeActivity bool
	if _, err := models.GetCurrentWebPayPointStrategyRecord(db); err == nil {
		hasPointExchangeActivity = true
	}

	for _, product := range products {
		buyCount := 0
		if count, ok := userBuyCounts[product.Id]; ok {
			buyCount = count
		}

		points := 0
		if activity.IsSupportCumulativePurchase && hasPointExchangeActivity {
			points = product.Points
		}
		activityProducts = append(activityProducts, &ActivityProduct{
			Product: Product{
				ProductId:     product.Id,
				GameProductId: product.GameProductId,
				Name:          product.Name,
				Currency:      currency,
				Price:         product.Prices.GetPrice(currency),
				Points:        points,
				Picture:       product.Picture,
				Description:   product.Description,
			},
			BuyCount: buyCount,
			Limit:    activity.ActivityProducts.GetProduct(product.Id).Limit,
			index:    activity.ActivityProducts.GetProduct(product.Id).Index,
		})
	}

	sort.Slice(activityProducts, func(i, j int) bool {
		return activityProducts[i].index < activityProducts[j].index
	})

	ct.APISuccess(c, activityProducts)
}
