package third_pay

import (
	"zeus/schema/request"
	"zeus/services/third_pay"

	"github.com/gin-gonic/gin"
)

// Query
// @Summary	ThirdPay 订单
// @Tags	ThirdPay
// @Accept	json
// @Produce json
// @Param	person	body  request.ThirdPayOrder	true   "订单查询"
// @Success 200 {object} controllers.APISuccessResponse{result=[]models.Order}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /third-pay/query [post]
func (ct *Controller) Query(c *gin.Context) {
	req := new(request.ThirdPayQuery)
	err := c.ShouldBind(req)
	if err != nil {
		ct.APIError(c, err)
		return
	}

	service := third_pay.NewServices(c, req.ProjectID)
	resp, err := service.Query(req)
	if err != nil {
		ct.APIError(c, err)
		return
	}

	ct.APISuccess(c, resp)
}
