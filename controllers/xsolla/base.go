package xsolla

import (
	"github.com/gin-gonic/gin"
)

type Controller struct{}

func (x Controller) ShouldBind(c *gin.Context, req any) error {
	_ = c.ShouldBindJSON(req)
	err := c.ShouldBindQuery(req)
	if err != nil {
		return err
	}
	return nil
}

var Xsolla = Controller{}

func SetupRouter(routerGroup *gin.RouterGroup) {
	routerGroup.POST("/login", Xsolla.Login)
	routerGroup.POST("/pay", Xsolla.Pay)
}
