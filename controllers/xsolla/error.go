package xsolla

type ErrorResp struct {
	Error Error `json:"error"`
}

type Error struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func InvalidUserError() *ErrorResp {
	return &ErrorResp{
		Error: Error{
			Code:    "INVALID_USER",
			Message: "Invalid user",
		},
	}
}

func InvalidParameterError() *ErrorResp {
	return &ErrorResp{
		Error: Error{
			Code:    "INVALID_PARAMETER",
			Message: "Invalid Parameter",
		},
	}
}

func InvalidSignATureError() *ErrorResp {
	return &ErrorResp{
		Error: Error{
			Code:    "INVALID_SIGNATURE",
			Message: "Invalid SignATure",
		},
	}
}
