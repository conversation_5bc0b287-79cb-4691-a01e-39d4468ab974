package xsolla

import (
	"net/http"

	"zeus/schema/domain"
	"zeus/schema/request"
	"zeus/schema/response"
	"zeus/services"

	"github.com/gin-gonic/gin"
)

// Login
// @Summary	Xsolla 登录
// @Tags	Xsolla
// @Accept	json
// @Produce json
// @Param   merchant_id  query  string   true  "merchant_id"
// @Param   project_id   query  string  true  "project_id"
// @Param	person	body  request.XsollaLogin	true	"xsolla 登录"
// @Success 200 {object} controllers.APISuccessResponse{result=response.XsollaLogin}
// @Failure 400 {object} controllers.APIDefaultResponse
// @Router /xsolla/login [post]
func (x Controller) Login(c *gin.Context) {
	req := new(request.XsollaLogin)
	err := x.ShouldBind(c, req)
	if err != nil {
		c.JSON(http.StatusBadRequest, InvalidParameterError())
		return
	}
	form := domain.XsollaLogin{}
	form.Map(req)
	xs := services.XsollaServices{}
	xs.InitXsollaServices(c, &form)
	role, err := xs.FindRole(form.RoleID)
	if err != nil {
		c.JSON(http.StatusNotFound, InvalidUserError())
		return
	}

	resp := response.NewXsollaLogin(role)
	c.JSON(http.StatusOK, resp)
}
