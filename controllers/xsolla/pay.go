package xsolla

import (
	"net/http"

	"zeus/common/constants"
	"zeus/schema/domain"
	"zeus/schema/request"
	"zeus/services"

	"zeus/common/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Pay
// @Summary	Xsolla 支付
// @Tags	Xsolla
// @Accept	json
// @Produce json
// @Param	person	body  request.XsollaPay	true	"xsolla 支付"
// @Param   merchant_id  query  string   true  "merchant_id"
// @Param   project_id   query  string  true  "project_id"
// @Success 204 {string} string
// @Failure 400 {string} string
// @Failure 500 {string} string
// @Router /xsolla/pay [post]
func (x Controller) Pay(c *gin.Context) {
	req := new(request.XsollaPay)
	err := x.ShouldBind(c, req)
	if err != nil {
		c.JSON(http.StatusBadRequest, InvalidParameterError())
		return
	}
	if req.NotificationType != constants.XsollaOrderPaidNotification {
		c.JSON(http.StatusNoContent, "")
		return
	}
	form := domain.XsollaPay{}
	form.Map(req)
	xs := services.XsollaServices{}
	xs.InitXsollaServices(c, form)
	db := c.MustGet("db").(*gorm.DB)
	for i := range form.XsollaItems {
		ok := xs.IsExistsOrder(form.XsollaItems[i].OrderID)
		if ok {
			continue
		}
		log.Debug(c.Request.Context(), "Create xsolla order", "data", form)
		order, err := xs.CreatOrder(&form.XsollaItems[i])
		if err != nil {
			c.JSON(http.StatusInternalServerError, "")
			return
		}
		_ = services.Notify(c.Request.Context(), db, order, services.NotifyOptions{})
	}
	c.JSON(http.StatusNoContent, "")
}
