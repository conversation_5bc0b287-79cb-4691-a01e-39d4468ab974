package xsolla

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"zeus/common/constants"
	"zeus/models"
	"zeus/schema/domain"
	"zeus/schema/request"
	"zeus/schema/response"
	"zeus/services"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestXsolla(t *testing.T) {
	r := gin.Default()
	group := r.Group("/xsolla")
	SetupRouter(group)

	var c *gin.Context
	ApplyMethod(reflect.TypeOf(c), "MustGet", func(_ *gin.Context, key string) any {
		return &gorm.DB{}
	})

	xs := &services.XsollaServices{}
	ApplyMethod(reflect.TypeOf(xs), "InitXsollaServices", func(_ *services.XsollaServices, _ *gin.Context, _ interface{}) {
	})
	ApplyMethod(reflect.TypeOf(xs), "FindRole", func(_ *services.XsollaServices, roleID string) (*models.Role, error) {
		return &models.Role{}, nil
	})
	ApplyMethod(reflect.TypeOf(xs), "IsExistsOrder", func(_ *services.XsollaServices, _ string) bool {
		return false
	})
	ApplyMethod(reflect.TypeOf(xs), "CreatOrder", func(_ *services.XsollaServices, v *domain.XsollaItem) (*models.Order, error) {
		return &models.Order{}, nil
	})

	Convey("TestXsollaLogin", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(response.NewXsollaLogin, func(role *models.Role) *response.XsollaLogin {
				return &response.XsollaLogin{}
			})

			args := request.XsollaLogin{
				XsollaSettings: request.XsollaSettings{MerchantID: "test_merchant_id", ProjectID: "test_project_id"},
			}
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/xsolla/login", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			So(w.Code, ShouldEqual, 200)
		})
	})

	Convey("TestXsollaPay", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.Notify, func(ctx context.Context, db *gorm.DB, order *models.Order, _ services.NotifyOptions) error {
				return nil
			})

			args := request.XsollaPay{
				NotificationType: constants.XsollaOrderPaidNotification,
				XsollaSettings:   request.XsollaSettings{MerchantID: "test_merchant_id", ProjectID: "test_project_id"},
			}
			args.Order.Amount = "1"
			itme := request.XsollaItem{
				Quantity: 1,
			}
			args.Items = append(args.Items, itme)
			v, _ := json.Marshal(args)
			req, _ := http.NewRequest("POST", "/xsolla/pay", bytes.NewBuffer(v))
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			So(w.Code, ShouldEqual, 204)
		})
	})

	Convey("TestXsollaError", t, func() {
		Convey("one func for fail", func() {
			InvalidSignATureError()
			InvalidParameterError()
			InvalidUserError()
		})
	})
}
