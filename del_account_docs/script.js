document.addEventListener("DOMContentLoaded", function () {
    let elementDiv = document.createElement("div")
    elementDiv.className = "dropdown"
    let elementSelect = document.createElement("select")
    elementSelect.id = "mySelect"

    let languages = [["zh", "中文简体"], ["zh-TW", "中文繁體"], ["ko", "한국어"], ["en", "English"], ["jp", "日本語"]]
    for (let i = 0; i < languages.length; i++) {
        let elementOption = document.createElement("option")
        elementOption.value = languages[i][0]
        elementOption.innerHTML = languages[i][1]
        elementSelect.appendChild(elementOption)
    }
    elementDiv.appendChild(elementSelect)
    document.body.appendChild(elementDiv)

    // 获取select元素和用于显示选项值的元素
    let fileNames = window.location.href.split("/")
    name = fileNames[fileNames.length - 1].split(".")[0]
    elementSelect.value = name

    // 监听select元素的变化事件
    elementSelect.addEventListener("change", function () {
        // 获取选中的选项值
        window.location.href = "./" + this.value + ".html";
    });
})
