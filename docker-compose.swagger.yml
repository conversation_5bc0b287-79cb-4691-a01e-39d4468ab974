version: '3.8'

services:
  # Swagger UI 服务
  swagger-ui:
    image: swaggerapi/swagger-ui:latest
    container_name: zeus-swagger-ui
    ports:
      - "8080:8080"
    environment:
      SWAGGER_JSON: /app/swagger.yaml
      BASE_URL: /swagger
    volumes:
      - ./docs/swagger.yaml:/app/swagger.yaml:ro
    networks:
      - zeus-network

  # Swagger Editor 服务 (可选)
  swagger-editor:
    image: swaggerapi/swagger-editor:latest
    container_name: zeus-swagger-editor
    ports:
      - "8081:8080"
    networks:
      - zeus-network

  # 简单的文件服务器，用于提供静态文件
  file-server:
    image: nginx:alpine
    container_name: zeus-file-server
    ports:
      - "8082:80"
    volumes:
      - ./:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - zeus-network

networks:
  zeus-network:
    driver: bridge
