package main

import (
	"context"
	"log/slog"
	"os"
	"time"

	"zeus/common"
	"zeus/common/conf"
	_ "zeus/common/log"
	"zeus/pkg/redis"
	"zeus/pkg/validator"
	"zeus/routers"
	"zeus/services"
	"zeus/services/customer_complaint_warning"
	"zeus/services/refund"
	"zeus/services/third_pay"

	"github.com/fvbock/endless"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"

	"github.com/gin-gonic/gin/binding"
	"github.com/robfig/cron/v3"
	_ "go.uber.org/automaxprocs"

	_ "zeus/services/settings"
)

func initTracer(endpoint, token, serviceName string) {
	ctx := context.Background()

	headers := map[string]string{"Authentication": token}
	traceClient := otlptracegrpc.NewClient(
		otlptracegrpc.WithInsecure(),
		otlptracegrpc.WithEndpoint(endpoint),
		otlptracegrpc.WithHeaders(headers),
	)

	traceExp, err := otlptrace.New(ctx, traceClient)
	if err != nil {
		return
	}
	bsp := sdktrace.NewBatchSpanProcessor(traceExp)

	res, _ := resource.New(ctx,
		resource.WithFromEnv(),
		resource.WithProcess(),
		resource.WithTelemetrySDK(),
		resource.WithHost(),
		resource.WithAttributes(
			semconv.ServiceNameKey.String(serviceName),
		),
	)

	tp := sdktrace.NewTracerProvider(
		sdktrace.WithSampler(sdktrace.AlwaysSample()),
		sdktrace.WithResource(res),
		sdktrace.WithSpanProcessor(bsp),
	)

	otel.SetTracerProvider(tp)
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(propagation.TraceContext{}, propagation.Baggage{}))
}

func main() {
	conf.ConfigInit()
	common.InitInfoDB()
	redis.Setup()

	secondParser := cron.NewParser(cron.SecondOptional | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	c := cron.New(cron.WithParser(secondParser), cron.WithChain())

	services.CronRequestStatisticsTask(c)
	customer_complaint_warning.CronGetCustomerComplaintTask(c)

	if conf.Conf.RunCronJob {
		services.CronNotifyFixTask(c)
		services.CronSubscriptionNotifyFixTask(c)
		third_pay.CronFixGashOrderTask(c)
		services.CronUnregisterTask(c)
		services.CronOrderStatisticsTask(c)
		refund.CronScanRefundOrderTask(c)
		services.CronPointStrategyRecordTask(c)
	}

	c.Start()

	binding.Validator = new(validator.CustomValidator)
	validator.InitValidator(binding.Validator)

	initTracer(conf.Conf.TracerInfo.Endpoint, conf.Conf.TracerInfo.Authentication, conf.Conf.TracerInfo.ServiceName)

	r := routers.SetupRouter()

	err := endless.ListenAndServe(":8000", r)
	if err != nil {
		slog.Error(err.Error())
	}

	ctx := c.Stop()
	slog.Info("Wait for Cron Jobs to finish...")
	select {
	case <-ctx.Done():
		slog.Info("All CronJobs has Done")
		os.Exit(0)
	case <-time.After(time.Second * 15):
		slog.Warn("Has been waiting CronJobs for 15s, exiting...")
		os.Exit(1)
	}
}
