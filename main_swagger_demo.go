// Package main Zeus API Server - Swagger Demo Version
//
//	@title			Zeus API Server
//	@version		1.0
//	@description	Zeus游戏服务平台API文档，提供用户管理、支付、防沉迷、反垃圾等服务
//	@termsOfService	http://swagger.io/terms/
//
//	@contact.name	Zeus API Support
//	@contact.url	http://www.swagger.io/support
//	@contact.email	<EMAIL>
//
//	@license.name	Apache 2.0
//	@license.url	http://www.apache.org/licenses/LICENSE-2.0.html
//
//	@host		localhost:8000
//	@BasePath	/
//
//	@securityDefinitions.apikey	ApiKeyAuth
//	@in							header
//	@name						Authorization
//	@description				Description for what is this security definition being used
//
//	@securityDefinitions.basic	BasicAuth
//
//	@externalDocs.description	OpenAPI
//	@externalDocs.url			https://swagger.io/resources/open-api/
package main

import (
	"net/http"

	_ "zeus/docs"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status  string `json:"status" example:"ok"`
	Message string `json:"message" example:"Zeus API is running"`
	Version string `json:"version" example:"1.0"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	ErrorNo int    `json:"error_no" example:"1001"`
	Message string `json:"message" example:"参数错误"`
}

// SuccessResponse 成功响应
type SuccessResponse struct {
	ErrorNo int         `json:"error_no" example:"0"`
	Message string      `json:"message" example:"success"`
	Result  interface{} `json:"result,omitempty"`
}

// @Summary		健康检查
// @Description	检查API服务状态
// @Tags		System
// @Accept		json
// @Produce		json
// @Success		200	{object}	HealthResponse
// @Router		/healthz [get]
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, HealthResponse{
		Status:  "ok",
		Message: "Zeus API is running",
		Version: "1.0",
	})
}

// @Summary		API信息
// @Description	获取API基本信息
// @Tags		System
// @Accept		json
// @Produce		json
// @Success		200	{object}	SuccessResponse{result=map[string]interface{}}
// @Router		/api/info [get]
func apiInfo(c *gin.Context) {
	info := map[string]interface{}{
		"name":        "Zeus API Server",
		"version":     "1.0",
		"description": "Zeus游戏服务平台API",
		"swagger":     "/swagger/index.html",
		"endpoints": map[string]interface{}{
			"health":  "/healthz",
			"swagger": "/swagger/index.html",
			"docs":    "/swagger/doc.json",
		},
	}

	c.JSON(http.StatusOK, SuccessResponse{
		ErrorNo: 0,
		Message: "success",
		Result:  info,
	})
}

// @Summary		演示接口
// @Description	这是一个演示接口，展示API文档格式
// @Tags		Demo
// @Accept		json
// @Produce		json
// @Param		name	query		string	false	"用户名"	default(demo)
// @Param		age		query		int		false	"年龄"		default(18)
// @Success		200		{object}	SuccessResponse{result=map[string]interface{}}
// @Failure		400		{object}	ErrorResponse
// @Router		/demo [get]
func demoAPI(c *gin.Context) {
	name := c.DefaultQuery("name", "demo")
	age := c.DefaultQuery("age", "18")

	result := map[string]interface{}{
		"message": "这是一个演示接口",
		"params": map[string]interface{}{
			"name": name,
			"age":  age,
		},
		"timestamp": "2025-06-10T17:40:00Z",
	}

	c.JSON(http.StatusOK, SuccessResponse{
		ErrorNo: 0,
		Message: "success",
		Result:  result,
	})
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	Username string `json:"username" binding:"required" example:"demo_user"`
	Password string `json:"password" binding:"required" example:"password123"`
	Device   string `json:"device" example:"mobile"`
}

// UserLoginResponse 用户登录响应
type UserLoginResponse struct {
	UserID string `json:"user_id" example:"12345"`
	Token  string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	Name   string `json:"name" example:"演示用户"`
}

// @Summary		用户登录演示
// @Description	演示用户登录接口
// @Tags		User
// @Accept		json
// @Produce		json
// @Param		request	body		UserLoginRequest	true	"登录信息"
// @Success		200		{object}	SuccessResponse{result=UserLoginResponse}
// @Failure		400		{object}	ErrorResponse
// @Router		/demo/user/login [post]
func demoUserLogin(c *gin.Context) {
	var req UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			ErrorNo: 1001,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	// 演示响应
	response := UserLoginResponse{
		UserID: "12345",
		Token:  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.demo.token",
		Name:   "演示用户",
	}

	c.JSON(http.StatusOK, SuccessResponse{
		ErrorNo: 0,
		Message: "登录成功",
		Result:  response,
	})
}

// PaymentRequest 支付请求
type PaymentRequest struct {
	Amount    float64 `json:"amount" binding:"required" example:"99.99"`
	Currency  string  `json:"currency" example:"CNY"`
	ProductID string  `json:"product_id" binding:"required" example:"product_001"`
	UserID    string  `json:"user_id" binding:"required" example:"12345"`
}

// PaymentResponse 支付响应
type PaymentResponse struct {
	OrderID   string `json:"order_id" example:"order_20250610_001"`
	PayURL    string `json:"pay_url" example:"https://pay.example.com/order_20250610_001"`
	Status    string `json:"status" example:"pending"`
	ExpiresAt string `json:"expires_at" example:"2025-06-10T18:40:00Z"`
}

// @Summary		创建支付订单演示
// @Description	演示创建支付订单接口
// @Tags		Payment
// @Accept		json
// @Produce		json
// @Param		request	body		PaymentRequest	true	"支付信息"
// @Success		200		{object}	SuccessResponse{result=PaymentResponse}
// @Failure		400		{object}	ErrorResponse
// @Security	ApiKeyAuth
// @Router		/demo/payment/create [post]
func demoCreatePayment(c *gin.Context) {
	var req PaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			ErrorNo: 1001,
			Message: "参数错误: " + err.Error(),
		})
		return
	}

	// 演示响应
	response := PaymentResponse{
		OrderID:   "order_20250610_001",
		PayURL:    "https://pay.example.com/order_20250610_001",
		Status:    "pending",
		ExpiresAt: "2025-06-10T18:40:00Z",
	}

	c.JSON(http.StatusOK, SuccessResponse{
		ErrorNo: 0,
		Message: "订单创建成功",
		Result:  response,
	})
}

func setupRouter() *gin.Engine {
	// 设置为发布模式
	gin.SetMode(gin.ReleaseMode)
	
	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// 添加CORS支持
	router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// Swagger UI
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 系统接口
	router.GET("/healthz", healthCheck)
	router.GET("/api/info", apiInfo)

	// 演示接口
	router.GET("/demo", demoAPI)
	router.POST("/demo/user/login", demoUserLogin)
	router.POST("/demo/payment/create", demoCreatePayment)

	// 根路径重定向到Swagger
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
	})

	return router
}

func main() {
	router := setupRouter()

	println("🚀 Zeus API Swagger Demo 启动成功!")
	println("📖 Swagger文档: http://localhost:8000/swagger/index.html")
	println("🔍 API信息: http://localhost:8000/api/info")
	println("❤️  健康检查: http://localhost:8000/healthz")
	println("")
	println("按 Ctrl+C 停止服务")

	// 启动服务
	if err := router.Run(":8000"); err != nil {
		panic("启动服务失败: " + err.Error())
	}
}
