package checkJWT

import (
	"errors"
	"time"

	"zeus/controllers"
	"zeus/pkg/ucenter"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
)

var bc = controllers.BaseController{}

type JWTUser struct {
	Name       string                 `json:"name"`
	UserID     string                 `json:"user_id"`
	AllowGames []ucenter.GameAndRoles `json:"allow_games"`
	IsAdmin    bool                   `json:"is_admin"`
}

type MyClaims struct {
	User *JWTUser
	jwt.StandardClaims
}

var MySecret = []byte("topjoy")

func JWTAuthMiddleware(routerIgnore map[string]struct{}) func(c *gin.Context) {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		if _, ok := routerIgnore[path]; ok {
			c.Next()
			return
		}
		authHeader := c.Request.Header.Get("Authorization")
		if authHeader == "" {
			c.Abort()
			bc.JWTError(c, errors.New("authorization is empty"))
			return
		}

		mc, err := ParseToken(authHeader)
		if err != nil {
			c.Abort()
			bc.JWTError(c, errors.New("token无效，请重新登录"))
			return
		}
		// 将当前请求的user信息保存到请求的上下文c上
		if mc.User.Name != "" {
			c.Set("user", mc.User)
		} else {
			c.Abort()
			bc.JWTError(c, errors.New("token无效，请重新登录"))
			return
		}
		c.Next()
	}
}

func (j *JWTUser) GenToken() (string, error) {
	c := MyClaims{}
	c = MyClaims{
		j,
		jwt.StandardClaims{
			ExpiresAt: time.Now().Add(time.Hour * 480).Unix(), // 有效期20天
			Issuer:    "zeus",
		},
	}
	// 使用指定的签名方法创建签名对象
	token, err := jwt.NewWithClaims(jwt.SigningMethodHS256, c).SignedString(MySecret)
	// 使用指定的secret签名并获得完成的编码后的字符串token
	return token, err
}

func ParseToken(tokenString string) (*MyClaims, error) {
	// 解析token
	token, err := jwt.ParseWithClaims(tokenString, &MyClaims{}, func(token *jwt.Token) (i interface{}, err error) {
		return MySecret, nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := token.Claims.(*MyClaims); ok && token.Valid {
		return claims, nil
	}
	return nil, errors.New("invalid token")
}
