package checkSignature

import (
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"sort"
	"strings"

	"zeus/pkg/json"
	"zeus/services/game"

	"zeus/controllers"

	"github.com/gin-gonic/gin"
)

var bc = controllers.BaseController{}

func CheckSignature(routerIgnore map[string]struct{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		if _, ok := routerIgnore[path]; ok {
			c.Next()
			return
		}

		body, _ := c.GetRawData()
		params := make(map[string]string)
		err := json.Unmarshal(body, &params)
		if err != nil {
			c.Abort()
			bc.Error(c, controllers.ErrorUnknown, err)
			return
		} else {
			c.Set("request", params)
		}

		sign, ok := params["sign"]
		if ok {
			delete(params, "sign")
		} else {
			c.Abort()
			bc.Error(c, controllers.ErrorEmptyParam, errors.New("sign is empty"))
			return
		}

		var keys []string
		for k := range params {
			keys = append(keys, k)
		}
		sort.Strings(keys)
		dataParams := make([]byte, 0)

		for _, k := range keys {
			dataParams = append(dataParams, k...)
			dataParams = append(dataParams, params[k]...)
		}

		appID, ok := params["appid"]
		if !ok {
			c.Abort()
			bc.Error(c, controllers.ErrorEmptyParam, errors.New("appid is empty"))
			return
		}

		secretKey, err := game.GetSecretKey(appID)
		if err != nil {
			_ = fmt.Errorf(err.Error())
		}

		if secretKey == "" {
			c.Abort()
			bc.Error(c, controllers.ErrorEmptyParam, errors.New("secretKey is empty"))
			return
		}

		dataParams = append(dataParams, secretKey...)

		m := md5.Sum(dataParams)
		dataParamsSign := hex.EncodeToString(m[:])
		if sign != dataParamsSign {
			c.Abort()
			bc.Error(c, controllers.ErrorSign, errors.New("signature verification failure"))
			return
		}
		c.Request.Body = io.NopCloser(strings.NewReader(string(body)))
		c.Next()
	}
}
