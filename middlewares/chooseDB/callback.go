package chooseDB

import (
	"errors"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"

	"zeus/controllers"
	"zeus/services"
)

var cb = controllers.BaseController{}

func CallbackSetBD(routerIgnore map[string]struct{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		if _, ok := routerIgnore[path]; ok {
			c.Next()
			return
		}

		var db *gorm.DB

		appid := c.Param("appid")
		if appid == "" {
			appid = c.Query("appid")
		}

		if appid == "" {
			c.Abort()
			cb.Error(c, controllers.ErrorEmptyParam, errors.New("缺少appid"))
			return
		}

		db, err := services.GetGameDBByAppID(appid)
		if err != nil {
			c.Abort()
			cb.Error(c, controllers.ErrorDBError, err)
			return
		}

		db = db.WithContext(c.Request.Context())
		c.Set("db", db)
		c.Next()
	}
}
