package chooseDB

import (
	"fmt"
	"net/http"
	"strconv"

	"zeus/services"

	"github.com/gin-gonic/gin"
)

func ThirdPaySetBD(c *gin.Context) {
	projectId, _ := c.Get("project_id")

	x, _ := strconv.Atoi(fmt.Sprint(projectId))
	db, err := services.GetGameDBByProjectId(x)
	if err != nil {
		c.Abort()
		c.JSON(http.StatusBadRequest, "获取数据库失败！")
		return
	}
	db = db.WithContext(c.Request.Context())
	c.Set("db", db)
	c.Next()
}
