package chooseDB

import (
	"net/http"

	"zeus/controllers/xsolla"
	"zeus/schema/request"
	"zeus/services"

	"github.com/gin-gonic/gin"
)

func XsollaSetBD(c *gin.Context) {
	settings := new(request.XsollaSettings)
	err := c.ShouldBind<PERSON>uery(settings)
	if err != nil {
		c.Abort()
		c.JSO<PERSON>(http.StatusBadRequest, xsolla.InvalidParameterError())
		return
	}
	db, err := services.GetGameDBByXsollaInfo(settings.ProjectID)
	if err != nil {
		c.Abort()
		c.JSON(http.StatusBadRequest, xsolla.InvalidParameterError())
		return
	}
	db = db.WithContext(c.Request.Context())
	c.Set("db", db)
	c.Next()
}
