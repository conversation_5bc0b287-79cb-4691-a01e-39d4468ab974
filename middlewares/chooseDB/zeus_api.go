package chooseDB

import (
	"io"
	"strconv"
	"strings"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"

	"zeus/controllers"
	"zeus/services"
	"zeus/services/game"
)

var basecontrol = controllers.BaseController{}

func ZeusAPISetBD(routerIgnore map[string]struct{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		if _, ok := routerIgnore[path]; ok {
			c.Next()
			return
		}
		for pathUrl := range routerIgnore {
			if pathUrl != "" {
				if pathUrl[len(pathUrl)-2:] == "/*" {
					if strings.Contains(path, pathUrl[0:len(pathUrl)-2]) {
						c.Next()
						return
					}
				}
			}
		}

		body, _ := c.GetRawData()
		params := make(map[string]interface{})
		if len(body) != 0 {
			err := json.Unmarshal(body, &params)
			if err != nil {
				c.Abort()
				basecontrol.APIError(c, err)
			}
			if params["game_id"] != nil {
				gameID := int(params["game_id"].(float64))
				gameDb, _ := game.GetGameById(gameID)
				params["appid"] = gameDb.Appid
			}
		}
		if c.Query("game_id") != "" {
			gameID, _ := strconv.Atoi(c.Query("game_id"))
			gameDb, _ := game.GetGameById(gameID)
			params["appid"] = gameDb.Appid
		}

		var db *gorm.DB

		appid := params["appid"].(string)
		db, err := services.GetGameDBByAppID(appid)
		if err != nil {
			c.Abort()
			basecontrol.APIError(c, err)
			return
		}

		db = db.WithContext(c.Request.Context())
		c.Set("db", db)
		c.Request.Body = io.NopCloser(strings.NewReader(string(body)))
		c.Next()
	}
}
