package chooseDB

import (
	"io"
	"strings"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"

	"zeus/controllers"
	"zeus/services"
)

var bc = controllers.BaseController{}

func ZeusSDKSetBD(routerIgnore map[string]struct{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		if _, ok := routerIgnore[path]; ok {
			c.Next()
			return
		}

		body, _ := c.GetRawData()
		params := make(map[string]interface{})
		err := json.Unmarshal(body, &params)
		if err != nil {
			c.Abort()
			bc.Error(c, controllers.ErrorUnknown, err)
			return
		}

		var db *gorm.DB

		appid := params["appid"]
		if appid == nil {
			appid = c.Query("appid")
		}
		db, err = services.GetGameDBByAppID(appid.(string))
		if err != nil {
			c.Abort()
			bc.Error(c, controllers.ErrorDBError, err)
			return
		}

		db = db.WithContext(c.Request.Context())
		c.Set("db", db)
		c.Request.Body = io.NopCloser(strings.NewReader(string(body)))
		c.Next()
	}
}
