package context

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"

	"github.com/gin-gonic/gin"
)

var getPath = map[string]bool{
	"/third-pay/notify":               true,
	"/third-pay/products":             true,
	"/third-pay/activity":             true,
	"/third-pay/v2/activities":        true,
	"/third-pay/v2/products":          true,
	"/third-pay/v2/notify":            true,
	"/third-pay/v2/cumulative-points": true,
}

func SetThirdPayContext(c *gin.Context) {
	body, _ := c.GetRawData()
	if _, ok := getPath[c.Request.URL.Path]; ok {
		projectId := c.Query("project_id")
		if projectId == "" {
			c.Abort()
			basecontrol.APIError(c, errors.New("缺少project_id"))
			return
		}
		c.Set("project_id", projectId)
		c.Set("request", string(body))
		c.Request.Body = io.NopCloser(bytes.NewReader(body))
		c.Next()
		return
	}

	params := make(map[string]interface{})
	if len(body) != 0 {
		err := json.Unmarshal(body, &params)
		if err != nil {
			c.Abort()
			basecontrol.APIError(c, err)
			return
		}
		_, ok := params["project_id"]
		if !ok {
			params["project_id"] = c.Query("project_id")
		}
		c.Set("project_id", params["project_id"])
	}
	c.Set("request", params)
	c.Request.Body = io.NopCloser(bytes.NewReader(body))
	c.Next()
}
