package context

import (
	"bytes"
	"encoding/json"
	"io"

	"github.com/gin-gonic/gin"
)

func SetXsollaContext(c *gin.Context) {
	body, _ := c.GetRawData()
	params := make(map[string]interface{})
	if len(body) != 0 {
		err := json.Unmarshal(body, &params)
		if err != nil {
			c.Abort()
			basecontrol.APIError(c, err)
			return
		}
	}
	c.Set("request", params)
	c.Request.Body = io.NopCloser(bytes.NewReader(body))
	c.Next()
}
