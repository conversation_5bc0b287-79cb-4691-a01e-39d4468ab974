package context

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"
	"strconv"
	"strings"

	"zeus/controllers"
	"zeus/services/game"

	"github.com/gin-gonic/gin"
)

var basecontrol = controllers.BaseController{}

func SetZeusAPIContext(routerIgnore map[string]struct{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		if _, ok := routerIgnore[path]; ok {
			c.Next()
			return
		}

		for pathUrl := range routerIgnore {
			if pathUrl != "" {
				if pathUrl[len(pathUrl)-2:] == "/*" {
					if strings.Contains(path, pathUrl[0:len(pathUrl)-2]) {
						c.Next()
						return
					}
				}
			}
		}

		body, _ := c.GetRawData()
		params := make(map[string]interface{})
		if len(body) != 0 {
			err := json.Unmarshal(body, &params)
			if err != nil {
				c.Set("request", body)
				c.Abort()
				basecontrol.APIError(c, err)
				return
			}
			if params["game_id"] != nil {
				gameID := int(params["game_id"].(float64))
				gameDb, _ := game.GetGameById(gameID)
				params["appid"] = gameDb.Appid
			}
		}
		if c.Query("game_id") != "" {
			gameID, _ := strconv.Atoi(c.Query("game_id"))
			gameDb, _ := game.GetGameById(gameID)
			params["appid"] = gameDb.Appid
		}

		appid, ok := params["appid"].(string)
		if !ok {
			c.Set("request", body)
			c.Abort()
			err := errors.New("参数缺少appid")
			basecontrol.APIError(c, err)
		}
		if appid == "" {
			c.Next()
			return
		}

		g, err := game.GetGameByAppid(appid)
		if err != nil {
			c.Set("request", body)
			c.Abort()
			basecontrol.APIError(c, err)
		}
		params["game_name"] = g.Name
		c.Set("gameID", g.Id)
		c.Set("request", params)
		c.Request.Body = io.NopCloser(bytes.NewReader(body))
		c.Next()
		return
	}
}
