package context

import (
	"bytes"
	"encoding/json"
	"errors"
	"io"

	"zeus/controllers"
	"zeus/services/game"

	"github.com/gin-gonic/gin"
)

var (
	bc           = controllers.BaseController{}
	flushLogPath = map[string]struct{}{
		"/account/register-with-email": {},
		"/account/login-with-email":    {},
		"/account/modify-password":     {},
	}
)

func SetZeusContext() gin.HandlerFunc {
	return func(c *gin.Context) {
		body, _ := c.GetRawData()
		params := make(map[string]interface{})
		err := json.Unmarshal(body, &params)
		if err != nil {
			c.Set("request", params)
			bc.Error(c, controllers.ErrorUnknown, errors.New("解析数据异常"))
			c.Abort()
			return
		}

		appid, ok := params["appid"].(string)
		if !ok {
			appid = c.Query("appid")
			if appid == "" {
				c.Set("request", params)
				err = errors.New("参数缺少appid")
				bc.Error(c, controllers.ErrorEmptyParam, err)
				c.Abort()
				return
			}
		}

		g, err := game.GetGameByAppid(appid)
		if err != nil {
			c.Set("request", params)
			bc.Error(c, controllers.ErrorEmptyParam, err)
			c.Abort()
			return
		}

		if _, ok = flushLogPath[c.Request.URL.Path]; ok {
			params["password"] = "***********"
		}

		params["game_name"] = g.Name
		c.Set("request", params)
		c.Set("appId", appid)
		c.Set("gameID", g.Id)
		c.Request.Body = io.NopCloser(bytes.NewReader(body))
		c.Next()
	}
}
