package counter

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gin-gonic/gin"
)

var lock = sync.RWMutex{}

type Counter struct {
	AppId       string `json:"appId"`
	RequestPath string `json:"path"`
	RequestDate string `json:"date"`
	Count       uint64 `json:"count"`
}

func (c *Counter) Reset() {
	atomic.StoreUint64(&c.Count, 0)
}

func (c *Counter) Inc() {
	atomic.AddUint64(&c.Count, 1)
}

func (c *Counter) Get() uint64 {
	return atomic.LoadUint64(&c.Count)
}

func (c *Counter) GetAndReset() uint64 {
	lock.Lock()
	defer lock.Unlock()
	counter := c.Get()
	c.Reset()
	return counter
}

type CounterMap map[string]*Counter

var ReqeustConterMap = make(CounterMap, 0)

func (cm CounterMap) GenerateHashKey(appId string, requestPath string, requestDate string) string {
	return fmt.Sprintf("%v.%v.%v", appId, requestPath, requestDate)
}

func (cm CounterMap) GetOrCreateCounter(appId string, requestPath string, requestDate string) *Counter {
	hashKey := cm.GenerateHashKey(appId, requestPath, requestDate)
	if c, ok := cm[hashKey]; ok {
		return c
	} else {
		c := &Counter{AppId: appId, RequestPath: requestPath, RequestDate: requestDate, Count: 0}
		cm[hashKey] = c
		return c
	}
}

func (cm CounterMap) Delete(appId string, requestPath string, requestDate string) {
	hashKey := cm.GenerateHashKey(appId, requestPath, requestDate)
	delete(cm, hashKey)
}

func RecordCounter() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
		appId := c.GetString("appId")
		requestPath := c.Request.URL.Path
		requestData := time.Now().Format("2006-01-02")
		ReqeustConterMap.GetOrCreateCounter(appId, requestPath, requestData).Inc()
	}
}
