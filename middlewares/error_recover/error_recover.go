package error_recover

import (
	"errors"
	"fmt"

	"github.com/gin-gonic/gin"

	"zeus/controllers"
)

var bc = controllers.BaseController{}

func ErrorRecover() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				errorMessage := fmt.Sprintf("zeus后端代码异常, 报错信息：%s", err)
				bc.Error(c, controllers.Fail, errors.New(errorMessage))
				return
			}
		}()
		c.Next()
	}
}
