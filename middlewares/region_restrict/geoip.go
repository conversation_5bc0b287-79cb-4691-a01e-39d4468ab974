package region_restrict

import (
	"errors"
	"net"
	"slices"
	"strings"
	"sync"

	"zeus/common/log"
	"zeus/controllers"
	"zeus/services/settings/converter"
	"zeus/services/settings/other"

	"github.com/gin-gonic/gin"
	"github.com/oschwald/geoip2-golang"
)

var (
	bc          = controllers.BaseController{}
	geoipClient *geoip2.Reader
	once        sync.Once
)

func init() {
	once.Do(func() {
		var err error
		geoipClient, err = geoip2.Open("GeoLite2-Country.mmdb")
		if err != nil {
			panic(err)
		}
	})
}

func CheckClientRegion() gin.HandlerFunc {
	return func(c *gin.Context) {
		appId, _ := c.Get("appId")
		var config other.RegionRestrictConfig
		converter.QueryByAppid(&config, appId.(string))

		if !*config.IsEnabled {
			return
		}

		currentIp := c.ClientIP()
		if slices.Contains(strings.Split(*config.IpWhiteList, ","), currentIp) {
			return
		}

		if slices.Contains(strings.Split(*config.IpBlackList, ","), currentIp) {
			errorMsg := "当前IP被限制访问"
			log.Warn(c.Request.Context(), errorMsg,
				"current_ip", currentIp,
				"restrict_ip", *config.IpBlackList)
			bc.Error(c, controllers.ErrorRegionRestrict, errors.New(errorMsg))
			c.Abort()
			return
		}

		ip := net.ParseIP(currentIp)
		record, _ := geoipClient.City(ip)
		if record.Country.IsoCode != "" && slices.Contains(strings.Split(*config.RegionCodes, ","), record.Country.IsoCode) {
			errorMsg := "当前区域被限制访问"
			log.Warn(c.Request.Context(), errorMsg,
				"current_region", record.Country.IsoCode,
				"restrict_region", *config.RegionCodes)
			bc.Error(c, controllers.ErrorRegionRestrict, errors.New(errorMsg))
			c.Abort()
			return
		}

		isp := c.Request.Header.Get("isp")
		c.Set("isp", isp)
		if isp != "" && slices.Contains(strings.Split(*config.ISP, ","), isp) {
			errorMsg := "当前运营商被限制访问"
			log.Warn(c.Request.Context(), errorMsg,
				"current_isp", isp,
				"restrict_isp", *config.ISP)
			bc.Error(c, controllers.ErrorRegionRestrict, errors.New(errorMsg))
			c.Abort()
			return
		}

	}
}
