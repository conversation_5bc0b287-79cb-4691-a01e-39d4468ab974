package request_log

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"zeus/common/log"
	"zeus/middlewares/checkJWT"

	"github.com/gin-gonic/gin"
)

type responseWriter struct {
	gin.ResponseWriter
	b *bytes.Buffer
}

func (w responseWriter) Write(b []byte) (int, error) {
	w.b.Write(b)
	return w.ResponseWriter.Write(b)
}

// Middleware gin middleware return HandlerFunc  with tracing.
func LoggerWithWriter(skipPaths map[string]struct{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		if _, ok := skipPaths[path]; ok {
			c.Next()
			return
		}

		start := time.Now()
		writer := responseWriter{
			c.Writer,
			bytes.NewBuffer([]byte{}),
		}
		c.Writer = writer
		c.Next()

		latency := time.Since(start)
		requestBody, _ := c.Get("request")

		if c.Writer.Status() == http.StatusNotFound {
			data, _ := io.ReadAll(c.Request.Body)
			requestBody = string(data)
		}

		var responseBody map[string]interface{}
		json.Unmarshal(writer.b.Bytes(), &responseBody)

		logContent := []any{
			"user_agent", c.Request.Header.Get("User-Agent"),
			"client_ip", c.ClientIP(),
			"method", c.Request.Method,
			"status_code", c.Writer.Status(),
			"request_path", c.Request.URL.String(),
			"request_body", requestBody,
			"response_body", responseBody,
			"latency", fmt.Sprintf("%v", latency),
		}
		if user, ok := c.Get("user"); ok {
			JWTUser := user.(*checkJWT.JWTUser)
			logContent = append(logContent, "username", JWTUser.Name)
		}

		if isp, ok := c.Get("isp"); ok {
			logContent = append(logContent, "isp", isp)
		}
		log.Info(c.Request.Context(), "", logContent...)
	}
}
