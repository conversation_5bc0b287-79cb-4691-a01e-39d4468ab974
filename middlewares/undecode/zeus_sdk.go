package undecode

import (
	"bytes"
	"encoding/base64"
	"errors"
	"io"
	"strings"

	"zeus/controllers"

	"github.com/gin-gonic/gin"
)

var bc = controllers.BaseController{}

func Base64UnDecode(routerIgnore map[string]struct{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		path := c.Request.URL.Path
		if _, ok := routerIgnore[path]; ok {
			c.Next()
			return
		}

		var bodyBase64 []byte
		bodyBase64, _ = c.GetRawData()
		body, err := io.ReadAll(base64.NewDecoder(base64.StdEncoding, bytes.NewReader(bodyBase64)))
		if err != nil {
			c.Set("request", bodyBase64)
			c.Abort()
			bc.Error(c, controllers.ErrorUnknown, errors.New("数据解析异常"))
			return
		}
		c.Request.Body = io.NopCloser(strings.NewReader(string(body)))
		c.Next()
	}
}
