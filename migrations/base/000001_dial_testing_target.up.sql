CREATE TABLE `dial_testing_target` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `game_id` int(11) unsigned NOT NULL COMMENT '游戏ID',
  `name` varchar(32) NOT NULL COMMENT '拨测目标名称',
  `path` varchar(128) NOT NULL COMMENT '拨测目标地址',
  `protocol` varchar(16) NOT NULL COMMENT '拨测协议',
  `method` varchar(16) DEFAULT NULL COMMENT '拨测请求方法',
  `content` varchar(128) DEFAULT NULL COMMENT '拨测请求内容',
  `timeout` int(10) NOT NULL COMMENT '拨测请求超时时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='拨测目标表'