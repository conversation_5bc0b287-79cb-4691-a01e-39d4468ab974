CREATE TABLE `game` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `name` varchar(50) NOT NULL COMMENT '游戏名称',
  `appid` varchar(16) NOT NULL COMMENT '游戏appid',
  `secret_key` varchar(16) NOT NULL DEFAULT '' COMMENT 'secret key,用于通讯加密',
  `platform` varchar(10) NOT NULL DEFAULT '' COMMENT '游戏平台.android,ios',
  `type` varchar(20) NOT NULL DEFAULT '' COMMENT '游戏类型：手游-卡牌...',
  `service_mail` varchar(100) DEFAULT '' COMMENT '客服邮箱',
  `open_status` tinyint(1) DEFAULT '1' COMMENT '开放状态',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态.0:未上线,1:已上线',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `project_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `appid` (`appid`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='游戏表'