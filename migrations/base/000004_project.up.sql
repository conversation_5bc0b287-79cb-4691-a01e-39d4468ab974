CREATE TABLE `project` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `name` varchar(50) NOT NULL COMMENT '游戏名称',
  `alias` varchar(32) NOT NULL COMMENT '别名，ios、android共用一个别名',
  `type` varchar(20) NOT NULL DEFAULT '' COMMENT '游戏类型',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态.0:未上线,1:已上线',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `mysql_database` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='游戏项目表'