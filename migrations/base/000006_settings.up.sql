CREATE TABLE `settings` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `appid` varchar(16) DEFAULT '' COMMENT '应用ID',
  `project_id` int(11) unsigned DEFAULT '0',
  `game_id` int(11) unsigned DEFAULT '0',
  `type` varchar(64) NOT NULL COMMENT '配置类型',
  `name` varchar(64) NOT NULL COMMENT '配置名称',
  `value` varchar(10240) NOT NULL COMMENT '配置内容',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='配置表'