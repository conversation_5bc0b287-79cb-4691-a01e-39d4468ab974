CREATE TABLE `unregister` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `game_id` int(11) unsigned DEFAULT '0' COMMENT '游戏id',
  `account` varchar(10) NOT NULL COMMENT '账号',
  `cancellation_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '删除账号时间',
  `actual_processing_time` timestamp NOT NULL DEFAULT '2023-01-07 00:00:00' COMMENT '实际处理时间',
  `status` tinyint(2) DEFAULT '0' COMMENT '当前账号状态， 0 冷静期；1 已删除；2 删除取消；-1 处理失败',
  PRIMARY KEY (`id`),
  UNIQUE KEY `game_account` (`game_id`,`account`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4