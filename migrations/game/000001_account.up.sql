/******************************************/
/*   TableName = account   */
/******************************************/
CREATE TABLE `account` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `union_id` varchar(32) NOT NULL COMMENT '用户身份唯一标识',
  `user_name` varchar(20) DEFAULT NULL COMMENT '用户名',
  `email` varchar(32) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(16) DEFAULT NULL COMMENT '手机号码',
  `password` varchar(128) DEFAULT NULL,
  `token` varchar(32) DEFAULT NULL COMMENT '登陆时生成的token,用于CP服务端登陆验证',
  `register_ip` varchar(32) DEFAULT NULL COMMENT '注册ip',
  `last_login_ip` varchar(32) DEFAULT NULL COMMENT '最近一次登陆ip',
  `register_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `last_login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近一次登陆时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `union_id` (`union_id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `phone` (`phone`),
  KEY `register_time` (`register_time`),
  KEY `last_login_time` (`last_login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='账号信息表'