/******************************************/
/*   TableName = order_statistics   */
/******************************************/
CREATE TABLE `order_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `game_id` int(11) NOT NULL COMMENT '游戏id',
  `product_id` varchar(100) NOT NULL COMMENT '商品id',
  `pay_way` tinyint(2) DEFAULT '0' COMMENT '支付方式:1苹果支付 2谷歌支付',
  `pay_date` date NOT NULL COMMENT '支付时间',
  `paid_orders` int(11) NOT NULL COMMENT '订单总数',
  `paid_price` decimal(10,6) DEFAULT '0.000000' COMMENT '订单总金额',
  `refund_orders` int(11) NOT NULL COMMENT '退款订单总数',
  `refund_price` decimal(10,6) DEFAULT '0.000000' COMMENT '退款订单总金额',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='订单统计表'