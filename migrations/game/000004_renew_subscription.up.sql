/******************************************/
/*   TableName = renew_subscription   */
/******************************************/
CREATE TABLE `renew_subscription` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `game_id` int(11) NOT NULL COMMENT '游戏id',
  `original_transaction_id` varchar(128) DEFAULT NULL COMMENT '原始交易id',
  `transaction_id` varchar(64) NOT NULL COMMENT '交易id',
  `order_id` varchar(64) NOT NULL COMMENT '关联订阅订单id',
  `notify_status` tinyint(3) DEFAULT NULL COMMENT '通知状态',
  `notify_result` varchar(2048) DEFAULT NULL COMMENT '游戏支付通知数据json',
  `notification_type` varchar(32) NOT NULL COMMENT '苹果服务器发送的通知类型',
  `notification_info` varchar(2048) NOT NULL COMMENT '苹果服务器发送的部分通知数据',
  `notification_uuid` varchar(64) NOT NULL COMMENT '通知的唯一标识符',
  `expires_date` int(11) NOT NULL COMMENT '过期时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '续订订单创建时间',
  `pay_way` tinyint(2) DEFAULT '0' COMMENT '支付方式:1苹果支付 2谷歌支付',
  PRIMARY KEY (`id`),
  UNIQUE KEY `notification_uuid` (`notification_uuid`) USING HASH
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='订阅续订表'