/******************************************/
/*   TableName = role   */
/******************************************/
CREATE TABLE `role` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `game_id` int(11) NOT NULL DEFAULT '0' COMMENT '游戏id',
  `user_id` int(11) NOT NULL COMMENT '账号id',
  `platform` varchar(16) DEFAULT NULL COMMENT '平台,google ios',
  `device` varchar(64) DEFAULT NULL COMMENT 'device id',
  `server_id` varchar(16) DEFAULT NULL COMMENT '区服ID',
  `server_name` varchar(16) DEFAULT NULL COMMENT '区服名称',
  `role_id` varchar(32) DEFAULT NULL COMMENT '角色ID',
  `role_name` varchar(32) DEFAULT NULL COMMENT '角色名字',
  `vip` tinyint(3) DEFAULT '0' COMMENT 'vip',
  `level` smallint(6) DEFAULT '1' COMMENT '等级',
  `play_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近次游戏时间',
  `play_ip` varchar(16) DEFAULT NULL COMMENT '最近次游戏ip',
  `extend` text,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `role_id` (`role_id`),
  KEY `play_time` (`play_time`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='角色表'