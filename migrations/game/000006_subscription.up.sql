/******************************************/
/*   TableName = subscription   */
/******************************************/
CREATE TABLE `subscription` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `game_id` int(11) NOT NULL COMMENT '游戏id',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `account` int(11) NOT NULL COMMENT '登陆账号',
  `platform` varchar(32) DEFAULT NULL COMMENT '来源:ios android',
  `device` varchar(64) DEFAULT NULL COMMENT '创建订单时device',
  `server_id` varchar(16) DEFAULT NULL COMMENT '区服id',
  `server_name` varchar(16) DEFAULT NULL COMMENT '区服名称',
  `role_id` varchar(32) NOT NULL COMMENT '玩家游戏内角色id',
  `role_name` varchar(32) DEFAULT '' COMMENT '游戏玩家昵称',
  `vip` tinyint(3) DEFAULT '0' COMMENT 'vip',
  `level` smallint(6) DEFAULT '1' COMMENT '等级',
  `original_transaction_id` char(255) DEFAULT NULL COMMENT '原始购买ID',
  `transaction_id` char(255) DEFAULT NULL COMMENT '最新订单ID',
  `order_id` varchar(100) NOT NULL COMMENT 'sdk生成的订单号',
  `product_id` varchar(100) NOT NULL COMMENT '下单时购买商品id',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '原价',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
  `extend` varchar(255) NOT NULL COMMENT '通知游戏方扩展(透传游戏方)',
  `pay_amount` decimal(10,2) DEFAULT '0.00' COMMENT '支付金额',
  `pay_currency` varchar(10) DEFAULT NULL COMMENT '支付货币:CNY USD...',
  `pay_time` timestamp NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '支付时间',
  `pay_status` tinyint(2) DEFAULT '0' COMMENT '支付状态 0失败 1成功',
  `pay_id` varchar(32) DEFAULT '' COMMENT '支付成功渠道返回的订单标识',
  `pay_product_id` varchar(512) DEFAULT NULL COMMENT '订单验证成功后返回的购买商品id',
  `expires_date` int(11) DEFAULT NULL COMMENT '订阅过期时间',
  `notification_type` tinyint(2) DEFAULT '0' COMMENT '推送的最新状态',
  `notify_time` timestamp NOT NULL DEFAULT '1970-01-01 08:00:01' COMMENT '最近次通知CP时间',
  `notify_cnt` tinyint(3) DEFAULT '0' COMMENT '通知CP次数',
  `notify_status` tinyint(2) DEFAULT '0' COMMENT '通知CP状态: -1 失败 1成功',
  `notify_result` text COMMENT '回调游戏服务器的返回',
  `origin_receipt` text COMMENT '原始凭证',
  `origin_receipt_info` text COMMENT '原始凭证信息',
  `latest_receipt` text COMMENT '最新凭证',
  `latest_receipt_info` text COMMENT '最新凭证信息',
  `pay_notify_url` varchar(255) DEFAULT NULL,
  `order_attribute` int(11) DEFAULT '0' COMMENT '订单属性',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  UNIQUE KEY `original_transaction_id` (`original_transaction_id`,`game_id`) USING BTREE,
  KEY `transaction_id` (`transaction_id`),
  KEY `extend` (`extend`),
  KEY `user_id` (`user_id`),
  KEY `expires_date` (`expires_date`),
  KEY `account` (`account`),
  KEY `pay_time` (`pay_time`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8 COMMENT='订阅表'