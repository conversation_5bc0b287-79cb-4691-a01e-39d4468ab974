CREATE TABLE `user` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `account` int(11) unsigned NOT NULL COMMENT '登陆账号',
  `name` varchar(20) DEFAULT '' COMMENT '名字',
  `email` varchar(32) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(16) DEFAULT NULL COMMENT '手机号码',
  `game_id` int(11) NOT NULL DEFAULT '0' COMMENT '游戏id',
  `pw_md5` varchar(32) DEFAULT NULL COMMENT '登陆密码md5值',
  `platform` varchar(16) DEFAULT NULL COMMENT '来源 ,ios android',
  `register_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  `register_ip` varchar(16) NOT NULL COMMENT '注册ip',
  `register_device` varchar(64) DEFAULT NULL COMMENT '注册时device id',
  `mobile_info` text COMMENT '注册时设备信息',
  `login_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近次登陆时间',
  `login_ip` varchar(16) DEFAULT NULL COMMENT '最近次登陆ip',
  `login_device` varchar(200) DEFAULT NULL COMMENT '登陆时device id列表',
  `login_cnt` smallint(6) DEFAULT '0' COMMENT '登陆次数',
  `token` varchar(32) DEFAULT NULL COMMENT '登陆时生成的token,用于CP服务端登陆验证',
  `birthday` varchar(8) DEFAULT NULL COMMENT '用户生日',
  `is_real_name_authentication` int(11) DEFAULT NULL COMMENT '是否通过实名认证',
  PRIMARY KEY (`id`),
  UNIQUE KEY `account` (`account`),
  KEY `login_time` (`login_time`),
  KEY `register_time` (`register_time`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='账号表'