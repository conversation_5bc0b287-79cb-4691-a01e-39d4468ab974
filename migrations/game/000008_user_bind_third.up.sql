CREATE TABLE `user_bind_third` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '绑定的user表主账号id',
  `union_id` varchar(64) NOT NULL COMMENT '第三方登陆时的union id',
  `type` tinyint(2) DEFAULT '0' COMMENT '第三方绑定方式:1 facebook、 2 twitter、 3 line、 4 gamecenter、 5 Google Play',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '重新绑定的时间',
  `status` tinyint(2) DEFAULT '1' COMMENT '状态:0、解绑 1、正常',
  `user_name` varchar(64) DEFAULT NULL COMMENT '三方账号的用户名',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `union_id` (`union_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10001 DEFAULT CHARSET=utf8mb4 COMMENT='第三方账号绑定表'