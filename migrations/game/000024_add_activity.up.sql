/******************************************/
/*   TableName = activity   */
/******************************************/
CREATE TABLE `activity` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `name` varchar(50) NOT NULL COMMENT '活动名称',
    `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '活动类型 1:限时活动',
    `activity_products` text NOT NULL COMMENT '活动商品',
    `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `end_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
    `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态 0:未开始 1:进行中 2:已结束',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0:未删除 1:已删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='活动表';