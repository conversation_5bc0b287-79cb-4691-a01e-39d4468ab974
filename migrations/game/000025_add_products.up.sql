/******************************************/
/*   TableName = product   */
/******************************************/
CREATE TABLE `product` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `game_product_id` varchar(128) NOT NULL COMMENT '游戏商品id',
    `name` varchar(50) NOT NULL COMMENT '商品名称',
    `prices` text NOT NULL COMMENT '价格',
    `attribute` int NOT NULL COMMENT '商品属性: 1.限时商品 2.长期商品 ',
    `icon` text NULL COMMENT '图标',
    `is_delete` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0:未删除 1:已删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `delete_time` int(11) NOT NULL DEFAULT 0 COMMENT '删除时间',
    PRIMARY KEY (`id`),
    CONSTRAINT name_game_product_id_unique UNIQUE (name, game_product_id, is_delete,delete_time)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='商品表';
