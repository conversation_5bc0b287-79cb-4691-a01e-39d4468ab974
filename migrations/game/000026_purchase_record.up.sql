/******************************************/
/*   TableName = purchase_record  */
/******************************************/
CREATE TABLE `purchase_record` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `role_id` varchar(32) NOT NULL COMMENT '角色id',
    `server_id` varchar(16) NOT NULL COMMENT '服务器id',
    `product_id` int(11) DEFAULT 0  COMMENT '商品id',
    `activity_id` int(11) NOT NULL COMMENT '活动id',
    `order_id` varchar(100) NOT NULL COMMENT '订单id',
    `pay_status` int(11) NOT NULL DEFAULT 0  COMMENT '状态: 0.未支付 1.已支付',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='购买记录表';
