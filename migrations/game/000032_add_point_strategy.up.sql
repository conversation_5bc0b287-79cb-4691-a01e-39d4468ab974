/******************************************/
/*   TableName = point_strategy_record   */
/******************************************/
CREATE TABLE `web_pay_point_strategy_record` (
   `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
   `name` varchar(50) NOT NULL COMMENT '名称',
   `description` varchar(1024) DEFAULT '' COMMENT '描述',
   `type` tinyint(1) NOT NULL  DEFAULT 1 COMMENT '类型. 1 普通类型',
   `products` text NOT NULL COMMENT '商品',
   `picture` varchar(255) NOT NULL COMMENT '图标',
   `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
   `end_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='累计充值积分策略记录表';

CREATE TABLE `web_pay_point_strategy` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `name` varchar(50) NOT NULL COMMENT '名称',
  `description` varchar(1024) DEFAULT '' COMMENT '描述',
  `points` int(10) NOT NULL COMMENT '积分',
  `product_id` varchar(50)  NOT NULL COMMENT '商品',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  CONSTRAINT points_unique UNIQUE (points)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='累计充值积分策略表';

CREATE TABLE `web_pay_points_exchange_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `order_id` varchar(100) NOT NULL COMMENT '订单id',
  `role_id` varchar(32) NOT NULL COMMENT '角色id',
  `server_id` varchar(16) NOT NULL COMMENT '服务器id',
  `points` int(10) NOT NULL COMMENT '积分',
  `point_strategy_record_id` int(11) NOT NULL COMMENT '累充策略记录ID',
  `product_id` varchar(50)  NOT NULL COMMENT '商品',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='积分礼品领取记录表';

CREATE TABLE `web_pay_whitelist` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `role_id` varchar(32) NOT NULL COMMENT '角色id',
  `server_id` varchar(16) NOT NULL COMMENT '服务器id',
  `sandbox_pay_enabled` tinyint(1) DEFAULT 0 COMMENT '是否使用沙盒支付',
  `preview_enabled` tinyint(1) DEFAULT 0 COMMENT '是否用于网页支付',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  CONSTRAINT role_id_server_id_unique UNIQUE (role_id, server_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户白名单';
