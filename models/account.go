package models

import (
	"time"

	"gorm.io/gorm"
)

type Account struct {
	ID            int       `gorm:"primarykey" json:"id"`
	UnionId       string    `gorm:"index;type:varchar(32);comment: 用户身份唯一标识" json:"union_id"`
	UserName      string    `gorm:"type:varchar(16;comment: 用户名" json:"user_name"`
	Password      string    `gorm:"type:varchar(255);comment: 密码" json:"password"`
	Email         *string   `gorm:"index;type:varchar(32);comment:'邮箱'" json:"email"`
	AreaCode      *string   `gorm:"index;type:varchar(8);comment:'手机区号'" json:"area_code"`
	Phone         *string   `gorm:"index;type:varchar(16);comment:'电话号码'" json:"phone"`
	RegisterIP    string    `gorm:"type:varchar(32);comment:'注册ip'" json:"register_ip"`
	RegisterTime  time.Time `gorm:"index;type:timestamp;default:CURRENT_TIMESTAMP;comment:'注册时间'" json:"register_time"`
	LastLoginIP   string    `gorm:"type:varchar(32);comment:'最近一次登录的IP地址'" json:"last_login_ip"`
	LastLoginTime time.Time `gorm:"index;type:timestamp;default:CURRENT_TIMESTAMP;comment:'最近一次登录时间'" json:"last_login_time"`
	Token         string    `gorm:"type:varchar(16);comment:'登录时生成的token,用于CP服务端登录验证'" json:"token"`
}

func (a *Account) TableName() string {
	return "account"
}

func GetAccount(db *gorm.DB, args map[string]interface{}) (*Account, error) {
	var account Account
	err := db.Where(args).First(&account).Error
	return &account, err
}
