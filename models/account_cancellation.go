package models

import (
	"time"

	"gorm.io/gorm"
)

type Unregister struct {
	Id                   int       `gorm:"primary_key" json:"id"`
	GameId               int       `gorm:"type:int(11) unsigned;default:0;comment:'游戏id'" json:"game_id"`
	Account              string    `gorm:"type:varchar(10);not null;comment:'账号'" json:"account"`
	CancellationTime     time.Time `gorm:"type:timestamp;default:CURRENT_TIMESTAMP;comment:'删除账号时间'" json:"cancellation_time"`
	ActualProcessingTime time.Time `gorm:"type:timestamp;default:0;comment:'实际处理时间'" json:"actual_processing_time"`
	Status               int       `gorm:"type:tinyint(2);default:0;comment:'当前账号状态， 0 冷静期；1 已删除；2 删除取消；-1 处理失败" json:"status"`
}

func (g *Unregister) TableName() string {
	return "unregister"
}

func GetUnregister(db *gorm.DB, args map[string]interface{}) *Unregister {
	var data Unregister
	_ = db.Where(args).First(&data).Error
	return &data
}

func GetUnregisterByStatus(db *gorm.DB, status int) []Unregister {
	var data []Unregister
	db.Where("status = ?", status).Find(&data)
	return data
}

func GetUnregisterByStatusAndTime(db *gorm.DB, status int, t time.Time) []Unregister {
	var data []Unregister
	db.Where("status = ?", status).Where("cancellation_time < ?", t).Find(&data)
	return data
}

func SaveUnregister(db *gorm.DB, args *Unregister) error {
	err := db.Save(args).Error
	return err
}
