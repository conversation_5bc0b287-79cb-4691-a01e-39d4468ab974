package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

type ActivityProduct struct {
	Limit    int `json:"limit"`
	Strategy int `json:"strategy"`
	Index    int `json:"index"`
}

type ProductId int

type ActivityProducts map[ProductId]*ActivityProduct

func (p ActivityProducts) Value() (driver.Value, error) {
	if p == nil {
		return "", nil
	}
	v, err := json.Marshal(p)
	return string(v), err
}

func (p *ActivityProducts) Scan(v interface{}) error {
	if len(v.([]byte)) == 0 {
		return nil
	}
	return json.Unmarshal(v.([]byte), p)
}

func (p ActivityProducts) GetProductIds() []int {
	ids := make([]int, 0)
	for key := range p {
		ids = append(ids, int(key))
	}
	return ids
}

func (p ActivityProducts) GetProduct(productId int) *ActivityProduct {
	return p[ProductId(productId)]
}

func (p ActivityProducts) SetProduct(productId int, activityProduct *ActivityProduct) {
	p[ProductId(productId)] = activityProduct
}

type Activity struct {
	Id                          int              `gorm:"primary_key;type:int(11) unsigned auto_increment;comment:'app id'" json:"id"`
	Name                        string           `gorm:"type:varchar(50);not null;comment:'活动名称'" json:"name"`
	Type                        int              `gorm:"type:varchar(255);not null;comment:'活动类型 1 限时活动 2 长期活动'" json:"type"`
	Description                 string           `gorm:"type:text;comment:'描述'" json:"description"`
	IsSupportCumulativePurchase bool             `gorm:"type:tinyint(1);DEFAULT:0;comment:'0 不支持累计充值 1 支持累计充值'" json:"is_support_cumulative_purchase"`
	ActivityProducts            ActivityProducts `gorm:"type:varchar(2048);not null;comment:'活动商品'" json:"activity_products"`
	StartTime                   time.Time        `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'活动开始时间'" json:"start_time"`
	EndTime                     time.Time        `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'活动结束时间'" json:"end_time"`
	CreateTime                  time.Time        `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'创建时间'" json:"create_time"`
	UpdateTime                  time.Time        `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'更新时间'" json:"update_time"`
	IsDelete                    bool             `gorm:"type:tinyint(1);DEFAULT:0;comment:'是否删除'" json:"is_delete"`
	DeleteTime                  int64            `gorm:"type:int(11);DEFAULT:0;comment:'删除时间'" json:"delete_time"`
}

func (g *Activity) TableName() string {
	return "activity"
}

func CreateActivity(db *gorm.DB, args *Activity) error {
	return db.Create(args).Error
}

func SaveActivity(db *gorm.DB, args *Activity) error {
	return db.Save(args).Error
}

func DeleteActivityById(db *gorm.DB, _id int) error {
	return db.Model(&Activity{}).Where("id = ?", _id).Update(
		"is_delete", true).Update("deleted_time", time.Now().Unix()).Error
}

type ActivityQueryOption func(*gorm.DB)

func WithValidActivitiesFlag() ActivityQueryOption {
	return func(query *gorm.DB) {
		query = query.Where("end_time > ?", time.Now())
	}
}

func WithIsSupportCumulativePurchase(isSupportCumulativePurchase int) ActivityQueryOption {
	return func(query *gorm.DB) {
		query = query.Where("is_support_cumulative_purchase = ?", isSupportCumulativePurchase)
	}
}
func WithName(name string) ActivityQueryOption {
	return func(query *gorm.DB) {
		query = query.Where("name like ?", "%"+name+"%")
	}
}
func WithIsNotDeleted() ActivityQueryOption {
	return func(query *gorm.DB) {
		query = query.Where("is_delete = ?", false)
	}
}

func GetActivitys(
	db *gorm.DB,
	options []ActivityQueryOption,
) ([]*Activity, error) {
	var activities []*Activity
	query := db.Model(&Activity{})

	for _, o := range options {
		o(query)
	}

	err := query.Order("case when type = 2 then 0 else 1 end, case when type = 1 then start_time end asc").Find(&activities).Error
	return activities, err
}

func GetActivityById(db *gorm.DB, _id int) (*Activity, error) {
	var activity Activity
	err := db.Where("id = ?", _id).Where("is_delete = ?", false).First(&activity).Error
	return &activity, err
}

func GetActivityByEndTimeAndType(db *gorm.DB, _time time.Time, _type int) ([]*Activity, error) {
	var activities []*Activity
	err := db.Where("end_time > ?", _time).Where(
		"type = ?", _type).Where("is_delete = ?", false).Find(&activities).Error
	return activities, err
}

func GetActivitiesBeforeDeadline(db *gorm.DB, _time time.Time) ([]Activity, error) {
	var activities []Activity
	err := db.Where("end_time > ?", _time).Where("is_delete = ?", false).Order(
		"case when type = 2 then 0 else 1 end, case when type = 1 then start_time end asc").Find(&activities).Error
	return activities, err
}

func GetCurrentActivity(db *gorm.DB, _time time.Time) (*Activity, error) {
	var activity Activity
	err := db.Where("start_time <= ?", _time).Where(
		"end_time > ?", _time).Where("is_delete = ?", false).First(&activity).Error
	return &activity, err
}

func GetCurrentActivities(db *gorm.DB) ([]Activity, error) {
	var activities []Activity
	nowTime := time.Now()
	err := db.Where("start_time <= ?", nowTime).Where(
		"end_time > ?", nowTime).Where("is_delete = ?", false).Order(
		"case when type = 2 then 0 else 1 end, case when type = 1 then start_time end asc").Find(&activities).Error
	return activities, err
}
