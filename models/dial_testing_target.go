package models

import (
	"zeus/common"
)

type DialTestingTarget struct {
	Id       int    `gorm:"primary_key;type:int(11) unsigned auto_increment;comment:'id'" json:"id"`
	GameId   int    `gorm:"type:int(11) unsigned;column:game_id;not null;comment:'游戏ID'" json:"game_id"`
	Name     string `gorm:"type:varchar(32);column:name;not null;comment:'拨测目标名称'" json:"name"`
	Path     string `gorm:"type:varchar(128);column:path;not null;comment:'拨测目标地址'" json:"path"`
	Protocol string `gorm:"type:varchar(16);column:protocol;not null;comment:'拨测协议'" json:"protocol"`
	Method   string `gorm:"type:varchar(16);column:method;not null;comment:'拨测请求方法'" json:"method"`
	Content  string `gorm:"type:varchar(128);column:content;not null;comment:'拨测请求内容'" json:"content"`
	Timeout  int    `gorm:"type:int(10);column:timeout;not null;comment:'拨测请求超时时间'" json:"timeout"`
}

func (o *DialTestingTarget) TableName() string {
	return "dial_testing_target"
}

func GetDialTestingTargetsByGameID(gameID int) ([]*DialTestingTarget, error) {
	var targets []*DialTestingTarget
	db := common.GetInfoDB()
	err := db.Where("game_id = ?", gameID).Find(&targets).Error
	return targets, err
}

func GetDialTestingTargetByID(id int) (*DialTestingTarget, error) {
	var target *DialTestingTarget
	db := common.GetInfoDB()
	err := db.Where("id = ?", id).Find(&target).Error
	return target, err
}

func GetDialTestingTargetByName(name string) (*DialTestingTarget, error) {
	var target *DialTestingTarget
	db := common.GetInfoDB()
	err := db.Where("name = ?", name).First(&target).Error
	return target, err
}

func CreateDialTestingTarget(args *DialTestingTarget) error {
	db := common.GetInfoDB()
	err := db.Create(args).Error
	return err
}

func SaveDialTestingTarget(args *DialTestingTarget) error {
	db := common.GetInfoDB()
	err := db.Save(args).Error
	return err
}

func DeleteDialTestingTarget(args *DialTestingTarget) error {
	db := common.GetInfoDB()
	err := db.Delete(args).Error
	return err
}
