package models

import (
	"time"

	"gorm.io/gorm"
)

type Game struct {
	Id          int       `gorm:"primary_key;type:int(11) unsigned auto_increment;comment:'app id'" json:"id"`
	ProjectID   int       `gorm:"foreign_key;type:int(11) unsigned;comment:'游戏项目id'" json:"project_id"`
	Name        string    `gorm:"type:varchar(50);not null;comment:'游戏名称'" json:"name"`
	Appid       string    `gorm:"uniqueIndex:appid,type:hash;type:varchar(16);not null;comment:'游戏appid'" json:"appid"`
	SecretKey   string    `gorm:"type:varchar(16);not null;comment:'游戏secretkey'" json:"secret_key"`
	Platform    string    `gorm:"type:varchar(10);not null;comment:'游戏平台'" json:"platform"`
	Type        string    `gorm:"type:varchar(20);not null;comment:'游戏类型'" json:"type"`
	ServiceMail string    `gorm:"type:varchar(100);default:'';comment:'游戏客服邮箱'" json:"service_mail"`
	Status      int       `gorm:"type:tinyint(1);default:1;comment:'游戏状态.0:关服,1:运营'" json:"status"`
	CreateTime  time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'游戏创建时间'" json:"create_time"`
	UpdateTime  time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'游戏更新时间'" json:"update_time"`
	OpenStatus  int       `json:"open_status"`
}

func (g *Game) TableName() string {
	return "game"
}

func (g *Game) GetOneGameInfo(db *gorm.DB, args map[string]interface{}) (*Game, error) {
	var game Game
	err := db.Where(args).First(&game).Error
	return &game, err
}

func SaveGameInfo(db *gorm.DB, args *Game) error {
	err := db.Save(args).Error
	return err
}

func CreateGame(db *gorm.DB, args *Game) error {
	err := db.Create(args).Error
	return err
}

func DeleteGame(db *gorm.DB, args *Game) error {
	err := db.Delete(args).Error
	return err
}

func GetGames(db *gorm.DB, args map[string]interface{}) ([]*Game, error) {
	var games []*Game
	err := db.Where(args).Find(&games).Error
	return games, err
}
