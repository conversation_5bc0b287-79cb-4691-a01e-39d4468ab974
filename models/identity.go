package models

import (
	"zeus/utils"

	"gorm.io/gorm"
)

type IdentityCache struct {
	Id      int    `gorm:"primary_key;auto_increment;column:id"`
	IdNoMd5 string `gorm:"size:255;column:id_no_md5;index;unique_index:name_id"` // 身份证号码使用md5加密后得到的值
	Name    string `gorm:"size:255;column:name;unique_index:name_id"`
}

func (i *IdentityCache) TableName() string {
	return "identity_cache"
}

func GetIdentityByIdCard(db *gorm.DB, idNo string) (*IdentityCache, error) {
	var identity IdentityCache
	idNoMd5 := utils.MD5(idNo)
	err := db.Where("id_no_md5 = ?", idNoMd5).First(&identity).Error
	return &identity, err
}

func AddIdentity(db *gorm.DB, idNo string, name string) error {
	identity := &IdentityCache{
		IdNoMd5: utils.MD5(idNo),
		Name:    name,
	}
	err := db.Create(identity).Error
	return err
}
