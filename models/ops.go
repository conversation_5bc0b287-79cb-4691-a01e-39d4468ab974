package models

import (
	"time"

	"gorm.io/gorm"
)

type Operation struct {
	Id           int       `gorm:"primary_key;type:int(11) unsigned auto_increment;comment:'操作id'" json:"id"`
	ProjectId    int       `gorm:"type:int(11) unsigned;not null;column:project_id;comment:'项目id'" json:"project_id"`
	User         string    `gorm:"type:varchar(128);not null;comment:'操作用户'" json:"user"`
	Action       string    `gorm:"type:varchar(128);not null;comment:'操作动作'" json:"action"`
	Resource     string    `gorm:"type:varchar(128);not null;comment:'操作资源'" json:"resource"`
	ResourceName string    `gorm:"type:varchar(128);not null;comment:'操作资源名称'" json:"resource_name"`
	ResourceFunc string    `gorm:"type:varchar(128);not null;comment:'操作资源功能'" json:"resource_func"`
	OldData      string    `gorm:"type:varchar(2048);not null;comment:'原数据'" json:"old_data"`
	NewData      string    `gorm:"type:varchar(2048);not null;comment:'新数据'" json:"new_data"`
	Time         time.Time `gorm:"type:datetime;default:CURRENT_TIMESTAMP;comment:'操作时间'" json:"time"`
	Detail       string    `gorm:"type:text;not null;comment:'请求详情'" json:"detail"`
}

func (o *Operation) TableName() string {
	return "operation"
}

func (o *Operation) GetOperationsByFilter(db *gorm.DB, args map[string]interface{}) (*Operation, error) {
	var operation Operation
	err := db.Where(args).Find(&operation).Error
	return &operation, err
}

func CreateOperation(db *gorm.DB, args *Operation) error {
	err := db.Create(args).Error
	return err
}

func GetOperationsByPage(db *gorm.DB, pageSize int, pageNum int, args map[string]interface{}, startTime string, endTime string) ([]*Operation, int64, error) {
	var operation []*Operation
	var total int64
	offset := pageSize * (pageNum - 1)
	operationQuery := db.Model(&Operation{}).Where(args).Order("time desc")
	if startTime != "" && endTime != "" {
		operationQuery = operationQuery.Where("time >= ?", startTime).Where("time <= ?", endTime).Order("time desc")
	}
	operationQuery.Count(&total)
	err := operationQuery.Limit(pageSize).Offset(offset).Find(&operation).Error
	return operation, total, err
}
