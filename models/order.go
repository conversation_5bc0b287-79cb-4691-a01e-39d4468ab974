package models

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"zeus/common/constants"
	"zeus/schema"

	"gorm.io/gorm"
)

type Order struct {
	Id              int       `gorm:"primary_key;type:int(11) unsigned;autoincrement;comment:'订单id'" json:"id"`
	GameId          int       `gorm:"type:int(11) unsigned;not null;comment:'游戏id'" json:"game_id"`
	UserId          int       `gorm:"index:user_id;type:int(11) unsigned;not null;comment:'用户id'" json:"user_id"`
	Account         string    `gorm:"index:account;type:varchar(10);not null;comment:'账号id'" json:"account"`
	Platform        string    `gorm:"type:varchar(16);not null;comment:'平台'" json:"platform"`
	Device          string    `gorm:"type:varchar(64);not null;comment:'设备'" json:"device"`
	ServerId        string    `gorm:"type:varchar(16);not null;comment:'服务器id'" json:"server_id"`
	ServerName      string    `gorm:"type:varchar(16);not null;comment:'服务器名称'" json:"server_name"`
	RoleId          string    `gorm:"index:role_id;type:varchar(32);not null;comment:'角色id'" json:"role_id"`
	RoleName        string    `gorm:"type:varchar(32);not null;comment:'角色名称'" json:"role_name"`
	Vip             int       `gorm:"type:tinyint(3);not null;comment:'VIP等级'" json:"vip"`
	Level           int       `gorm:"type:smallint(6);not null;comment:'等级'" json:"level"`
	OrderId         string    `gorm:"unique_index:order_id;type:varchar(64);not null;comment:'订单id'" json:"order_id"`
	ProductId       string    `gorm:"type:varchar(32);not null;comment:'商品id'" json:"product_id"`
	Price           float64   `gorm:"type:float(10,2);not null;comment:'价格'" json:"price"`
	CreateTime      time.Time `gorm:"index:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:'创建时间'" json:"create_time"`
	Extend          string    `gorm:"index:extend;type:varchar(255);not null;comment:'扩展'" json:"extend"`
	PayId           string    `gorm:"index:pay_id;type:varchar(32);not null;comment:'支付id'" json:"pay_id"`
	PayAmount       float64   `gorm:"type:float(10,2);not null;comment:'支付金额'" json:"pay_amount"`
	PayCurrency     string    `gorm:"type:varchar(32);not null;comment:'支付币种'" json:"pay_currency"`
	PayTime         time.Time `gorm:"index:pay_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:'支付时间'" json:"pay_time"`
	PayWay          int       `gorm:"type:tinyint(8);not null;comment:'支付方式'" json:"pay_way"`
	PayIp           string    `gorm:"type:varchar(255);not null;comment:'支付ip'" json:"pay_ip"`
	PayStatus       int       `gorm:"type:tinyint(3);not null;comment:'支付状态 0失败 1成功'" json:"pay_status"`
	PayProductId    string    `gorm:"type:varchar(32);not null;comment:'支付商品id'" json:"pay_product_id"`
	NotifyTime      time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:'最近次通知CP时间'" json:"notify_time"`
	NotifyCnt       int       `gorm:"type:int(11);not null;comment:'通知CP次数'" json:"notify_cnt"`
	NotifyStatus    int       `gorm:"type:tinyint(3);not null;comment:'游戏支付通知CP状态: -1 失败 1成功'" json:"notify_status"`
	RefundTime      time.Time `gorm:"type:timestamp;column:refund_time;not null;default:CURRENT_TIMESTAMP;comment:'订单退款时间'" json:"refund_time"`
	Receipt         string    `gorm:"type:varchar(1024);not null;comment:'支付服务器数据json'" json:"receipt"`
	NotifyResult    string    `gorm:"type:varchar(1024);not null;comment:'游戏支付通知数据json'" json:"notify_result"`
	PayNotifyUrl    string    `gorm:"type:varchar(255);not null;comment:'支付回调地址'" json:"pay_notify_url"`
	OrderAttribute  int       `gorm:"type:int(11);default:0;comment:'订单属性'" json:"order_attribute"`
	PayErrorMessage string    `gorm:"type:varchar(1024);not null;comment:'游戏支付失败原因'" json:"pay_error_message"`
	VerifyStatus    int       `gorm:"type:bool;column:verify_status;comment:'验证状态：0 未验证，1：验证通过，2:验证未通过'" json:"verify_status"`
	PrepayId        string    `gorm:"type:varchar(64);comment:'预支付ID'" json:"prepay_id"`
	OpenId          string    `gorm:"type:varchar(32);comment:'用户OpenId'" json:"open_id"`
}

func (Order) TableName() string {
	return "order"
}

func (order Order) StructToMap() map[string]interface{} {
	o := make(map[string]interface{})
	j, _ := json.Marshal(order)
	json.Unmarshal(j, &o)

	return o
}

func GetTotalPrice(db *gorm.DB, uniqueIdentity string, status int, startTime, endTime time.Time) (float64, error) {
	var totalPrice float64
	err := db.Raw("select sum(price) as total_price from `order` where pay_time >= ? and pay_time < ?  and pay_status = ?  and user_id in (select id from `user` where unique_identity = ?)",
		startTime, endTime, status, uniqueIdentity).Scan(&totalPrice).Error
	return totalPrice, err
}

func GetOrderByOrderID(db *gorm.DB, OrderID string) (*Order, error) {
	var order Order
	err := db.Where("order_id = ?", OrderID).First(&order).Error
	return &order, err
}

func GetOrderByPayIDAndPayStatus(db *gorm.DB, payID string, payStatus int) (*Order, error) {
	var order Order
	err := db.Where("pay_id = ?", payID).Where("pay_status = ?", payStatus).First(&order).Error
	return &order, err
}

func GetOrdersByTime(db *gorm.DB, payWay, payStatus int, startTime, endTime string) ([]Order, error) {
	var order []Order
	err := db.Where("pay_time > ?", startTime).
		Where("pay_time < ?", endTime).
		Where("pay_way = ?", payWay).
		Where("pay_status = ?", payStatus).
		Find(&order).Error
	return order, err
}

func GetOrders(db *gorm.DB, args map[string]interface{}) ([]*Order, error) {
	var order []*Order
	err := db.Where(args).Order("create_time desc").Find(&order).Error
	return order, err
}

func GetLatestOneOrder(db *gorm.DB, args map[string]interface{}) (*Order, error) {
	var order *Order
	err := db.Where(args).Order("create_time desc").First(&order).Error
	return order, err
}

func GetOrderByPayId(db *gorm.DB, payId string) (Order, error) {
	var order Order
	err := db.Where("pay_id = ?", payId).First(&order).Error
	return order, err
}

func GetOneOrder(db *gorm.DB, args map[string]interface{}) (*Order, error) {
	var order Order
	err := db.Where(args).First(&order).Error
	return &order, err
}

func SaveOrder(db *gorm.DB, args *Order) error {
	err := db.Save(args).Error
	return err
}

func CreateOrder(db *gorm.DB, args *Order) error {
	err := db.Create(args).Error
	return err
}

func CreateOrders(db *gorm.DB, args []Order) error {
	err := db.Create(args).Error
	return err
}

func GetOrderByPage(db *gorm.DB, pageSize int, pageNum int, orderAttribute []string, args map[string]interface{}, startTime string, endTime string) ([]Order, int64, error) {
	var order []Order
	var total int64
	offset := pageSize * (pageNum - 1)
	userQuery := db.Model(&Order{}).Where(args).Order("create_time desc")
	if len(orderAttribute) > 0 {
		userQuery = userQuery.Where("order_attribute IN (?)", orderAttribute)
	}
	if startTime != "" && endTime != "" {
		userQuery = userQuery.Where("create_time >= ?", startTime).Where("create_time <= ?", endTime).Order("create_time desc")
	}

	userQuery.Count(&total)
	err := userQuery.Limit(pageSize).Offset(offset).Find(&order).Error
	return order, total, err
}

func GetOrderByOptions(db *gorm.DB, options ...QueryOrderOptions) ([]*Order, int64, error) {
	orders := make([]*Order, 0)
	var total int64

	userQuery := db.Model(&Order{})
	for _, o := range options {
		userQuery = o(userQuery)
	}
	err := userQuery.Find(&orders).Error

	userQuery.Limit(-1).Offset(-1).Count(&total)
	return orders, total, err
}

type QueryOption func(*string)

func WithDefaultPayStatus() QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and pay_status in (%d, %d)", constants.PaymentSuccess, constants.PaymentRefund)
	}
}

func WithPayStatus(payStatus int) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and pay_status = %d", payStatus)
	}
}

func WithPayWay(payWay int) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and pay_way = %d", payWay)
	}
}

func WithNotifyStatus(notifyStatus int) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and notify_status = %d", notifyStatus)
	}
}

func WithOrderID(orderID string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and order_id = %q", orderID)
	}
}

func WithPayID(payID string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and pay_id = %q", payID)
	}
}

func WithRoleID(roleID string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and role_id = %q", roleID)
	}
}

func WithRoleName(roleName string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and role_name = %q", roleName)
	}
}

func WithUserID(userID string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and user_id = %q", userID)
	}
}

func WithDevice(device string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and device = %q", device)
	}
}

func WithServerID(serverID string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and server_id = %q", serverID)
	}
}

func WithProductId(productId string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and product_id = %q", productId)
	}
}

func WithAccount(account string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and account = %q", account)
	}
}

func WithOrderAttributes(orderAttribute []string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and order_attribute in (%q)", strings.Join(orderAttribute, ","))
	}
}

func WithDefaultSubscriptionAttribute() QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and order_attribute = %d", constants.NormalOrder)
	}
}

func WithSubscriptionAttribute(orderAttribute int) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and order_attribute = %d", orderAttribute)
	}
}

func WithTimeRange(startTime string, endTime string) QueryOption {
	return func(sqlString *string) {
		*sqlString += fmt.Sprintf(" and create_time >= %q and create_time <= %q ", startTime, endTime)
	}
}

func GetDownloadOrder(db *gorm.DB, gameID int, pageNum int, pageSize int, chooseList []string, options []QueryOption) *sql.Rows {
	var chooseColumns []string
	for _, chooseColumn := range chooseList {
		var column string
		if chooseColumn == "pay_way" {
			column = " CASE WHEN pay_way = 1 THEN '苹果支付' WHEN pay_way = 2 THEN 'Google支付' WHEN pay_way = 3 THEN '微信支付' WHEN pay_way = 4 THEN '支付宝支付' " +
				"WHEN pay_way = 7 THEN 'Xsolla支付' WHEN pay_way = 8 THEN 'MyCard支付' WHEN pay_way = 9 THEN 'Gash支付' else '' end as pay_way"
		} else if chooseColumn == "pay_status" {
			column = " CASE WHEN pay_status = 0 THEN '未支付' WHEN pay_status = 1 THEN '支付成功' WHEN pay_status = 2 THEN '已退款' else '支付失败' end as pay_status"
		} else if chooseColumn == "notify_status" {
			column = "CASE WHEN notify_status = 0 THEN '未通知' WHEN notify_status = 1 THEN '通知成功' else '通知失败' end as notify_status"
		} else if chooseColumn == "order_attribute" {
			column = " CASE WHEN order_attribute = 0 THEN '普通订单' WHEN order_attribute = 1 THEN '测试订单' WHEN order_attribute = 4 THEN '网页支付活动' else '' end as order_attribute"
		} else if chooseColumn == "verify_status" {
			column = "CASE WHEN verify_status = 0 THEN '未验证' WHEN verify_status = 1 THEN '验证通过' WHEN verify_status = 2 THEN '验证未通过' WHEN verify_status = 3 THEN '异常订单' else '' end as verify_status"
		} else {
			column = chooseColumn
		}
		chooseColumns = append(chooseColumns, column)
	}

	sqlString := fmt.Sprintf("select %s from `order` where game_id= %d", strings.Join(chooseColumns, ", "), gameID)

	for _, o := range options {
		o(&sqlString)
	}

	sqlString += " order by create_time desc"
	if pageSize != 0 && pageNum != 0 {
		sqlString += fmt.Sprintf(" limit %d offset %d", pageSize, pageSize*(pageNum-1))
	}
	rows, _ := db.Raw(sqlString).Rows()
	return rows
}

func GetDownloadOrderCount(db *gorm.DB, gameID int, options []QueryOption) int {
	sqlString := fmt.Sprintf("select count(id) from `order` where game_id= %d", gameID)

	for _, o := range options {
		o(&sqlString)
	}
	var orderCount int
	db.Raw(sqlString).Scan(&orderCount)
	return orderCount
}

func GetDownloadQueryOptions(params schema.OrderQueryParams) []QueryOption {
	var opt []QueryOption

	if params.PayStatus != nil {
		opt = append(opt, WithPayStatus(*params.PayStatus))
	} else {
		opt = append(opt, WithDefaultPayStatus())
	}

	if params.PayWay != nil {
		opt = append(opt, WithPayWay(*params.PayWay))
	}
	if params.NotifyStatus != nil {
		opt = append(opt, WithNotifyStatus(*params.NotifyStatus))
	}
	if params.OrderID != "" {
		opt = append(opt, WithOrderID(params.OrderID))
	}
	if params.PayID != "" {
		opt = append(opt, WithPayID(params.PayID))
	}
	if params.RoleID != "" {
		opt = append(opt, WithRoleID(params.RoleID))
	}
	if params.RoleName != "" {
		opt = append(opt, WithRoleName(params.RoleName))
	}
	if params.UserID != "" {
		opt = append(opt, WithUserID(params.UserID))
	}
	if params.Device != "" {
		opt = append(opt, WithDevice(params.Device))
	}
	if params.ServerID != "" {
		opt = append(opt, WithServerID(params.ServerID))
	}
	if params.ProductId != "" {
		opt = append(opt, WithProductId(params.ProductId))
	}
	if params.Account != "" {
		opt = append(opt, WithAccount(params.Account))
	}
	if len(params.OrderAttribute) > 0 {
		opt = append(opt, WithOrderAttributes(params.OrderAttribute))
	}
	if params.StartTime != "" && params.EndTime != "" {
		opt = append(opt, WithTimeRange(params.StartTime, params.EndTime))
	}

	return opt
}

type QueryOrderOptions func(*gorm.DB) *gorm.DB

func QueryOrderWithOrderId(orderId string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("order_id = ?", orderId)
	}
}

func QueryOrderWithPayID(payID string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("pay_id = ?", payID)
	}
}

func QueryOrderWithAccount(account string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("account = ?", account)
	}
}
func QueryOrderWithUserId(userId string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("user_id = ?", userId)
	}
}
func QueryOrderWithRoleId(roleId string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("role_id = ?", roleId)
	}
}
func QueryOrderWithRoleName(roleName string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("role_name = ?", roleName)
	}
}

func QueryOrderWithServerId(serverId string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("server_id = ?", serverId)
	}
}

func QueryOrderWithProductId(productId string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("product_id = ?", productId)
	}
}
func QueryOrderWithDevice(device string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("device = ?", device)
	}
}

func QueryOrderWithPlatform(platform string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("platform = ?", platform)
	}
}

func QueryOrderWithPayIp(payIp string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("pay_ip = ?", payIp)
	}
}
func QueryOrderWithPayWay(payWay int) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("pay_way = ?", payWay)
	}
}
func QueryOrderWithOrderAttribute(orderAttribute []string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("order_attribute in ?", orderAttribute)
	}
}

func QueryOrderWithPayStatus(payStatus int) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("pay_status = ?", payStatus)
	}
}
func QueryOrderWithNotifyStatus(notifyStatus int) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("notify_status = ?", notifyStatus)
	}
}

func QueryOrderWithStartTime(startTime string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("create_time >= ?", startTime)
	}
}

func QueryOrderWithEndTime(endTime string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("create_time <= ?", endTime)
	}
}

func QueryOrderWithRefundStartTime(startTime string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("refund_time >= ?", startTime)
	}
}

func QueryOrderWithRefundEndTime(endTime string) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("refund_time <= ?", endTime)
	}
}

func QueryOrderWithCreateTimeSort() QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Order("create_time desc")
	}
}

func QueryOrderWithRefundTimeSort() QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Order("refund_time desc")
	}
}

func QueryOrderWithPage(pageSize, pageNum int) QueryOrderOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Limit(pageSize).Offset(pageSize * (pageNum - 1))
	}
}
