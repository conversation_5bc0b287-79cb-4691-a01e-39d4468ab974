package models

import (
	"fmt"
	"time"

	"zeus/schema"

	"google.golang.org/genproto/googleapis/type/date"
	"gorm.io/gorm"
)

type OrderStatistics struct {
	Id          int       `gorm:"primary_key;type int(11) unsigned;autoincrement;comment:'id'" json:"id"`
	GameID      int       `gorm:"type int(11) unsigned;not null;comment:'游戏id'" json:"game_id"`
	ProductID   string    `gorm:"type:varchar(32);not null;comment:'商品id'" json:"product_id"`
	PayWay      int       `gorm:"type:tinyint(2);not null;comment:'支付方式'" json:"pay_way"`
	PayDate     date.Date `gorm:"type:date;not null;comment:'支付时间'" json:"pay_date"`
	Total       int       `gorm:"type:int(11) unsigned;not null;comment:'订单总数'" json:"total"`
	TotalPrice  float64   `gorm:"type:int(11) unsigned;not null;comment:'订单总金额'" json:"total_price"`
	RefundCount int       `gorm:"type:int(11) unsigned;not null;comment:'退款订单总数'" json:"refund_count"`
	RefundPrice float64   `gorm:"type:int(11) unsigned;not null;comment:'退款订单总金额'" json:"refund_price"`
	UpdateTime  time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:'更新时间'" json:"update_time"`
}

func (g *OrderStatistics) TableName() string {
	return "order_statistics"
}

func CreateOrderStatistics(db *gorm.DB, args []schema.OrderStatistics) error {
	err := db.CreateInBatches(args, len(args)).Error
	return err
}

func DeleteOrderStatistics(db *gorm.DB, gameID int, startDate string, EndDate string) error {
	var err error
	sqlCondition := fmt.Sprintf("game_id = %d", gameID)
	if startDate != "" {
		sqlCondition += " and pay_date >= '" + startDate + "'"
	}
	if EndDate != "" {
		sqlCondition += " and pay_date <= '" + EndDate + "'"
	}
	err = db.Delete(&OrderStatistics{}, sqlCondition).Error

	return err
}
