package models

import (
	"gorm.io/gorm"
	"time"
)

type WebPayPointStrategy struct {
	Id          int       `gorm:"primary_key;type:int(11) unsigned auto_increment;comment:'id'" json:"id"`
	Name        string    `gorm:"type:varchar(50);not null;comment:'名称'" json:"name"`
	Description string    `gorm:"type:varchar(1024);DEFAULT:'';comment:'描述'" json:"description"`
	Points      int       `gorm:"type:int(10);not null;comment:'积分'" json:"points"`
	ProductId   string    `gorm:"type:varchar(50);not null;comment:'商品'" json:"product_id"`
	CreateTime  time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'创建时间'" json:"create_time"`
	UpdateTime  time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'更新时间'" json:"update_time"`
}

func (p *WebPayPointStrategy) TableName() string {
	return "web_pay_point_strategy"
}

func CreateWebPayPointStrategy(db *gorm.DB, args *WebPayPointStrategy) error {
	return db.Create(args).Error
}

func SaveWebPayPointStrategy(db *gorm.DB, args *WebPayPointStrategy) error {
	return db.Save(args).Error
}

func DeleteWebPayPointStrategy(db *gorm.DB, args *WebPayPointStrategy) error {
	err := db.Delete(args).Error
	return err
}

func GetWebPayPointStrategyById(db *gorm.DB, _id int) (*WebPayPointStrategy, error) {
	var strategy WebPayPointStrategy
	err := db.Where("id = ?", _id).First(&strategy).Error
	return &strategy, err
}

func GetWebPayPointStrategy(
	db *gorm.DB,
) ([]*WebPayPointStrategy, int64, error) {
	var pointStrategies []*WebPayPointStrategy
	var total int64
	query := db.Model(&WebPayPointStrategy{})
	query.Count(&total)
	err := query.Order("create_time desc").Find(&pointStrategies).Error
	return pointStrategies, total, err
}
