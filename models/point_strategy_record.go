package models

import (
	"gorm.io/gorm"
	"time"
)

type PointsAndProducts struct {
	Points  int    `json:"points"`
	Product string `json:"product"`
}

type WebPayPointStrategyRecord struct {
	Id          int       `gorm:"primary_key;type:int(11) unsigned auto_increment;comment:'id'" json:"id"`
	Name        string    `gorm:"type:varchar(50);not null;comment:'名称'" json:"name"`
	Type        int       `gorm:"type:tinyint(1);not null;DEFAULT:1;comment:'类型. 1 普通类型'" json:"type"`
	Description string    `gorm:"type:varchar(1024);DEFAULT:'';comment:'描述'" json:"description"`
	Products    string    `gorm:"type:text;not null;comment:'积分档位和商品'" json:"products"`
	Picture     string    `gorm:"type:varchar(255);not null;comment:'图片'" json:"picture"`
	StartTime   time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'活动开始时间'" json:"start_time"`
	EndTime     time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'活动结束时间'" json:"end_time"`
	CreateTime  time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'创建时间'" json:"create_time"`
	UpdateTime  time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'更新时间'" json:"update_time"`
}

func (p *WebPayPointStrategyRecord) TableName() string {
	return "web_pay_point_strategy_record"
}

func CreateWebPayPointStrategyRecord(db *gorm.DB, args *WebPayPointStrategyRecord) error {
	return db.Create(args).Error
}

func SaveWebPayPointStrategyRecord(db *gorm.DB, args *WebPayPointStrategyRecord) error {
	return db.Save(args).Error
}

func GetWebPayPointStrategyRecords(
	db *gorm.DB,
	pageSize int,
	pageNum int,
	args map[string]interface{},
) ([]*WebPayPointStrategyRecord, int64, error) {
	var pointStrategyRecords []*WebPayPointStrategyRecord
	var total int64
	query := db.Model(&WebPayPointStrategyRecord{}).Where(args)
	query.Count(&total)
	err := query.Order("create_time desc").Limit(pageSize).Offset(pageSize * (pageNum - 1)).Find(&pointStrategyRecords).Error
	return pointStrategyRecords, total, err
}

func GetWebPayPointStrategyRecordById(db *gorm.DB, _id int) (*WebPayPointStrategyRecord, error) {
	var strategy WebPayPointStrategyRecord
	err := db.Where("id = ?", _id).First(&strategy).Error
	return &strategy, err
}

func GetCurrentWebPayPointStrategyRecord(db *gorm.DB) (*WebPayPointStrategyRecord, error) {
	var strategy *WebPayPointStrategyRecord
	nowTime := time.Now()
	err := db.Where("start_time <= ?", nowTime).Where(
		"end_time > ?", nowTime).First(&strategy).Error
	return strategy, err
}

func GetWebPayPointStrategyRecordByEndTime(db *gorm.DB, recordId int, _time time.Time) ([]*WebPayPointStrategyRecord, error) {
	var strategies []*WebPayPointStrategyRecord
	query := db.Model(&WebPayPointStrategyRecord{})
	if recordId != 0 {
		query = query.Where("id != ?", recordId)
	}
	err := query.Where("end_time > ?", _time).Find(&strategies).Error
	return strategies, err
}

func GetLatestWebPayPointStrategyRecord(db *gorm.DB, exceptRecordId int) (*WebPayPointStrategyRecord, error) {
	var strategy *WebPayPointStrategyRecord
	query := db.Model(&WebPayPointStrategyRecord{})
	if exceptRecordId != 0 {
		query = query.Where("id != ?", exceptRecordId)
	}
	err := query.Order("end_time DESC").First(&strategy).Error
	return strategy, err
}
