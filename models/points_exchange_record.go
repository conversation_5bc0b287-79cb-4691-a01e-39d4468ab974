package models

import (
	"gorm.io/gorm"
	"time"
)

type WebPayPointsExchangeRecord struct {
	Id                    int       `gorm:"primary_key;type:int(11) unsigned auto_increment;comment:'id'" json:"id"`
	OrderId               string    `gorm:"unique_index:order_id;type:varchar(64);not null;comment:'订单id'" json:"order_id"`
	RoleId                string    `gorm:"index;type:varchar(32);comment: 角色ID" json:"role_id"`
	ServerId              string    `gorm:"type varchar(16);comment:'服务器id'" json:"server_id"`
	PointStrategyRecordId int       `gorm:"type:int(11);comment:'累充策略记录ID'" json:"point_strategy_record_id"`
	Points                int       `gorm:"type:int(10) unsigned;comment:'商品充值积分'" json:"points"`
	ProductId             string    `gorm:"type:varchar(50);not null;comment:'商品'" json:"product_id"`
	CreateTime            time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'创建时间'" json:"create_time"`
	UpdateTime            time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'更新时间'" json:"update_time"`
}

func (p *WebPayPointsExchangeRecord) TableName() string {
	return "web_pay_points_exchange_record"
}

func CreatePointsExchangeRecords(db *gorm.DB, args []WebPayPointsExchangeRecord) error {
	return db.Create(args).Error
}

func GetPointsExchangeRecords(db *gorm.DB, args map[string]interface{}) ([]*WebPayPointsExchangeRecord, error) {
	var pointExchangeRecords []*WebPayPointsExchangeRecord
	err := db.Model(&WebPayPointsExchangeRecord{}).Where(args).Order("create_time desc").Find(&pointExchangeRecords).Error
	return pointExchangeRecords, err
}

func GetPointsExchangeRecordsByRoleIdAndServerId(db *gorm.DB, roleId string, serverId string, currentPointStrategyRecordId int) ([]*WebPayPointsExchangeRecord, error) {
	args := make(map[string]interface{})
	args["point_strategy_record_id"] = currentPointStrategyRecordId
	args["role_id"] = roleId
	args["server_id"] = serverId
	pointsExchangeRecords, err := GetPointsExchangeRecords(db, args)
	if err != nil {
		return nil, err
	}
	return pointsExchangeRecords, nil
}

func GetPointsExchangeRecordsByPage(db *gorm.DB, options ...QueryRecordOptions) ([]*WebPayPointsExchangeRecord, int64, error) {
	records := make([]*WebPayPointsExchangeRecord, 0)
	var total int64
	userQuery := db.Model(&WebPayPointsExchangeRecord{})
	for _, o := range options {
		userQuery = o(userQuery)
	}

	err := userQuery.Find(&records).Error
	userQuery.Limit(-1).Offset(-1).Count(&total)

	return records, total, err
}

type QueryRecordOptions func(*gorm.DB) *gorm.DB

func QueryRecordWithOrderId(orderId string) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("order_id = ?", orderId)
	}
}

func QueryRecordWithRoleId(roleId string) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("role_id = ?", roleId)
	}
}

func QueryRecordWithServerId(serverId string) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("server_id = ?", serverId)
	}
}

func QueryRecordWithProductId[T string | int](productId T) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("product_id = ?", productId)
	}
}

func QueryRecordWithActivityId(activityId int) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("activity_id = ?", activityId)
	}
}

func QueryRecordWithPayStatus(payStatus int) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("pay_status = ?", payStatus)
	}
}

func QueryRecordWithPoints(points int) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("points = ?", points)
	}
}

func QueryRecordWithStartTime(startTime string) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("create_time >= ?", startTime)
	}
}

func QueryRecordWithEndTime(endTime string) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("create_time <= ?", endTime)
	}
}

func QueryRecordWithCreateTimeSort() QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Order("create_time desc")
	}
}

func QueryRecordWithPage(pageSize, pageNum int) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Limit(pageSize).Offset(pageSize * (pageNum - 1))
	}
}

func QueryRecordWithJoinProduct() QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Joins("JOIN product ON purchase_record.product_id = product.id")
	}
}
