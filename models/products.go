package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

type PriceItem struct {
	Currency string  `json:"currency" binding:"required"`
	Price    float64 `json:"price" binding:"required"`
}

type PriceItems []*PriceItem

func (p PriceItems) ToMap() Prices {
	prices := make(Prices, 0)
	for _, price := range p {
		prices[Currency(price.Currency)] = Price(price.Price)
	}
	return prices
}

type (
	Currency string
	Price    float64
	Prices   map[Currency]Price
)

func (p Prices) Value() (driver.Value, error) {
	if p == nil {
		return "", nil
	}
	v, err := json.Marshal(p)
	return string(v), err
}

func (p *Prices) Scan(v interface{}) error {
	if len(v.([]byte)) == 0 {
		return nil
	}
	return json.Unmarshal(v.([]byte), p)
}

func (p Prices) GetPrice(currency string) float64 {
	price, ok := p[Currency(currency)]
	if !ok {
		return 0
	}
	return float64(price)
}

func (p Prices) GetDefaultCurrency() string {
	for k := range p {
		return string(k)
	}
	return ""
}

func (p Prices) SetPrice(currency string, price float64) {
	p[Currency(currency)] = Price(price)
}

func (p Prices) Items() []*PriceItem {
	items := make([]*PriceItem, 0)
	for k, price := range p {
		items = append(items, &PriceItem{Currency: string(k), Price: float64(price)})
	}
	return items
}

type Product struct {
	Id            int       `gorm:"primary_key;type:int(11) unsigned auto_increment;comment:'app id'" json:"id"`
	GameProductId string    `gorm:"type:varchar(50);not null;comment:'游戏商品ID'" json:"game_product_id"`
	Name          string    `gorm:"type:varchar(50);not null;comment:'商品名称'" json:"name"`
	Prices        Prices    `gorm:"type:varchar(255);not null;comment:'价格列表'" json:"prices"`
	Picture       string    `gorm:"type:varchar(255);not null;comment:'商品图片'" json:"picture"`
	Attribute     int       `gorm:"type:int(11) unsigned;comment:'商品属性: 1.限时商品 2.长期商品 '" json:"attribute"`
	CreateTime    time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'创建时间'" json:"create_time"`
	UpdateTime    time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'更新时间'" json:"update_time"`
	IsDelete      bool      `gorm:"type:tinyint(1);DEFAULT:0;comment:'是否删除'" json:"is_delete"`
	DeleteTime    int64     `gorm:"type:int(11);DEFAULT:0;comment:'删除时间'" json:"delete_time"`
	Description   string    `gorm:"type:text;comment:'描述'" json:"description"`
	Points        int       `gorm:"type:int(11) unsigned;comment:'商品充值积分'" json:"points"`
}

func (g *Product) TableName() string {
	return "product"
}

func CreateProduct(db *gorm.DB, args *Product) error {
	return db.Create(args).Error
}

func SaveProduct(db *gorm.DB, args *Product) error {
	return db.Save(args).Error
}

func GetProductById(db *gorm.DB, _id int) (*Product, error) {
	var product Product
	err := db.Where("id = ?", _id).Where("is_delete = ?", false).First(&product).Error
	return &product, err
}

func GetProductByGameProductId(db *gorm.DB, gameProductId string) (*Product, error) {
	var product Product
	err := db.Where("game_product_id = ?", gameProductId).Where("is_delete = ?", false).First(&product).Error
	return &product, err
}

func DeleteProductById(db *gorm.DB, _id int) error {
	return db.Model(&Product{}).Where("id = ?", _id).Update(
		"is_delete", true).Update("delete_time", time.Now().Unix()).Error
}

func GetProducts(
	db *gorm.DB,
	pageSize int,
	pageNum int,
	name string,
	gameProductId string,
	args map[string]interface{},
) ([]*Product, int64, error) {
	var products []*Product
	var total int64
	query := db.Model(&Product{}).Where(args).Where("is_delete = ?", false)
	if name != "" {
		query = query.Where("name like ?", "%"+name+"%")
	}
	if gameProductId != "" {
		query = query.Where("game_product_id like ?", "%"+gameProductId+"%")
	}
	query = query.Order("attribute desc").Order("game_product_id asc")
	query.Count(&total)
	err := query.Limit(pageSize).Offset(pageSize * (pageNum - 1)).Find(&products).Error
	return products, total, err
}

func GetProductByIds(db *gorm.DB, ids []int) ([]*Product, error) {
	if len(ids) == 0 {
		return make([]*Product, 0), nil
	}
	var products []*Product
	err := db.Where("id IN ?", ids).Where("is_delete = ?", false).Find(&products).Error
	return products, err
}
