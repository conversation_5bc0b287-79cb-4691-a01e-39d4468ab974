package models

import (
	"time"

	"gorm.io/gorm"
)

type Project struct {
	ID            int       `gorm:"primary_key;type:int(11) unsigned auto_increment;comment:'游戏项目id'" json:"id"`
	Name          string    `gorm:"type:varchar(50);not null;comment:'游戏项目名称'" json:"name"`
	<PERSON><PERSON>         string    `gorm:"type:varchar(32);not null;comment:'游戏项目别名'" json:"alias"`
	Type          string    `gorm:"type:varchar(20);not null;comment:'游戏项目类型'" json:"type"`
	Status        int       `gorm:"type:tinyint(1);default:1;comment:'游戏状态.0:关服,1:运营,2:测试'" json:"status"`
	CreateTime    time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'游戏创建时间'" json:"create_time"`
	UpdateTime    time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'游戏更新时间'" json:"update_time"`
	MysqlDatabase string    `gorm:"type:varchar(50);not null;comment:'数据库库名'" json:"mysql_database"`
}

func (p *Project) TableName() string {
	return "project"
}

func (p *Project) GetOneProjectInfo(db *gorm.DB, args map[string]interface{}) (*Project, error) {
	var project Project
	err := db.Where(args).First(&project).Error
	return &project, err
}

func GetProjectInfoById(db *gorm.DB, projectId int) (*Project, error) {
	var project Project
	err := db.Where("id = ?", projectId).First(&project).Error
	return &project, err
}

func GetProjectInfoByAlias(db *gorm.DB, projectAlias int) (*Project, error) {
	var project Project
	err := db.Where("alias = ?", projectAlias).First(&project).Error
	return &project, err
}

func GetProject(db *gorm.DB) ([]*Project, error) {
	var projects []*Project
	err := db.Where("status != 0").Find(&projects).Error
	return projects, err
}

func SaveProject(db *gorm.DB, args *Project) error {
	err := db.Save(args).Error
	return err
}

func CreateProject(db *gorm.DB, args *Project) error {
	err := db.Create(args).Error
	return err
}

func DeleteProject(db *gorm.DB, args *Project) error {
	err := db.Delete(args).Error
	return err
}
