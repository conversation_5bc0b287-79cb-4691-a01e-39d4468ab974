package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"zeus/common/constants"

	"gorm.io/gorm"
)

type PayUrl map[string]interface{}

func (p PayUrl) Value() (driver.Value, error) {
	if p == nil {
		return "", nil
	}
	v, err := json.Marshal(p)
	return string(v), err
}

func (p *PayUrl) Scan(v interface{}) error {
	if len(v.([]byte)) == 0 {
		return nil
	}
	return json.Unmarshal(v.([]byte), p)
}

type PurchaseRecord struct {
	ID                    int       `gorm:"primarykey" json:"id"`
	OrderId               string    `gorm:"unique_index:order_id;type:varchar(64);not null;comment:'订单id'" json:"order_id"`
	RoleId                string    `gorm:"index;type:varchar(32);comment: 角色ID" json:"role_id"`
	ServerId              string    `gorm:"type varchar(16);default null;comment:'服务器id'" json:"server_id"`
	ActivityId            int       `gorm:"type int(11) unsigned;default 0;comment:'活动id'" json:"activity_id"`
	ProductId             int       `gorm:"type varchar(16);default null;comment:'商品id'" json:"product_id"`
	Points                int       `gorm:"type:int(11) unsigned;comment:'商品充值积分'" json:"points"`
	PayStatus             int       `gorm:"type tinyint(3);default 0;comment:'支付状态 0 未支付 1 已支付'" json:"pay_status"`
	CreateTime            time.Time `gorm:"type:timestamp;default:CURRENT_TIMESTAMP;comment:'创建时间'" json:"create_time"`
	UpdateTime            time.Time `gorm:"type:timestamp;default:CURRENT_TIMESTAMP;comment:'更新时间'" json:"update_time"`
	PointStrategyRecordId int       `gorm:"type:int(11);default null;comment:'累充策略记录ID'" json:"point_strategy_record_id"`
}

type PurchaseRecordInfo struct {
	PurchaseRecord
	GameProductId string `json:"game_product_id"`
}

func (p *PurchaseRecord) TableName() string {
	return "purchase_record"
}

func CreatePurchaseRecord(db *gorm.DB, args *PurchaseRecord) error {
	return db.Create(args).Error
}

func CreatePurchaseRecords(db *gorm.DB, args []PurchaseRecord) error {
	return db.Create(args).Error
}

func GetRoleActivityPurchaseRecords(
	db *gorm.DB,
	roleID string,
	serverID string,
	activityID int,
) ([]*PurchaseRecord, error) {
	var purchaseRecords []*PurchaseRecord
	err := db.Where("role_id = ?", roleID).
		Where("server_id = ?", serverID).
		Where("activity_id = ?", activityID).
		Where("pay_status = ?", constants.PaymentSuccess).
		Find(&purchaseRecords).Error
	return purchaseRecords, err
}

func GetUnpaidRecord(
	db *gorm.DB,
	roleID string,
	serverID string,
	activityID int,
	ProductId int,
) (*PurchaseRecord, error) {
	var purchaseRecord PurchaseRecord
	err := db.Where("role_id = ?", roleID).
		Where("server_id = ?", serverID).
		Where("activity_id = ?", activityID).
		Where("product_id = ?", ProductId).
		Where("pay_status = ?", constants.PaymentUnpaid).
		First(&purchaseRecord).Error
	return &purchaseRecord, err
}

func GetRoleActivityProductPurchaseCount(
	db *gorm.DB,
	roleID string,
	serverID string,
	activityID int,
	ProductId int,
) (int, error) {
	var count int64
	err := db.Model(&PurchaseRecord{}).
		Where("role_id = ?", roleID).
		Where("server_id = ?", serverID).
		Where("activity_id = ?", activityID).
		Where("product_id = ?", ProductId).
		Where("pay_status = ?", constants.PaymentSuccess).
		Count(&count).Error
	return int(count), err
}

func GetPurchaseRecordByOrderId(db *gorm.DB, orderId string) (*PurchaseRecord, error) {
	var purchaseRecord PurchaseRecord
	err := db.Where("order_id = ?", orderId).First(&purchaseRecord).Error
	return &purchaseRecord, err
}

func SavePurchaseRecord(db *gorm.DB, purchaseRecord *PurchaseRecord) error {
	return db.Save(purchaseRecord).Error
}

func GetPurchaseRecordByPage(db *gorm.DB, options ...QueryRecordOptions) ([]*PurchaseRecordInfo, int64, error) {
	purchaseRecords := make([]*PurchaseRecordInfo, 0)
	var total int64

	userQuery := db.Model(&PurchaseRecord{}).Select("purchase_record.*, product.game_product_id")
	for _, o := range options {
		userQuery = o(userQuery)
	}
	err := userQuery.Find(&purchaseRecords).Error

	userQuery.Limit(-1).Offset(-1).Count(&total)
	return purchaseRecords, total, err
}

func GetPurchaseTotalPoints(db *gorm.DB, roleId string, serverId string, startTime time.Time, endTime time.Time) int {
	var totalPoints = 0

	sqlString := fmt.Sprintf("select COALESCE(sum(a.counts * product.points), 0) as total_points from "+
		"(select activity_id, product_id, count(id) as counts from purchase_record "+
		"where create_time >= %q and create_time <= %q and pay_status = 1 and "+
		"activity_id in (select id from activity where is_support_cumulative_purchase = 1) and"+
		" role_id = %q and server_id = %q group by activity_id, product_id) as a left join product on a.product_id = product.id", startTime.Format(constants.DateTimeMinuteOnly), endTime.Format(constants.DateTimeMinuteOnly), roleId, serverId)
	db.Raw(sqlString).Scan(&totalPoints)

	return totalPoints
}

func QueryPurchaseRecordWithStartTime(startTime string) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("purchase_record.create_time >= ?", startTime)
	}
}

func QueryPurchaseRecordWithEndTime(endTime string) QueryRecordOptions {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("purchase_record.create_time <= ?", endTime)
	}
}
