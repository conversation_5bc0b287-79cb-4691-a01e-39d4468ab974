package models

import (
	"time"

	"gorm.io/gorm"
)

type RenewSubscription struct {
	Id                    int       `gorm:"primary_key;type:int(11) unsigned auto_increment;column:id;comment:'续订订单id'" json:"id"`
	GameId                int       `gorm:"type:int(11) unsigned;default:0;comment:'游戏id'" json:"game_id"`
	PayWay                int       `gorm:"type:tinyint(2);not null;comment:'支付方式'" json:"pay_way"`
	OriginalTransactionId string    `gorm:"type:varchar(128);default:null;column:original_transaction_id;comment:'原始交易id'" json:"original_transaction_id"`
	TransactionId         string    `gorm:"type:varchar(64);not null;column:transaction_id;comment:'交易id'" json:"transaction_id"`
	OrderId               string    `gorm:"type:varchar(64);not null;column:order_id;comment:'关联订阅订单id'" json:"order_id"`
	NotifyStatus          int       `gorm:"type:tinyint(3);default:null;column:notify_status;comment:'通知状态'" json:"notify_status"`
	NotifyResult          string    `gorm:"type:varchar(2048);default:null;column:notify_result;comment:'游戏支付通知数据json'" json:"notify_result"`
	NotificationInfo      string    `gorm:"type:varchar(2048);not null;column:notification_info;comment:'苹果服务器发送的部分通知数据'" json:"notification_info"`
	NotificationType      string    `gorm:"type:varchar(32);not null;column:notification_type;comment:'苹果服务器发送的通知类型'" json:"notification_type"`
	NotificationUUID      string    `gorm:"unique_index:notification_uuid;type:varchar(64);not null;column:notification_uuid;comment:'通知的唯一标识符'" json:"notification_uuid"`
	ExpiresDate           int       `gorm:"type:int(11);not null;column:expires_date;comment:'过期时间'" json:"expires_date"`
	CreateTime            time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;column:create_time;comment:'创建时间'" json:"create_time"`
}

func (RenewSubscription) TableName() string {
	return "renew_subscription"
}

func CreateRenewSubscription(db *gorm.DB, args *RenewSubscription) error {
	err := db.Create(args).Error
	return err
}

func SaveRenewSubscription(db *gorm.DB, args *RenewSubscription) error {
	err := db.UpdateColumns(args).Error
	return err
}

func GetRenewSubscriptionByFilter(db *gorm.DB, args map[string]interface{}) ([]RenewSubscription, error) {
	var renewSubscriptions []RenewSubscription
	err := db.Where(args).Order("id desc").Find(&renewSubscriptions).Error
	return renewSubscriptions, err
}

func GetLastedRenewSubscriptionByFilter(db *gorm.DB, args map[string]interface{}) (*RenewSubscription, error) {
	var renewSubscriptions RenewSubscription
	err := db.Where(args).Order("id desc").First(&renewSubscriptions).Error
	return &renewSubscriptions, err
}
