package models

import (
	"zeus/common"
)

type RequestStatistics struct {
	Id           int    `gorm:"primary_key;type int(11) unsigned;autoincrement;comment:'id'" json:"id"`
	AppId        string `gorm:"type:varchar(16);column:appid;not null;comment:'游戏id'" json:"appid"`
	ProjectId    int    `gorm:"type:int(11) unsigned;column:project_id;not null;comment:'项目id'" json:"project_id"`
	RequestPath  string `gorm:"type:varchar(64);not null;comment:'请求路径'" json:"request_path"`
	RequestDate  string `gorm:"type:varchar(32);not null;comment:'支付时间'" json:"request_date"`
	RequestCount int64  `gorm:"type:int(11) unsigned;not null;comment:'请求次数'" json:"request_count"`
}

func (r *RequestStatistics) TableName() string {
	return "request_statistics"
}

func CreateRequestStatistics(args *RequestStatistics) error {
	db := common.GetInfoDB()
	err := db.Create(args).Error
	return err
}

func SaveRequestStatistics(args *RequestStatistics) error {
	db := common.GetInfoDB()
	err := db.Save(args).Error
	return err
}

func GetRequestStatistics(args map[string]interface{}) (*RequestStatistics, error) {
	db := common.GetInfoDB()
	var requestDB RequestStatistics
	err := db.Where(args).First(&requestDB).Error
	return &requestDB, err
}
