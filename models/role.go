package models

import (
	"time"

	"gorm.io/gorm"
)

type Role struct {
	Id         int       `gorm:"primary_key;type int(11) unsigned auto increment 10001;comment:'角色id'" json:"id"`
	GameID     int       `gorm:"type int(11) unsigned;default 0;comment:'游戏id'" json:"game_id"`
	UserID     int       `gorm:"index:user_id;type int(11) unsigned;default 0;comment:'用户id'" json:"user_id"`
	Platform   string    `gorm:"type varchar(16);default null;comment:'平台'" json:"platform"`
	Device     string    `gorm:"type varchar(64);default null;comment:'设备'" json:"device"`
	ServerId   string    `gorm:"type varchar(16);default null;comment:'服务器id'" json:"server_id"`
	ServerName string    `gorm:"type varchar(16);default null;comment:'服务器名称'" json:"server_name"`
	RoleId     string    `gorm:"index:role_id;type varchar(32);default null;comment:'角色id'" json:"role_id"`
	RoleName   string    `gorm:"type varchar(32);default null;comment:'角色名称'" json:"role_name"`
	Vip        int       `gorm:"type tinyint(3);default 0;comment:'VIP等级'" json:"vip"`
	Level      int       `gorm:"type smallint(6);default 1;comment:'等级'" json:"level"`
	PlayTime   time.Time `gorm:"index:play_time;type:TIMESTAMP;default:CURRENT_TIMESTAMP;comment:'游戏时间'" json:"play_time"`
	PlayIp     string    `gorm:"type varchar(255);default null;comment:'最近次游戏ip'" json:"play_ip"`
	Extend     string    `gorm:"type varchar(65535);default null;comment:'扩展信息'" json:"extend"`
}

func (g *Role) TableName() string {
	return "role"
}

type Roles []Role

func (rs Roles) GetRolesByUserID(userID int) Roles {
	roles := make(Roles, 0)
	for _, r := range rs {
		if r.UserID == userID {
			roles = append(roles, r)
		}
	}
	return roles
}

func GetRole(db *gorm.DB, roleID string) (*Role, error) {
	var role Role
	err := db.Where("role_id = ?", roleID).First(&role).Error
	return &role, err
}

func GetRoleWithGameId(db *gorm.DB, roleID string, gameIds []int) (*Role, error) {
	var role Role
	err := db.Where("role_id = ?", roleID).Where("game_id in ?", gameIds).First(&role).Error
	return &role, err
}

func GetRoleByServerIdAndRoleId(db *gorm.DB, roleID, serverID string) (*Role, error) {
	var role Role
	err := db.Where("role_id = ?", roleID).Where("server_id = ?", serverID).First(&role).Error
	return &role, err
}

func GetRoles(db *gorm.DB, roleID string) ([]Role, error) {
	var roles []Role
	err := db.Where("role_id = ?", roleID).Find(&roles).Error
	return roles, err
}

func GetRolesByUserIDs(db *gorm.DB, gameID int, userID []int) (Roles, error) {
	roles := make([]Role, 0)
	if len(userID) == 0 {
		return roles, nil
	}
	err := db.Where("game_id = ?", gameID).Where("user_id in ?", userID).Find(&roles).Error
	return roles, err
}

func QueryRole(db *gorm.DB, args map[string]interface{}) (*Role, error) {
	var role Role
	err := db.Where(args).First(&role).Error
	return &role, err
}

func QueryRoleByName(db *gorm.DB, roleName string) ([]Role, error) {
	var roles []Role
	err := db.Where("role_name like ?", "%"+roleName+"%").Find(&roles).Error
	return roles, err
}

func SaveRole(db *gorm.DB, role *Role) error {
	err := db.Save(role).Error
	return err
}

func GetAllRole(db *gorm.DB, args map[string]interface{}) ([]Role, error) {
	var role []Role
	err := db.Where(args).Find(&role).Error
	return role, err
}

func DeleteRoleByUserId(db *gorm.DB, userId int) error {
	return db.Delete(&Role{}, "user_id = ?", userId).Error
}
