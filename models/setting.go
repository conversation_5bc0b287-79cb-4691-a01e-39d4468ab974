package models

type Setting struct {
	ID        int    `gorm:"primary_key;type:int(11) unsigned auto_increment 10001;comment:'用户id'" json:"id"`
	Appid     string `gorm:"type:varchar(16);default:'';comment:'应用ID'" json:"appid"`
	ProjectID int    `gorm:"type:int(11)" json:"project_id"`
	GameId    int    `gorm:"type:int(11)" json:"game_id"`
	Type      string `gorm:"type:varchar(20);default:'';comment:'类型'" json:"type"`
	Name      string `gorm:"type:varchar(20);default:'';comment:'名称'" json:"name"`
	Value     string `gorm:"type:varchar(20);default:'';comment:'值'" json:"value"`
}

func (s *Setting) TableName() string {
	return "settings"
}
