package models

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"zeus/schema"

	"gorm.io/gorm"
)

type Subscription struct {
	Id                    int       `gorm:"primary_key;type:int(11) unsigned auto_increment;comment:'订阅id'" json:"id"`
	GameId                int       `gorm:"type:int(11) unsigned;default:0;comment:'游戏id'" json:"game_id"`
	UserId                int       `gorm:"index:user_id;type:int(11) unsigned;not null;comment: '用户id'" json:"user_id"`
	Account               string    `gorm:"index:account;type:varchar(11);not null;comment: '账号id'" json:"account"`
	Platform              string    `gorm:"type:varchar(16);default:null;comment:'平台'" json:"platform"`
	Device                string    `gorm:"type:varchar(64);default:null;comment:'设备'" json:"device"`
	ServerId              string    `gorm:"type:varchar(16);default:null;comment:'服务器id'" json:"server_id"`
	ServerName            string    `gorm:"type:varchar(16);default:null;comment:'服务器名称'" json:"server_name"`
	RoleId                string    `gorm:"type:varchar(32);default:null;comment:'角色id'" json:"role_id"`
	RoleName              string    `gorm:"type:varchar(32);default:null;comment:'角色名称'" json:"role_name"`
	Vip                   int       `gorm:"type:tinyint(3);default:0;comment:'VIP等级'" json:"vip"`
	Level                 int       `gorm:"type:smallint(6);default:1;comment:'等级'" json:"level"`
	OriginalTransactionId string    `gorm:"unique_index:original_transaction_id;type:varchar(64);default:null;comment:'原始订单id'" json:"original_transaction_id"`
	TransactionId         string    `gorm:"index:transaction_id;type:varchar(64);default:null;comment:'订单id'" json:"transaction_id"`
	OrderId               string    `gorm:"unique_index:order_id;type:varchar(64);default:null;comment:'订单id'" json:"order_id"`
	ProductId             string    `gorm:"type:varchar(64);default:null;comment:'产品id'" json:"product_id"`
	Price                 float64   `gorm:"type:float(10,2);default:0.00;comment:'价格'" json:"price"`
	CreateTime            time.Time `gorm:"index:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:'创建时间'" json:"create_time"`
	Extend                string    `gorm:"index:extend;type:varchar(255);default:null;comment:'扩展'" json:"extend"`
	PayAmount             float64   `gorm:"type:float(10,2);default:0.00;comment:'支付金额'" json:"pay_amount"`
	PayCurrency           string    `gorm:"type:varchar(64);default:null;comment:'支付币种'" json:"pay_currency"`
	PayTime               time.Time `gorm:"index:pay_time;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:'支付时间'" json:"pay_time"`
	PayStatus             int       `gorm:"type:tinyint(3);not null;comment:'支付状态 0失败 1成功'" json:"pay_status"`
	PayId                 string    `gorm:"type:varchar(64);default:null;comment:'支付id'" json:"pay_id"`
	PayProductId          string    `gorm:"type:varchar(64);default:null;comment:'支付产品id'" json:"pay_product_id"`
	ExpiresDate           int       `gorm:"type:int(11);default:null;comment:'过期时间'" json:"expires_date"`
	NotificationType      int       `gorm:"type:int(11);default:null;comment:'通知类型'" json:"notification_type"`
	NotifyTime            time.Time `gorm:"type:timestamp;default:CURRENT_TIMESTAMP;comment:'通知时间'" json:"notify_time"`
	NotifyCnt             int       `gorm:"type:int(11);default:0;comment:'通知次数'" json:"notify_cnt"`
	NotifyStatus          int       `gorm:"type:tinyint(3);default:null;comment:'通知状态'" json:"notify_status"`
	OriginReceipt         string    `gorm:"type:varchar(64);default:null;comment:'原始回执'" json:"origin_receipt"`
	OriginReceiptInfo     string    `gorm:"type:varchar(64);default:null;comment:'原始回执信息'" json:"origin_receipt_info"`
	LatestReceipt         string    `gorm:"type:varchar(64);default:null;comment:'最新回执'" json:"latest_receipt"`
	LatestReceiptInfo     string    `gorm:"type:varchar(64);default:null;comment:'最新回执信息'" json:"latest_receipt_info"`
	PayNotifyUrl          string    `gorm:"type:varchar(255);not null;comment:'支付回调地址'" json:"pay_notify_url"`
	OrderAttribute        int       `gorm:"type:int(11);default:0;comment:'订单属性'" json:"order_attribute"`
}

func (Subscription) TableName() string {
	return "subscription"
}

func CreateSubscription(db *gorm.DB, args *Subscription) error {
	err := db.Create(args).Error
	return err
}

func GetOneSubscription(db *gorm.DB, args map[string]interface{}) (*Subscription, error) {
	var subscription Subscription
	err := db.Where(args).First(&subscription).Error
	return &subscription, err
}

func SaveSubscription(db *gorm.DB, args *Subscription) error {
	err := db.Save(args).Error
	return err
}

func UpdateSubscriptionColumns(db *gorm.DB, args *Subscription) error {
	err := db.UpdateColumns(args).Error
	return err
}

func GetSubscriptionByPage(db *gorm.DB, pageSize int, pageNum int, args map[string]interface{}, startTime string, endTime string) ([]Subscription, int64, error) {
	var order []Subscription
	var total int64
	offset := pageSize * (pageNum - 1)
	userQuery := db.Model(&Subscription{}).Where(args).Order("create_time desc")
	if startTime != "" && endTime != "" {
		userQuery = userQuery.Where("create_time >= ?", startTime).Where("create_time <= ?", endTime).Order("create_time desc")
	}
	userQuery.Count(&total)
	err := userQuery.Limit(pageSize).Offset(offset).Find(&order).Error
	return order, total, err
}

func GetDownloadSubscriptionRows(db *gorm.DB, gameID int, pageNum int, pageSize int, chooseList []string, options []QueryOption) *sql.Rows {
	var chooseColumns []string
	for _, chooseColumn := range chooseList {
		var column string
		if chooseColumn == "pay_status" {
			column = " CASE WHEN pay_status = 0 THEN '未支付' WHEN pay_status = 1 THEN '支付成功' WHEN pay_status = 2 THEN '已退款' else '支付失败' end as pay_status"
		} else if chooseColumn == "notify_status" {
			column = "CASE WHEN notify_status = 0 THEN '未通知' WHEN notify_status = 1 THEN '通知成功' else '通知失败' end as notify_status"
		} else if chooseColumn == "order_attribute" {
			column = " CASE WHEN order_attribute = 0 THEN '普通订单' WHEN order_attribute = 1 THEN '测试订单' else '' end as order_attribute"
		} else {
			column = chooseColumn
		}
		chooseColumns = append(chooseColumns, column)
	}

	sqlString := fmt.Sprintf("select %s from `subscription` where game_id= %d", strings.Join(chooseColumns, ", "), gameID)

	for _, o := range options {
		o(&sqlString)
	}

	sqlString += " order by create_time desc"
	if pageSize != 0 && pageNum != 0 {
		sqlString += fmt.Sprintf(" limit %d offset %d", pageSize, pageSize*(pageNum-1))
	}
	rows, _ := db.Raw(sqlString).Rows()
	return rows
}

func GetDownloadSubscriptionCount(db *gorm.DB, gameID int, options []QueryOption) int {
	sqlString := fmt.Sprintf("select count(id) from `subscription` where game_id= %d", gameID)

	for _, o := range options {
		o(&sqlString)
	}
	var count int
	db.Raw(sqlString).Scan(&count)
	return count
}

func GetDownloadSubscriptionQueryOptions(params schema.SubscriptionQueryParams) []QueryOption {

	var options []QueryOption

	if params.PayStatus != nil {
		options = append(options, WithPayStatus(*params.PayStatus))
	} else {
		options = append(options, WithDefaultPayStatus())
	}

	if params.NotifyStatus != nil {
		options = append(options, WithNotifyStatus(*params.NotifyStatus))
	}

	if params.OrderID != "" {
		options = append(options, WithOrderID(params.OrderID))
	}
	if params.PayID != "" {
		options = append(options, WithPayID(params.PayID))
	}
	if params.RoleID != "" {
		options = append(options, WithRoleID(params.RoleID))
	}
	if params.ServerID != "" {
		options = append(options, WithServerID(params.ServerID))
	}
	if params.ProductId != "" {
		options = append(options, WithProductId(params.ProductId))
	}
	if params.Account != "" {
		options = append(options, WithAccount(params.Account))
	}

	if params.OrderAttribute != 0 {
		options = append(options, WithSubscriptionAttribute(params.OrderAttribute))
	} else {
		options = append(options, WithDefaultSubscriptionAttribute())
	}

	if params.StartTime != "" && params.EndTime != "" {
		options = append(options, WithTimeRange(params.StartTime, params.EndTime))
	}

	return options
}
