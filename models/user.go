package models

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"zeus/common/constants"

	"github.com/gin-gonic/gin"

	"gorm.io/gorm"
)

type User struct {
	ID                       int       `gorm:"primary_key;type:int(11) unsigned auto_increment 10001;comment:'用户id'" json:"id"`
	Account                  string    `gorm:"uniqueindex:account;type:varchar(10);not null;comment:'账号'" json:"account"`
	Name                     string    `gorm:"type:varchar(20);default:'';comment:'姓名'" json:"name"`
	GameID                   int       `gorm:"type:int(11) unsigned;default:0;comment:'游戏id'" json:"game_id,string"`
	PwMd5                    string    `gorm:"type:varchar(255);default:null;comment:'密码'" json:"pw_md5"`
	Platform                 string    `gorm:"type:varchar(255);default:null;comment:'平台'" json:"platform"`
	RegisterTime             time.Time `gorm:"index:register_time;type:timestamp;default:CURRENT_TIMESTAMP;comment:'注册时间'" json:"register_time"`
	RegisterIP               string    `gorm:"type:varchar(255);not null;comment:'注册ip'" json:"register_ip"`
	RegisterDevice           string    `gorm:"type:varchar(255);default:null;comment:'注册设备'" json:"register_device"`
	MobileInfo               string    `gorm:"type:text;comment:'手机信息'" json:"mobile_info"`
	LoginTime                time.Time `gorm:"index:login_time;type:timestamp;default:CURRENT_TIMESTAMP;comment:'登录时间'" json:"login_time"`
	LoginIP                  string    `gorm:"type:varchar(16);comment:'登录ip'" json:"login_ip"`
	LoginDevice              Devices   `gorm:"foreignkey:UserID" json:"login_device"`
	LoginCnt                 int       `gorm:"type:smallint(6);default:0;comment:'登录次数'" json:"login_cnt,string"`
	Token                    string    `gorm:"type:varchar(32);default:null;comment:'登录时生成的token,用于CP服务端登录验证'" json:"token"`
	Birthday                 string    `gorm:"type:varchar(8);comment:'出生日期'" json:"birthday"`
	UniqueIdentity           string    `gorm:"type:varchar(32);default:null;comment:'唯一标识'" json:"unique_identity"`
	IsRealNameAuthentication int       `gorm:"type:tinyint(3);not null;default:0;comment:'实名状态 0未实名 1已实名'" json:"is_real_name_authentication"`
	Email                    string    `gorm:"index:email;type:varchar(32);default:'';comment:'邮箱'" json:"email"`
	Phone                    string    `gorm:"index:phone;type:varchar(16);default:'';comment:'电话号码'" json:"phone"`
	Status                   int       `gorm:"type:int(11);default:0;comment:'账号状态 0正常 1删除中 2已删除'" json:"status"`
	IsDeleted                bool      `gorm:"type:bool;default:false;comment:'是否删除'" json:"is_deleted"`
}

func (u *User) TableName() string {
	return "user"
}

func (u *User) DeviceInLoginDevice(c *gin.Context, device string) bool {
	db := c.MustGet("db").(*gorm.DB)
	_, err := GetUserDeviceByUserID(db, u.ID, device)
	return err == nil
}

type (
	Users   []User
	Devices []UserDevice
)

func (devices Devices) MarshalJSON() ([]byte, error) {
	ds := make([]string, 0, len(devices))
	for _, device := range devices {
		ds = append(ds, device.Device)
	}
	return json.Marshal(strings.Join(ds, ","))
}

func (us Users) GetIDs() []int {
	ids := make([]int, 0)
	for _, u := range us {
		ids = append(ids, u.ID)
	}
	return ids
}

func GetOneUser(db *gorm.DB, args map[string]interface{}) (*User, error) {
	var user User
	err := db.Where(args).First(&user).Error
	return &user, err
}

func GetUserByAccount(db *gorm.DB, account string) (*User, error) {
	var user User
	err := db.Where("account = ?", account).First(&user).Error
	return &user, err
}

func GetUserByID(db *gorm.DB, id int) (*User, error) {
	var user User
	err := db.Where("id = ?", id).First(&user).Error
	return &user, err
}

func GetUserByAccountAndRole(db *gorm.DB, account string, roleId string, serverId string) (*User, *Role, error) {
	var user User
	var role Role

	query := db.Model(&User{})
	if account != "" {
		query.Where("account = ?", account)
	}
	if roleId != "" && serverId != "" {
		if err := db.Model(&Role{}).Where("role_id = ?", roleId).Where("server_id = ?", serverId).First(&role).Error; err != nil {
			return nil, nil, err
		}
		userId := role.UserID
		if userId != 0 {
			query.Where("id = ?", userId)
		}
	}
	err := query.First(&user).Error
	return &user, &role, err
}

func GetUserByIDs(db *gorm.DB, ids []int) ([]User, error) {
	if len(ids) == 0 {
		return make([]User, 0), nil
	}
	var users []User
	// 不要直接使用db.find(&users, ids)进行查询，当ids为空列表时会返回所有的数据
	err := db.Where("id IN ?", ids).Find(&users).Error
	return users, err
}

func GetUsers(db *gorm.DB, args map[string]interface{}) ([]User, error) {
	var user []User
	err := db.Where(args).Order("login_time desc").Find(&user).Error
	return user, err
}

func GetUsersByPage(db *gorm.DB, pageSize int, pageNum int, bindUserIDs []int, userIDs []int, args map[string]interface{}, loginTime [2]string, registerTime [2]string) ([]User, int64, error) {
	var user []User
	var total int64
	offset := pageSize * (pageNum - 1)
	userQuery := db.Model(&User{}).Preload("LoginDevice").Where(args).Order("login_time desc")
	if len(userIDs) > 0 {
		userQuery = userQuery.Where("id IN ?", userIDs)
	}
	if len(bindUserIDs) > 0 {
		userQuery = userQuery.Where("id NOT IN ?", bindUserIDs)
	}
	if loginTime[0] != "" && loginTime[1] != "" {
		userQuery = userQuery.Where("login_time >= ?", loginTime[0]).Where("login_time <= ?", loginTime[1]).Order("login_time desc")
	}
	if registerTime[0] != "" && registerTime[1] != "" {
		userQuery = userQuery.Where("register_time >= ?", registerTime[0]).Where("register_time <= ?", registerTime[1]).Order("register_time desc")
	}
	userQuery.Count(&total)
	err := userQuery.Limit(pageSize).Offset(offset).Find(&user).Error
	return user, total, err
}

func SaveUser(db *gorm.DB, args *User) error {
	err := db.Save(args).Error
	return err
}

func CreateUser(db *gorm.DB, args *User) error {
	err := db.Create(args).Error
	return err
}

func GetUserByUnionIDAndBindThirdTypeAndBindThirdStatus(db *gorm.DB, unionID string, bindThirdType, bindThirdStatus int, gameID int) (Users, error) {
	users := make([]User, 0)
	err := db.Raw("select * from `user` where `user`.id in (select user_id from `user_bind_third` where `user_bind_third`."+
		"type = ? and `user_bind_third`.status = ? and `user_bind_third`.union_id = ?) and game_id = ?",
		bindThirdType, bindThirdStatus, unionID, gameID).Scan(&users).Error
	return users, err
}

func GetUserByOpenIDAndBindThirdTypeAndBindThirdStatus(db *gorm.DB, unionID string, bindThirdType, bindThirdStatus int, gameID int) (Users, error) {
	users := make([]User, 0)
	err := db.Raw("select * from `user` where `user`.id in (select user_id from `user_bind_third` where `user_bind_third`."+
		"type = ? and `user_bind_third`.status = ? and `user_bind_third`.open_id = ?) and game_id = ?",
		bindThirdType, bindThirdStatus, unionID, gameID).Scan(&users).Error
	return users, err
}

func GetRecentlyLoggedInUnboundUserByRegisterDevice(db *gorm.DB, device string, gameID any) (*User, error) {
	var u User
	err := db.Raw("select * from `user` where `user`.register_device = ? and `user`.pw_md5 = ? and `user`.game_id = ? and"+
		" `user`.is_deleted != 1 and `user`.status != 2 and"+
		" `user`.id not in (select `user_bind_third`.user_id from `user_bind_third` where `user_bind_third`.status = 1)"+
		" order by `user`.login_time desc limit 1", device, constants.DefaultPassword, gameID).Scan(&u).Error
	return &u, err
}

type QueryUserOption func(*gorm.DB) *gorm.DB

func QueryUserWithGameId(gameId int) QueryUserOption {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("game_id = ?", gameId)
	}
}

func QueryUserWithId(id int) QueryUserOption {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("id = ?", id)
	}
}

func QueryUserWithIds(ids []int) QueryUserOption {
	return func(tx *gorm.DB) *gorm.DB {
		fmt.Println("userids", ids)
		return tx.Where("id in ?", ids)
	}
}

func QueryUserWithAccount(account string) QueryUserOption {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("account = ?", account)
	}
}

func QueryUserWithLoginStartTime(loginStartTime string) QueryUserOption {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("login_time >= ?", loginStartTime)
	}
}

func QueryUserWithLoginEndTime(loginEndTime string) QueryUserOption {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("login_time <= ?", loginEndTime)
	}
}

func QueryUserWithRegisterStartTime(registerStartTime string) QueryUserOption {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("register_time >= ?", registerStartTime)
	}
}

func QueryUserWithRegisterEndTime(registerEndTime string) QueryUserOption {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("register_time <= ?", registerEndTime)
	}
}

func QueryUserWithPage(pageSize, pageNum int) QueryUserOption {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Limit(pageSize).Offset(pageSize * (pageNum - 1))
	}
}

func QueryUserWithRegisterDevice(registerDevice string) QueryUserOption {
	return func(tx *gorm.DB) *gorm.DB {
		return tx.Where("register_device = ?", registerDevice)
	}
}

func QueryUsers(db *gorm.DB, options ...QueryUserOption) ([]User, int64, error) {
	var users []User
	var total int64
	userQuery := db.Model(&User{})
	for _, o := range options {
		userQuery = o(userQuery)
	}
	err := userQuery.Find(&users).Error
	userQuery.Limit(-1).Offset(-1).Count(&total)
	return users, total, err
}

func UpdateUserStatusByUserIds(db *gorm.DB, ids []int, status int) error {
	if err := db.Model(&User{}).Where("id IN ?", ids).Update("status", status).Error; err != nil {
		return err
	}
	return nil
}
