package models

import (
	"gorm.io/gorm"
)

type UserDevice struct {
	ID     int    `gorm:"primary_key;type:int(11) unsigned auto_increment 10001;comment:'用户id'" json:"id"`
	UserID int    `gorm:"index:user_id;type int(11) unsigned;default 0;comment:'用户id'" json:"user_id"`
	Device string `gorm:"type:varchar(200);comment:'登录设备'" json:"login_device"`
}

func (u *UserDevice) TableName() string {
	return "user_device"
}

func GetUserDeviceByUserID(db *gorm.DB, userId int, device string) (*UserDevice, error) {
	var userDevice UserDevice
	err := db.Where("user_id = ?", userId).Where("device = ?", device).First(&userDevice).Error
	return &userDevice, err
}

func SaveUserDevice(db *gorm.DB, args *UserDevice) error {
	err := db.Save(args).Error
	return err
}
