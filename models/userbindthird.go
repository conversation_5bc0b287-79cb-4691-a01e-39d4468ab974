package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

type UserBindThird struct {
	ID         int       `gorm:"primary_key;type:int(11) unsigned auto_increment" json:"id"`
	UserID     int       `gorm:"index:user_id;type:int(11) unsigned;not null;unique_index:user_union_type;comment: '绑定的user表主账号id'" json:"user_id"`
	UnionID    string    `gorm:"index:union_id;type:varchar(64);not null;unique_index:user_union_type;comment: '第三方平台的id'" json:"union_id"`
	OpenID     string    `gorm:"index:open_id;type:varchar(64);not null;unique_index:user_union_type;comment: '第三方平台的open_id'" json:"open_id"`
	Type       int       `gorm:"type:tinyint(2);default:0;unique_index:user_union_type;comment: '第三方平台的类型'" json:"type"`
	CreateTime time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment: '创建时间'" json:"create_time"`
	UpdateTime time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment: '更新时间'" json:"update_time"`
	Status     int       `gorm:"type:tinyint(2);default:1;comment:'状态:0、解绑 1、正常'" json:"status"`
	UserName   string    `gorm:"type:varchar(64);comment:'三方账号用户名'" json:"user_name"`
	Detail     string    `gorm:"type:text;comment:'三方信息详情'" json:"detail"`
}

func (UserBindThird) TableName() string {
	return "user_bind_third"
}

func (UserBindThird) Comment() string {
	return "第三方账号绑定表"
}

func GetOneBindThird(db *gorm.DB, args map[string]interface{}) (*UserBindThird, error) {
	var userBindThird UserBindThird
	err := db.Where(args).First(&userBindThird).Error
	return &userBindThird, err
}

func GetAllBindThird(db *gorm.DB, args map[string]interface{}) ([]UserBindThird, error) {
	var userBindThird []UserBindThird
	err := db.Where(args).Find(&userBindThird).Error
	return userBindThird, err
}

type UserBindThirdWithAreaCode struct {
	UserBindThird
	AreaCode string `json:"area_code"`
}

func GetAllBindThirdWithAreaCode(db *gorm.DB, userId int64) []UserBindThirdWithAreaCode {
	var userBindThird []UserBindThirdWithAreaCode
	sql := fmt.Sprintf("select user_bind_third.*, account.area_code from user_bind_third left join account on user_bind_third.union_id = account.union_id where user_bind_third.user_id=%d;", userId)
	db.Raw(sql).Scan(&userBindThird)
	return userBindThird
}

func SaveBindThird(db *gorm.DB, args *UserBindThird) error {
	err := db.Save(args).Error
	return err
}

func CreateBindThird(db *gorm.DB, args *UserBindThird) error {
	err := db.Create(args).Error
	return err
}

func ReleaseAllBindings(db *gorm.DB, userId int) error {
	return db.Model(&UserBindThird{}).Where("user_id = ?", userId).Update("status", 0).Error
}

func UpdateThirdUserName(db *gorm.DB, unionID string, _type int, status int, userName string) error {
	return db.Model(&UserBindThird{}).Where("union_id = ? and type = ? and status = ?",
		unionID, _type, status).Update("user_name", userName).Error
}

func UpdateThirdDetail(db *gorm.DB, unionID string, _type int, status int, detail string) error {
	return db.Model(&UserBindThird{}).Where("union_id = ? and type = ? and status = ?",
		unionID, _type, status).Update("detail", detail).Error
}


func GetUserIdsByBindType(db *gorm.DB, bindType int, status int) []int {
	userIds := make([]int, 0)
	db.Model(&UserBindThird{}).Select([]string{"user_id"}).Where("type = ? and status = ?", bindType, status).Find(&userIds)
	return userIds
}
