package models

import (
	"gorm.io/gorm"
	"time"
)

type WebPayWhiteList struct {
	ID                int       `gorm:"primary_key;type:int(11) unsigned auto_increment" json:"id"`
	RoleId            string    `gorm:"index;type:varchar(32);comment: 角色ID" json:"role_id"`
	ServerId          string    `gorm:"type varchar(16);comment:'服务器id'" json:"server_id"`
	SandboxPayEnabled bool      `gorm:"type:tinyint(1);DEFAULT:0;comment:'是否开启沙盒支付'" json:"sandbox_pay_enabled"`
	PreviewEnabled    bool      `gorm:"type:tinyint(1);DEFAULT:0;comment:'是否用于网页支付'" json:"preview_enabled"`
	CreateTime        time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'创建时间'" json:"create_time"`
	UpdateTime        time.Time `gorm:"type:datetime;DEFAULT:CURRENT_TIMESTAMP;comment:'更新时间'" json:"update_time"`
}

func (WebPayWhiteList) TableName() string {
	return "web_pay_whitelist"
}

func (WebPayWhiteList) Comment() string {
	return "用户白名单表"
}

func SaveWebPayWhiteList(db *gorm.DB, args *WebPayWhiteList) error {
	err := db.Save(args).Error
	return err
}

func CreateWebPayWhiteList(db *gorm.DB, args *WebPayWhiteList) error {
	err := db.Create(args).Error
	return err
}

func DeleteWebPayWhiteList(db *gorm.DB, args *WebPayWhiteList) error {
	err := db.Delete(args).Error
	return err
}

func GetWebPayWhiteListById(db *gorm.DB, _id int) (*WebPayWhiteList, error) {
	var whiteList WebPayWhiteList
	err := db.Where("id = ?", _id).First(&whiteList).Error
	return &whiteList, err
}
func GetWebPayWhiteListByRoleId(db *gorm.DB, roleId string, serverId string) (*WebPayWhiteList, error) {
	var whiteList WebPayWhiteList
	err := db.Where("role_id = ?", roleId).Where("server_id = ?", serverId).First(&whiteList).Error
	return &whiteList, err
}

func GetWebPayWhiteListByArgs(
	db *gorm.DB,
	args map[string]interface{},
) ([]*WebPayWhiteList, int64, error) {
	var whiteList []*WebPayWhiteList
	var total int64
	query := db.Model(&WebPayWhiteList{}).Where(args)
	query.Count(&total)
	err := query.Order("create_time desc").Find(&whiteList).Error
	return whiteList, total, err
}
