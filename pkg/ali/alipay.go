package ali

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	"zeus/schema"
	"zeus/services"

	"github.com/avast/retry-go/v4"
	"github.com/gin-gonic/gin"
	"github.com/smartwalle/alipay/v3"
)

func getClient(ctx context.Context, appid string) (*alipay.Client, error) {
	var config payment.AliPay
	converter.QueryByAppid(&config, appid)
	if *config.AliPubKey == "" || *config.AliPrivateKey == "" || *config.AliAppId == "" {
		return nil, errors.New("missing private key or aliPubKey")
	}
	var aliClient *alipay.Client
	var err error
	if aliClient, err = alipay.New(*config.AliAppId, *config.AliPrivateKey, true); err != nil {
		log.Error(ctx, "init alipay fail", "error", err)
		return nil, err
	}

	err = aliClient.LoadAliPayPublicKey(*config.AliPubKey)
	if err != nil {
		log.Error(ctx, "load aliPubKey fail", "error", err)
		return nil, err
	}
	return aliClient, nil
}

func AliPay(c *gin.Context, order schema.AliExchangeForm) (*models.Order, string, error) {
	var subject string
	if order.ProductName != "" {
		subject = order.ProductName
	} else {
		subject = order.ProductID
	}

	p := alipay.TradeAppPay{}
	host := c.Request.Host
	price := order.Price
	p.NotifyURL = fmt.Sprintf("https://%s/callback/app/alipay/notify/%s", host, order.AppID)
	p.ReturnURL = ""
	p.Subject = subject
	p.GoodsType = "0"
	p.OutTradeNo = services.GenerateOrderID(constants.PayWayAli, constants.OrderIDTokenSize)
	p.TotalAmount = strconv.FormatFloat(price/100, 'f', 2, 64)
	p.ProductCode = order.ProductID

	aliClient, err := getClient(c, order.AppID)
	if err != nil {
		log.Error(c.Request.Context(), "支付宝参数配置不完整")
		return nil, "", err
	}
	var payURL string

	err = retry.Do(
		func() error {
			var err error
			payURL, err = aliClient.TradeAppPay(p)
			if err != nil {
				return err
			}
			return nil
		},
		retry.Attempts(3),
	)
	if err != nil {
		log.Error(c.Request.Context(), err.Error())
		return nil, "", err
	}

	aliOrder := formatAliOrder(p, order)
	return &aliOrder, payURL, nil
}

func formatAliOrder(p alipay.TradeAppPay, orderReq schema.AliExchangeForm) models.Order {
	var order models.Order
	order.ProductId = orderReq.ProductID
	order.UserId = orderReq.UserID

	price := orderReq.Price
	order.Price = price
	order.PayWay = constants.PayWayAli
	order.OrderId = p.OutTradeNo

	return order
}

func QueryAliPayTrade(ctx context.Context, order models.Order, appid string) (*alipay.TradeQueryRsp, error) {
	aliClient, err := getClient(ctx, appid)
	if err != nil {
		return nil, err
	}
	p := alipay.TradeQuery{}
	p.OutTradeNo = order.OrderId
	var rsp *alipay.TradeQueryRsp
	err = retry.Do(
		func() error {
			var err error
			rsp, err = aliClient.TradeQuery(p)
			return err
		},
		retry.Attempts(3),
	)
	if err != nil {
		return nil, err
	}

	return rsp, nil
}
