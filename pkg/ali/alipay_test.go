package ali

import (
	"context"
	"net/http"
	"reflect"
	"testing"

	"zeus/common/constants"
	"zeus/schema"
	"zeus/services"
	"zeus/services/settings/converter"
	settings_schema "zeus/services/settings/field"
	"zeus/services/settings/payment"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	"github.com/smartwalle/alipay/v3"
	. "github.com/smartystreets/goconvey/convey"
)

func TestAligetClient(t *testing.T) {
	ApplyFunc(alipay.New, func(_, _ string, _ bool, opts ...alipay.OptionFunc) (client *alipay.Client, err error) {
		return &alipay.Client{}, nil
	})

	apply1 := ApplyFunc(alipay.New, func(string, string, bool, ...alipay.OptionFunc) (*alipay.Client, error) {
		return &alipay.Client{}, nil
	})
	defer apply1.Reset()

	aliClient := &alipay.Client{}
	ApplyMethod(reflect.TypeOf(aliClient), "LoadAliPayPublicKey", func(_ *alipay.Client, _ string) error {
		return nil
	})

	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config settings_schema.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentAliPay":
			aliInfo := config.(*payment.AliPay)
			status := 1
			aliInfo.OpenStatus = &status
			aliPubKey := "test_ali_pub_key"
			aliInfo.AliPubKey = &aliPubKey
			aliPrivateKey := "test_ali_private_key"
			aliInfo.AliPrivateKey = &aliPrivateKey
			aliappId := "test_ali_app_id"
			aliInfo.AliAppId = &aliappId
		case "Payment":
			payConfig := config.(*payment.Payment)
			status := constants.IsRechargeOpen
			payConfig.IsRechargeOpen = &status
		}
	})
	defer QueryMockFunc.Reset()

	Convey("TestAliPay", t, func() {
		Convey("one func for succ", func() {
			_, err := getClient(context.Background(), "test_appid")

			So(err, ShouldBeNil)
		})
	})
}

func TestAli(t *testing.T) {
	ApplyFunc(alipay.New, func(_, _ string, _ bool, opts ...alipay.OptionFunc) (client *alipay.Client, err error) {
		return &alipay.Client{}, nil
	})

	aliClient := &alipay.Client{}
	ApplyMethod(reflect.TypeOf(aliClient), "LoadAliPayPublicKey", func(_ *alipay.Client, _ string) error {
		return nil
	})
	ApplyMethod(reflect.TypeOf(aliClient), "TradeAppPay", func(_ *alipay.Client, _ alipay.TradeAppPay) (result string, err error) {
		return "http://127.0.0.1/test", nil
	})

	ApplyFunc(getClient, func(ctx context.Context, appid string) (*alipay.Client, error) {
		return &alipay.Client{}, nil
	})

	Convey("TestAliPay", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(services.GenerateOrderID, func(orderType int, tokenNumber int, extra ...string) string {
				return "test_order_id"
			})

			order := schema.AliExchangeForm{}
			c := &gin.Context{}
			var r *http.Request
			r = new(http.Request)
			r.Host = ""
			c.Request = r
			_, output, _ := AliPay(c, order)

			So(output, ShouldEqual, "http://127.0.0.1/test")
		})
	})
}
