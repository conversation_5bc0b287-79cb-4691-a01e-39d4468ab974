package anti_addiction

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/url"

	"zeus/pkg/http"
)

const (
	AilYunSuccessCode = "0" // 请求成功

	Agreement = "1" // 一致

	AilYunIdentifyCheckUrl = "https://eid.shumaidata.com/eid/check"
)

type AilYun struct {
	appCode string
}

func NewAilYun(appCode string) *AilYun {
	return &AilYun{
		appCode: appCode,
	}
}

func (y *AilYun) Check(ctx context.Context, name, idCard string) (bool, error) {
	values := url.Values{}
	values.Set("name", name)
	values.Set("idcard", idCard)

	checkUrl := AilYunIdentifyCheckUrl + "?" + values.Encode()
	headers := map[string]string{
		"Content-Type":  "application/x-www-form-urlencoded; charset=UTF-8",
		"Authorization": fmt.Sprintf("APPCODE %s", y.appCode),
	}

	resp, err := http.PostWithTracer(ctx, checkUrl, headers, []byte{})
	if err != nil {
		return false, err
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, err
	}

	var data ailResponse
	_ = json.Unmarshal(body, &data)

	if data.Code != AilYunSuccessCode {
		return false, fmt.Errorf("aliyun anti addiction check failed, with code: %s, message: %s", data.Code, data.Message)
	}

	if data.Result.Res != Agreement {
		return false, nil
	}

	return true, nil
}

type ailResponse struct {
	Code    string    `json:"code"`
	Message string    `json:"message"`
	Result  ailResult `json:"result"`
}

type ailResult struct {
	Name        string `json:"name"`
	IdCard      string `json:"idcard"`
	Res         string `json:"res"`
	Description string `json:"description"`
	Sex         string `json:"sex"`
	Birthday    string `json:"birthday"`
	Address     string `json:"address"`
}
