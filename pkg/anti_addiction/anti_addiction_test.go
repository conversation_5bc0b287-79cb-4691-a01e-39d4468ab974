package anti_addiction

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"
	"testing"

	httpclient "zeus/pkg/http"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestAntiAddicton(t *testing.T) {
	<PERSON>vey("实名认证", t, func() {
		<PERSON><PERSON>("Check", func() {
			ApplyFunc(httpclient.PostWithTracer, func(_ context.Context, _ string, headers map[string]string, _ []byte, _ ...httpclient.Option) (*http.Response, error) {
				httpResp := &http.Response{}
				var resp ailResponse
				resp.Code = "0"
				resp.Result = ailResult{Res: "1"}
				body, _ := json.Marshal(resp)
				httpResp.Body = io.NopCloser(strings.NewReader(string(body)))
				return httpResp, nil
			})

			ID := "50011219980801xxxx"
			Name := "张三"
			ail := NewAilYun("appCode")
			output, _ := ail.Check(context.Background(), ID, Name)
			So(output, ShouldEqual, true)
		})
	})
}
