package anti_addiction

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"sort"
	"time"

	"zeus/pkg/http"
)

const (
	PublicityAuthenticationCheckUrl  = "https://api.wlc.nppa.gov.cn/idcard/authentication/check"
	PublicityAuthenticationQueryUrl  = "http://api2.wlc.nppa.gov.cn/idcard/authentication/query"
	PublicityAuthenticationLogoutUrl = "http://api2.wlc.nppa.gov.cn/behavior/collection/loginout"

	CheckSuccess  = 0
	CheckProgress = 1
	CheckFailed   = 2

	AuthenticatedUser = 0
	TouristUser       = 1
)

type Publicity struct {
	appId     string
	bizId     string
	secretKey string
}

func NewPublicity(appId, bizId, secretKey string) *Publicity {
	return &Publicity{
		appId:     appId,
		bizId:     bizId,
		secretKey: secretKey,
	}
}

func (pub *Publicity) headers(body []byte, args map[string]string) map[string]string {
	headers := map[string]string{
		"appId":      pub.appId,
		"bizId":      pub.bizId,
		"timestamps": fmt.Sprintf("%d", time.Now().UnixMilli()),
	}
	for k, v := range args {
		headers[k] = v
	}

	sign := pub.sign(headers, body)
	headers["sign"] = sign
	headers["Content-Type"] = "application/json; charset=utf-8"
	return headers
}

func (pub *Publicity) Check(ctx context.Context, ai, name, idNum string) (*CheckResult, error) {
	body := map[string]string{
		"ai":    ai,
		"name":  name,
		"idNum": idNum,
	}

	jsonBody, _ := json.Marshal(body)
	reqBody := map[string]string{
		"data": pub.encrypt(jsonBody),
	}
	jsonReqBody, _ := json.Marshal(reqBody)
	resp, err := http.PostWithTracer(ctx, PublicityAuthenticationCheckUrl, pub.headers(jsonReqBody, map[string]string{}), jsonReqBody)
	if err != nil {
		return nil, err
	}
	buff, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var data CheckResponse
	_ = json.Unmarshal(buff, &data)
	if data.ErrCode != 0 {
		return nil, fmt.Errorf("publicity anti addiction check failed, code: %d, message: %s", data.ErrCode, data.ErrMsg)
	}
	return &data.Data.Result, nil
}

func (pub *Publicity) Query(ctx context.Context, ai string) (*CheckResult, error) {
	u := PublicityAuthenticationQueryUrl + "?" + url.Values{"ai": {ai}}.Encode()
	resp, err := http.GetWithTracer(ctx, u, pub.headers([]byte{}, map[string]string{"ai": ai}))
	if err != nil {
		fmt.Println(err)
	}
	buff, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var data CheckResponse
	_ = json.Unmarshal(buff, &data)
	if data.ErrCode != 0 {
		return nil, fmt.Errorf("publicity anti addiction query failed, code: %d, message: %s", data.ErrCode, data.ErrMsg)
	}
	return &data.Data.Result, nil
}

func (pub *Publicity) Logout(ctx context.Context, collections []LogoutCollection) error {
	logoutCollections := LogoutCollections{
		Collections: collections,
	}
	jsonBody, _ := json.Marshal(logoutCollections)
	reqBody := map[string]string{
		"data": pub.encrypt(jsonBody),
	}
	jsonReqBody, _ := json.Marshal(reqBody)

	resp, err := http.PostWithTracer(ctx, PublicityAuthenticationLogoutUrl, pub.headers(jsonReqBody, map[string]string{}), jsonReqBody)
	if err != nil {
		return err
	}
	buff, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	var data BaseResponse
	_ = json.Unmarshal(buff, &data)
	if data.ErrCode != 0 {
		return fmt.Errorf("publicity anti addiction login out failed, code: %d, message: %s", data.ErrCode, data.ErrMsg)
	}
	return err
}

func (pub *Publicity) encrypt(body []byte) string {
	key, _ := hex.DecodeString(pub.secretKey)
	block, _ := aes.NewCipher(key)
	aesGcm, _ := cipher.NewGCM(block)
	nonce := make([]byte, aesGcm.NonceSize())
	_, _ = io.ReadFull(rand.Reader, nonce)

	cipherText := aesGcm.Seal(nonce, nonce, body, nil)
	encoded := base64.StdEncoding.EncodeToString(cipherText)
	return encoded
}

func (pub *Publicity) sign(headers map[string]string, body []byte) string {
	var data string
	var keys []string
	for k := range headers {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		data += k + headers[k]
	}
	data = pub.secretKey + data + string(body)

	h := sha256.New()
	h.Write([]byte(data))
	sum := h.Sum(nil)
	return hex.EncodeToString(sum)
}

type BaseResponse struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

type CheckResult struct {
	Status int    `json:"status"`
	Pi     string `json:"pi"`
}

type CheckData struct {
	Result CheckResult `json:"result"`
}

type CheckResponse struct {
	BaseResponse
	Data CheckData `json:"data"`
}

type LogoutCollections struct {
	Collections []LogoutCollection `json:"collections"`
}

type LogoutCollection struct {
	No int    `json:"no"`
	Si string `json:"si"`
	Bt int    `json:"bt"`
	Ot int64  `json:"ot"`
	Ct int    `json:"ct"`
	Di string `json:"di"`
	Pi string `json:"pi"`
}
