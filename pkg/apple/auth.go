package apple

import (
	"context"
	"fmt"
	"net/http"

	zeushttp "zeus/pkg/http"
)

const appleApi = "https://appleid.apple.com"

type AuthClient struct {
	clientId     string
	clientSecret string
}

func NewAuthClient(iss, bid, kid, secret string) *AuthClient {
	return &AuthClient{
		clientId:     bid,
		clientSecret: GenerateJWTToken(iss, bid, kid, secret, "https://appleid.apple.com"),
	}
}

func (c *AuthClient) RevokeToken(ctx context.Context, token string) (*http.Response, error) {
	payload := []byte(fmt.Sprintf("client_id=%s&client_secret=%s&token=%s&token_type_hint=access_token", c.clientId, c.clientSecret, token))

	headers := map[string]string{
		"content-type": "application/x-www-form-urlencoded",
	}

	u := appleApi + "/auth/revoke"

	return zeushttp.PostWithTracer(ctx, u, headers, payload)
}
