package douyin

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/url"
	"reflect"
	"sort"
	"strconv"
	"time"
	"zeus/pkg/http"
)

type Client struct {
	Appid   string `json:"appid"`
	Secret  string `json:"secret"`
	SignKey string `json:"sign_key"`
}

const (
	TokenBaseUrl          = "https://guard.topjoy.com/douyin/token"
	DouyinBaseUrl         = "https://developer.toutiao.com"
	WalletGamePayPath     = "/api/apps/game/wallet/game_pay"
	GetBalancePath        = "/api/apps/game/wallet/get_balance"
	QueryPayStateBasePath = "/api/apps/game/payment/queryPayState"
)

type AccessTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

type QueryPayStateResponse struct {
	ErrNo   int    `json:"err_no"`
	Message string `json:"message"`
	Data    struct {
		Status string `json:"status"`
	} `json:"data"`
}

type WalletGamePayRequest struct {
	Openid      string `json:"openid"`
	Appid       string `json:"appid"`
	Ts          int64  `json:"ts"`
	ZoneId      string `json:"zone_id"`
	Pf          string `json:"pf"`
	Amt         int64  `json:"amt"`
	BillNo      string `json:"bill_no"`
	AccessToken string `json:"access_token"`
	MpSig       string `json:"mp_sig,omitempty"`
}

type WalletGamePayResponse struct {
	Errcode    int64  `json:"errcode"`
	Errmsg     string `json:"errmsg"`
	BillNo     string `json:"bill_no"`
	Balance    int64  `json:"balance"`
	UsedGenAmt int64  `json:"used_gen_amt"`
}

type GetBalanceResponse struct {
	ErrNo   int    `json:"err_no"`
	Message string `json:"message"`
	Data    struct {
		Status string `json:"status"`
	} `json:"data"`
}

func (c *Client) GetAccessToken(ctx context.Context) (string, error) {
	params := url.Values{}
	params.Set("appid", c.Appid)
	params.Set("secret", c.Secret)
	getTokenUrl := TokenBaseUrl + "?" + params.Encode()
	resp, err := http.GetWithTracer(ctx, getTokenUrl, map[string]string{})
	if err != nil {
		return "", err
	}
	body, _ := io.ReadAll(resp.Body)
	defer resp.Body.Close()
	var token AccessTokenResponse
	_ = json.Unmarshal(body, &token)
	return token.AccessToken, nil
}

func (c *Client) QueryPayState(ctx context.Context, orderno string) (string, error) {
	token, err := c.GetAccessToken(ctx)
	if err != nil {
		return "", err
	}

	params := url.Values{}
	params.Set("access_token", token)
	params.Set("orderno", orderno)

	queryPayStateUrl := DouyinBaseUrl + QueryPayStateBasePath + "/" + params.Encode()
	resp, err := http.GetWithTracer(ctx, queryPayStateUrl, map[string]string{})
	if err != nil {
		return "", err
	}
	body, _ := io.ReadAll(resp.Body)
	defer resp.Body.Close()
	var queryPayStateResponse QueryPayStateResponse
	_ = json.Unmarshal(body, &queryPayStateResponse)

	if queryPayStateResponse.ErrNo != 0 {
		return "", errors.New(queryPayStateResponse.Message)
	}
	return queryPayStateResponse.Data.Status, nil
}

func (c *Client) GetBalance(ctx context.Context, openid string, zoneId string) (string, error) {
	accessToken, _ := c.GetAccessToken(ctx)
	requestBody := WalletGamePayRequest{
		Openid:      openid,
		Appid:       c.Appid,
		ZoneId:      zoneId,
		AccessToken: accessToken,
		Ts:          time.Now().Unix(),
		Pf:          "android",
	}

	requestBody.MpSig = requestBody.Sign(GetBalancePath, "POST", c.SignKey)
	jsonData, _ := json.Marshal(requestBody)

	resp, err := http.PostWithTracer(ctx, fmt.Sprintf("%s%s", DouyinBaseUrl, GetBalancePath), map[string]string{}, jsonData)
	if err != nil {
		return "", err
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	getBalanceResponse := GetBalanceResponse{}
	err = json.Unmarshal(body, &getBalanceResponse)
	if err != nil {
		return "", err
	}

	if getBalanceResponse.ErrNo != 0 {
		return "", errors.New(getBalanceResponse.Message)
	}

	return getBalanceResponse.Data.Status, nil
}

func (c *Client) WalletGamePay(ctx context.Context, openid string, zoneId string, amt int64, billNo string) (string, error) {
	accessToken, _ := c.GetAccessToken(ctx)
	requestBody := WalletGamePayRequest{
		Openid:      openid,
		Appid:       c.Appid,
		Amt:         amt,
		ZoneId:      zoneId,
		AccessToken: accessToken,
		BillNo:      billNo,
		Ts:          time.Now().Unix(),
		Pf:          "android",
	}

	requestBody.MpSig = requestBody.Sign(WalletGamePayPath, "POST", c.SignKey)
	jsonData, _ := json.Marshal(requestBody)

	resp, err := http.PostWithTracer(ctx, fmt.Sprintf("%s%s", DouyinBaseUrl, WalletGamePayPath), map[string]string{}, jsonData)
	if err != nil {
		return "", err
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	walletGamePayResponse := WalletGamePayResponse{}
	err = json.Unmarshal(body, &walletGamePayResponse)
	if err != nil {
		return "", err
	}

	if walletGamePayResponse.Errcode != 0 {
		return "", errors.New(walletGamePayResponse.Errmsg)
	}

	return walletGamePayResponse.BillNo, nil
}

func (r *WalletGamePayRequest) Sign(url string, method string, key string) string {
	params := make(map[string]any)
	temp, _ := json.Marshal(r)
	json.Unmarshal(temp, &params)
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	dataParams := ""
	for _, k := range keys {
		switch v := params[k].(type) {
		case int:
			dataParams += k + "=" + strconv.Itoa(v) + "&"
		case int64:
			dataParams += k + "=" + strconv.FormatInt(v, 10) + "&" // 处理 int64 类型
		case float64:
			dataParams += k + "=" + strconv.FormatFloat(v, 'f', -1, 64) + "&" // 处理 float64 类型
		case string:
			dataParams += k + "=" + v + "&"
		default:
			fmt.Println(reflect.TypeOf(v).String())
		}
	}

	dataParams += fmt.Sprintf("org_loc=%s&method=%s", url, method)
	h := hmac.New(sha256.New, []byte(key))
	h.Write([]byte(dataParams))
	return hex.EncodeToString(h.Sum(nil))
}
