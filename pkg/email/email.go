package email

import (
	"crypto/tls"
	"fmt"
	"log"
	"net"
	"net/smtp"
	"net/textproto"
	"strings"

	"github.com/jordan-wright/email"
)

type Client struct {
	User     string
	Password string
	Host     string
	Sender   string
}

type Option func(*email.Email)

func AddCc(recipients []string) Option {
	return func(content *email.Email) {
		content.Cc = recipients
	}
}

func AddBcc(recipients []string) Option {
	return func(content *email.Email) {
		content.Bcc = recipients
	}
}

func Dial(addr string) (*smtp.Client, error) {
	conn, err := tls.Dial("tcp", addr, nil)
	if err != nil {
		log.Println("Dialing Error:", err)
		return nil, err
	}
	host, _, _ := net.SplitHostPort(addr)
	return smtp.NewClient(conn, host)
}

func SendMailUsingTLS(addr string, auth smtp.Auth, from string, to []string, msg []byte) (err error) {
	c, err := Dial(addr)
	if err != nil {
		log.Println("Create smpt client error:", err)
		return err
	}
	defer c.Close()
	if auth != nil {
		if ok, _ := c.Extension("AUTH"); ok {
			if err = c.Auth(auth); err != nil {
				log.Println("Error during AUTH", err)
				return err
			}
		}
	}
	if err = c.Mail(from); err != nil {
		return err
	}
	for _, addr := range to {
		if err = c.Rcpt(addr); err != nil {
			return err
		}
	}
	w, err := c.Data()
	if err != nil {
		return err
	}
	_, err = w.Write(msg)
	if err != nil {
		return err
	}
	err = w.Close()
	if err != nil {
		return err
	}
	return c.Quit()
}

func NewClient(user, sender, password, host string) *Client {
	return &Client{
		User:     user,
		Sender:   sender,
		Password: password,
		Host:     host,
	}
}

func (c *Client) SendMessage(subject, body string, to []string, options ...Option) error {
	e := email.Email{
		From:    fmt.Sprintf("%s <%s>", c.Sender, c.User),
		To:      to,
		Subject: subject,
		HTML:    []byte(body),
		Headers: textproto.MIMEHeader{},
	}
	for _, option := range options {
		option(&e)
	}

	hp := strings.Split(c.Host, ":")
	auth := smtp.PlainAuth("", c.User, c.Password, hp[0])
	raw, _ := e.Bytes()
	err := SendMailUsingTLS(
		c.Host,
		auth,
		c.User,
		to,
		raw,
	)
	return err
}
