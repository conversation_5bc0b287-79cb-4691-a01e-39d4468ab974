package email

import (
	"fmt"
	"net/smtp"
	"testing"

	. "github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
)

func TestSend(t *testing.T) {
	template := "<html><body><p>您的验证码为：%s，有效期30分钟</p></body></html>"
	subject := "账号验证码"
	user := "<EMAIL>"
	password := "xxx6EypyqAGcLaxxx"
	host := "xxxpdm.aliyun.com:80"
	sender := "TopJoy Global Games"

	client := NewClient(user, sender, password, host)
	func1 := ApplyFunc(SendMailUsingTLS, func(_ string, _ smtp.Auth, _ string, _ []string, _ []byte) error {
		return nil
	})
	defer func1.Reset()

	err := client.SendMessage(subject, fmt.Sprintf(template, "sssqqq"), []string{"<EMAIL>"}, AddCc([]string{"<EMAIL>"}), AddBcc([]string{"<EMAIL>"}))
	assert.Equal(t, nil, err)
}
