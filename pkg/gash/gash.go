package gash

import (
	"bytes"
	"crypto/cipher"
	"crypto/des"
	"crypto/sha1"
	"encoding/base64"
	"encoding/xml"
	"fmt"
	"io"
)

const (
	BaseUrl     = "https://api.eg.gashplus.com"
	BaseUrlTest = "https://stage-api.eg.gashplus.com"

	PayPath            = "/CP_Module/order.aspx"
	PaymentConfirmPath = "/CP_Module/settle.asmx?WSDL"
	checkOrderPath     = "/CP_Module/checkorder.asmx?WSDL"

	MsgTypeOrderRequest  = "0100"
	MsgTypeOrderResponse = "0110"

	MsgTypeRequestFundsReq  = "0500"
	MsgTypeRequestFundsResp = "0510"

	PCodeOrder     = "300000"
	PCodeFindOrder = "200000"

	DesignatedPaymentChannels  = "M"
	UnspecifiedPaymentChannels = "E"

	ReqSuccess   = "0000"
	RepeatSettle = "2002"
)

type Order struct {
	XMLName       xml.Name `xml:"TRANS"`
	MsgType       string   `xml:"MSG_TYPE"`     // 交易通信代码例如：MsgTypeOrderRequest
	PCode         string   `xml:"PCODE"`        // 交易处理代理：PCodeOrder
	ERQC          string   `xml:"ERQC"`         // 用于校验
	CID           string   `xml:"CID"`          // 商家服务代码
	COID          string   `xml:"COID"`         // 订单编号
	CUID          string   `xml:"CUID"`         // 币种
	Amount        float64  `xml:"AMOUNT"`       // 金额
	ProductName   string   `xml:"PRODUCT_NAME"` // 商品名
	ReturnURL     Cdata    `xml:"RETURN_URL"`   // 回调地址
	OrderType     string   `xml:"ORDER_TYPE"`   // 订单类型例如： UnspecifiedPaymentChannels
	UserAccountID string   `xml:"USER_ACCTID"`  // 角色ID
}

type PaymentConfirmReq struct {
	XMLName xml.Name `xml:"TRANS"`
	MsgType string   `xml:"MSG_TYPE"`
	PCode   string   `xml:"PCODE"`
	ERQC    string   `xml:"ERQC"`
	CID     string   `xml:"CID"`
	COID    string   `xml:"COID"`
	CUID    string   `xml:"CUID"`
	PAID    string   `xml:"PAID"`
	Amount  float64  `xml:"AMOUNT"`
}

type PaymentConfirmResp struct {
	XMLName     xml.Name `xml:"TRANS"`
	MsgType     string   `xml:"MSG_TYPE"`
	PCode       string   `xml:"PCODE"`
	ERPC        string   `xml:"ERPC"`
	CID         string   `xml:"CID"`
	COID        string   `xml:"COID"`
	CUID        string   `xml:"CUID"`
	PAID        string   `xml:"PAID"`
	Amount      float64  `xml:"AMOUNT"`
	RRN         string   `xml:"RRN"`
	OrderType   string   `xml:"ORDER_TYPE"`
	PayStatus   string   `xml:"PAY_STATUS"`
	RCode       string   `xml:"RCODE"`
	RentPhase   int      `xml:"RENT_PHASE"`
	PayRCode    string   `xml:"PAY_RCODE"`
	ErpID       string   `xml:"ERP_ID"`
	MID         string   `xml:"MID"`
	BID         string   `xml:"BID"`
	MEMO        string   `xml:"MEMO"`
	ProductName string   `xml:"PRODUCT_NAME"`
	ProductID   string   `xml:"PRODUCT_ID"`
	UserAcctid  string   `xml:"USER_ACCTID"`
	UserIp      string   `xml:"USER_IP"`
	TXTime      string   `xml:"TXTIME"`
	EXTENSION   string   `xml:"EXTENSION"`
	GpsInfo     string   `xml:"GPS_INFO"`
}

type CheckOrder struct {
	XMLName xml.Name `xml:"TRANS"`
	MsgType string   `xml:"MSG_TYPE"`
	PCode   string   `xml:"PCODE"`
	ERQC    string   `xml:"ERQC"`
	CID     string   `xml:"CID"`
	COID    string   `xml:"COID"`
	CUID    string   `xml:"CUID"`
	Amount  float64  `xml:"AMOUNT"`
}

type XMLTemplate struct {
	Envelope string `xml:"soap:Envelope"`
	Body     struct {
		GetResponseResponse struct {
			GetResponseResult string `xml:"getResponseResult"`
		} `xml:"getResponseResponse"`
	} `xml:"Body"`
}

type Cdata struct {
	Value string `xml:",cdata"`
}

type TripleDES struct {
	key []byte
	iv  []byte
}

func (tdes *TripleDES) Encrypt(plain string) (string, error) {
	block, err := des.NewTripleDESCipher(tdes.key)
	if err != nil {
		return "", err
	}
	input := []byte(plain)
	input = PKCS5Padding(input, block.BlockSize())
	blockMode := cipher.NewCBCEncrypter(block, tdes.iv)
	crypted := make([]byte, len(input))
	blockMode.CryptBlocks(crypted, input)

	b64Crypted := base64.StdEncoding.EncodeToString(crypted)
	hasher := sha1.New()
	hasher.Write([]byte(b64Crypted))
	result := base64.StdEncoding.EncodeToString(hasher.Sum(nil))
	return result, nil
}

func PKCS5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS5UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

type Client struct {
	key      string
	iv       string
	password string
	baseUrl  string
}

func NewClient(key, iv, password string, sandBox bool) *Client {
	var urlPath string
	if sandBox {
		urlPath = BaseUrlTest
	} else {
		urlPath = BaseUrl
	}

	return &Client{
		key:      key,
		iv:       iv,
		password: password,
		baseUrl:  urlPath,
	}
}

func (g *Client) getKey() ([]byte, error) {
	return base64.StdEncoding.DecodeString(g.key)
}

func (g *Client) getIv() ([]byte, error) {
	return base64.StdEncoding.DecodeString(g.iv)
}

func (g *Client) getErqc(data string) string {
	erocData := data + g.password
	key, _ := g.getKey()
	iv, _ := g.getIv()
	t := TripleDES{key: key, iv: iv}
	s, _ := t.Encrypt(erocData)
	return s
}

func (g *Client) getErpc(data string) string {
	key, _ := g.getKey()
	iv, _ := g.getIv()
	t := TripleDES{key: key, iv: iv}
	s, _ := t.Encrypt(data)
	return s
}

func (g *Client) Pay(o *Order) string {
	hash := fmt.Sprintf("%s%s%s%s", o.CID, o.COID, o.CUID, fmt.Sprintf("%014.0f", o.Amount*100))
	o.ERQC = g.getErqc(hash)
	buff, _ := xml.Marshal(o)
	header := []byte(`<?xml version="1.0" ?>`)
	buff = append(header, buff...)
	return base64.StdEncoding.EncodeToString(buff)
}

func (g *Client) PaymentConfirm(req *PaymentConfirmReq) (*PaymentConfirmResp, error) {
	hash := fmt.Sprintf("%s%s%s%s", req.CID, req.COID, req.CUID, fmt.Sprintf("%014.0f", req.Amount*100))
	req.ERQC = g.getErqc(hash)
	buff, _ := xml.Marshal(req)
	header := []byte(`<?xml version="1.0" encoding="utf-8"?>`)
	buff = append(header, buff...)
	data := base64.StdEncoding.EncodeToString(buff)

	urlPath := fmt.Sprintf("%s%s", g.baseUrl, PaymentConfirmPath)

	resp, err := SoapCall(urlPath, data)
	if err != nil {
		return nil, err
	}
	root := XMLResp{}
	decoder := xml.NewDecoder(resp.Body)
	decoder.Token()
	err = decoder.Decode(&root)
	respData := root.Body.GetResponseResponse.GetResponseResult

	decodedBytes, err := base64.StdEncoding.DecodeString(respData)
	paymentConfirmResp := PaymentConfirmResp{}
	err = xml.Unmarshal(decodedBytes, &paymentConfirmResp)
	return &paymentConfirmResp, err
}

func (g *Client) CheckNotifyInfo(n *PaymentConfirmResp) bool {
	hash := fmt.Sprintf("%s%s%s%s%s%s", n.CID, n.COID, n.RRN, n.CUID,
		fmt.Sprintf("%014.0f", n.Amount*100), n.RCode)
	s := g.getErpc(hash)
	if s != n.ERPC {
		return false
	}
	return true
}

func (g *Client) CheckOrder(cID, cOID, cUID string, amount float64) (*PaymentConfirmResp, error) {
	req := CheckOrder{
		MsgType: MsgTypeOrderRequest,
		PCode:   PCodeFindOrder,
		CID:     cID,
		COID:    cOID,
		CUID:    cUID,
		Amount:  amount,
	}
	hash := fmt.Sprintf("%s%s%s%s", cID, cOID, cUID, fmt.Sprintf("%014.0f", amount*100))
	req.ERQC = g.getErqc(hash)
	buff, _ := xml.Marshal(req)
	header := []byte(`<?xml version="1.0" encoding="utf-8"?>`)
	buff = append(header, buff...)
	data := base64.StdEncoding.EncodeToString(buff)

	urlPath := fmt.Sprintf("%s%s", g.baseUrl, checkOrderPath)

	resp, err := SoapCall(urlPath, data)
	if err != nil {
		return nil, err
	}

	body, _ := io.ReadAll(resp.Body)
	root := XMLTemplate{}
	decoder := xml.NewDecoder(bytes.NewReader(body))
	_, _ = decoder.Token()
	_ = decoder.Decode(&root)
	respData := root.Body.GetResponseResponse.GetResponseResult
	decodedBytes, _ := base64.StdEncoding.DecodeString(respData)
	paymentConfirmResp := PaymentConfirmResp{}
	err = xml.Unmarshal(decodedBytes, &paymentConfirmResp)
	return &paymentConfirmResp, err
}
