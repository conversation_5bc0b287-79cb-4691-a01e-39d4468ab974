package gash

import (
	"bytes"
	"crypto/tls"
	"encoding/xml"
	"net/http"
)

type SoapHeader struct {
	XMLName xml.Name `xml:"x:Header"`
}

type GetResponse struct {
	XMLName xml.Name `xml:"getResponse"`
	Attr    string   `xml:"xmlns,attr"`
	Data    string   `xml:"data"`
}

type SoapBody struct {
	XMLName xml.Name `xml:"soap:Body"`
	Request GetResponse
}

type SoapRoot struct {
	XMLName xml.Name `xml:"soap:Envelope"`
	Xsi     string   `xml:"xmlns:xsi,attr"`
	Xsd     string   `xml:"xmlns:xsd,attr"`
	Soap    string   `xml:"xmlns:soap,attr"`
	Body    SoapBody
}

type XMLResp struct {
	Envelope string `xml:"soap:Envelope"`
	Body     struct {
		GetResponseResponse struct {
			GetResponseResult string `xml:"getResponseResult"`
		} `xml:"getResponseResponse"`
	} `xml:"Body"`
}

func SoapCall(url, data string) (*http.Response, error) {
	root := SoapRoot{}

	root.Soap = "http://schemas.xmlsoap.org/soap/envelope/"
	root.Xsi = "http://www.w3.org/2001/XMLSchema-instance"
	root.Xsd = "http://www.w3.org/2001/XMLSchema"
	root.Body = SoapBody{}

	root.Body.Request.Attr = "http://egsys.org/"
	root.Body.Request.Data = data

	buff, _ := xml.Marshal(root)
	header := []byte(`<?xml version="1.0" encoding="utf-8"?>`)
	buff = append(header, buff...)

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(buff))
	req.Header.Set("Content-Type", "text/xml; charset=utf-8")
	req.Header.Set("SOAPAction", "http://egsys.org/getResponse")
	return client.Do(req)
}
