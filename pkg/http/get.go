package http

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"time"

	"github.com/avast/retry-go"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	oteltrace "go.opentelemetry.io/otel/trace"
)

var tracer = otel.Tracer("gin-server")

func GetWithRetry(ctx context.Context, url string, headers map[string]string) (resp *http.Response, err error) {
	err = retry.Do(
		func() error {
			client := http.Client{}
			req, _ := http.NewRequest("GET", url, nil)

			if len(headers) == 0 {
				headers = map[string]string{"Content-Type": "application/json"}
			}

			for k, v := range headers {
				req.Header.Set(k, v)
			}
			resp, err = client.Do(req)
			return err
		},
		retry.DelayType(func(n uint, err error, config *retry.Config) time.Duration {
			return retry.BackOffDelay(n, err, config)
		}),
		retry.Attempts(3),
		retry.Delay(1*time.Second),
	)
	return
}

func GetWithTracer(ctx context.Context, url string, headers map[string]string) (resp *http.Response, err error) {
	tracer := otel.Tracer("HTTPClient")
	_, span := tracer.Start(ctx, url, oteltrace.WithAttributes(attribute.String("method", "GET")))
	defer span.End()
	if tracer == nil {
		return GetWithRetry(ctx, url, headers)
	}
	resp, err = GetWithRetry(ctx, url, headers)

	if err != nil {
		span.SetAttributes(attribute.Int("status_code", 400))
	} else {
		span.SetAttributes(attribute.Int("status_code", resp.StatusCode))
	}
	return
}

func MarshalWithTracer(ctx context.Context, url string, headers map[string]string, target any) error {
	resp, err := GetWithTracer(ctx, url, headers)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	err = json.Unmarshal(body, &target)
	if err != nil {
		return err
	}
	return nil
}
