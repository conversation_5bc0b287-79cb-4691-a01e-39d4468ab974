package http

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/SkyAPM/go2sky"
	"github.com/avast/retry-go"
)

const componentIDGOHttpClient = 5005

func Get(u string) (resp *http.Response, err error) {
	err = retry.Do(
		func() error {
			resp, err = http.Get(u)
			return err
		},
		retry.DelayType(func(n uint, err error, config *retry.Config) time.Duration {
			// default is backoffdelay
			return retry.BackOffDelay(n, err, config)
		}),
		retry.Attempts(3),
		retry.Delay(1*time.Second),
	)
	return
}

func Post(u string, paramsData any) (resp *http.Response, err error) {
	bytesData, _ := json.Marshal(paramsData)
	resp, err = http.Post(u, "application/json;charset=utf-8", bytes.NewBuffer(bytesData))
	return resp, err
}

func PostForm(ctx context.Context, u string, paramsData url.Values) (resp *http.Response, err error) {
	tracer := go2sky.GetGlobalTracer()
	span, _, err := tracer.CreateLocalSpan(ctx)
	span.SetOperationName(u)

	defer span.End()
	span.SetComponent(componentIDGOHttpClient)

	span.Tag(go2sky.TagHTTPMethod, "POST")
	span.Tag(go2sky.TagURL, u)
	span.SetSpanLayer(3)

	body := bytes.NewBufferString(paramsData.Encode())

	resp, err = http.Post(u, "application/x-www-form-urlencoded", body)
	if err != nil {
		span.Error(time.Now(), err.Error())
		return
	}
	span.Tag(go2sky.TagStatusCode, strconv.Itoa(resp.StatusCode))
	if resp.StatusCode >= http.StatusBadRequest {
		span.Error(time.Now(), "Errors on handling client")
	}
	return resp, nil
}
