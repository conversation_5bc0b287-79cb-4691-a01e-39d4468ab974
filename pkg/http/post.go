package http

import (
	"bytes"
	"context"
	"net/http"
	"time"

	"github.com/avast/retry-go"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	oteltrace "go.opentelemetry.io/otel/trace"
)

type Option func(*http.Client)

func WithTimeout(timeOut time.Duration) Option {
	return func(client *http.Client) {
		client.Timeout = timeOut
	}
}

func PostWithRetry(ctx context.Context, url string, headers map[string]string, body []byte, options ...Option) (resp *http.Response, err error) {
	err = retry.Do(
		func() error {
			client := http.Client{}
			for _, o := range options {
				o(&client)
			}

			req, _ := http.NewRequest("POST", url, bytes.NewBuffer(body))

			if len(headers) == 0 {
				headers = map[string]string{"Content-Type": "application/json"}
			}

			for k, v := range headers {
				req.Header.Set(k, v)
			}

			resp, err = client.Do(req)
			return err
		},
		retry.DelayType(func(n uint, err error, config *retry.Config) time.Duration {
			return retry.BackOffDelay(n, err, config)
		}),
		retry.Attempts(3),
		retry.Delay(1*time.Second),
	)
	return
}

func PostWithTracer(ctx context.Context, url string, headers map[string]string, body []byte, options ...Option) (resp *http.Response, err error) {
	tracer := otel.Tracer("")
	_, span := tracer.Start(ctx, url, oteltrace.WithAttributes(attribute.String("method", "POST")))
	defer span.End()

	resp, err = PostWithRetry(ctx, url, headers, body, options...)
	if err != nil {
		span.SetAttributes(attribute.Int("status_code", 400))
	} else {
		span.SetAttributes(attribute.Int("status_code", resp.StatusCode))
	}
	return
}
