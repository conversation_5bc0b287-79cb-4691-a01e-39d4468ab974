package idno

import (
	"errors"
	"regexp"
	"strconv"
	"time"
)

// 权重
var idNoWeightArray = []int{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2}

// 身份证号码
var idNoCheckCode = "10X98765432"

// 身份证号码正则匹配
var idCardNoRegexpPattern = "^([1-9]\\d{7}((0\\d)|(1[0-2]))(([012]\\d)|3[0-1])\\d{3})|([1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([012]\\d)|3[0-1])((\\d{4})|\\d{3}[Xx]))$"

// IdCardNoInfo 身份证信息
type IdCardNoInfo struct {
	IdCardNo    string // 身份证号码
	AreaCode    string // 地区编号
	AreaName    string // 地区名称
	BirthDayYMD string // 年月日生日，20060102
	Age         int    // 年龄
	Sex         int    // 性别，女0，男1
}

// Validate18IdCardNo 校验18位身份证号码有效性
func Validate18IdCardNo(idNo string) bool {
	if len(idNo) != 18 {
		return false
	}
	isMatch, _ := regexp.MatchString(idCardNoRegexpPattern, idNo)
	if !isMatch {
		return false
	}
	return getCheckDigit(idNo) == idNo[17:18]
}

// Parse18IdCardNoInfo 获取18位身份证号码信息
func Parse18IdCardNoInfo(idNo string) (*IdCardNoInfo, error) {
	isIdCardNo := Validate18IdCardNo(idNo)
	if !isIdCardNo {
		return nil, errors.New("非法的身份证件号")
	}
	return &IdCardNoInfo{
		IdCardNo:    idNo,
		AreaCode:    getAreaCode(idNo),
		AreaName:    getAreaName(idNo),
		BirthDayYMD: getBirthDayYMD(idNo),
		Age:         getAge(idNo),
		Sex:         getSex(idNo),
	}, nil
}

// 获取校验位
func getCheckDigit(idNo string) string {
	data := idNo[0:17]
	s := 0
	for i := range data {
		n, _ := strconv.Atoi(string(data[i]))
		s += n * idNoWeightArray[i]
	}
	y := s % 11
	return idNoCheckCode[y : y+1]
}

func getAreaCode(idNo string) string {
	return idNo[0:6]
}

func getAreaName(idNo string) string {
	return areaNameMap[idNo[0:6]]
}

func getBirthDayYMD(idNo string) string {
	return idNo[6:14]
}

func getAge(idNo string) int {
	return GetAgeByBirthDayYMD(idNo[6:14])
}

func getSex(idNo string) int {
	sex, _ := strconv.Atoi(idNo[16:17])
	return sex % 2
}

func GetAgeByBirthDayYMD(birthDayYMD string) int {
	if len(birthDayYMD) != 8 {
		return 0
	}

	today := time.Now()
	birthDay, err := time.ParseInLocation("20060102", birthDayYMD, time.Local)
	if err != nil {
		return 0
	}

	age := today.Year() - birthDay.Year()
	if time.Date(today.Year(), birthDay.Month(), birthDay.Day(), 0, 0, 0, 0, time.Local).After(time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, time.Local)) {
		age -= 1
	}
	if age <= 0 {
		age = 1
	}

	return age
}
