package idno

import (
	"testing"
	"time"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestIdNo(t *testing.T) {
	Convey("TestIdNo", t, func() {
		Convey("Validate18IdCardNo", func() {
			idNo := "510126197202086449"
			output := Validate18IdCardNo(idNo)
			So(output, ShouldEqual, true)
		})

		<PERSON>vey("GetAgeByBirthDayYMD", func() {
			ApplyFunc(time.Now, func() time.Time {
				return time.Date(2022, 12, 9, 0, 0, 0, 0, time.Local)
			})
			BirthDayYMD := "19980601"
			output := GetAgeByBirthDayYMD(BirthDayYMD)
			expected := 24
			So(output, ShouldEqual, expected)
		})

		<PERSON>vey("Parse18IdCardNoInfo", func() {
			idNo := "510126197202086449"
			output, _ := Parse18IdCardNoInfo(idNo)
			expected := 50
			So(output.Age, ShouldEqual, expected)
		})
	})
}
