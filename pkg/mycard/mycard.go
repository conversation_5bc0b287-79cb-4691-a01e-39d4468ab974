package mycard

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"strings"

	"zeus/common/log"
	"zeus/pkg/http"
)

const (
	baseUrl     = "https://b2b.mycard520.com.tw"
	baseUrlTest = "https://testb2b.mycard520.com.tw"

	payPath            = "/MyBillingPay/v1.2/AuthGlobal"
	verifyPath         = "/MyBillingPay/v1.2/TradeQuery"
	paymentConfirmPath = "/MyBillingPay/v1.2/PaymentConfirm"

	TradeTypeSDK = "1"
	TradeTypeWeb = "2"

	ReqSuccess = "1"

	TradeSuccess = "3"
)

type Order struct {
	FacServiceId string `json:"FacServiceId"` // 厂商服务ID
	FacTradeSeq  string `json:"FacTradeSeq"`  // 交易订单号
	FacGameId    string `json:"FacGameId"`    // 游戏ID
	FacGameName  string `json:"FacGameName"`  // 游戏名称
	TradeType    string `json:"TradeType"`    // 交易类型 1:Android SDK 2:WEB
	ServerId     string `json:"ServerId"`     // 服务器ID
	CustomerId   string `json:"CustomerId"`   // 会员代号 用户在厂商的唯一标识
	PaymentType  string `json:"PaymentType"`  // 付费方式 非必填
	ItemCode     string `json:"ItemCode"`     // 商品代码 非必填
	ProductName  string `json:"ProductName"`  // 商品名称
	Amount       string `json:"Amount"`       // 交易金额 单位元 精确到分
	Currency     string `json:"Currency"`     // 货币 TWD/HKD/USD
	SandBoxMode  string `json:"SandBoxMode"`  // 是否为测试环境 true/false
	FacReturnURL string `json:"FacReturnURL"` // 回调地址
	Hash         string `json:"Hash"`         // 校验码
}

type PayResp struct {
	InGameSaveType string `json:"InGameSaveType"` // 儲值服務的使用者介面
	ReturnCode     string `json:"ReturnCode"`     // 1 为成功， 其他为失败
	ReturnMsg      string `json:"ReturnMsg"`
	AuthCode       string `json:"AuthCode"`       // 校验授权码
	TradeSeq       string `json:"TradeSeq"`       // 交易序号
	TransactionUrl string `json:"TransactionUrl"` // 交易地址
}

type VerifyResp struct {
	ReturnCode    string `json:"ReturnCode"` // 1 为请求成功
	ReturnMsg     string `json:"ReturnMsg"`
	PayResult     string `json:"PayResult"`     // 交易结果。 3 为成功， 0 为失败
	FacTradeSeq   string `json:"FacTradeSeq"`   // 交易订单号
	PaymentType   string `json:"PaymentType"`   // 付费方式 非必填
	Amount        string `json:"Amount"`        // 交易金额 单位元 精确到分
	Currency      string `json:"Currency"`      // 货币 TWD/HKD/USD
	MyCardTradeNo string `json:"MyCardTradeNo"` //
	MyCardType    string `json:"MyCardType"`    //
	PromoCode     string `json:"PromoCode"`     // 活动代码
	SerialId      string `json:"SerialId"`      // 订阅ID
}

type PaymentConfirmResp struct {
	ReturnCode  string `json:"ReturnCode"`
	ReturnMsg   string `json:"ReturnMsg"`
	FacTradeSeq string `json:"FacTradeSeq"` // 交易订单号
	TradeSeq    string `json:"TradeSeq"`    // 交易序号
	SerialId    string `json:"SerialId"`    // 订阅ID
}

type Client struct {
	baseUrl string
	secret  string
}

func NewClient(secret string, sandBoxMode bool) *Client {
	var urlPath string
	if sandBoxMode {
		urlPath = baseUrlTest
	} else {
		urlPath = baseUrl
	}

	return &Client{
		baseUrl: urlPath,
		secret:  secret,
	}
}

func (c *Client) Pay(ctx context.Context, o *Order) (*PayResp, error) {
	hash := o.FacServiceId + o.FacTradeSeq + o.FacGameId + o.FacGameName + o.TradeType + o.ServerId + o.CustomerId +
		o.PaymentType + o.ItemCode + o.ProductName + o.Amount + o.Currency + o.SandBoxMode + o.FacReturnURL + c.secret
	hash = url.QueryEscape(hash)
	hash = strings.ToLower(hash)
	buff := sha256.Sum256([]byte(hash))
	o.Hash = strings.ToLower(fmt.Sprintf("%x", buff))
	formData, _ := json.Marshal(o)

	urlPath := c.baseUrl + payPath
	headers := map[string]string{"Content-Type": "application/json;charset=utf-8"}
	data, err := http.PostWithTracer(ctx, urlPath, headers, formData)
	if err != nil {
		return nil, err
	}

	body, err := io.ReadAll(data.Body)
	if err != nil {
		return nil, err
	}
	resp := PayResp{}
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

func (c *Client) Verify(ctx context.Context, authCode string) (*VerifyResp, error) {
	urlPath := fmt.Sprintf("%s%s?AuthCode=%s", c.baseUrl, verifyPath, authCode)
	data, err := http.GetWithTracer(ctx, urlPath, map[string]string{})
	if err != nil {
		return nil, err
	}
	body, err := io.ReadAll(data.Body)
	if err != nil {
		return nil, err
	}
	resp := VerifyResp{}
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

func (c *Client) PaymentConfirm(ctx context.Context, authCode string) (*PaymentConfirmResp, error) {
	log.Debug(ctx, "开始MyCard请款", "data", authCode)
	urlPath := fmt.Sprintf("%s%s?AuthCode=%s", c.baseUrl, paymentConfirmPath, authCode)
	data, err := http.GetWithTracer(ctx, urlPath, map[string]string{})
	if err != nil {
		return nil, err
	}
	body, err := io.ReadAll(data.Body)
	if err != nil {
		return nil, err
	}
	resp := PaymentConfirmResp{}
	err = json.Unmarshal(body, &resp)
	if err != nil {
		log.Debug(ctx, "请求MyCard请款失败", "error", err.Error())
		return nil, err
	}

	return &resp, nil
}
