package notify

import (
	"bytes"
	"context"
	"encoding/json"

	"zeus/common/log"

	zeusHttp "zeus/pkg/http"
)

type SendMessage interface{}

type DingTalk struct {
	AccessToken string
}

func (d *DingTalk) send(ctx context.Context, message interface{}) (response string, err error) {
	baseUrl := "https://oapi.dingtalk.com/robot/send?access_token=" + d.AccessToken
	formData, _ := json.Marshal(message)
	headers := map[string]string{"Content-Type": "application/json"}
	resp, err := zeusHttp.PostWithTracer(ctx, baseUrl, headers, formData)
	if err != nil {
		log.Error(ctx, err.Error())
		return "", err
	}
	buf := new(bytes.Buffer)
	buf.ReadFrom(resp.Body)
	respStr := buf.String()
	return respStr, err
}

func (d *DingTalk) SendMarkDown(ctx context.Context, title string, text interface{}) (response string, err error) {
	message := make(map[string]interface{})
	message["msgtype"] = "markdown"
	message["markdown"] = map[string]interface{}{
		"title": title,
		"text":  text,
	}
	message["at"] = map[string]interface{}{
		"atMobiles": []string{},
		"isAtAll":   false,
	}
	respStr, err := d.send(ctx, message)
	if err != nil {
		log.Error(ctx, "发送钉钉通知失败", "error", err.Error())
		return "", err
	}
	return respStr, nil
}
