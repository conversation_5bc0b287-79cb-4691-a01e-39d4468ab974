package parrot

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	httpclient "zeus/pkg/http"
)

const (
	apiUrl = "https://parrot.topjoy.com"
	AppID  = "zeus"
	Token  = "fc3d22fc613fd1184896c5ac092112e6"
)

type RequestBody struct {
	AppID   string `json:"app_id"`
	UserID  string `json:"user_id"`
	Target  string `json:"target"`
	Content string `json:"content"`
}

type SupportLanguage struct {
	Language string `json:"language"`
	Name     string `json:"name"`
}

type SupportLanguages []SupportLanguage

type Client struct {
	ApiUrl string
	AppID  string
	Token  string
}

func NewClient() *Client {
	return &Client{
		ApiUrl: apiUrl,
		AppID:  AppID,
		Token:  Token,
	}
}

func (c *Client) sign(req RequestBody, timestamp int64) string {
	hash := md5.New()
	text := fmt.Sprintf("%s:%s-%s-%s-%s-%d", c.<PERSON>, req.AppID, req.UserID, req.Target, req.Content, timestamp)
	hash.Write([]byte(text))
	return fmt.Sprintf("%s:%s", req.AppID, hex.EncodeToString(hash.Sum(nil)))
}

func (c *Client) Translate(ctx context.Context, UserID, Target, Content string) (string, error) {
	requestBody := RequestBody{
		AppID:   c.AppID,
		UserID:  UserID,
		Target:  Target,
		Content: Content,
	}
	jsonData, _ := json.Marshal(requestBody)

	timestamp := time.Now().Unix()
	headers := map[string]string{
		"Signature": c.sign(requestBody, timestamp),
		"timestamp": fmt.Sprintf("%d", timestamp),
	}
	url := fmt.Sprintf("%s/rpc/translate", c.ApiUrl)
	resp, _ := httpclient.PostWithTracer(ctx, url, headers, jsonData)
	body, _ := io.ReadAll(resp.Body)
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return "", errors.New(string(body))
	}

	var content string
	_ = json.Unmarshal(body, &content)
	return content, nil
}

func (c *Client) GetSupportLanguages(ctx context.Context) SupportLanguages {
	url := fmt.Sprintf("%s/rpc/get_support_languages", c.ApiUrl)
	resp, _ := httpclient.GetWithTracer(ctx, url, map[string]string{})
	body, _ := io.ReadAll(resp.Body)
	defer resp.Body.Close()
	var supportLanguages SupportLanguages
	_ = json.Unmarshal(body, &supportLanguages)
	return supportLanguages
}
