package redis

import (
	"context"
	"errors"
	"fmt"
	"log"
	"sync"
	"time"

	"zeus/common/conf"

	"github.com/go-redis/redis/v8"
)

var (
	mutex sync.Mutex
	ctx   = context.Background()
	rdb   *redis.Client
	once  sync.Once
)

func Setup() {
	once.Do(func() {
		rdb = redis.NewClient(&redis.Options{
			Addr:     conf.Conf.Redis.Host,
			Password: conf.Conf.Redis.Password, // no password set
			DB:       conf.Conf.Redis.Database,
		})
		_, err := rdb.Ping(ctx).Result()
		if err != nil {
			panic(err)
		}
	})
	return
}

func Get(key string) string {
	result, err := rdb.Get(ctx, key).Result()
	if err != nil {
		return ""
	}
	return result
}

func Set(key string, data interface{}, duration time.Duration) error {
	err := rdb.Set(ctx, key, data, duration).Err()
	return err
}

func Del(key string) error {
	err := rdb.Del(ctx, key).Err()
	return err
}

func Lock(key string, duration time.Duration) error {
	result, err := rdb.SetNX(ctx, key, 1, duration).Result()
	if err != nil {
		return err
	}
	if !result {
		return errors.New(fmt.Sprintf("key: %s 为已加锁状态", key))
	}

	return err
}

func UnLock(key string) int64 {
	nums, err := rdb.Del(ctx, key).Result()
	if err != nil {
		log.Println(err.Error())
		return 0
	}
	return nums
}

func Exists(key string) bool {
	result, err := rdb.Exists(ctx, key).Result()
	if err != nil {
		return false
	}

	return result > 0
}

func GetRedisKey(strType string, key string) string {
	redisKey := "sdk_" + strType + key
	return redisKey
}
