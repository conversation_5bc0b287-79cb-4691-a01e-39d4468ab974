package sms

import (
	"encoding/json"
	"errors"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/dysmsapi"
)

type Client struct {
	AccessKey string
	SecretKey string
}

func NewClient(accessKey, secretKey string) *Client {
	return &Client{
		AccessKey: accessKey,
		SecretKey: secretKey,
	}
}

func (c *Client) SendMessage(mobile, code, sign, templateId string) error {
	client, _ := dysmsapi.NewClientWithAccessKey("", c.<PERSON>ey, c.<PERSON>)
	request := dysmsapi.CreateSendSmsRequest()
	request.Scheme = "https"

	parameter, _ := json.Marshal(Param{Code: code})

	request.TemplateParam = string(parameter)
	request.PhoneNumbers = mobile
	request.SignName = sign
	request.TemplateCode = templateId

	body, err := client.SendSms(request)
	if err != nil {
		return err
	}
	if body.Code != "OK" {
		return errors.New(body.Message)
	}
	return nil
}

type Param struct {
	Code string `json:"code"`
}
