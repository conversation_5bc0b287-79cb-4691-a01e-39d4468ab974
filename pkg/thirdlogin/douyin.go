package thirdlogin

import (
	"context"
	"fmt"
	"net/url"

	"zeus/pkg/http"
)

const (
	DouyinJSCode2SessionUrl = "https://minigame.zijieapi.com/mgplatform/api/apps/jscode2session"
)

type Douyin struct {
	AppId  string
	Secret string
}

func (d Douyin) MiniGameLogin(ctx context.Context, code string) (map[string]interface{}, error) {
	values := url.Values{}
	values.Set("appid", d.AppId)
	values.Set("secret", d.Secret)
	values.Set("code", code)

	headers := make(map[string]string)
	var data douyinJs2CodeResponse
	err := http.MarshalWithTracer(ctx, fmt.Sprintf("%s?%s", DouyinJSCode2SessionUrl, values.Encode()), headers, &data)
	if err != nil {
		return nil, err
	}

	if data.Errcode != 0 {
		return nil, fmt.Errorf("douyin mini game login failed, errcode: %d, errmsg: %s", data.Errcode, data.Errmsg)
	}

	return map[string]interface{}{
		"openid":      data.Openid,
		"unionid":     data.Unionid,
		"session_key": data.SessionKey,
	}, nil
}

type douyinJs2CodeResponse struct {
	Error           int64  `json:"error"`
	AnonymousOpenid string `json:"anonymous_openid"`
	Errcode         int64  `json:"errcode"`
	Errmsg          string `json:"errmsg"`
	Message         string `json:"message"`
	Openid          string `json:"openid"`
	SessionKey      string `json:"session_key"`
	Unionid         string `json:"unionid"`
}
