package thirdlogin

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/url"
	"strings"

	"zeus/pkg/http"
)

const faceBookApiUrl = "https://graph.facebook.com/debug_token"

type FaceBook struct {
	AppID  string
	Secret string
}

func (fb *FaceBook) getAccessToken() string {
	var buf strings.Builder
	buf.WriteString(fb.AppID)
	buf.WriteByte('|')
	buf.WriteString(fb.Secret)
	return buf.String()
}

func (fb FaceBook) Login(_ context.Context, token string, _ string) (UserInfo, error) {
	values := url.Values{}
	values.Set("input_token", token)
	values.Set("access_token", fb.getAccessToken())
	buf := fmt.Sprintf("%s?%s", faceBookApiUrl, values.Encode())

	resp, err := http.Get(buf)
	if err != nil {
		return UserInfo{}, err
	}

	defer resp.Body.Close()

	// TODO 临时解决方案
	result := make(map[string]interface{})
	body, _ := io.ReadAll(resp.Body)
	_ = json.Unmarshal(body, &result)
	data, ok := result["data"]
	if !ok {
		e := result["error"]
		Message, ok1 := e.(map[string]interface{})
		if !ok1 {
			return UserInfo{}, errors.New("input_token与access_token不一致")
		}
		return UserInfo{}, errors.New(Message["message"].(string))
	}
	user, _ := data.(map[string]interface{})
	return UserInfo{UnionID: user["user_id"].(string)}, nil
}

type FacebookResp struct {
	Data struct {
		UserId string `json:"user_id"`
	} `json:"data"`
	Error struct {
		Message string `json:"message"`
	} `json:"error"`
}
