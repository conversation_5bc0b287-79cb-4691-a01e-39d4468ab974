package thirdlogin

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"testing"

	zeushttp "zeus/pkg/http"

	. "github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
)

func TestFacebookLogin(t *testing.T) {
	mockGetFunc := ApplyFunc(zeushttp.Get, func(u string) (resp *http.Response, err error) {
		resp = &http.Response{}
		body := `{"data": {"user_id": "A1111"}}`
		resp.Body = io.NopCloser(bytes.NewReader([]byte(body)))
		return
	})
	defer mockGetFunc.Reset()

	facebook := FaceBook{"test_appid", "test_secret"}
	info, _ := facebook.Login(context.Background(), "test_token", "")
	expected := "A1111"
	assert.Equal(t, info.UnionID, expected)
}
