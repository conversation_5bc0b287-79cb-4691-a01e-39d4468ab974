package thirdlogin

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/url"

	"zeus/pkg/http"
)

const googleApiUrl = "https://oauth2.googleapis.com/tokeninfo"

type Google struct{}

func (g Google) Login(_ context.Context, token string, _ string) (UserInfo, error) {
	values := url.Values{}
	values.Set("id_token", token)
	u := fmt.Sprintf("%s?%s", googleApiUrl, values.Encode())

	resp, err := http.Get(u)
	if err != nil {
		return UserInfo{}, err
	}

	var data GoogleResp
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return UserInfo{}, err
	}

	err = json.Unmarshal(body, &data)
	if err != nil {
		return UserInfo{}, err
	}

	if data.Error != "" {
		return UserInfo{}, errors.New(data.ErrorDescription)
	}

	return UserInfo{UnionID: data.Sub}, nil
}

type GoogleResp struct {
	Sub              string `json:"sub"`
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
}
