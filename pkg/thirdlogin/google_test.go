package thirdlogin

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"testing"

	zeushttp "zeus/pkg/http"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestGoogleLogin(t *testing.T) {
	Convey("TestGoogleLogin", t, func() {
		<PERSON><PERSON>("Login", func() {
			ApplyFunc(zeushttp.Get, func(u string) (resp *http.Response, err error) {
				resp = &http.Response{}
				body := `{"sub":"A1111"}`
				resp.Body = io.NopCloser(bytes.NewReader([]byte(body)))
				return
			})

			google := Google{}
			output, _ := google.Login(context.Background(), "token", "")
			expected := "A1111"
			So(output.UnionID, ShouldEqual, expected)
		})
	})
}
