package thirdlogin

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
)

const lineApiUrl = "https://api.line.me/v2/profile"

type Line struct{}

func (l Line) Login(_ context.Context, token string, _ string) (UserInfo, error) {
	client := &http.Client{}

	req, err := http.NewRequest("GET", lineApiUrl, nil)
	if err != nil {
		return UserInfo{}, err
	}

	req.Header.Add("Authorization", fmt.Sprintf("Bearer %s", token))
	resp, err := client.Do(req)
	if err != nil {
		return UserInfo{}, err
	}

	var data LineResp
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return UserInfo{}, err
	}

	err = json.Unmarshal(body, &data)
	if err != nil {
		return UserInfo{}, err
	}

	if data.Error != "" {
		return UserInfo{}, errors.New(data.ErrorDescription)
	}

	return UserInfo{UnionID: data.UserId}, nil
}

type LineResp struct {
	UserId           string `json:"userId"`
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
}
