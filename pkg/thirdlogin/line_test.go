package thirdlogin

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"reflect"
	"testing"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestLineLogin(t *testing.T) {
	Convey("TestLineLogin", t, func() {
		<PERSON><PERSON>("Login", func() {
			client := &http.Client{}
			mockGetFunc := ApplyMethod(reflect.TypeOf(client), "Do", func(_ *http.Client, req *http.Request) (*http.Response, error) {
				resp := &http.Response{}
				body := `{"userId": "A1111"}`
				resp.Body = io.NopCloser(bytes.NewReader([]byte(body)))
				return resp, nil
			})
			defer mockGetFunc.Reset()

			line := Line{}
			output, _ := line.Login(context.Background(), "Token", "")
			expected := "A1111"
			So(output.UnionID, ShouldEqual, expected)
		})
	})
}
