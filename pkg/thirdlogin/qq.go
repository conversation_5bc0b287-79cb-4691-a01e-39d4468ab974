package thirdlogin

import (
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/url"
	"strings"

	"zeus/pkg/http"
)

const qqApiUrl = "https://graph.qq.com/oauth2.0/me"

type QQ struct{}

func (q QQ) Login(_ context.Context, token string, _ string) (UserInfo, error) {
	values := url.Values{}
	values.Set("access_token", token)
	values.Set("fmt", "json")

	var buf strings.Builder
	buf.WriteString(qqApiUrl)
	buf.WriteByte('?')
	buf.WriteString(values.Encode())
	u := buf.String()

	resp, err := http.Get(u)
	if err != nil {
		return UserInfo{}, err
	}

	var data qqResponse
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return UserInfo{}, err
	}
	err = json.Unmarshal(body, &data)
	if err != nil {
		return UserInfo{}, err
	}

	if data.Code != "" && data.MSG != "" {
		return UserInfo{}, errors.New(data.MSG)
	}

	return UserInfo{UnionID: data.OpenID}, nil
}

type qqResponse struct {
	OpenID string `json:"openid"`
	Code   string `json:"code"`
	MSG    string `json:"msg"`
}
