package thirdlogin

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"testing"

	zeushttp "zeus/pkg/http"

	. "github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
)

func TestQQLogin(t *testing.T) {
	mockGetFunc := ApplyFunc(zeushttp.Get, func(u string) (resp *http.Response, err error) {
		resp = &http.Response{}
		body := `{"client_id": "2222", "openid": "A1111"}`
		resp.Body = io.NopCloser(bytes.NewReader([]byte(body)))
		return
	})
	defer mockGetFunc.Reset()
	qq := QQ{}
	info, _ := qq.<PERSON><PERSON>(context.Background(), "test_token_1111", "")
	expected := "A1111"
	assert.Equal(t, info.UnionID, expected)
}
