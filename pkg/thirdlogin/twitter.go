package thirdlogin

import (
	"context"

	"github.com/dghubble/go-twitter/twitter"
	"github.com/dghubble/oauth1"
)

type Twitter struct {
	Key    string
	Secret string
}

func (tw Twitter) Login(_ context.Context, token string, secretToken string) (UserInfo, error) {
	config := oauth1.NewConfig(tw.Key, tw.Secret)
	t := oauth1.NewToken(token, secretToken)
	httpClient := config.Client(oauth1.NoContext, t)
	client := twitter.NewClient(httpClient)

	verifyParams := &twitter.AccountVerifyParams{
		SkipStatus:   twitter.Bool(true),
		IncludeEmail: twitter.Bool(true),
	}
	user, _, err := client.Accounts.VerifyCredentials(verifyParams)
	if err != nil {
		return UserInfo{}, err
	}
	return UserInfo{UnionID: user.IDStr}, nil
}
