package thirdlogin

import (
	"context"
	"net/http"
	"reflect"
	"testing"

	. "github.com/agiledragon/gomonkey"
	"github.com/dghubble/go-twitter/twitter"
	. "github.com/smartystreets/goconvey/convey"
)

func TestTwitter(t *testing.T) {
	Convey("TestTwitterLogin", t, func() {
		<PERSON><PERSON>("Login", func() {
			var as *twitter.AccountService
			ApplyMethod(reflect.TypeOf(as), "VerifyCredentials", func(_ *twitter.AccountService, params *twitter.AccountVerifyParams) (*twitter.User, *http.Response, error) {
				return &twitter.User{IDStr: "A1111"}, &http.Response{}, nil
			})

			tw := Twitter{"test_key", "test_secret"}
			output, _ := tw.Login(context.Background(), "test_token", "test_secretToken")
			expected := "A1111"
			So(output.UnionID, ShouldEqual, expected)
		})
	})
}
