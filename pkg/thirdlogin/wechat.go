package thirdlogin

import (
	"context"
	"fmt"
	"net/url"

	"zeus/pkg/http"
)

const (
	grantType         = "authorization_code"
	wechatApiUrl      = "https://api.weixin.qq.com/sns/oauth2/access_token"
	wechatUserInfoUrl = "https://api.weixin.qq.com/sns/userinfo"

	WechatJSCode2SessionUrl = "https://api.weixin.qq.com/sns/jscode2session"
)

type Wechat struct {
	AppId  string
	Secret string
}

func (w Wechat) Login(ctx context.Context, token string, _ string) (UserInfo, error) {
	values := url.Values{}
	values.Set("appid", w.AppId)
	values.Set("secret", w.Secret)
	values.Set("code", token)
	values.Set("grant_type", grantType)

	headers := make(map[string]string)
	var data wechatResponse
	err := http.MarshalWithTracer(ctx, fmt.Sprintf("%s?%s", wechatApiUrl, values.Encode()), headers, &data)
	if err != nil {
		return UserInfo{}, err
	}

	var userInfo wechatUserInfo
	err = http.MarshalWithTracer(ctx, fmt.Sprintf("%s?access_token=%s&openid=%s&lang=zh_CN", wechatUserInfoUrl, data.AccessToken, data.OpenID), headers, &userInfo)
	if err != nil {
		return UserInfo{}, err
	}
	return UserInfo{UnionID: userInfo.UnionID, UserName: userInfo.NickName}, nil
}

func (w Wechat) MiniGameLogin(ctx context.Context, code string) (map[string]interface{}, error) {
	values := url.Values{}
	values.Set("appid", w.AppId)
	values.Set("secret", w.Secret)
	values.Set("js_code", code)
	values.Set("grant_type", grantType)

	headers := make(map[string]string)
	var data wechatMiniGameResponse
	err := http.MarshalWithTracer(ctx, fmt.Sprintf("%s?%s", WechatJSCode2SessionUrl, values.Encode()), headers, &data)
	if err != nil {
		return nil, err
	}

	if data.Errcode != 0 {
		return nil, fmt.Errorf("wechat mini game login failed, errcode: %d, errmsg: %s", data.Errcode, data.Errmsg)
	}

	return map[string]interface{}{
		"openid":      data.OpenId,
		"unionid":     data.UnionId,
		"session_key": data.SessionKey,
	}, nil
}

type wechatResponse struct {
	OpenID       string `json:"openid"`
	AccessToken  string `json:"access_token"`
	ExpiresIn    int    `json:"expires_in"`
	Refreshtoken string `json:"refresh_token"`
	Scope        string `json:"scope"`
	UnionID      string `json:"unionid"`
}

type wechatUserInfo struct {
	OpenID       string `json:"openid"`
	NickName     string `json:"nickname"`
	Sex          int    `json:"sex"`
	Language     string `json:"language"`
	City         string `json:"city"`
	Province     string `json:"province"`
	Country      string `json:"country"`
	HeadImageUrl string `json:"headimageurl"`
	UnionID      string `json:"unionid"`
}

type wechatMiniGameResponse struct {
	SessionKey string `json:"session_key"`
	OpenId     string `json:"openid"`
	UnionId    string `json:"unionid"`
	Errmsg     string `json:"errmsg"`
	Errcode    int    `json:"errcode"`
}
