package thirdlogin

import (
	"context"
	"io"
	"net/http"
	"strings"
	"testing"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestWeChatLogin(t *testing.T) {
	Convey("WeChatLogin", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(http.Get, func(u string) (resp *http.Response, err error) {
				return &http.Response{Body: io.NopCloser(strings.NewReader(`{"errcode": 0, "openid": "test_openid", "errmsg": ""}`))}, nil
			})
			w := Wechat{AppId: "test_app_id", Secret: "test_secret"}
			output, err := w.<PERSON>(context.Background(), "test_token", "")
			So(output.UnionID, ShouldEqual, "")
			So(err, ShouldBeNil)
		})
	})
}
