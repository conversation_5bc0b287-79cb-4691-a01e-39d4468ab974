package ucenter

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"time"
)

type Client struct {
	APIKey    string
	APISecret string
	APIUrl    string
}

const UcenterApiUrl = "http://apiucenter.topjoy.com/"

func NewClient(APIKey string, APISecret string) *Client {
	return &Client{APIUrl: UcenterApiUrl, APIKey: APIKey, APISecret: APISecret}
}

type RequestData struct {
	ID      int               `json:"id"`
	Method  string            `json:"method"`
	Params  map[string]string `json:"params"`
	Jsonrpc string            `json:"jsonrpc"`
}

type ResponseData struct {
	ID      string `json:"id"`
	Jsonrpc string `json:"jsonrpc"`
}

type ResponseCheckToken struct {
	ResponseData
	Result string `json:"result"`
}

type Game struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	Alias string `json:"alias"`
}

type ResponseGetGames struct {
	RequestData
	Result []Game `json:"result"`
}

type GameRole struct {
	GameId int    `json:"game_id"`
	Name   string `json:"name"`
	EnName string `json:"en_name"`
}

type ResponseGameRole struct {
	RequestData
	Result []GameRole `json:"result"`
}

type UserInfo struct {
	Name string `json:"name"`
}

type ResponseUserInfo struct {
	RequestData
	Result UserInfo `json:"result"`
}

type GameAndRoles struct {
	Alias  string `json:"alias"`
	EnName string `json:"en_name"`
}

func (c *Client) call(api string, method string, params map[string]string) ([]byte, error) {
	currentTime := time.Now().Format("2006-01-02T15:04:05+08:00")
	reqBody := &RequestData{
		ID:      1,
		Method:  method,
		Params:  params,
		Jsonrpc: "2.0",
	}

	authorization := c.generateAuthorization(reqBody, currentTime)

	apiURL := fmt.Sprintf("%s%s/rpc", c.APIUrl, api)

	bytesData, _ := json.Marshal(reqBody)

	client := &http.Client{}
	request, _ := http.NewRequest("POST", apiURL, bytes.NewReader(bytesData))
	request.Header.Add("Date", currentTime)
	request.Header.Add("Content-Type", "application/json")
	request.Header.Add("Authorization", authorization)

	resp, err := client.Do(request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	b, _ := ioutil.ReadAll(resp.Body)
	return b, nil
}

func (c *Client) generateAuthorization(req *RequestData, date string) string {
	dataParams := make([]byte, 0)
	dataParams = append(dataParams, fmt.Sprintf("id%s", "1")...)
	dataParams = append(dataParams, fmt.Sprintf("jsonrpc%s", req.Jsonrpc)...)
	dataParams = append(dataParams, fmt.Sprintf("method%s", req.Method)...)
	dataParams = append(dataParams, "params"...)

	var keys []string
	for k := range req.Params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for _, k := range keys {
		dataParams = append(dataParams, req.Params[k]...)
	}

	dataParams = append(dataParams, c.APISecret...)
	dataParams = append(dataParams, date...)
	m := md5.Sum(dataParams)
	dataParamsSign := hex.EncodeToString(m[:])

	return fmt.Sprintf("SERVICE %s:%s", c.APIKey, dataParamsSign)
}

func (c *Client) CheckToken(token string) string {
	resp, _ := c.call("common", "checkToken", map[string]string{"token": token})
	var data ResponseCheckToken
	json.Unmarshal(resp, &data)
	return data.Result
}

func (c *Client) GetUserById(userId string) UserInfo {
	resp, _ := c.call("user", "getUserById", map[string]string{"id": userId})
	var data ResponseUserInfo
	json.Unmarshal(resp, &data)
	return data.Result
}

func (c *Client) GetGames() []Game {
	resp, _ := c.call("game", "getAll", map[string]string{})
	var data ResponseGetGames
	json.Unmarshal(resp, &data)
	return data.Result
}

func (c *Client) GetIsAdminAndAllowGamesByUserId(userId string) (bool, []GameAndRoles) {
	games := c.GetGames()
	gameMapping := make(map[string]Game)
	for _, v := range games {
		gameMapping[v.Id] = v
	}

	resp, _ := c.call("user", "getRolesById", map[string]string{"id": userId})

	var data ResponseGameRole
	json.Unmarshal(resp, &data)

	gameAndRoles := make([]GameAndRoles, 0)
	var isAdmin bool
	gameRoleMapping := map[int]string{}

	for _, d := range data.Result {
		if d.EnName == "project_manager" {
			isAdmin = true
		}

		if _, ok := gameRoleMapping[d.GameId]; ok {
			if gameRoleMapping[d.GameId] == "admin" {
				continue
			} else {
				gameRoleMapping[d.GameId] = d.EnName
			}
		} else {
			gameRoleMapping[d.GameId] = d.EnName
		}
	}

	for gameId := range gameRoleMapping {
		if val, ok := gameMapping[strconv.Itoa(gameId)]; ok {
			var roles GameAndRoles
			roles.Alias = val.Alias
			roles.EnName = gameRoleMapping[gameId]
			gameAndRoles = append(gameAndRoles, roles)
		}
	}

	return isAdmin, gameAndRoles
}
