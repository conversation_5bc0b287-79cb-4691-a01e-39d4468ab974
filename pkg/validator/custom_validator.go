package validator

import (
	"math"
	"regexp"

	"zeus/common/constants"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
)

func IsPhoneNumber(fl validator.FieldLevel) bool {
	if phone, ok := fl.Field().Interface().(string); ok {
		pattern := `^\d{1,14}$`
		reg := regexp.MustCompile(pattern)
		return reg.MatchString(phone)
	}
	return true
}

func IsEmail(fl validator.FieldLevel) bool {
	if email, ok := fl.Field().Interface().(string); ok {
		emailRegex := regexp.MustCompile(`^[a-zA-Z\d._%+-]+@[a-zA-Z\d.-]+\.[a-zA-Z]{2,}$`)
		return emailRegex.MatchString(email)
	}
	return true
}

func CheckPrice(fl validator.FieldLevel) bool {
	if price, ok := fl.Field().Interface().(float64); ok {
		if price <= 0 {
			return false
		}
		return price == math.Round(price*100)/100
	}
	return true
}

func CheckPlatform(fl validator.FieldLevel) bool {
	if platform, ok := fl.Field().Interface().(string); ok {
		return platform == constants.PlatformAll || platform == constants.PlatformIOS ||
			platform == constants.PlatformAndroid
	}
	return true
}

func InitValidator(structValidator binding.StructValidator) {
	if v, ok := structValidator.Engine().(*validator.Validate); ok {
		v.RegisterValidation("isPhoneNumber", IsPhoneNumber)
		v.RegisterValidation("isEmail", IsEmail)
		v.RegisterValidation("checkPrice", CheckPrice)
		v.RegisterValidation("checkPlatform", CheckPlatform)
	}
}
