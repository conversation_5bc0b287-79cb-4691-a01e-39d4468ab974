package jsapi

import (
	"context"
	"crypto/rsa"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

type Client struct {
	WechatApiClientKey         string `json:"wechat_api_client_key"`
	MchID                      string `json:"mch_id"`
	MchCertificateSerialNumber string `json:"mch_certificate_serial_number"`
	MchAPIv3Key                string `json:"mch_api_v3_key"`
	WechatAppid                string `json:"wechat_appid"`
}

type PrepayOrder struct {
	ProductName string
	OrderID     string
	NotifyUrl   string
	Price       int
	OpenId      string
}

func (cl *Client) NewClient(ctx context.Context) (*core.Client, error) {
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(
			cl.MchID,
			cl.MchCertificateSerialNumber,
			getMchPrivateKey(cl.WechatApiClientKey),
			cl.MchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		log.Error(ctx, "new wechat pay client", "error", err)
	}
	return client, err
}

func getMchPrivateKey(WechatApiClientKey string) *rsa.PrivateKey {
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKey(WechatApiClientKey)
	if err != nil {
		return nil
	}
	return mchPrivateKey
}

func (cl *Client) CreatePrepay(c *gin.Context, order PrepayOrder) (*jsapi.PrepayResponse, *core.APIResult, error) {
	client, err := cl.NewClient(c.Request.Context())
	if err != nil {
		return nil, nil, err
	}

	svc := jsapi.JsapiApiService{Client: client}
	resp, result, err := svc.Prepay(c.Request.Context(),
		jsapi.PrepayRequest{
			Appid:       core.String(cl.WechatAppid),
			Mchid:       core.String(cl.MchID),
			Description: core.String(order.ProductName),
			TimeExpire:  core.Time(time.Now().Add(time.Minute * constants.MiniGameOrderExpireMinute)),
			OutTradeNo:  core.String(order.OrderID),
			NotifyUrl:   core.String(order.NotifyUrl),
			Payer: &jsapi.Payer{
				Openid: core.String(order.OpenId),
			},
			Amount: &jsapi.Amount{
				Total: core.Int64(int64(order.Price)), // 单位为分
			},
		},
	)
	if err != nil {
		return nil, nil, err
	}
	return resp, result, nil
}

func QueryWechatPayTrade(ctx context.Context, orderId string, appid string) (*payments.Transaction, error) {
	var config payment.MiniGameWeChatPay
	converter.QueryByAppid(&config, appid)

	wechatClient := Client{
		WechatApiClientKey:         *config.ApiClientKey,
		WechatAppid:                *config.Appid,
		MchID:                      *config.MchID,
		MchAPIv3Key:                *config.MchAPIKey,
		MchCertificateSerialNumber: *config.MchSN,
	}

	client, err := wechatClient.NewClient(ctx)
	if err != nil {
		return nil, err
	}

	svc := jsapi.JsapiApiService{Client: client}
	resp, _, _ := svc.QueryOrderByOutTradeNo(ctx,
		jsapi.QueryOrderByOutTradeNoRequest{
			OutTradeNo: core.String(orderId),
			Mchid:      core.String(*config.MchID),
		},
	)
	return resp, nil
}
