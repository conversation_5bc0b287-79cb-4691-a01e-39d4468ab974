package wechat

import (
	"context"
	"crypto/rsa"
	"encoding/json"
	"errors"
	"fmt"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"github.com/avast/retry-go/v4"
	"github.com/gin-gonic/gin"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/app"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

func getMchPrivateKey(WechatApiClientKey string) *rsa.PrivateKey {
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKey(WechatApiClientKey)
	if err != nil {
		return nil
	}
	return mchPrivateKey
}

func WechatPay(c *gin.Context, order schema.WeChatExchangeForm) (*schema.WeChatOrder, *models.Order, error) {
	ctx := c.Request.Context()
	host := c.Request.Host
	var config payment.WeChatPay
	converter.QueryByAppid(&config, order.AppID)

	if *config.WechatApiClientKey == "" || *config.WechatMchID == "" || *config.WechatMchSN == "" || *config.WechatMchAPIKey == "" || *config.WechatAppid == "" {
		log.Error(c.Request.Context(), "微信支付参数配置不完整")
		return nil, nil, errors.New("微信支付参数配置不完整")
	}
	mchPrivateKey := getMchPrivateKey(*config.WechatApiClientKey)
	mchID := *config.WechatMchID
	mchCertificateSerialNumber := *config.WechatMchSN
	mchAPIv3Key := *config.WechatMchAPIKey
	appID := *config.WechatAppid
	notifyUrl := fmt.Sprintf("https://%s/callback/app/wechat/notify/%s", host, order.AppID)
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(
			mchID,
			mchCertificateSerialNumber,
			mchPrivateKey,
			mchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		log.Error(ctx, "new wechat pay client", "error", err)
	}

	var orderModel models.Order

	svc := app.AppApiService{Client: client}

	if err != nil {
		return nil, nil, err
	}
	// 得到prepay_id，以及调起支付所需的参数和签名
	orderID := services.GenerateOrderID(constants.PayWayWeChat, constants.OrderIDTokenSize)

	var description string
	if order.ProductName != "" {
		description = order.ProductName
	} else {
		description = order.ProductID
	}

	var resp *app.PrepayWithRequestPaymentResponse
	err = retry.Do(
		func() error {
			resp, _, err = svc.PrepayWithRequestPayment(ctx,
				app.PrepayRequest{
					Appid:       core.String(appID),
					Mchid:       core.String(mchID),
					Description: core.String(description),
					OutTradeNo:  core.String(orderID),
					NotifyUrl:   core.String(notifyUrl),
					Amount: &app.Amount{
						Total: core.Int64(int64(order.Price)),
					},
				},
			)
			if err != nil {
				return err
			}
			return nil
		},
		retry.Attempts(3),
	)
	if err != nil {
		return nil, nil, err
	}

	var orderJson schema.WeChatOrder
	j, _ := json.Marshal(resp)
	_ = json.Unmarshal(j, &orderJson)
	orderModel = formatWechatOrder(orderID, order.Price, order)
	orderJson.OrderID = orderID

	return &orderJson, &orderModel, nil
}

func formatWechatOrder(orderID string, TotalAmount float64, orderReq schema.WeChatExchangeForm) models.Order {
	var order models.Order
	order.ProductId = orderReq.ProductID
	order.UserId = orderReq.UserID
	order.OrderId = orderID
	order.PayWay = constants.PayWayWeChat
	return order
}

func DecodeWechatCallbackRequest(c *gin.Context, apiClientKey string, mchCertificateSerialNumber string, mchID string, mchAPIv3Key string) (*payments.Transaction, error) {
	log.Info(c.Request.Context(), "开始解析微信订单信息", "request", c.Request)
	mchPrivateKey := getMchPrivateKey(apiClientKey)

	err := downloader.MgrInstance().RegisterDownloaderWithPrivateKey(c.Request.Context(), mchPrivateKey, mchCertificateSerialNumber, mchID, mchAPIv3Key)
	if err != nil {
		return nil, err
	}

	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(mchID)
	handler := notify.NewNotifyHandler(mchAPIv3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor))

	transaction := new(payments.Transaction)

	_, err = handler.ParseNotifyRequest(c.Request.Context(), c.Request, transaction)
	if err != nil {
		return nil, err
	}

	c.Set("request", transaction)
	log.Info(c.Request.Context(), "解析微信小游戏回调请求参数", "transaction", transaction)

	return transaction, nil
}

func QueryWechatPayTrade(ctx context.Context, order models.Order, appid string) (*payments.Transaction, error) {
	log.Debug(ctx, "开始校验微信订单信息", "data", order)
	var config payment.WeChatPay
	converter.QueryByAppid(&config, appid)

	if *config.WechatApiClientKey == "" || *config.WechatMchID == "" || *config.WechatMchSN == "" || *config.WechatMchAPIKey == "" || *config.WechatAppid == "" {
		log.Error(ctx, "微信支付参数配置不完整")
		return nil, errors.New("微信支付参数配置不完整")
	}
	mchID := *config.WechatMchID
	mchCertificateSerialNumber := *config.WechatMchSN
	mchAPIv3Key := *config.WechatMchAPIKey
	mchPrivateKey := getMchPrivateKey(*config.WechatApiClientKey)
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(
			mchID,
			mchCertificateSerialNumber,
			mchPrivateKey,
			mchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		log.Error(ctx, "new wechat pay client", "err:", err)
		return nil, err
	}
	svc := jsapi.JsapiApiService{Client: client}
	resp, _, err := svc.QueryOrderByOutTradeNo(ctx,
		jsapi.QueryOrderByOutTradeNoRequest{
			OutTradeNo: core.String(order.OrderId),
			Mchid:      core.String(mchID),
		},
	)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
