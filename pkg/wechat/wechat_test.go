package wechat

import (
	"context"
	"crypto/rsa"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/settings/converter"
	settings_schema "zeus/services/settings/field"
	"zeus/services/settings/payment"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/app"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
)

func TestWechat(t *testing.T) {
	ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
		return &models.Game{}, nil
	})

	Convey("WechatPay", t, func() {
		Convey("one func for fail", func() {
			QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config settings_schema.BaseConfiger, _ string) {
				switch config.GetType() {
				case "PaymentWeChatPay":
					weChatPayConfig := config.(*payment.WeChatPay)
					wechatAppid := ""
					weChatPayConfig.WechatAppid = &wechatAppid
					wechatApiClientKey := ""
					weChatPayConfig.WechatApiClientKey = &wechatApiClientKey
					wechatMchID := ""
					weChatPayConfig.WechatMchID = &wechatMchID
					wechatMchSN := ""
					weChatPayConfig.WechatMchSN = &wechatMchSN
					wechatMchAPIKey := ""
					weChatPayConfig.WechatMchAPIKey = &wechatMchAPIKey
				}
			})

			defer QueryMockFunc.Reset()

			order := schema.WeChatExchangeForm{}
			c, _ := gin.CreateTestContext(httptest.NewRecorder())
			c.Request = new(http.Request)
			_, _, err := WechatPay(c, order)
			So(err, ShouldBeError, errors.New("微信支付参数配置不完整"))
		})

		Convey("one func for succ", func() {
			QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config settings_schema.BaseConfiger, _ string) {
				switch config.GetType() {
				case "PaymentWeChatPay":
					weChatPayConfig := config.(*payment.WeChatPay)
					wechatAppid := "WechatAppid"
					weChatPayConfig.WechatAppid = &wechatAppid
					wechatApiClientKey := "WechatApiClientKey"
					weChatPayConfig.WechatApiClientKey = &wechatApiClientKey
					wechatMchID := "WechatMchID"
					weChatPayConfig.WechatMchID = &wechatMchID
					wechatMchSN := "WechatMchSN"
					weChatPayConfig.WechatMchSN = &wechatMchSN
					wechatMchAPIKey := "WechatMchAPIKey"
					weChatPayConfig.WechatMchAPIKey = &wechatMchAPIKey
				}
			})
			defer QueryMockFunc.Reset()

			LoadPrivateKeyMockFunc := ApplyFunc(utils.LoadPrivateKey, func(privateKeyStr string) (privateKey *rsa.PrivateKey, err error) {
				privateKey = &rsa.PrivateKey{}
				return
			})
			defer LoadPrivateKeyMockFunc.Reset()

			NewClientMockFunc := ApplyFunc(core.NewClient, func(ctx context.Context, opts ...core.ClientOption) (*core.Client, error) {
				return &core.Client{}, nil
			})
			defer NewClientMockFunc.Reset()

			GenerateOrderIDMockFunc := ApplyFunc(services.GenerateOrderID, func(orderType int, tokenNumber int, extra ...string) string {
				return "order_id_test"
			})
			defer GenerateOrderIDMockFunc.Reset()

			svc := &app.AppApiService{}
			PrepayWithRequestPaymentMockFunc := ApplyMethod(reflect.TypeOf(svc), "PrepayWithRequestPayment", func(
				_ *app.AppApiService, ctx context.Context, req app.PrepayRequest,
			) (resp *app.PrepayWithRequestPaymentResponse, result *core.APIResult, err error) {
				resp = &app.PrepayWithRequestPaymentResponse{}
				return
			})
			defer PrepayWithRequestPaymentMockFunc.Reset()

			WithWechatPayAutoAuthCipherMockFunc := ApplyFunc(option.WithWechatPayAutoAuthCipher, func(_ string, _ string,
				_ *rsa.PrivateKey, _ string,
			) core.ClientOption {
				return nil
			})
			defer WithWechatPayAutoAuthCipherMockFunc.Reset()

			order := schema.WeChatExchangeForm{}
			c, _ := gin.CreateTestContext(httptest.NewRecorder())
			c.Request = new(http.Request)
			_, _, err := WechatPay(c, order)
			So(err, ShouldEqual, nil)
		})
	})

	Convey("QueryWechatPayTrade", t, func() {
		Convey("one func for fail", func() {
			QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config settings_schema.BaseConfiger, _ string) {
				switch config.GetType() {
				case "PaymentWeChatPay":
					weChatPayConfig := config.(*payment.WeChatPay)
					wechatAppid := ""
					weChatPayConfig.WechatAppid = &wechatAppid
					wechatApiClientKey := ""
					weChatPayConfig.WechatApiClientKey = &wechatApiClientKey
					wechatMchID := ""
					weChatPayConfig.WechatMchID = &wechatMchID
					wechatMchSN := ""
					weChatPayConfig.WechatMchSN = &wechatMchSN
					wechatMchAPIKey := ""
					weChatPayConfig.WechatMchAPIKey = &wechatMchAPIKey
				}
			})
			defer QueryMockFunc.Reset()

			order := models.Order{}
			appid := "test_app_id"
			_, err := QueryWechatPayTrade(context.Background(), order, appid)
			So(err, ShouldBeError, errors.New("微信支付参数配置不完整"))
		})

		Convey("one func for succ", func() {
			QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config settings_schema.BaseConfiger, _ string) {
				switch config.GetType() {
				case "PaymentWeChatPay":
					weChatPayConfig := config.(*payment.WeChatPay)
					wechatAppid := "WechatAppid"
					weChatPayConfig.WechatAppid = &wechatAppid
					wechatApiClientKey := "WechatApiClientKey"
					weChatPayConfig.WechatApiClientKey = &wechatApiClientKey
					wechatMchID := "WechatMchID"
					weChatPayConfig.WechatMchID = &wechatMchID
					wechatMchSN := "WechatMchSN"
					weChatPayConfig.WechatMchSN = &wechatMchSN
					wechatMchAPIKey := "WechatMchAPIKey"
					weChatPayConfig.WechatMchAPIKey = &wechatMchAPIKey
				}
			})
			defer QueryMockFunc.Reset()

			svc := &jsapi.JsapiApiService{}
			QueryOrderByOutTradeNoMockFunc := ApplyMethod(reflect.TypeOf(svc), "QueryOrderByOutTradeNo",
				func(_ *jsapi.JsapiApiService, ctx context.Context,
					req jsapi.QueryOrderByOutTradeNoRequest) (resp *payments.Transaction,
					result *core.APIResult, err error,
				) {
					appid := "test_app_id"
					resp = &payments.Transaction{}
					resp.Appid = &appid
					return
				})
			defer QueryOrderByOutTradeNoMockFunc.Reset()

			NewClientMockFunc := ApplyFunc(core.NewClient, func(ctx context.Context, opts ...core.ClientOption) (*core.Client, error) {
				return &core.Client{}, nil
			})
			defer NewClientMockFunc.Reset()

			WithWechatPayAutoAuthCipherMockFunc := ApplyFunc(option.WithWechatPayAutoAuthCipher, func(_ string, _ string,
				_ *rsa.PrivateKey, _ string,
			) core.ClientOption {
				return nil
			})
			defer WithWechatPayAutoAuthCipherMockFunc.Reset()

			order := models.Order{}
			appid := "test_app_id"
			resp, _ := QueryWechatPayTrade(context.Background(), order, appid)
			output := *resp.Appid
			expected := "test_app_id"
			So(output, ShouldEqual, expected)
		})
	})
}
