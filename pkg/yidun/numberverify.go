package yidun

import (
	"context"
	"errors"

	mobileverify "github.com/yidun/yidun-golang-sdk/yidun/service/mobileverify"
)

type NumberVerify struct {
	secretID   string // 产品密钥ID，产品标识
	secretKey  string // 产品私有密钥，服务端生成签名信息使用，请严格保管，避免泄露
	businessID string // 业务ID，易盾根据产品业务特点分配
}

func NewNumberVerifyClient(businessID, secretID, secretKey string) *NumberVerify {
	return &NumberVerify{
		secretID:   secretID,
		secretKey:  secretKey,
		businessID: businessID,
	}
}

func (n *NumberVerify) GetNumber(ctx context.Context, token string, accessToken string) (string, error) {
	re := mobileverify.NewMobileNumberGetRequest(n.businessID)
	mobileNumberVerifyClient := mobileverify.NewMobileNumberClientWithAccessKey(n.secretID, n.secretKey)
	re.SetToken(token).SetAccessToken(accessToken)
	response, err := mobileNumberVerifyClient.GetMobileNumber(re)
	if err != nil {
		return "", err
	}

	if response.GetCode() == 200 {
		data := response.Data
		return *data.Phone, nil
	} else {
		return "", errors.New(response.GetMsg())
	}
}
