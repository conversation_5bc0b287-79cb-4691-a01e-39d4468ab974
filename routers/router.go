package routers

import (
	"net/http"

	"zeus/controllers/minigame"
	"zeus/controllers/third_pay"
	"zeus/middlewares/checkSignature"
	"zeus/middlewares/region_restrict"
	"zeus/middlewares/undecode"

	api_v1 "zeus/controllers/api/v1"
	callback_app "zeus/controllers/callback/app"
	callback_minigame "zeus/controllers/callback/minigame"
	sdk_v1 "zeus/controllers/sdk/v1"
	sdk_v2 "zeus/controllers/sdk/v2"
	"zeus/controllers/applovin"
	"zeus/controllers/xsolla"
	"zeus/middlewares/checkJWT"
	"zeus/middlewares/chooseDB"
	"zeus/middlewares/context"
	"zeus/middlewares/cors"
	"zeus/middlewares/counter"
	"zeus/middlewares/ginprometheus"
	"zeus/middlewares/request_log"

	"github.com/gin-gonic/gin"

	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

type Empty struct{}

var skipPaths = map[string]struct{}{
	"/":            Empty{},
	"/favicon.ico": Empty{},
	"/healthz":     Empty{},
	"/metrics":     Empty{},
}

func SetupRouter() *gin.Engine {
	router := gin.New()

	router.TrustedPlatform = "True-Client-IP"

	router.Use(otelgin.Middleware("zeus", otelgin.WithFilter(
		func(r *http.Request) bool {
			if _, ok := skipPaths[r.URL.Path]; ok {
				return false
			}
			return true
		})))

	router.Use(request_log.LoggerWithWriter(skipPaths))

	router.Static("/del_account_docs", "./del_account_docs")

	router.GET("/healthz", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
		})
	})

	prom := ginprometheus.NewPrometheus("zeus")
	prom.Use(router)
	router.Use(cors.Cors())

	// SDK V1 API Group
	rootGroup := router.Group("/")
	rootGroupIgnoreUnDecode := map[string]struct{}{
		"/login/verify":               {},
		"/server/antispam/text-check": {},
		"/callback/google-notify":     {},
		"/callback/ios-notify":        {},
	}
	rootGroupIgnoreCheckSignature := map[string]struct{}{
		"/login/verify":               {},
		"/server/antispam/text-check": {},
		"/callback/google-notify":     {},
		"/callback/ios-notify":        {},
	}
	rootGroupIgnore := map[string]struct{}{
		"/game/info":              {},
		"/account/get-area-codes": {},
		"/account/get-regions":    {},
		"/user/third-list":        {},
	}
	rootGroup.Use(undecode.Base64UnDecode(rootGroupIgnoreUnDecode))
	rootGroup.Use(checkSignature.CheckSignature(rootGroupIgnoreCheckSignature))
	rootGroup.Use(context.SetZeusContext())
	rootGroup.Use(region_restrict.CheckClientRegion())
	rootGroup.Use(chooseDB.ZeusSDKSetBD(rootGroupIgnore))
	rootGroup.Use(counter.RecordCounter())

	// SDK V2 API Group

	v2Group := router.Group("/v2")
	v2GroupIgnoreUnDecode := map[string]struct{}{}
	v2Group.Use(undecode.Base64UnDecode(v2GroupIgnoreUnDecode))

	v2GroupIgnoreCheckSignature := map[string]struct{}{}
	v2Group.Use(checkSignature.CheckSignature(v2GroupIgnoreCheckSignature))
	v2Group.Use(context.SetZeusContext())

	v2GroupIgnore := map[string]struct{}{}
	v2Group.Use(chooseDB.ZeusSDKSetBD(v2GroupIgnore))
	v2Group.Use(counter.RecordCounter())

	// MiniGame Group
	miniGameGroup := router.Group("/minigame")
	miniGameGroup.Use(undecode.Base64UnDecode(map[string]struct{}{}))
	miniGameGroup.Use(checkSignature.CheckSignature(map[string]struct{}{}))
	miniGameGroup.Use(context.SetZeusContext())
	miniGameGroup.Use(chooseDB.ZeusSDKSetBD(map[string]struct{}{}))

	// Xsolla Group
	xsollaGroup := router.Group("/xsolla")
	xsollaGroup.Use(chooseDB.XsollaSetBD)
	xsollaGroup.Use(context.SetXsollaContext)

	// Third Pay Group
	thirdPayGroup := router.Group("/third-pay")
	thirdPayGroup.Use(cors.Cors())
	thirdPayGroup.Use(context.SetThirdPayContext)
	thirdPayGroup.Use(chooseDB.ThirdPaySetBD)

	apiV1GroupIgnoreSetContext := map[string]struct{}{
		"/api/v1/login":                         {},
		"/api/v1/operation":                     {},
		"/api/v1/project/*":                     {},
		"/api/v1/game/*":                        {},
		"/api/v1/orders/statistics_reload":      {},
		"/api/v1/request_count":                 {},
		"/api/v1/request_statistics/percentage": {},
		"/api/v1/configs/consumer_complaint":    {},
		"/api/v1/dial_testing/targets/*":        {},
		"/api/v1/web_pay/upload":                {},
	}

	// API V1 Group
	apiV1Group := router.Group("/api/v1")
	apiV1Group.Use(context.SetZeusAPIContext(apiV1GroupIgnoreSetContext))

	apiV1GroupIgnoreCheckJWT := map[string]struct{}{
		"/api/v1/login": {},
	}
	apiV1Group.Use(checkJWT.JWTAuthMiddleware(apiV1GroupIgnoreCheckJWT))

	apiV1GroupIgnore := map[string]struct{}{
		"/api/v1/login":                         {},
		"/api/v1/operation":                     {},
		"/api/v1/project/*":                     {},
		"/api/v1/game/*":                        {},
		"/api/v1/orders/statistics_reload":      {},
		"/api/v1/request_count":                 {},
		"/api/v1/request_statistics/percentage": {},
		"/api/v1/configs/consumer_complaint":    {},
		"/api/v1/dial_testing/targets/*":        {},
		"/api/v1/web_pay/upload":                {},
	}
	apiV1Group.Use(chooseDB.ZeusAPISetBD(apiV1GroupIgnore))

	callbackGroup := router.Group("/callback")
	callbackGroup.Use(chooseDB.CallbackSetBD(map[string]struct{}{}))

	applovinGroup := router.Group("/applovin")

	// SetupRouter
	sdk_v1.SetupRouter(rootGroup)
	sdk_v2.SetupRouter(v2Group)
	api_v1.SetupRouter(apiV1Group)
	xsolla.SetupRouter(xsollaGroup)
	third_pay.SetupRouter(thirdPayGroup)
	callback_app.SetupRouter(callbackGroup)
	callback_minigame.SetupRouter(callbackGroup)
	minigame.SetupRouter(miniGameGroup)
	applovin.SetupRouter(applovinGroup)
	return router
}
