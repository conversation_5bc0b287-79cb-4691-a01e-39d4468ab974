package schema

import (
	"fmt"
	"time"
)

type Base struct {
	Sign string `json:"sign"`
}

type DateTime struct {
	time.Time
}

func (dt DateTime) MarshalJSON() ([]byte, error) {
	if dt.Time.IsZero() {
		return []byte("\"\""), nil // 时间为空时返回空字符串
	}
	formatted := fmt.Sprintf(`"%s"`, dt.Time.Format("2006-01-02 15:04:05"))
	return []byte(formatted), nil
}

func (dt *DateTime) Scan(v interface{}) error {
	value, ok := v.(time.Time)
	if ok {
		*dt = DateTime{Time: value}
		return nil
	}
	return fmt.Errorf("can not convert %v to timestamp", v)
}
