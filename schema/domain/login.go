package domain

import (
	"zeus/common/constants"
	"zeus/schema/request"
	"zeus/utils"
)

type Login struct {
	APPID     string `json:"appid"`
	LoginType int    `json:"login_type"`
	Device    string `json:"device"`
	Account   string `json:"account"`
	Email     string `json:"email"`
	Phone     string `json:"phone"`
	Password  string `json:"password"`
}

func (l *Login) Map(v *request.Login) {
	l.APPID = v.APPID
	l.Device = v.Device
	l.Account = v.Account
	l.Phone = v.Phone
	l.Email = v.Email

	l.LoginType = constants.LoginTypeDevice
	if l.Email != "" {
		l.LoginType = constants.LoginTypeEmail
	} else {
		if l.Phone != "" {
			l.LoginType = constants.LoginTypePhone
		}
	}
	if l.Email != "" || l.Phone != "" {
		l.Password = utils.MD5(v.Password)
	}
}
