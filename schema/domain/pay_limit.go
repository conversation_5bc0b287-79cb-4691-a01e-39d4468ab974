package domain

import (
	"time"
	"zeus/common/constants"

	"zeus/schema/request"
)

type PayLimit struct {
	AppID     string    `json:"app_id"`
	Account   string    `json:"account"`
	Region    string    `json:"region"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

func (l *PayLimit) Map(v *request.PayLimit) {
	l.AppID = v.AppID
	l.Account = v.Account
	l.Region = v.Region
	l.setConstrainsTime()
}

func (l *PayLimit) setConstrainsTime() {
	year, month, _ := time.Now().Date()
	thisMonthStartTime := time.Date(year, month+1, 1, 0, 0, 0, 0, time.Local)
	thisMonthEndTime := time.Date(year, month+1, 1, 23, 59, 59, 0, time.Local)

	if l.Region == constants.Japan {
		// 获取日本时区
		loc, err := time.LoadLocation("Asia/Tokyo")
		if err != nil {
			panic(err)
		}
		year, month, _ = time.Now().In(loc).Date()
		thisMonthStartTime = time.Date(year, month+1, 1, 0, 0, 0, 0, loc)
		thisMonthEndTime = time.Date(year, month+1, 1, 23, 59, 59, 0, loc)
	}

	l.StartTime = thisMonthStartTime.AddDate(0, -1, 0)
	l.EndTime = thisMonthEndTime.AddDate(0, 0, -1)
}
