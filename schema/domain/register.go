package domain

import (
	"errors"
	"strconv"

	"zeus/common/constants"
	"zeus/schema/request"
	"zeus/utils"
)

type Register struct {
	APPID        string `json:"appid"`
	RegisterType int    `json:"register_type"`
	Device       string `json:"device"`
	Platform     string `json:"platform"`
	ForceRenew   bool   `json:"force_renew"`
	MobileInfo   string `json:"mobile_info"`
	Email        string `json:"email"`
	Phone        string `json:"phone"`
	Password     string `json:"password"`
	Name         string `json:"name"`
}

func (r *Register) Map(v request.Register) error {
	var err error
	r.APPID = v.APPID
	r.Device = v.Device
	r.Platform = v.Platform
	r.Email = v.Email
	r.Phone = v.Phone
	r.Name = v.Name
	r.RegisterType = constants.RegisterTypeDevice

	if r.Email != "" {
		r.RegisterType = constants.RegisterTypeEmail
	} else {
		if r.Phone != "" {
			r.RegisterType = constants.RegisterTypePhone
		}
	}

	if r.Email != "" || r.Phone != "" {
		if v.Password == "" {
			return errors.New("密码不允许为空")
		}
		r.Password = utils.MD5(v.Password)
	}

	if v.ForceRenew != "" {
		r.ForceRenew, err = strconv.ParseBool(v.ForceRenew)
	} else {
		r.ForceRenew = true
	}
	r.MobileInfo = v.MobileInfo
	return err
}
