package domain

import (
	"strconv"

	"zeus/schema/request"
)

type XsollaLogin struct {
	MerchantID string `json:"merchant_id"`
	ProjectID  string `json:"project_id"`
	RoleID     string `json:"role_id"`
}

func (x *XsollaLogin) Map(v *request.XsollaLogin) {
	x.MerchantID = v.MerchantID
	x.ProjectID = v.ProjectID
	x.RoleID = v.User.ID
}

type XsollaItem struct {
	RoleID    string  `json:"role_id"`
	PayID     string  `json:"pay_id"`
	OrderID   string  `json:"order_id"`
	Price     float64 `json:"amount"`
	Currency  string  `json:"currency"`
	ProductID string  `json:"product_id"`
	PayWay    string  `json:"pay_way"`
}

type XsollaPay struct {
	ProjectID   string `json:"project_id"`
	XsollaItems []XsollaItem
}

func (xp *XsollaPay) Map(v *request.XsollaPay) {
	xp.ProjectID = v.ProjectID
	for i := range v.Items {
		for j := 0; j < v.Items[i].Quantity; j++ {
			var item XsollaItem
			item.RoleID = v.User.ExternalID
			item.Currency = v.Order.Currency
			item.Price, _ = strconv.ParseFloat(v.Items[i].Amount, 64)
			item.OrderID = v.Order.InvoiceId + "_" + v.Items[i].Sku + "_" + strconv.Itoa(j)
			item.PayID = v.Order.InvoiceId
			item.ProductID = v.Items[i].Sku
			item.PayWay = v.Order.Platform
			xp.XsollaItems = append(xp.XsollaItems, item)
		}
	}
}
