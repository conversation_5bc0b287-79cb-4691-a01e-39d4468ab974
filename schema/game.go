package schema

type Game struct{}

type GameSummary struct {
	Game
	Id          int    `json:"id" label:"游戏ID"`
	ProjectID   int    `json:"project_id" binding:"required" label:"游戏项目id"`
	Name        string `json:"name" label:"游戏名"`
	Appid       string `json:"appid" label:"游戏appid"`
	Type        string `json:"type" label:"游戏类型"`
	SecretKey   string `json:"secret_key" label:"游戏secret key"`
	Platform    string `json:"platform" label:"游戏platform"`
	ServiceMail string `json:"service_mail" label:"游戏服务邮箱"`
	CreateTime  string `json:"create_time" label:"创建时间"`
	Status      int    `json:"status" label:"游戏状态"`
}

type ShushuConfig struct {
	ShushuAppid string `json:"shushu_appid"`
	ShushuHost  string `json:"shushu_host"`
}
type SamplingConfig struct {
	SamplingRate float64 `json:"sampling_rate"`
}

type FreshdeskConfig struct {
	FreshdeskAppkey string `json:"freshdesk_app_key"`
	FreshdeskDomain string `json:"freshdesk_domain"`
	FreshdeskAppid  string `json:"freshdesk_appid"`
}

type OtherGameConfig struct {
	ShushuConfig    ShushuConfig    `json:"shushu_config"`
	FreshdeskConfig FreshdeskConfig `json:"freshdesk_config"`
	SamplingConfig  SamplingConfig  `json:"sampling_config"`
	AntispamConfig  AntispamConfig  `json:"antispam_config"`
}

type AntispamConfig struct {
	AntispamSecretID   string `json:"antispam_secret_id"`
	AntispamSecretKey  string `json:"antispam_secret_key"`
	AntispamBusinessID string `json:"antispam_business_id"`
}

type RobustConfigs struct {
	DialTestingConfig   DialTestingConfig   `json:"dial_testing_config"`
	LogCollectionConfig LogCollectionConfig `json:"log_collection_config"`
}

type DialTestingConfig struct {
	DialTestingWhiteList    string  `json:"dial_testing_white_list"`
	DialTestingSamplingRate float64 `json:"dial_testing_sampling_rate"`
}

type LogCollectionConfig struct {
	LogCollectionSamplingRate float64 `json:"log_collection_sampling_rate"`
	LogCollectionWhiteList    string  `json:"log_collection_white_list"`
}

type GameAccountInfo struct {
	Game
	AccountModule     string `json:"account_module"`
	OpenStatus        int    `json:"open_status"`
	RechargeSwitch    bool   `json:"recharge_switch"`
	FbAppid           string `json:"fb_appid"`
	FbSecret          string `json:"fb_secret"`
	FbClientToken     string `json:"fb_client_token"`
	TwKey             string `json:"tw_key"`
	TwSecret          string `json:"tw_secret"`
	WechatAppid       string `json:"wechat_appid"`
	WechatSecret      string `json:"wechat_secret"`
	GoogleClientID    string `json:"google_client_id"`
	AppleClientId     string `json:"apple_client_id"`
	AppleClientSecret string `json:"apple_client_secret"`
	QQAppId           string `json:"qq_appid"`
	LineAppid         string `json:"line_appid"`
}

type GamePaymentInfo struct {
	Game
	AccountModule             string `json:"account_module"`
	OpenStatus                int    `json:"open_status"`
	RechargeSwitch            bool   `json:"recharge_switch"`
	GooglePayKey              string `json:"google_pay_key"`
	GooglePayPackageName      string `json:"google_pay_package_name"`
	GoogleClientId            string `json:"google_client_id"`
	GoogleClientSecret        string `json:"google_client_secret"`
	GoogleRedirectUri         string `json:"google_redirect_uri"`
	GoogleRefreshToken        string `json:"google_refresh_token"`
	WechatApiClientKey        string `json:"wechat_api_client_key"`
	WechatAppid               string `json:"wechat_appid"`
	WechatMchID               string `json:"wechat_mch_id"`
	WechatMchSN               string `json:"wechat_mch_sn"`
	WechatMchAPIKey           string `json:"wechat_mch_api_key"`
	AliPrivateKey             string `json:"ali_private_key"`
	AliAppId                  string `json:"ali_app_id"`
	AliPubKey                 string `json:"ali_pub_key"`
	ApplePassword             string `json:"apple_password"`
	AppleBundleId             string `json:"apple_bundle_id"`
	AppleISS                  string `json:"apple_iss"`
	AppleKeyID                string `json:"apple_key_id"`
	AppleSecret               string `json:"apple_secret"`
	IsAppleCheckTranscationId string `json:"is_apple_check_transcation_id"`
	IsIOSSubscriptionOpen     string `json:"is_ios_subscription_open"`
	IsGoogleSubscriptionOpen  string `json:"is_google_subscription_open"`
	MyCardID                  string `json:"mycard_id"`
	MyCardServiceID           string `json:"mycard_service_id"`
	MyCardSecret              string `json:"mycard_secret"`
	MyCardSandBoxMode         string `json:"mycard_sandbox_mode"`
	MyCardConfigs             string `json:"mycard_configs"`
	GashConfigs               string `json:"gash_configs"`
}

type GameInfoCreateForm struct {
	Game
	ProjectAlias string `json:"project_alias" binding:"required" label:"游戏项目名称"`
	Name         string `json:"name" binding:"required" label:"游戏名"`
	Appid        string `json:"appid" label:"游戏appid"`
	SecretKey    string `json:"secret_key" label:"游戏secret key"`
	ServiceMail  string `json:"service_mail" label:"游戏服务邮箱"`
	Platform     string `json:"platform" binding:"required,checkPlatform" label:"游戏平台"`
}

type GameInfo struct {
	Game
	IsFbLogin      int    `json:"is_fb_login,omitempty" label:"是否开启facebook登录" `
	IsLineLogin    int    `json:"is_line_login,omitempty" label:"是否开启line登录"`
	GoogleClientId string `json:"google_client_id" label:"游戏google client"`
	IsGiLogin      int    `json:"is_gi_login,omitempty" label:"是否开启google id登录"`
	IsAiLogin      int    `json:"is_ai_login,omitempty" label:"是否开启appid登录"`
	IsGcLogin      int    `json:"is_gc_login,omitempty" label:"是否开启GameCenter登录"`
	IsGpLogin      int    `json:"is_gp_login,omitempty" label:"是否开启google商店登录"`
	LineAppid      string `json:"line_appid,omitempty" label:"是否开启Line appid登录"`
	IsTwLogin      int    `json:"is_tw_login,omitempty" label:"是否开启twitter登录"`
	IsQQLogin      int    `json:"is_qq_login,omitempty" label:"是否开启qq登录"`
	IsWechatLogin  int    `json:"is_wechat_login,omitempty" label:"是否开启微信登录"`
	IsMobileLogin  int    `json:"is_mobile_login,omitempty" label:"是否开启手机登录"`
	IsEmailLogin   int    `json:"is_email_login,omitempty" label:"是否开启email登录"`
}

type GameAccountStatus struct {
	Label       string `json:"label"`
	Name        string `json:"name"`
	IsLoginOpen int    `json:"is_login_open"`
}

type GamePaymentStatus struct {
	Label         string `json:"label"`
	Name          string `json:"name"`
	IsPaymentOpen int    `json:"is_payment_open"`
}

type GameInfoFieldConfig struct {
	Label       string         `json:"label"`
	Name        string         `json:"name"`
	Value       interface{}    `json:"value"`
	Type        string         `json:"type"`
	Placeholder string         `json:"placeholder"`
	Required    bool           `json:"required"`
	Options     []FieldOptions `json:"options"`
}

type FieldOptions struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

func (game *Game) TableName() string {
	return "game"
}

type MessageData struct {
	GameId     int    `json:"game_id"`
	GameName   string `json:"game_name"`
	OrderId    string `json:"order_id"`
	Platform   string `json:"platform"`
	Channel    string `json:"channel"`
	ProductId  string `json:"product_id"`
	RoleName   string `json:"role_name"`
	RoleId     string `json:"role_id"`
	ServerId   string `json:"server_id"`
	CreateTime string `json:"create_time"`
	NotifyErr  error  `json:"notify_err"`
}
