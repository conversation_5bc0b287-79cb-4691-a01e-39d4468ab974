package schema

type BaseMiniGameOrderForm struct {
	AppID        string  `json:"appid" binding:"required"`
	UserID       int     `json:"user_id,string" binding:"required"`
	ProductID    string  `json:"product_id" binding:"required"`
	ProductName  string  `json:"product_name"`
	RoleID       string  `json:"role_id" binding:"required"`
	Price        float64 `json:"price,string" binding:"required"`
	Device       string  `json:"device"`
	RoleName     string  `json:"role_name" binding:"required"`
	Extend       string  `json:"extend"`
	PlatForm     string  `json:"platform"`
	SdkVersion   string  `json:"sdk_version"`
	ServerID     string  `json:"server_id"`
	ServerName   string  `json:"server_name"`
	Vip          string  `json:"vip"`
	Level        string  `json:"level"`
	PayNotifyURL string  `json:"pay_notify_url"`
	PayType      string  `json:"pay_type" binding:"required"`
	Env          int     `json:"env,string"`
}

type WechatMiniGameExchange struct {
	BaseMiniGameOrderForm
}

type DouyinMiniGameExchange struct {
	BaseMiniGameOrderForm
}

type PrepayOrderResult struct {
	PrepayId string `json:"prepay_id"`
	OrderId  string `json:"order_id"`
}

type VirtualPaymentSignData struct {
	Mode         string `json:"mode"`
	OfferId      string `json:"offerId"`
	BuyQuantity  int    `json:"buyQuantity"`
	Env          int    `json:"env"`
	CurrencyType string `json:"currencyType"`
	Platform     string `json:"platform"`
	ProductId    string `json:"productId"`
	GoodsPrice   int    `json:"goodsPrice"`
	OutTradeNo   string `json:"outTradeNo"`
}

type VirtualPaymentResult struct {
	SignData  string `json:"signData"`
	PaySig    string `json:"paySig"`
	Signature string `json:"signature"`
}

type VirtualPaymentNotify struct {
	ToUserName   string                      `json:"ToUserName"`
	FromUserName string                      `json:"FromUserName"`
	CreateTime   int64                       `json:"CreateTime"`
	MsgType      string                      `json:"MsgType"`
	Event        string                      `json:"Event"`
	MiniGame     VirtualPaymentMiniGameParam `json:"MiniGame"`
}

type VirtualPaymentMiniGameParam struct {
	Payload     string `json:"Payload"`
	PayEventSig string `json:"PayEventSig"`
}

type MiniGamePayload struct {
	OpenId        string                `json:"OpenId"`
	Env           int                   `json:"Env"`
	OutTradeNo    string                `json:"OutTradeNo"`
	WeChatPayInfo MiniGameWeChatPayInfo `json:"WeChatPayInfo"`
	GoodsInfo     MiniGameGoodsInfo     `json:"GoodsInfo"`
}

type MiniGameGoodsInfo struct {
	ProductId   string `json:"ProductId"`
	Quantity    int    `json:"Quantity"`
	ZoneId      string `json:"ZoneId"`
	OrigPrice   int    `json:"OrigPrice"`
	ActualPrice int    `json:"ActualPrice"`
	Attach      string `json:"Attach"`
	OrderSource int    `json:"OrderSource"`
}

type MiniGameWeChatPayInfo struct {
	MchOrderNo    string `json:"MchOrderNo"`
	TransactionId string `json:"TransactionId"`
}

type OrderInfo struct {
	OrderId   string  `json:"order_id"`
	PrepayId  string  `json:"prepay_id"`
	Price     float64 `json:"price"`
	ProductId string  `json:"product_id"`
	UserID    int     `json:"user_id"`
	RoleName  string  `json:"role_name"`
	PayStatus int     `json:"pay_status"`
}

type GetOrderInfoForm struct {
	OrderId string `json:"order_id"`
	Appid   string `json:"appid"`
}

type GetUnpaidOrderForm struct {
	OpenId string `json:"open_id"`
	Appid  string `json:"appid"`
}
