package schema

import (
	"time"
)

type BaseOrderForm struct {
	AppID        string  `json:"appid" binding:"required"`
	UserID       int     `json:"user_id,string" binding:"required"`
	ProductID    string  `json:"product_id" binding:"required"`
	RoleID       string  `json:"role_id" binding:"required"`
	Price        float64 `json:"price,string" binding:"required"`
	Device       string  `json:"device" binding:"required"`
	RoleName     string  `json:"role_name" binding:"required"`
	Extend       string  `json:"extend"`
	PlatForm     string  `json:"platform"`
	SdkVersion   string  `json:"sdk_version"`
	ServerID     string  `json:"server_id"`
	ServerName   string  `json:"server_name"`
	Vip          string  `json:"vip"`
	Level        string  `json:"level"`
	PayNotifyURL string  `json:"pay_notify_url"`
}

type WeChatExchangeForm struct {
	BaseOrderForm
	ProductName string `json:"product_name"`
}

type DouyinExchangeForm struct {
	BaseOrderForm
	ProductName string `json:"product_name"`
}

type GoogleExchangeForm struct {
	BaseOrderForm
}

type IOSExchangeForm struct {
	BaseOrderForm
}

type AliExchangeForm struct {
	BaseOrderForm
	ProductName string `json:"product_name"`
}

type HuaweiExchangeForm struct {
	BaseOrderForm
}

type MockPayExchangeForm struct {
	BaseOrderForm
}

type OrderVerify struct {
	AppID   string `json:"appid" binding:"required"`
	OrderID string `json:"order_id" binding:"required"`
}
type WeChatOrderVerify struct {
	AppID   string `json:"appid" binding:"required"`
	OrderID string `json:"order_id" binding:"required"`
}

type AliVerify struct {
	AppID   string `json:"appid" binding:"required"`
	OrderID string `json:"order_id" binding:"required"`
}

type GoogleVerifyForm struct {
	AppID            string `json:"appid" binding:"required"`
	PurchaseToken    string `json:"purchaseToken" binding:"required"`
	PayCurrency      string `json:"pay_currency"`
	PayAmount        string `json:"pay_amount"`
	DeveloperPayload string `json:"developerPayload" binding:"required"`
	ProductID        string `json:"productId" binding:"required"`
	PackageName      string `json:"packageName" binding:"required"`
	PurchaseData     string `json:"purchase_data" binding:"required"`
}

type IOSOrderVerify struct {
	AppID       string `json:"appid" binding:"required"`
	OrderID     string `json:"order_id" binding:"required"`
	Paper       string `json:"paper" binding:"required"`
	PayCurrency string `json:"pay_currency"`
	PayAmount   string `json:"pay_amount"`
}

type IOSVerifyV2 struct {
	AppID         string `json:"appid" binding:"required"`
	OrderID       string `json:"order_id"`
	TransactionID string `json:"transaction_id" binding:"required"`
	PayCurrency   string `json:"pay_currency"`
	PayAmount     string `json:"pay_amount"`
}

type HuaweiVerifyForm struct {
	AppID            string `json:"appid" binding:"required"`
	PurchaseToken    string `json:"purchaseToken" binding:"required"`
	PayCurrency      string `json:"pay_currency"`
	PayAmount        string `json:"pay_amount"`
	DeveloperPayload string `json:"developerPayload" binding:"required"`
	ProductID        string `json:"productId" binding:"required"`
	PackageName      string `json:"packageName" binding:"required"`
	PurchaseData     string `json:"purchase_data" binding:"required"`
	DataSignature    string `json:"dataSignature" binding:"required"`
}

type OrderPayResult struct {
	Status   int     `json:"status"`
	Currency string  `json:"currency"`
	Amount   float64 `json:"amount"`
	PayID    string  `json:"pay_id"`
}

type WeChatOrder struct {
	PrepayID  string `json:"prepayId"`
	PartnerID string `json:"partnerId"`
	TimeStamp string `json:"timestamp"`
	NonceStr  string `json:"nonceStr"`
	Package   string `json:"package"`
	Sign      string `json:"sign"`
	OrderID   string `json:"order_id"`
}

type DouyinOrder struct {
	OrderID string `json:"order_id"`
}

type RefundOrder struct {
	RoleID       string  `json:"role_id"`
	RoleName     string  `json:"role_name"`
	ServerID     string  `json:"server_id"`
	ServerName   string  `json:"server_name"`
	TotalOrders  int     `json:"total_orders"`
	TotalPrice   float64 `json:"total_price"`
	RefundOrders int     `json:"refund_orders"`
	RefundPrice  float64 `json:"refund_price"`
}

type RefundOrderInfo struct {
	OrderID     string    `json:"order_id"`
	PayID       string    `json:"pay_id"`
	ProductID   string    `json:"product_id"`
	Device      string    `json:"device"`
	PayWay      int       `json:"pay_way"`
	Price       float64   `json:"price"`
	PayCurrency string    `json:"pay_currency"`
	PayTime     time.Time `json:"pay_time"`
	RefundTime  time.Time `json:"refund_time"`
}

type TransactionInfo struct {
	AppAppleId                 int                    `json:"appAppleId"`
	BundleId                   string                 `json:"bundleId"`
	BundleVersion              string                 `json:"bundleVersion"`
	Environment                string                 `json:"environment"`
	SignedTransactionInfo      string                 `json:"signedTransactionInfo"`
	SignedTransactionInfoParse *SignedTransactionInfo `json:"signed_transaction_info_parse"`
}

type SignedTransactionInfo struct {
	TransactionId         string `json:"transactionId"`
	OriginalTransactionId string `json:"originalTransactionId"`
	BundleId              string `json:"bundleId"`
	ProductId             string `json:"productId"`
	PurchaseDate          int64  `json:"purchaseDate"`
	OriginalPurchaseDate  int64  `json:"originalPurchaseDate"`
	ExpiresDate           int    `json:"expiresDate"`
	Quantity              int    `json:"quantity"`
	Type                  string `json:"type"`
	AppAccountToken       string `json:"appAccountToken"`
	InAppOwnershipType    string `json:"inAppOwnershipType"`
	SignedDate            int64  `json:"signedDate"`
	Environment           string `json:"environment"`
	RevocationDate        int64  `json:"revocationDate"`
	RevocationReason      int    `json:"revocationReason"`
}

type NotificationInfo struct {
	NotificationType string          `json:"notificationType"`
	NotificationUUID string          `json:"notificationUUID"`
	Data             TransactionInfo `json:"data"`
	Version          string          `json:"version"`
	SignedDate       int             `json:"signedDate"`
}

type NotificationCallback struct {
	SignedPayload string `json:"signedPayload"`
}

type NotificationCallbackParams struct {
	NotificationType string                      `json:"notificationType"`
	Subtype          string                      `json:"subtype"`
	Data             NotificationCallbackData    `json:"data"`
	Summary          NotificationCallbackSummary `json:"summary"`
	Version          string                      `json:"version"`
	SignedDate       int                         `json:"signedDate"`
	NotificationUUID int                         `json:"notificationUUID"`
}

type NotificationCallbackData struct {
	AppAppleId    int    `json:"appAppleId"`
	BundleId      string `json:"bundleId"`
	BundleVersion string `json:"bundleVersion"`
	Environment   string `json:"environment"`
}
type NotificationCallbackSummary struct {
	RequestIdentifier      int      `json:"requestIdentifier"`
	Environment            string   `json:"environment"`
	AppAppleId             int      `json:"appAppleId"`
	BundleId               string   `json:"bundleId"`
	ProductId              string   `json:"productId"`
	StorefrontCountryCode  string   `json:"storefrontCountryCode"`
	StorefrontCountryCodes []string `json:"storefrontCountryCodes"`
	FailedCount            string   `json:"failedCount"`
	SucceededCount         string   `json:"succeededCount"`
}

type DownloadOrder struct {
	OrderId        string    `json:"order_id"`
	PayId          string    `json:"pay_id"`
	ProductId      string    `json:"product_id"`
	PayProductId   string    `json:"pay_product_id"`
	Price          float64   `json:"price"`
	Account        string    `json:"account"`
	ServerId       string    `json:"server_id"`
	ServerName     string    `json:"server_name"`
	Device         string    `json:"device"`
	RoleId         string    `json:"role_id"`
	RoleName       string    `json:"role_name"`
	Platform       string    `json:"platform"`
	CreateTime     time.Time `json:"create_time"`
	PayTime        time.Time `json:"pay_time"`
	NotifyTime     time.Time `json:"notify_time"`
	RefundTime     time.Time `json:"refund_time"`
	PayWay         string    `json:"pay_way"`
	PayStatus      string    `json:"pay_status"`
	NotifyStatus   string    `json:"notify_status"`
	OrderAttribute string    `json:"order_attribute"`
	VerifyStatus   string    `json:"verify_status"`
}

type OrderQueryParams struct {
	BaseOrderFields
	StartTime  string   `json:"start_time"`
	EndTime    string   `json:"end_time"`
	ChooseList []string `json:"choose_list" binding:"required"`
}

type BaseOrderFields struct {
	GameID         int      `json:"game_id" binding:"required"`
	OrderID        string   `json:"order_id"`
	PayID          string   `json:"pay_id"`
	PayWay         *int     `json:"pay_way,omitempty"`
	PayStatus      *int     `json:"pay_status,omitempty"`
	NotifyStatus   *int     `json:"notify_status,omitempty"`
	UserID         string   `json:"user_id"`
	RoleID         string   `json:"role_id"`
	RoleName       string   `json:"role_name"`
	ServerID       string   `json:"server_id"`
	ProductId      string   `json:"product_id"`
	Account        string   `json:"account"`
	Device         string   `json:"device"`
	OrderAttribute []string `json:"order_attribute"`
}
