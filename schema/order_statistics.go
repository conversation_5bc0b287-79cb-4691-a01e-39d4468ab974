package schema

type OrderStatisticsParams struct {
	GameID       int    `json:"game_id" binding:"required"`
	PayStartDate string `json:"pay_start_date"`
	PayEndDate   string `json:"pay_end_date"`
	Dimension    string `json:"dimension" binding:"required"`
	PayWay       int    `json:"pay_way" binding:"required"`
	PageNumber   int    `json:"pageNumber"`
	PageSize     int    `json:"pageSize"`
	Field        string `json:"field"` // 排序字段
	Order        string `json:"order"` // 排序方式
}

type StatisticsData struct {
	ProductID    string `json:"product_id"`
	PayDate      string `json:"pay_date"`
	PaidOrders   int    `json:"paid_orders"`
	RefundOrders int    `json:"refund_orders"`
	UpdateTime   string `json:"update_time"`
}

type OrderStatistics struct {
	GameID       int    `json:"game_id"`
	ProductID    string `json:"product_id"`
	PayWay       string `json:"pay_way"`
	PayDate      string `json:"pay_date"`
	PaidOrders   int    `json:"paid_orders"`
	RefundOrders int    `json:"refund_orders"`
}

type UpdateOrderStatisticsForm struct {
	GameID    int    `json:"game_id"`
	StartDate string `json:"startDate"`
	EndDate   string `json:"endDate"`
}
