package schema

type Project struct{}

type ProjectSummary struct {
	Project
	Id            int    `json:"id" label:"游戏ID"`
	Name          string `json:"name" label:"游戏名"`
	<PERSON>as         string `json:"alias" label:"游戏别名"`
	Type          string `json:"type" label:"游戏类型"`
	Status        int    `json:"status" label:"游戏状态"`
	ServiceMail   string `json:"service_mail" label:"游戏客服"`
	MysqlDatabase string `json:"mysql_database" label:"数据库 库名"`
}

type ProjectCreateForm struct {
	Project
	Name          string `json:"name" label:"游戏名"`
	Alias         string `json:"alias" label:"游戏别名"`
	Type          string `json:"type" label:"游戏类型"`
	Status        int    `json:"status" label:"游戏状态"`
	ServiceMail   string `json:"service_mail" label:"游戏客服"`
	MysqlDatabase string `json:"mysql_database" label:"数据库 库名"`
}
