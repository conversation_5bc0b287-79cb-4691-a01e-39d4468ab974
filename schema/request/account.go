package request

type InfoForm struct {
	AppId string `json:"appid" binding:"required"`
}

type RegisterWithEmail struct {
	AppId    string `json:"appid" binding:"required"`
	Email    string `json:"email" binding:"required,isEmail"`
	Password string `json:"password" binding:"required"`
	Captcha  string `json:"captcha"`
}

type ModifyPassword struct {
	AppId    string `json:"appid" binding:"required"`
	Email    string `json:"email" binding:"required,isEmail"`
	Password string `json:"password" binding:"required"`
	Captcha  string `json:"captcha" binding:"required"`
}

type LoginWithEmail struct {
	AppId    string `json:"appid" binding:"required"`
	Email    string `json:"email" binding:"required,isEmail"`
	Password string `json:"password" binding:"required"`
}

type LoginWithPhone struct {
	AppId    string `json:"appid" binding:"required"`
	AreaCode string `json:"area_code" binding:"required"`
	Phone    string `json:"phone" binding:"required,isPhoneNumber"`
	Captcha  string `json:"captcha" binding:"required"`
}

type LoginWithNumberVerify struct {
	AppId       string `json:"appid" binding:"required"`
	Token       string `json:"token" binding:"required"`
	AccessToken string `json:"access_token" binding:"required"`
}

type SendEmailCaptcha struct {
	AppId    string `json:"appid" binding:"required"`
	Language string `json:"language" binding:"required"`
	Email    string `json:"email" binding:"required,isEmail"`
}

type SendSMSCaptcha struct {
	AppId    string `json:"appid" binding:"required"`
	AreaCode string `json:"area_code" binding:"required"`
	Phone    string `json:"phone" binding:"required,isPhoneNumber"`
}

type ResetPassword struct {
	SendEmailCaptcha
}
