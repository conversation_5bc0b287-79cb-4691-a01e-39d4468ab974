package request

type GashLogin struct {
	GashID string `json:"gash_id" binding:"required"`
	RoleID string `json:"role_id" binding:"required"`
}

type GashPay struct {
	GashID       string  `json:"gash_id"`
	RoleID       string  `json:"role_id"`
	ServerID     string  `json:"server_id"`
	ProductID    string  `json:"product_id"`
	ProductName  string  `json:"product_name"`
	Price        float64 `json:"price"`
	Currency     string  `json:"currency"`
	PayReturnUrl string  `json:"pay_return_url"`
	PayNotifyUrl string  `json:"pay_notify_url"`
	Timestamp    string  `json:"timestamp"`
	Sign         string  `json:"sign"`
}

type GashNotify struct {
	AuthCode string `json:"AuthCode"`
}
