package request

type MiniGameLoginForm struct {
	APPID   string `json:"appid" binding:"required"`
	Device  string `json:"device" binding:"required"`
	Account string `json:"account" binding:"required"`
}

type MiniGameThirdLoginForm struct {
	APPID      string `json:"appid" binding:"required"`
	Device     string `json:"device" binding:"required"`
	Type       int    `json:"type,string" binding:"required"`
	Code       string `json:"code" binding:"required"`
	Platform   string `json:"platform" binding:"required"`
	SdkVersion string `json:"sdk_version"`
	MobileInfo string `json:"mobile_info"`
}

type MiniGameLoginResponse struct {
	Id        int                    `json:"id"`
	Account   string                 `json:"account"`
	Token     string                 `json:"token"`
	LoginTime string                 `json:"login_time"`
	Detail    map[string]interface{} `json:"detail"`
}

type MiniGameUserQueryRequestForm struct {
	APPID  string `json:"appid" binding:"required"`
	Type   int    `json:"type,string" binding:"required"`
	OpenId string `json:"open_id" binding:"required"`
}

type MiniGameUserQueryResponse struct {
	Account string `json:"account"`
}
