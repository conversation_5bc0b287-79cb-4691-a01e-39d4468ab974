package request

import "zeus/common/constants"

type GameAccount struct {
	AppID   string `json:"appid" binding:"required"`
	Account string `json:"account" binding:"required"`
}

type PayLimit struct {
	Region string `json:"region"`
	GameAccount
}

type Playable struct {
	GameAccount
}

func NewPayLimit() *PayLimit {
	return &PayLimit{
		Region: constants.China,
	}
}

type PayLimitInformation struct {
	AppID  string `json:"appid" binding:"required"`
	Region string `json:"region"`
}
