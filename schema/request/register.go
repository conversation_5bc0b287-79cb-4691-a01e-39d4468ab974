package request

type Register struct {
	APPID      string `json:"appid"`
	Device     string `json:"device" binding:"required"`
	Platform   string `json:"platform" binding:"required"`
	ForceRenew string `json:"force_renew"`
	MobileInfo string `json:"mobile_info"`
	Email      string `json:"email"`
	Phone      string `json:"phone"`
	Password   string `json:"password"`
	Name       string `json:"name"`
}

type Unregister struct {
	APPID   string `json:"appid" binding:"required"`
	Account string `json:"account" binding:"required"`
}

type UnregisterAPI struct {
	GameId  int    `json:"game_id" binding:"required"`
	Account string `json:"account" binding:"required"`
}

type RevokeAppleToken struct {
	APPID string `json:"appid"`
	Token string `json:"token"`
}
