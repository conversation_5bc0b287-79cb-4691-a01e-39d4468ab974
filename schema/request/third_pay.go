package request

type ThirdPayLogin struct {
	RoleID    string `json:"role_id" binding:"required"`
	ProjectID string `json:"project_id" binding:"required"`
}

type ThirdPayOrder struct {
	ProjectID    string  `json:"project_id" binding:"required"`
	PayWay       int     `json:"pay_way" binding:"required"`
	Region       string  `json:"region"`
	RoleID       string  `json:"role_id"`
	ServerID     string  `json:"server_id"`
	ProductID    string  `json:"product_id"`
	ProductName  string  `json:"product_name"`
	Price        float64 `json:"price"`
	Currency     string  `json:"currency"`
	PayReturnUrl string  `json:"pay_return_url"`
	PayNotifyUrl string  `json:"pay_notify_url"`
	Timestamp    string  `json:"timestamp"`
	Sign         string  `json:"sign"`
}

type ThirdPayOrderV2 struct {
	ProjectId    string `json:"project_id" binding:"required"`
	PayWay       int    `json:"pay_way" binding:"required"`
	Region       string `json:"region"`
	RoleId       string `json:"role_id"`
	ServerId     string `json:"server_id"`
	ActivityId   int    `json:"activity_id"`
	ProductId    int    `json:"product_id"`
	Currency     string `json:"currency"`
	PayReturnUrl string `json:"pay_return_url"`
	PayNotifyUrl string `json:"pay_notify_url"`
	Timestamp    string `json:"timestamp"`
	Sign         string `json:"sign"`
}

type ThirdPayQuery struct {
	ProjectID     string `json:"project_id" binding:"required"`
	PayWay        int    `json:"pay_way" binding:"required"`
	StartDateTime string `json:"StartDateTime"`
	EndDateTime   string `json:"EndDateTime"`
	PayID         string `json:"pay_id"`
}
