package request

type ThirdForm struct {
	APPID       string `json:"appid" binding:"required"`
	Type        int    `json:"type,string" binding:"required"`
	UnionId     string `json:"union_id"`
	Token       string `json:"token"`
	SecretToken string `json:"secret_token"`
	UserName    string `json:"user_name"`
}

type BindThirdForm struct {
	ThirdForm
	UserId int `json:"user_id,string"`
}

type UnBindThirdForm struct {
	ThirdForm
	UserId int `json:"user_id,string"`
}

type ThirdLogin struct {
	ThirdForm
	Device     string `json:"device" binding:"required"`
	Platform   string `json:"platform" binding:"required" `
	MobileInfo string `json:"mobile_info"`
}
