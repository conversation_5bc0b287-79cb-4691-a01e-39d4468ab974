package request

type XsollaSettings struct {
	MerchantID string `json:"merchant_id" form:"merchant_id" binding:"required"`
	ProjectID  string `json:"project_id" form:"project_id" binding:"required"`
}

type XsollaUser struct {
	Country string `json:"country"`
	Email   string `json:"email"`
	ID      string `json:"id"`
	Ip      string `json:"ip"`
	Name    string `json:"name"`
	Phone   string `json:"phone"`
	Zip     string `json:"zip"`
}

type XsollaItem struct {
	Sku        string        `json:"sku"`
	Type       string        `json:"type"`
	IsPreOrder bool          `json:"is_pre_order"`
	Quantity   int           `json:"quantity"`
	Amount     string        `json:"amount"`
	Promotions []interface{} `json:"promotions"`
}

type XsollaLogin struct {
	XsollaSettings
	User XsollaUser `json:"user"`
}

type XsollaPay struct {
	NotificationType string       `json:"notification_type"`
	Items            []XsollaItem `json:"items"`
	Order            struct {
		Id           int           `json:"id"`
		Mode         string        `json:"mode"`
		CurrencyType string        `json:"currency_type"`
		Currency     string        `json:"currency"`
		Amount       string        `json:"amount"`
		Status       string        `json:"status"`
		Platform     string        `json:"platform"`
		Comment      interface{}   `json:"comment"`
		InvoiceId    string        `json:"invoice_id"`
		Promotions   []interface{} `json:"promotions"`
	} `json:"order"`
	XsollaSettings
	User struct {
		ExternalID string `json:"external_id"`
		Email      string `json:"Email"`
	} `json:"user"`
}
