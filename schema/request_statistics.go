package schema

type RequestStatistics struct {
	RequestPath  string `json:"request_path"`
	RequestDate  string `json:"request_date"`
	RequestCount int64  `json:"request_count"`
}

type RequestStatisticsForm struct {
	GameID           int    `form:"game_id" binding:"required"`
	RequestDateStart string `form:"request_date_start"`
	RequestDateEnd   string `form:"request_date_end"`
	Dimension        string `form:"dimension" binding:"required"`
	PageSize         int    `form:"pageSize"`
	PageNumber       int    `form:"pageNumber"`
	Field            string `form:"field"`
	Order            string `form:"order"`
}

type QueryRequestPercentageForm struct {
	StartDate string `form:"start_date" binding:"required"`
	EndDate   string `form:"end_date" binding:"required"`
}

type RequestPercentage struct {
	ProjectAlias string `json:"project_alias"`
	RequestCount string `json:"request_count"`
	Percentage   string `json:"percentage"`
}
