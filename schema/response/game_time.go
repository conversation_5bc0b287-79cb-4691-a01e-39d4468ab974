package response

import (
	"fmt"

	"zeus/common/constants"
)

type Playable struct {
	CanPlay      bool   `json:"can_play"`
	Message      string `json:"message"`
	RemainTime   int    `json:"remain_time"`
	CostTime     int    `json:"cost_time"`
	RestrictType int    `json:"restrict_type"`
	Title        string `json:"title"`
	Description  string `json:"description"`
}

func NewPlayable(remainTime int, age int) *Playable {
	playable := Playable{}
	if age >= constants.ComeOfAge {
		playable.CanPlay = true
		playable.Message = "游戏时间不受限制"
		playable.RemainTime = remainTime
		playable.RestrictType = constants.UnRestricted
		playable.Title = "健康游戏提示"
		playable.Description = "当前为成年人账号"
		return &playable
	}

	if remainTime <= 0 {
		playable.CanPlay = false
		playable.Message = "游戏时间耗尽"
		playable.RemainTime = 0
		playable.RestrictType = constants.Restricted
		playable.Title = "健康游戏提示"
		playable.Description = "你当前为未成年账号，已被纳入防沉迷系统。根据国家相关规定，周五、周六、周日及法定节假日 20 点 - 21 点之外为健康保护时段。当前时间段无法游玩，请合理安排时间。"
		return &playable
	}

	playable.CanPlay = true
	playable.Message = "游戏允许时间"
	playable.RemainTime = remainTime
	playable.RestrictType = constants.Restricted
	playable.Title = "健康游戏提示"
	playable.Description = fmt.Sprintf("你当前为未成年账号，已被纳入防沉迷系统。根据国家相关规定，周五、周六、周日及法定节假日 20 点 - 21 点之外为健康保护时段。你今日游戏时间还剩余%d分钟，请注意适当休息。", remainTime/60)
	return &playable
}
