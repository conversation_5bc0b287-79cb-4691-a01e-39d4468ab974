package response

import (
	"time"
	"zeus/common/conf"

	"zeus/schema"
)

type PayLimit struct {
	PayMaxCap   float64         `json:"accumulation_limit"`
	PayOneCap   float64         `json:"transaction_limit"`
	PaidAmount  float64         `json:"accumulation_amount"`
	AllowAmount float64         `json:"remaining_amount"`
	StartTime   schema.DateTime `json:"start_time"`
	EndTime     schema.DateTime `json:"end_time"`
}

type PayLimitInformation struct {
	PayLimitInformation conf.PayLimitInformation
}

func NewPayLimit(PayMaxCap, PayOneCap, PaidAmount float64, StartTime, EndTime time.Time) *PayLimit {
	AllowAmount := PayMaxCap - PaidAmount
	return &PayLimit{
		PayMaxCap:   PayMaxCap,
		PayOneCap:   PayOneCap,
		AllowAmount: AllowAmount,
		PaidAmount:  PaidAmount,
		StartTime:   schema.DateTime{Time: StartTime},
		EndTime:     schema.DateTime{Time: EndTime},
	}
}
