package response

import (
	"sort"

	"zeus/models"
	"zeus/schema"
)

type ThirdLogin struct {
	ID        int             `json:"id,string"`
	Account   string          `json:"account"`
	Token     string          `json:"token"`
	LoginTime schema.DateTime `json:"login_time"`
	Roles     []*RoleInfo     `json:"roles,omitempty"`
}

func NewThirdLogin(user models.User, roles models.Roles) ThirdLogin {
	var roleInfos []*RoleInfo
	for _, role := range roles {
		roleInfo := &RoleInfo{
			ServerID:   role.ServerId,
			ServerName: role.ServerName,
			RoleID:     role.RoleId,
			RoleName:   role.RoleName,
			Vip:        role.Vip,
			Level:      role.Level,
			Extend:     role.Extend,
		}
		roleInfos = append(roleInfos, roleInfo)
	}

	return ThirdLogin{
		ID:        user.ID,
		Account:   user.Account,
		Roles:     roleInfos,
		Token:     user.Token,
		LoginTime: schema.DateTime{Time: user.LoginTime},
	}
}

type RoleInfo struct {
	ServerID   string `json:"server_id"`
	ServerName string `json:"server_name"`
	RoleID     string `json:"role_id"`
	RoleName   string `json:"role_name"`
	Vip        int    `json:"vip"`
	Level      int    `json:"level"`
	Extend     string `json:"extend"`
}

type ThirdLogins []ThirdLogin

func NewThirdLogins(users models.Users, roles models.Roles) ThirdLogins {
	var userBindThirds ThirdLogins
	for _, user := range users {
		rs := roles.GetRolesByUserID(user.ID)
		userBindThird := NewThirdLogin(user, rs)
		userBindThirds = append(userBindThirds, userBindThird)
	}
	sort.Sort(userBindThirds)
	return userBindThirds
}

func (t ThirdLogins) Len() int {
	return len(t)
}

func (t ThirdLogins) Less(i, j int) bool {
	return t[j].LoginTime.Time.Before(t[i].LoginTime.Time)
}

func (t ThirdLogins) Swap(i, j int) {
	t[i], t[j] = t[j], t[i]
}
