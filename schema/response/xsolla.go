package response

import (
	"strconv"

	"zeus/models"
)

type Attribute struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
type XsollaLogin struct {
	Attributes []Attribute `json:"attributes"`
	User       struct {
		Id   string `json:"id"`
		Name string `json:"name"`
	} `json:"user"`
}

func NewXsollaLogin(role *models.Role) *XsollaLogin {
	xl := XsollaLogin{}
	xl.User.Id = role.RoleId
	xl.User.Name = role.RoleName
	xl.Attributes = append(xl.Attributes, Attribute{"level", strconv.Itoa(role.Level)})
	return &xl
}

type XsollaRespError struct {
	Error struct {
		Code    string `json:"code"`
		Message string `json:"message"`
	} `json:"error"`
}
