package schema

type IosSubscription struct {
	BaseOrderForm
}

type GoogleSubscription struct {
	BaseOrderForm
}

type IosSubscriptionVerify struct {
	AppID       string `json:"appid" binding:"required"`
	OrderID     string `json:"order_id"`
	Paper       string `json:"paper" binding:"required"`
	PayCurrency string `json:"pay_currency" binding:"required"`
	PayAmount   string `json:"pay_amount" binding:"required"`
}

type GoogleSubscriptionVerify struct {
	AppID        string `json:"appid" binding:"required"`
	PurchaseSign string `json:"purchase_sign" binding:"required"`
	PurchaseData string `json:"purchase_data" binding:"required"`
	PayCurrency  string `json:"pay_currency"`
	PayAmount    string `json:"pay_amount"`
}

type PurchaseData struct {
	OrderID          string  `json:"orderId" binding:"required"`
	DeveloperPayload string  `json:"developerPayload" binding:"required"`
	PurchaseState    float64 `json:"purchaseState" binding:"required"`
	PackageName      string  `json:"packageName" binding:"required"`
	ProductID        string  `json:"productId" binding:"required"`
	PurchaseToken    string  `json:"purchaseToken" binding:"required"`
}

type GoogleSubscriptionNotify struct {
	Message struct {
		Data string `json:"data" binding:"required"`
	} `json:"message" binding:"required"`
	Subscription string `json:"subscription" binding:"required"`
}

type SubscriptionQueryParams struct {
	GameID         int      `json:"game_id" binding:"required"`
	OrderID        string   `json:"order_id"`
	PayID          string   `json:"pay_id"`
	PayWay         string   `json:"pay_way"`
	PayStatus      *int     `json:"pay_status"`
	NotifyStatus   *int     `json:"notify_status"`
	RoleID         string   `json:"role_id"`
	ServerID       string   `json:"server_id"`
	ProductId      string   `json:"product_id"`
	Account        string   `json:"account"`
	OrderAttribute int      `json:"order_attribute"`
	StartTime      string   `json:"start_time"`
	EndTime        string   `json:"end_time"`
	ChooseList     []string `json:"choose_list" binding:"required"`
}

type RenewSubscriptionCreate struct {
	GameId                int    `json:"game_id"`
	PayWay                int    `json:"pay_way"`
	OriginalTransactionId string `json:"original_transaction_id"`
	TransactionId         string `json:"transaction_id"`
	OrderId               string `json:"order_id"`
	NotifyStatus          int    `json:"notify_status"`
	NotifyResult          string `json:"notify_result"`
	NotificationType      string `json:"notification_type"`
	NotificationInfo      string `json:"notification_info"`
	NotificationUUID      string `json:"notification_uuid"`
	ExpiresDate           int    `json:"expires_date"`
}

type GoogleSubscriptionNotificationResponse struct {
	StartTimeMillis             string `json:"startTimeMillis"`
	ExpiryTimeMillis            string `json:"expiryTimeMillis"`
	AutoRenewing                bool   `json:"autoRenewing"`
	PriceCurrencyCode           string `json:"priceCurrencyCode"`
	PriceAmountMicros           string `json:"priceAmountMicros"`
	CountryCode                 string `json:"countryCode"`
	DeveloperPayload            string `json:"developerPayload"`
	PaymentState                int    `json:"paymentState"`
	OrderId                     string `json:"orderId"`
	AcknowledgementState        int    `json:"acknowledgementState"`
	Kind                        string `json:"kind"`
	ObfuscatedExternalAccountId string `json:"obfuscatedExternalAccountId"`
	LinkedPurchaseToken         string `json:"linkedPurchaseToken"`
}
