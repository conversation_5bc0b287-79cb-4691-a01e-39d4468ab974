package schema

type WarningConfigForm struct {
	ProjectID              int    `json:"project_id" binding:"required"`
	FreshChatUrl           string `json:"freshchat_url" binding:"required"`
	FreshChatToken         string `json:"freshchat_token" binding:"required"`
	HourlyWarningSwitch    bool   `json:"hourly_warning_switch" binding:"required"`
	HourlyWarningThreshold int    `json:"hourly_warning_threshold"`
	DailyWarningSwitch     bool   `json:"daily_warning_switch" binding:"required"`
	DailyWarningThreshold  int    `json:"daily_warning_threshold"`
	DingTalkToken          string `json:"ding_talk_token"`
	PhoneNumber            string `json:"phone_number"`
}
