package payment

import (
	"context"
	"time"

	"zeus/models"
	"zeus/schema"
	"zeus/utils"
)

type OrderHandler interface {
	CreateOrder(ctx context.Context, options ...OrderOptionFunc)
	QueryOrderStatus(ctx context.Context, orderId string) (orderStatus string, err error)
	VerifyOrder(ctx context.Context, orderId string) (orderStatus string, err error)
}

func NewOrder(orderForm schema.BaseOrderForm, options ...OrderOptionFunc) *models.Order {
	dbOrder := &models.Order{}

	dbOrder.UserId = orderForm.UserID
	dbOrder.RoleId = orderForm.RoleID
	dbOrder.Extend = orderForm.Extend
	dbOrder.Device = orderForm.Device
	dbOrder.RoleName = orderForm.RoleName
	dbOrder.CreateTime = time.Now()
	dbOrder.PayNotifyUrl = orderForm.PayNotifyURL
	dbOrder.Vip = utils.ConvertStringToInt(orderForm.Vip)
	dbOrder.Level = utils.ConvertStringToInt(orderForm.Level)
	dbOrder.Platform = orderForm.PlatForm
	dbOrder.ServerId = orderForm.ServerID
	dbOrder.ServerName = orderForm.ServerName

	for _, o := range options {
		o(dbOrder)
	}

	return dbOrder
}

type OrderOptionFunc func(*models.Order)

func WithPayWay(payWay int) OrderOptionFunc {
	return func(order *models.Order) {
		order.PayWay = payWay
	}
}
