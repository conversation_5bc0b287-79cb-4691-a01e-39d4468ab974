package account

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"zeus/common/conf"
	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/email"
	"zeus/pkg/redis"
	"zeus/pkg/sms"
	"zeus/schema/request"
	"zeus/utils"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type Server struct {
	c  *gin.Context
	db *gorm.DB
}

func NewServer(c *gin.Context) *Server {
	db := c.MustGet("db").(*gorm.DB)
	return &Server{
		c:  c,
		db: db,
	}
}

func (s *Server) create(account *models.Account) error {
	account.RegisterTime = time.Now()
	account.LastLoginTime = time.Now()
	account.UnionId = s.generateAccountId()
	account.LastLoginIP = s.c.ClientIP()
	account.RegisterIP = s.c.ClientIP()
	account.Token = generateToken()
	return s.db.Create(account).Error
}

func (s *Server) generateAccountId() string {
	for {
		random := rand.New(rand.NewSource(time.Now().UnixNano()))
		accountId := strconv.Itoa(random.Intn(**********))
		account, _ := s.FindByUnionId(accountId)
		if account.ID == 0 {
			return accountId
		}
	}
}

func (s *Server) refreshToken(account *models.Account) {
	account.Token = generateToken()
	s.db.Save(account)
}

func (s *Server) find(args map[string]interface{}) (*models.Account, error) {
	var account models.Account
	err := s.db.Where(args).First(&account).Error
	return &account, err
}

func (s *Server) FindByUnionId(unionId string) (*models.Account, error) {
	args := map[string]interface{}{
		"union_id": unionId,
	}
	return s.find(args)
}

func (s *Server) UpdateThirdUserName(unionId string, _type int, userName string) error {
	return models.UpdateThirdUserName(s.db, unionId, _type, constants.BoundStatus, userName)
}

func (s *Server) SaveAccount(account *models.Account) error {
	err := s.db.Save(account).Error
	return err
}

func (s *Server) CreateAccountWithEmail(v *request.RegisterWithEmail) (*models.Account, error) {
	account := &models.Account{
		Email:    &v.Email,
		Password: encryption(v.Password),
	}
	err := s.create(account)
	return account, err
}

func (s *Server) GetAccountByEmail(email string) (*models.Account, error) {
	args := map[string]interface{}{
		"email": email,
	}
	return s.find(args)
}

func (s *Server) ModifyPassword(v *request.ModifyPassword) error {
	return s.db.Model(&models.Account{}).Where("email = ?", v.Email).Update("password", encryption(v.Password)).Error
}

func (s *Server) LoginWithEmail(v *request.LoginWithEmail) (*models.Account, error) {
	args := map[string]interface{}{
		"email": v.Email,
	}
	account, err := s.find(args)
	if err != nil {
		return nil, err
	}
	if !checkPassword(v.Password, account.Password) {
		return nil, errors.New("密码错误")
	}
	s.refreshToken(account)
	return account, err
}

func (s *Server) LoginWithPhone(v *request.LoginWithPhone) (*models.Account, error) {
	args := map[string]interface{}{
		"area_code": v.AreaCode,
		"phone":     v.Phone,
	}
	account, err := s.find(args)
	if err == nil {
		s.refreshToken(account)
		return account, nil
	}
	if err == gorm.ErrRecordNotFound {
		account.Phone = &v.Phone
		account.AreaCode = &v.AreaCode
		err = s.create(account)
	}
	if err != nil {
		return nil, err
	}
	return account, err
}

func (s *Server) LoginWithNumberVerify(areaCode string, phoneNumber string) (*models.Account, error) {
	args := map[string]interface{}{
		"area_code": areaCode,
		"phone":     phoneNumber,
	}
	account, err := s.find(args)
	if err == nil {
		s.refreshToken(account)
		return account, nil
	}
	if err == gorm.ErrRecordNotFound {
		account.Phone = &phoneNumber
		account.AreaCode = &areaCode
		err = s.create(account)
	}
	if err != nil {
		return nil, err
	}
	return account, err
}

func SendEmailCaptcha(ctx context.Context, appId string, language string, emailAddress string) error {
	client := email.NewClient(
		conf.Conf.EmailService.User,
		conf.Conf.EmailService.Sender,
		conf.Conf.EmailService.Password,
		conf.Conf.EmailService.Host,
	)

	template, err := conf.Conf.EmailService.Templates.GetTemplate(language)
	if err != nil {
		return err
	}

	code := GenerateCode()
	key := fmt.Sprintf("%s_captcha_%s", appId, emailAddress)
	redis.Set(key, code, constants.EmailCaptchaTTL*time.Second)

	return client.SendMessage(
		template.Subject,
		fmt.Sprintf(template.Body, code),
		[]string{emailAddress},
	)
}

func SendSMSCaptcha(ctx context.Context, appId string, areaCode string, phoneNumber string) error {
	client := sms.NewClient(conf.Conf.SMS.AccessKey, conf.Conf.SMS.SecretKey)
	area, err := conf.Conf.SMS.Areas.GetAreaByCode(areaCode)
	if err != nil {
		return err
	}
	code := GenerateCode()
	phoneNumber = fmt.Sprintf("%s%s", strings.Replace(areaCode, "+", "", 1), phoneNumber)
	key := fmt.Sprintf("%s_captcha_%s", appId, phoneNumber)
	redis.Set(key, code, constants.MobileCaptchaTTL*time.Second)
	return client.SendMessage(phoneNumber, code, area.SignName, area.TemplateCode)
}

func CheckCaptchaValid(appid, k, v string) bool {
	return redis.Get(fmt.Sprintf("%s_captcha_%s", appid, k)) == v
}

func generateToken() string {
	return utils.GenerateRandomStr(16)
}

func encryption(s string) string {
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(s), bcrypt.DefaultCost)
	return string(hashedPassword)
}

func checkPassword(password, hashedPassword string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	if err == nil {
		return true
	}
	return false
}

func GenerateCode() string {
	if conf.Conf.Debug {
		return "6666"
	}
	return fmt.Sprintf("%04v", rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(10000))
}
