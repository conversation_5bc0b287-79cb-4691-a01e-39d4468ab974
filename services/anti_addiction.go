package services

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"zeus/common"
	"zeus/common/conf"
	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	"zeus/pkg/anti_addiction"
	"zeus/pkg/idno"
	"zeus/pkg/mapper"
	"zeus/services/settings/converter"
	"zeus/services/settings/other"
	"zeus/utils"

	"github.com/gin-gonic/gin"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"
)

type AntiAddictionServices struct {
	DB        *gorm.DB
	AppId     string    `json:"appid"`
	Account   string    `json:"account"`
	IDNo      string    `json:"id_no"`
	Name      string    `json:"name"`
	Region    string    `json:"region"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	user      *models.User
}

func (aas *AntiAddictionServices) InitAntiAddictionServices(c *gin.Context, v interface{}) {
	mapper.Map(aas, v)
	aas.DB = c.MustGet("db").(*gorm.DB)
}

func (aas *AntiAddictionServices) GetPaidAmount() float64 {
	user, _ := aas.getUser()
	totalPrice, _ := models.GetTotalPrice(aas.DB, user.UniqueIdentity, constants.PaymentSuccess, aas.StartTime, aas.EndTime)
	return totalPrice
}

func (aas *AntiAddictionServices) getUser() (*models.User, error) {
	if aas.user == nil {
		var err error
		aas.user, err = models.GetUserByAccount(aas.DB, aas.Account)
		if err != nil {
			return nil, err
		}
	}
	return aas.user, nil
}

func (aas *AntiAddictionServices) GetUserAge() (age int, err error) {
	user, err := aas.getUser()
	if err != nil {
		return -1, err
	}
	age = idno.GetAgeByBirthDayYMD(user.Birthday)
	return
}

func (aas *AntiAddictionServices) GetPayCap() (PayMaxCap, PayOneCap float64, err error) {
	age, err := aas.GetUserAge()
	if err != nil {
		return
	}

	payLimit := getPayLimit(aas.Region)

	PayOneCap, PayMaxCap = payLimit.GetPayCap(age)
	return
}

func getPayLimit(region string) PayLimit {
	switch {
	case region == constants.Japan:
		return &PayLimitInJapan{}
	default:
		return &PayLimitInChina{}
	}
}

type PayLimit interface {
	GetPayCap(age int) (PayOneCap float64, PayMaxCap float64)
}

type PayLimitInJapan struct {
}

func (p *PayLimitInJapan) GetPayCap(age int) (PayOneCap float64, PayMaxCap float64) {
	switch {
	case age < 16:
		return 0, 5000
	case 16 <= age && age < 18:
		return 0, 20000
	default:
		return 0, constants.MaxValue
	}
}

type PayLimitInChina struct {
}

func (p *PayLimitInChina) GetPayCap(age int) (PayOneCap float64, PayMaxCap float64) {
	switch {
	case age < 8:
		return 0, 0
	case 8 <= age && age < 16:
		return 5000, 20000
	case 16 <= age && age < 18:
		return 10000, 40000
	default:
		return constants.MaxValue, constants.MaxValue
	}
}

func (aas *AntiAddictionServices) GetPlayTime(age int) (int, error) {
	if age >= constants.ComeOfAge {
		return constants.MaxValue, nil
	}
	return GetPlayableTimeSlots(), nil
}

func CheckDateTimeIsHoliday(t time.Time) bool {
	date := t.Format("2006-01-02")
	if slices.Contains[string](conf.Conf.AntiAddiction.FestivalTime, date) {
		return true
	}

	if slices.Contains[string](conf.Conf.AntiAddiction.Exception, date) {
		return false
	}

	if slices.Contains[time.Weekday]([]time.Weekday{time.Friday, time.Saturday, time.Sunday}, t.Weekday()) {
		return true
	}
	return false
}

func GetPlayableTimeSlots() int {
	now := time.Now()

	if !CheckDateTimeIsHoliday(now) {
		return 0
	}
	startTime, _ := time.ParseInLocation(time.DateTime,
		fmt.Sprintf("%s %s", now.Format(time.DateOnly), conf.Conf.AntiAddiction.StartTime), now.Location())

	endTime, _ := time.ParseInLocation(time.DateTime,
		fmt.Sprintf("%s %s", now.Format(time.DateOnly), conf.Conf.AntiAddiction.EndTime), now.Location())

	if now.After(startTime) && now.Before(endTime) {
		return int(endTime.Sub(now).Seconds()) - 1 // 让小盆友少玩一秒
	} else {
		return 0
	}
}

func CheckName(name string) bool {
	if len(name) < 4 { // 一个汉字占两个字节，使用len统计长度时一个汉字算两个字符
		return false
	}
	isOk, _ := regexp.MatchString("[^[\\u4e00-\\u9fa5.·]]*$", name)
	return !isOk
}

func BingAccount(db *gorm.DB, account string, birthDayYMD string, UniqueIdentity string) error {
	user, err := models.GetUserByAccount(db, account)
	if err != nil {
		return err
	}

	user.Birthday = birthDayYMD
	user.UniqueIdentity = UniqueIdentity
	user.IsRealNameAuthentication = constants.Authorized
	err = models.SaveUser(db, user)
	if err != nil {
		return err
	}
	return nil
}

type AntiAddiction interface {
	Check(ctx context.Context, name, idNo string) (bool, string)
	LogOut(ctx context.Context, db *gorm.DB, account string, status int)
}

type AntiAddictionAilYun struct {
	appCode string
}

func (a *AntiAddictionAilYun) Check(ctx context.Context, name, idNo string) (bool, string) {
	identity, err := models.GetIdentityByIdCard(common.GetInfoDB(), idNo)
	if err == nil && identity.Name == name {
		return true, utils.MD5(idNo)
	}
	result, err := anti_addiction.NewAilYun(a.appCode).Check(ctx, name, idNo)
	if err != nil {
		log.Error(ctx, "实名认证失败", "error", err)
		return false, ""
	}
	if result {
		_ = models.AddIdentity(common.GetInfoDB(), idNo, name)
	}

	return result, utils.MD5(idNo)
}

func (a *AntiAddictionAilYun) LogOut(ctx context.Context, db *gorm.DB, account string, status int) {
	log.Warn(ctx, "AntiAddictionAilYun 不支持上报用户上下线行为数据")
}

type AntiAddictionPublicity struct {
	publicityAppId     string
	publicityBizId     string
	publicitySecretKey string
}

func (a *AntiAddictionPublicity) Check(ctx context.Context, name, idNo string) (bool, string) {
	ai := utils.MD5(idNo + name)
	publicityBuResult, err := anti_addiction.NewPublicity(
		a.publicityAppId,
		a.publicityBizId,
		a.publicitySecretKey,
	).Check(ctx, ai, name, idNo)
	if err != nil {
		log.Error(ctx, "实名认证失败", "error", err)
	} else {
		if publicityBuResult.Status == anti_addiction.CheckFailed {
			return false, ""
		}

		if publicityBuResult.Status == anti_addiction.CheckSuccess {
			return true, publicityBuResult.Pi
		}
	}

	// 当中宣部认证失败后使用阿里云认证
	yun := AntiAddictionAilYun{conf.Conf.AilYunIdentifyCheckAppCode}
	return yun.Check(ctx, name, idNo)
}

func (a *AntiAddictionPublicity) LogOut(ctx context.Context, db *gorm.DB, account string, status int) {
	user, err := models.GetUserByAccount(db, account)
	if err != nil {
		log.Error(ctx, "LogOut get user by account failed", "error", err)
	}

	// 只有通过中宣部认证的才能拿到这个38位的唯一标识
	if len(user.UniqueIdentity) == 38 {
		item := anti_addiction.LogoutCollection{
			No: 1,
			Pi: user.UniqueIdentity,
			Si: user.Token,
			Ct: anti_addiction.AuthenticatedUser,
			Bt: status,
			Ot: time.Now().Unix(),
		}

		err = anti_addiction.NewPublicity(
			a.publicityAppId,
			a.publicityBizId,
			a.publicitySecretKey,
		).Logout(ctx, []anti_addiction.LogoutCollection{item})
		if err != nil {
			log.Error(ctx, "publicity logout failed", "error", err)
		}
	}
}

type AntiAddictionMock struct{}

func (a *AntiAddictionMock) Check(ctx context.Context, name, idNo string) (bool, string) {
	return true, utils.MD5(idNo)
}

func (a *AntiAddictionMock) LogOut(ctx context.Context, db *gorm.DB, account string, status int) {
	log.Warn(ctx, "AntiAddictionMock 不支持上报用户上下线行为数据")
}

func CreateAntiAddictionBehavior(appId string) AntiAddiction {
	var config other.AntiAddictionConfig
	converter.QueryByAppid(&config, appId)

	switch *config.CheckStrategy {
	case 1:
		return &AntiAddictionAilYun{conf.Conf.AilYunIdentifyCheckAppCode}
	case 2:
		return &AntiAddictionPublicity{*config.PublicityAppId, *config.PublicityBizId, *config.PublicitySecretKey}
	case 3:
		return &AntiAddictionMock{}
	default:
		return nil
	}
}

func GetBirthDayYMD(idNo string) string {
	return idNo[6:14]
}
