package services

import (
	"testing"
	"time"

	"zeus/common/conf"
	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/idno"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

type TestAntiAddictionValue struct {
	Account   string    `json:"account"`
	IDNo      string    `json:"id_no"`
	Name      string    `json:"name"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

func TestAntiAddictionServices(t *testing.T) {
	Convey("TestAntiAddictionServices", t, func() {
		db := new(gorm.DB)
		c := &gin.Context{}
		c.Set("db", db)
		os := new(AntiAddictionServices)
		v := TestAntiAddictionValue{
			IDNo: "50011219980801xxxx", Name: "张三",
		}

		os.InitAntiAddictionServices(c, &v)

		ApplyFunc(models.GetUserByAccount, func(db *gorm.DB, account string) (*models.User, error) {
			return &models.User{}, nil
		})

		Convey("GetPaidAmount", func() {
			ApplyFunc(models.GetTotalPrice, func(_ *gorm.DB, _ string, _ int, _, _ time.Time) (float64, error) {
				return 22, nil
			})

			output := os.GetPaidAmount()
			expected := float64(22)
			So(output, ShouldEqual, expected)
		})

		Convey("GetUserAge", func() {
			ApplyFunc(models.GetUserByAccount, func(_ *gorm.DB, _ string) (*models.User, error) {
				return &models.User{}, nil
			})
			ApplyFunc(idno.GetAgeByBirthDayYMD, func(_ string) int {
				return 18
			})

			output, _ := os.GetUserAge()
			expected := 18
			So(output, ShouldEqual, expected)
		})
	})
}

func TestCheckDateTimeIsHoliday(t *testing.T) {
	Convey("GetPlayableTimeSlots", t, func() {
		ApplyGlobalVar(&conf.Conf, &conf.Config{
			AntiAddiction: conf.AntiAddiction{
				FestivalTime: []string{"2023-10-01"},
				Exception:    []string{"2023-10-07"},
			},
		})
		Convey("CheckDateTimeIsHoliday", func() {
			t1, _ := time.Parse(time.DateTime, "2023-10-01 00:00:00")
			t2, _ := time.Parse(time.DateTime, "2023-10-07 00:00:00")
			t3, _ := time.Parse(time.DateTime, "2023-10-12 00:00:00")
			t4, _ := time.Parse(time.DateTime, "2023-10-13 00:00:00")
			t5, _ := time.Parse(time.DateTime, "2023-10-14 00:00:00")
			t6, _ := time.Parse(time.DateTime, "2023-10-15 00:00:00")
			t7, _ := time.Parse(time.DateTime, "2023-10-16 00:00:00")
			So(CheckDateTimeIsHoliday(t1), ShouldBeTrue)
			So(CheckDateTimeIsHoliday(t2), ShouldBeFalse)
			So(CheckDateTimeIsHoliday(t3), ShouldBeFalse)
			So(CheckDateTimeIsHoliday(t4), ShouldBeTrue)
			So(CheckDateTimeIsHoliday(t5), ShouldBeTrue)
			So(CheckDateTimeIsHoliday(t6), ShouldBeTrue)
			So(CheckDateTimeIsHoliday(t7), ShouldBeFalse)
		})
	})
}

func TestGetPlayableTimeSlots(t *testing.T) {
	Convey("GetPlayableTimeSlots", t, func() {
		ApplyGlobalVar(&conf.Conf, &conf.Config{
			AntiAddiction: conf.AntiAddiction{
				StartTime: "20:00:00",
				EndTime:   "21:00:00",
			},
		})
		func1 := ApplyFunc(CheckDateTimeIsHoliday, func(time.Time) bool {
			return true
		})

		Convey("在开始时间前，获取可玩时长", func() {
			ApplyFunc(time.Now, func() time.Time {
				now, _ := time.Parse(time.DateTime, "2023-10-13 00:00:00")
				return now
			})
			So(GetPlayableTimeSlots(), ShouldEqual, 0)
		})
		Convey("在结束时间后，获取可玩时长", func() {
			ApplyFunc(time.Now, func() time.Time {
				now, _ := time.Parse(time.DateTime, "2023-10-13 00:00:00")
				return now
			})
			So(GetPlayableTimeSlots(), ShouldEqual, 0)
		})
		Convey("在可玩时间内", func() {
			ApplyFunc(time.Now, func() time.Time {
				now, _ := time.Parse(time.DateTime, "2023-10-13 20:50:00")
				return now
			})
			So(GetPlayableTimeSlots(), ShouldEqual, 599)
		})
		func1.Reset()

		func2 := ApplyFunc(CheckDateTimeIsHoliday, func(time.Time) bool {
			return false
		})
		Convey("非节假日", func() {
			ApplyFunc(time.Now, func() time.Time {
				now, _ := time.Parse(time.DateTime, "2023-10-13 20:50:00")
				return now
			})
			So(GetPlayableTimeSlots(), ShouldEqual, 0)
		})
		func2.Reset()
	})
}

func TestGetPayCap(t *testing.T) {
	Convey("GetPayCap", t, func() {
		var once, total float64

		payLimit := getPayLimit(constants.China)

		once, total = payLimit.GetPayCap(0)
		So(once, ShouldEqual, 0)
		So(total, ShouldEqual, 0)

		once, total = payLimit.GetPayCap(-1)
		So(once, ShouldEqual, 0)
		So(total, ShouldEqual, 0)

		once, total = payLimit.GetPayCap(8)
		So(once, ShouldEqual, 5000)
		So(total, ShouldEqual, 20000)

		once, total = payLimit.GetPayCap(12)
		So(once, ShouldEqual, 5000)
		So(total, ShouldEqual, 20000)

		once, total = payLimit.GetPayCap(14)
		So(once, ShouldEqual, 5000)
		So(total, ShouldEqual, 20000)

		once, total = payLimit.GetPayCap(16)
		So(once, ShouldEqual, 10000)
		So(total, ShouldEqual, 40000)

		once, total = payLimit.GetPayCap(17)
		So(once, ShouldEqual, 10000)
		So(total, ShouldEqual, 40000)

		once, total = payLimit.GetPayCap(18)
		So(once, ShouldEqual, constants.MaxValue)
		So(total, ShouldEqual, constants.MaxValue)

		once, total = payLimit.GetPayCap(20)
		So(once, ShouldEqual, constants.MaxValue)
		So(total, ShouldEqual, constants.MaxValue)

		once, total = payLimit.GetPayCap(100)
		So(once, ShouldEqual, constants.MaxValue)
		So(total, ShouldEqual, constants.MaxValue)

		payLimit = getPayLimit(constants.Japan)

		once, total = payLimit.GetPayCap(0)
		So(once, ShouldEqual, 0)
		So(total, ShouldEqual, 5000)

		once, total = payLimit.GetPayCap(15)
		So(once, ShouldEqual, 0)
		So(total, ShouldEqual, 5000)

		once, total = payLimit.GetPayCap(16)
		So(once, ShouldEqual, 0)
		So(total, ShouldEqual, 20000)

		once, total = payLimit.GetPayCap(17)
		So(once, ShouldEqual, 0)
		So(total, ShouldEqual, 20000)

		once, total = payLimit.GetPayCap(18)
		So(once, ShouldEqual, 0)
		So(total, ShouldEqual, constants.MaxValue)

		once, total = payLimit.GetPayCap(100)
		So(once, ShouldEqual, 0)
		So(total, ShouldEqual, constants.MaxValue)
	})
}
