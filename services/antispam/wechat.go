package antispam

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"zeus/common/log"
	httpclient "zeus/pkg/http"
	"zeus/pkg/redis"
)

type WechatTextCheck struct {
	Appid  string `json:"appid"`
	Secret string `json:"secret"`
	Openid string `json:"openid"`
}

func NewWechatChecker(appid string, secret string, openid string) *WechatTextCheck {
	return &WechatTextCheck{
		Appid:  appid,
		Secret: secret,
		Openid: openid,
	}
}

type requestWechatForm struct {
	Content string `json:"content"`
	Version int    `json:"version"`
	Scene   int    `json:"scene"`
	OpenId  string `json:"openid"`
}

type WechatTextCheckResponse struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
	Detail  []struct {
		Strategy string `json:"strategy"`
		Errcode  int    `json:"errcode"`
		Suggest  string `json:"suggest"`
		Label    int    `json:"label"`
		Keyword  string `json:"keyword"`
		Prob     int    `json:"prob"`
	} `json:"detail"`
	TraceId string `json:"trace_id"`
	Result  struct {
		Suggest string `json:"suggest"`
		Label   int    `json:"label"`
	}
}

type accessTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

func getAccessToken(ctx context.Context, appid string, secret string) (string, error) {
	cacheKey := fmt.Sprintf("wechat_token_%v_%v", appid, secret)
	token := redis.Get(cacheKey)
	if token != "" {
		return token, nil
	}

	params := url.Values{}
	params.Set("appid", appid)
	params.Set("secret", secret)
	tokenUrl := "https://guard.topjoy.com/wechat/token" + "?" + params.Encode()
	resp, err := httpclient.GetWithTracer(ctx, tokenUrl, map[string]string{})
	if err != nil {
		return "", err
	}
	body, _ := io.ReadAll(resp.Body)
	defer resp.Body.Close()
	var accessToken accessTokenResponse
	_ = json.Unmarshal(body, &accessToken)

	redis.Set(cacheKey, accessToken.AccessToken, time.Duration(accessToken.ExpiresIn)*time.Second)
	return accessToken.AccessToken, nil
}

// Check 检查文本内容
func (w *WechatTextCheck) requestWechat(ctx context.Context, _ string, text string) (suggestion string, label int, err error) {
	token, err := getAccessToken(ctx, w.Appid, w.Secret)
	if err != nil {
		return "", 0, err
	}
	params := url.Values{}
	params.Set("access_token", token)

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	msgSecCheckUrl := "https://api.weixin.qq.com/wxa/msg_sec_check" + "?" + params.Encode()
	data := requestWechatForm{
		OpenId:  w.Openid,
		Content: text,
		Version: 2,
		Scene:   2,
	}

	jsonData, _ := json.Marshal(data)
	var resp *http.Response
	for i := 0; i < 3; i++ {
		resp, _ = httpclient.PostWithTracer(ctx, msgSecCheckUrl, headers, jsonData)
		if err == nil {
			break
		}
	}
	var respData []byte
	if respData, err = io.ReadAll(resp.Body); err != nil {
		return
	}
	var ret WechatTextCheckResponse
	if err = json.Unmarshal(respData, &ret); err != nil {
		return "", 0, err
	}
	if ret.Errcode != 0 {
		err = fmt.Errorf(ret.Errmsg)
		return "", 0, err
	}
	log.Info(ctx, "Request Wechat Text Check", "trace_id", ret.TraceId, "content", text, "suggestion", ret.Result.Suggest)
	return ret.Result.Suggest, ret.Result.Label, nil
}

func (w WechatTextCheck) Check(ctx context.Context, dataid, text string) (CheckTextResult, error) {
	suggestion, _, err := w.requestWechat(ctx, dataid, text)
	if err != nil {
		return CheckTextResult{}, err
	}

	if suggestion == "risky" {
		return CheckTextResult{DataId: dataid, CheckRet: 2, PunishType: 1, Result: ""}, nil
	}

	if suggestion == "review" {
		return CheckTextResult{DataId: dataid, CheckRet: 2, PunishType: 1, Result: ""}, nil
	}
	return CheckTextResult{DataId: dataid, CheckRet: 0, PunishType: 0, Result: text}, nil
}

var LabelEnumValues = map[int]string{
	100:   "正常",
	10001: "广告",
	20001: "时政",
	20002: "色情",
	20003: "辱骂",
	20006: "违法犯罪",
	20008: "欺诈",
	20012: "低俗",
	20013: "版权",
	21000: "其他",
}

func (w *WechatTextCheck) GetTextCheckInfo(ctx context.Context, text string) ([]CheckTextResultInfo, error) {
	var result []CheckTextResultInfo

	_, label, err := w.requestWechat(ctx, "dataid", text)
	if err != nil {
		return result, err
	}
	if labelValue, ok := LabelEnumValues[label]; ok {
		checkTextResultInfo := CheckTextResultInfo{
			LabelCategory: labelValue,
		}
		result = append(result, checkTextResultInfo)
	}
	return result, nil
}
