package antispam

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	httpclient "zeus/pkg/http"

	"github.com/tjfoc/gmsm/sm3"
)

type YiDunTextCheck struct {
	apiURL     string // 服务地址
	secretID   string // 产品密钥ID，产品标识
	secretKey  string // 产品私有密钥，服务端生成签名信息使用，请严格保管，避免泄露
	businessID string // 业务ID，易盾根据产品业务特点分配
}

type Label struct {
	Label     int        `json:"label"`
	Level     int        `json:"level"`
	Rate      float64    `json:"rate"`
	SubLabels []SubLabel `json:"subLabels"`
}

type SubLabel struct {
	SubLabel string  `json:"subLabel"`
	Details  Details `json:"details"`
}

type Details struct {
	HitInfos []HitInfo `json:"hitInfos"`
}

type HitInfo struct {
	Value     string     `json:"value"`
	Positions []Position `json:"positions"`
}

type Position struct {
	FieldName string `json:"fieldName"`
	StartPos  int    `json:"startPos"`
	EndPos    int    `json:"endPos"`
}

type DunResponse struct {
	Code   float64 `json:"code"`
	Msg    string  `json:"msg"`
	Result DunRespResult
}

type DunRespResult struct {
	Antispam DunRespResultAntispam
}

type DunRespResultAntispam struct {
	Suggestion float64 `json:"suggestion"`
	Labels     []Label `json:"labels"`
}

type CheckTextResultInfo struct {
	LabelCategory string   `json:"label_category"`
	Words         []string `json:"words"`
}

const (
	version = "v5.2"
)

var standardCode = map[string]string{
	"500070":    "一号领导人",
	"500040":    "核心领导人",
	"50000635":  "国内领导人相关",
	"500377":    "国外领导人相关",
	"500043":    "落马官员",
	"500014":    "敏感专项",
	"500042":    "邪教迷信",
	"50000637":  "反动分裂",
	"50000638":  "政治运动",
	"500044":    "舆情事件",
	"500041":    "英雄烈士",
	"50000639":  "社会事件",
	"50000636":  "政治违禁作品",
	"500045":    "政治综合",
	"50000640":  "国际局势",
	"50000641":  "涉国家机关",
	"50000642":  "涉军警",
	"500378":    "知名人物",
	"50000643":  "政治经济关联人物",
	"50000644":  "政治用语表述不规范",
	"500214":    "宗教相关",
	"500013":    "政治其他",
	"500015":    "政治相关",
	"100002":    "色情资源",
	"100003":    "性器官",
	"100004":    "色情聊骚",
	"100006":    "性行为",
	"100376":    "性用品",
	"10000640":  "色情人物",
	"10000617":  "性癖好",
	"100001":    "色情其他",
	"10000618":  "儿童色情",
	"400273":    "违禁物品",
	"400274":    "赌博",
	"400275":    "违禁工具",
	"400276":    "违禁行为",
	"400277":    "违禁毒品",
	"400446":    "欺诈",
	"400017":    "违禁其他",
	"400459":    "违禁人物",
	"40000629":  "违禁化学品",
	"40000630":  "违禁药品",
	"40000631":  "违禁作品",
	"40000634":  "儿童邪典",
	"40000635":  "恐怖灵异",
	"40000633":  "个人隐私",
	"40000632":  "野生动植物买卖",
	"30000294":  "暴恐人物",
	"30000295":  "恐怖组织",
	"30000296":  "暴恐事件",
	"30000297":  "极端主义",
	"300016":    "暴恐其他",
	"20000694":  "招聘广告",
	"20000695":  "金融广告",
	"20000696":  "电商微商",
	"20000697":  "教育培训",
	"20000698":  "医院广告",
	"20000699":  "旅游广告",
	"20000700":  "音乐广告",
	"20000704":  "流量造假",
	"20000706":  "影视资源",
	"200144":    "房产广告",
	"200009":    "引流广告",
	"200268":    "社交广告",
	"20000709":  "游戏广告",
	"200011":    "刷量广告",
	"200012":    "广告其他",
	"260052":    "涉医疗用语",
	"260053":    "迷信用语",
	"260054":    "需要凭证",
	"260056":    "涉嫌诱导消费者",
	"260057":    "涉嫌欺诈消费者",
	"260059":    "极限词",
	"260055":    "限时性用语",
	"260058":    "法律风险较高",
	"260279":    "价格夸大营销",
	"260280":    "效果性承诺或保证",
	"260281":    "网络营销涉嫌欺诈",
	"260282":    "权威性用语",
	"600018":    "人身攻击",
	"60000547":  "祖安谩骂",
	"60000548":  "地域黑",
	"600383":    "口头禅谩骂",
	"60000549":  "饭圈互撕",
	"600379":    "谩骂其他",
	"700355":    "中文无意义",
	"700356":    "英文无意义",
	"70000532":  "乱码无意义",
	"700019":    "灌水其他",
	"1100107":   "封建迷信",
	"1100106":   "自杀自残",
	"1100103":   "吃播（涉夸张浪费）",
	"1100105":   "腐文化相关",
	"1100104":   "涉黑相关",
	"1100102":   "拜金炫富",
	"1100380":   "劣迹艺人相关",
	"110000635": "同性交友",
	"110000636": "未成年相关",
	"1100101":   "涉价值观其他",
}

// NewYiDunChecker 创建DunChecker
func NewYiDunChecker(apiURL, secretID, secretKey, businessID string) *YiDunTextCheck {
	return &YiDunTextCheck{
		apiURL:     apiURL,
		secretID:   secretID,
		secretKey:  secretKey,
		businessID: businessID,
	}
}

func (d *YiDunTextCheck) genSignature(params url.Values) string {
	var paramStr string
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, key := range keys {
		paramStr += key + params[key][0]
	}
	paramStr += d.secretKey
	if params["signatureMethod"] != nil && params["signatureMethod"][0] == "SM3" {
		sm3Reader := sm3.New()
		sm3Reader.Write([]byte(paramStr))
		return hex.EncodeToString(sm3Reader.Sum(nil))
	} else {
		md5Reader := md5.New()
		md5Reader.Write([]byte(paramStr))
		return hex.EncodeToString(md5Reader.Sum(nil))
	}
}

// Check 检查文本内容
func (d *YiDunTextCheck) requestYiDun(ctx context.Context, dataid, text string) (suggestion int, labels []Label, err error) {
	params := url.Values{}
	params["secretId"] = []string{d.secretID}
	params["businessId"] = []string{d.businessID}
	params["version"] = []string{version}
	params["timestamp"] = []string{fmt.Sprintf("%d", time.Now().UnixNano()/(1000*1000))}
	params["nonce"] = []string{fmt.Sprintf("%d", rand.Int31())}
	params["dataId"] = []string{dataid}
	params["content"] = []string{text}
	params["signature"] = []string{d.genSignature(params)}

	formData := []byte(params.Encode())
	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}

	var resp *http.Response
	for i := 0; i < 3; i++ {
		resp, _ = httpclient.PostWithTracer(ctx, d.apiURL, headers, formData)
		if err == nil {
			break
		}
	}
	if err != nil {
		return
	}
	var respData []byte
	if respData, err = io.ReadAll(resp.Body); err != nil {
		return
	}
	var ret DunResponse
	if err = json.Unmarshal(respData, &ret); err != nil {
		return
	}
	if ret.Code != 200 {
		err = fmt.Errorf(ret.Msg)
		return
	}
	suggestion = int(ret.Result.Antispam.Suggestion)
	labels = ret.Result.Antispam.Labels
	return
}

func (d YiDunTextCheck) Check(ctx context.Context, dataid, text string) (CheckTextResult, error) {
	suggestion, labels, err := d.requestYiDun(ctx, dataid, text)
	if err != nil {
		return CheckTextResult{}, err
	}

	if suggestion == 0 {
		return CheckTextResult{DataId: dataid, CheckRet: 0, PunishType: 0, Result: text}, nil
	}

	if suggestion == 2 {
		return CheckTextResult{DataId: dataid, CheckRet: 2, PunishType: 1, Result: ""}, nil
	}

	for _, label := range labels {
		for _, subLabel := range label.SubLabels {
			for _, hitInfo := range subLabel.Details.HitInfos {
				for _, position := range hitInfo.Positions {
					if (position.EndPos + 1 - position.StartPos) == strings.Count(text, "") {
						return CheckTextResult{DataId: dataid, CheckRet: suggestion, PunishType: 1, Result: ""}, nil
					} else {
						textRune := []rune(text)
						for i := position.StartPos; i < position.EndPos; i++ {
							textRune[i] = '*'
						}
						text = string(textRune)
					}
				}
			}
		}
	}
	return CheckTextResult{DataId: dataid, CheckRet: suggestion, PunishType: 2, Result: text}, nil
}

func (d *YiDunTextCheck) GetTextCheckInfo(ctx context.Context, text string) ([]CheckTextResultInfo, error) {
	var result []CheckTextResultInfo

	_, labels, err := d.requestYiDun(ctx, "dataid", text)
	if err != nil {
		return result, err
	}
	for _, label := range labels {
		var checkTextResultInfo CheckTextResultInfo
		for _, s := range label.SubLabels {
			checkTextResultInfo.LabelCategory = standardCode[s.SubLabel]
			var hitWords []string
			for _, hit := range s.Details.HitInfos {
				hitWords = append(hitWords, hit.Value)
			}
			checkTextResultInfo.Words = hitWords
			result = append(result, checkTextResultInfo)
		}
	}
	return result, nil
}
