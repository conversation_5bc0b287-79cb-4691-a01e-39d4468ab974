package customer_complaint_warning

import (
	"archive/zip"
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/avast/retry-go"
	"github.com/robfig/cron/v3"

	"zeus/common/log"
	"zeus/models"
	zeusHttp "zeus/pkg/http"
	"zeus/pkg/notify"
	"zeus/pkg/redis"
	"zeus/services"
	"zeus/services/settings/converter"
	"zeus/services/settings/other"
)

const messageTemplate = "## ❗ 客诉告警：\n " +
	"* 项目： %s  \n " +
	"* 时间区间: %s \n " +
	"* 类型: %s \n " +
	"* 客诉单量: %d \n " +
	"* 告警阈值: %d "

type ReportID struct {
	ID   string     `json:"id"`
	Link ReportLink `json:"link"`
}
type Link struct {
	Link   ReportLink `json:"link"`
	From   string     `json:"from"`
	To     string     `json:"to"`
	Status string     `json:"status"`
}
type Reports struct {
	ID       string `json:"id"`
	Status   string `json:"status"`
	Interval string `json:"interval"`
	Links    []Link `json:"links"`
}

type DingTalkToken struct {
	Daily  string `json:"daily"`
	Hourly string `json:"hourly"`
}

func (r *Reports) GetConversationCount(ctx context.Context, reportID *ReportID, freshChatClient *FreshChatClient) int {
	count := 0
	// Status: COMPLETED 表示文件下载完成
	retry.Do(
		func() error {
			log.Debug(ctx, "获取reportsLinks数据", "Job", "客诉告警任务")

			reportLinks, err := freshChatClient.GetFreshChatReportLinks(ctx, reportID)
			if err != nil {
				return err
			}
			if reportLinks.Status != "COMPLETED" {
				return errors.New("GetFreshChatReportLinks status is not COMPLETED")
			}
			r = reportLinks
			return nil
		},
		retry.DelayType(func(n uint, err error, config *retry.Config) time.Duration {
			return retry.BackOffDelay(n, err, config)
		}),
		retry.Attempts(3),
		retry.Delay(1*time.Second),
	)

	for _, link := range r.Links {
		linesCount, err := link.Link.GetFileLinesCount(ctx)
		count += linesCount
		if err != nil {
			continue
		}
	}
	// 表头占一行
	if count > 1 {
		return count - 1
	} else {
		return count
	}
}

type ReportLink struct {
	Rel  string `json:"rel"`
	Href string `json:"href"`
}

func (rl *ReportLink) GetFileLinesCount(ctx context.Context) (int, error) {
	if rl.Href == "" {
		return 0, errors.New("请求压缩包的url 为空")
	}
	log.Debug(ctx, "开始请求report压缩包文件", "Job", "客诉告警任务", "method", "GET", "url", rl.Href)

	resp, err := http.Get(rl.Href)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()
	byt, _ := io.ReadAll(resp.Body)

	num := int64(len(byt))
	zipReader, _ := zip.NewReader(bytes.NewReader(byt), num)

	lineCount := 0

	for _, file := range zipReader.File {
		filePath, err := rl.GenerateCSVFile(ctx, file)
		if err != nil {
			log.Warn(ctx, fmt.Sprintf("下载report文件出错: %s", err), "Job", "客诉告警任务")
			continue
		}
		count, _ := GetFileLinesCount(ctx, filePath)
		lineCount += count
	}
	return lineCount, nil
}

func (rl *ReportLink) GenerateCSVFile(ctx context.Context, file *zip.File) (string, error) {
	rc, err := file.Open()
	if err != nil {
		return "", err
	}
	defer rc.Close()
	dest := "./"
	filePath := dest + file.Name
	err = os.MkdirAll(dest, 0o755)
	if err != nil {
		return "", err
	}
	w, err := os.Create(filePath)
	if err != nil {
		return "", err
	}
	_, err = io.Copy(w, rc)
	if err != nil {
		return "", err
	}
	rc.Close()
	return filePath, nil
}

func GetFileLinesCount(ctx context.Context, filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, err
	}
	defer func() {
		err := os.RemoveAll(filePath)
		if err != nil {
			log.Warn(ctx, fmt.Sprintf("读取行数之后删除csv文件出错: %s", err), "Job", "客诉告警任务")
		}
	}()
	fd := bufio.NewReader(file)
	count := 0
	for {
		_, err := fd.ReadString('\n')
		if err != nil {
			break
		}
		count++
	}
	return count, nil
}

func GetCustomerComplaint(ctx context.Context) error {
	log.Debug(ctx, "任务开始执行", "Job", "客诉告警任务")

	projects, _ := services.GetProjects()
	for _, project := range projects {
		go func(project *models.Project) {
			key := fmt.Sprintf("ComplaintWarning_ProjectID_%d", project.ID)
			if err := redis.Lock(key, time.Minute*5); err != nil {
				return
			}

			defer redis.UnLock(key)

			CustomerComplaintWarning(ctx, project)
		}(project)
	}
	return nil
}

func CustomerComplaintWarning(ctx context.Context, project *models.Project) error {
	var warningConfig other.ComplaintWarningConfig
	converter.QueryByProjectId(&warningConfig, project.ID)
	log.Warn(ctx, fmt.Sprintf("%s - 项目开始执行", project.Alias), "Job", "客诉告警任务")

	if !*warningConfig.HourlyWarningSwitch && !*warningConfig.DailyWarningSwitch {
		errMessage := "告警开关为关闭状态"
		log.Warn(ctx, errMessage, "Job", "客诉告警任务")
		return errors.New(errMessage)
	}

	freshChatClient := FreshChatClient{
		FreshChatUrl:   *warningConfig.FreshchatUrl,
		FreshChatToken: *warningConfig.FreshchatToken,
	}
	reportID, err := freshChatClient.GetFreshChatReportID(ctx)
	if err != nil {
		return err
	}
	var reportLinks Reports
	conversationCount := reportLinks.GetConversationCount(ctx, reportID, &freshChatClient)
	log.Warn(ctx, fmt.Sprintf("%s 项目获取前一小时客诉量为%d", project.Alias, conversationCount), "Job", "客诉告警任务")

	ComplaintAlreadyWarnedKey := fmt.Sprintf("%s_ComplaintWarnedAlready", project.Alias)   // 已经发送过日告警
	DailyConversationsCountKey := fmt.Sprintf("%s_DailyConversationsCount", project.Alias) // 日累计客诉量

	// 日累计告警
	if *warningConfig.DailyWarningSwitch {
		log.Warn(ctx, fmt.Sprintf("%s 项目客诉日告警开关为打开状态, 日告警阈值为%d", project.Alias, *warningConfig.DailyWarningThreshold), "Job", "客诉告警任务")

		timeLocal := time.FixedZone("CST", 3600*8)
		time.Local = timeLocal
		curHour := time.Now().Local().Hour()

		if curHour == 1 { // 1点， 开始统计新一天数据， 清空redis缓存
			log.Warn(ctx, fmt.Sprintf("清除redis中缓存计数字段: %s,%s", ComplaintAlreadyWarnedKey, DailyConversationsCountKey), "Job", "客诉告警任务")
			redis.Del(ComplaintAlreadyWarnedKey)
			redis.Del(DailyConversationsCountKey)
		}

		alreadyWarned := redis.Get(ComplaintAlreadyWarnedKey)

		if alreadyWarned != "1" {
			redisConversationCount, _ := strconv.Atoi(redis.Get(DailyConversationsCountKey))

			redisConversationCount += conversationCount
			log.Warn(ctx, fmt.Sprintf("日累计客诉量: %d", redisConversationCount), "Job", "客诉告警任务")

			if redisConversationCount >= *warningConfig.DailyWarningThreshold {
				if *warningConfig.DailyDingTalkToken == "" {
					errMessage := "日累计告警钉钉群Token未配置, 无法发送告警信息"
					log.Warn(ctx, errMessage, "Job", "客诉告警任务")
					return errors.New(errMessage)
				}

				err = sendDingWarning(ctx, project.Alias, "daily", *warningConfig.DailyDingTalkToken, redisConversationCount, *warningConfig.DailyWarningThreshold)
				if err != nil {
					log.Warn(ctx, fmt.Sprintf("日累计告警失败: %s", err), "Job", "客诉告警任务")
				} else {
					redis.Set(ComplaintAlreadyWarnedKey, "1", -1)
				}
			} else {
				redis.Set(DailyConversationsCountKey, redisConversationCount, -1)
			}
		}
	}
	// 小时告警
	if *warningConfig.HourlyWarningSwitch {
		log.Warn(ctx, fmt.Sprintf("%s 项目客诉小时告警开关为打开状态, 小时告警阈值为%d", project.Alias, *warningConfig.HourlyWarningThreshold), "Job", "客诉告警任务")

		if conversationCount >= *warningConfig.HourlyWarningThreshold {
			if *warningConfig.HourlyDingTalkToken == "" {
				errMessage := fmt.Sprintf("小时告警钉钉群Token未配置, 无法发送告警信息")
				log.Warn(ctx, errMessage, "Job", "客诉告警任务")
				return errors.New(errMessage)
			}
			err = sendDingWarning(ctx, project.Alias, "hourly", *warningConfig.HourlyDingTalkToken, conversationCount, *warningConfig.HourlyWarningThreshold)
			if err != nil {
				log.Warn(ctx, fmt.Sprintf("小时告警失败: %s", err), "Job", "客诉告警任务")
				return err
			}
		}
	}

	return nil
}

func sendDingWarning(ctx context.Context, projectName string, WarningType string, dingTalkToken string, conversationCount int, warningThresholdCount int) error {
	timeLocal := time.FixedZone("CST", 3600*8)
	time.Local = timeLocal
	nowDate := time.Now().Local().Add(-time.Hour * 1).Format("01月02日")
	nowHour := time.Now().Local().Hour()
	beforeOneHour := time.Now().Local().Add(-time.Hour * 1).Hour()

	var timeInterval string
	var WarningTypeTitle string
	switch WarningType {
	case "hourly":
		WarningTypeTitle = "小时累计单告警"
		timeInterval = fmt.Sprintf("%s  %d时-%d时", nowDate, beforeOneHour, nowHour)
	case "daily":
		WarningTypeTitle = "日累计单告警"
		timeInterval = fmt.Sprintf("%s", nowDate)
	}

	dingTalkMessage := fmt.Sprintf(messageTemplate, projectName, timeInterval, WarningTypeTitle, conversationCount, warningThresholdCount)

	d := notify.DingTalk{AccessToken: dingTalkToken}
	_, err := d.SendMarkDown(ctx, "客诉告警", dingTalkMessage)
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("%s 项目发送%s(dingding告警)失败 : %s", projectName, WarningType, dingTalkMessage), "Job", "客诉告警任务")
		return err
	}
	log.Warn(ctx, fmt.Sprintf("%s 项目发送%s(dingding告警)完成 : %s", projectName, WarningType, dingTalkMessage), "Job", "客诉告警任务")
	return nil
}

type FreshChatClient struct {
	FreshChatUrl   string
	FreshChatToken string
}

func (f *FreshChatClient) GenerateHeaders() map[string]string {
	headers := map[string]string{
		"Accept":        "application/json",
		"Authorization": fmt.Sprintf("Bearer %s", f.FreshChatToken),
	}
	return headers
}

func (f *FreshChatClient) HttpGet(ctx context.Context, rawID string) (*http.Response, error) {
	headers := f.GenerateHeaders()
	url := fmt.Sprintf("%s/reports/raw/%s", f.FreshChatUrl, rawID)

	log.Debug(ctx, "开始请求接口获取数据", "Job", "客诉告警任务", "method", "GET", "url", url)
	return zeusHttp.GetWithTracer(ctx, url, headers)
}

func (f *FreshChatClient) HttpPost(ctx context.Context, data map[string]string) (*http.Response, error) {
	bytesData, _ := json.Marshal(data)

	headers := f.GenerateHeaders()
	url := fmt.Sprintf("%s/reports/raw", f.FreshChatUrl)

	log.Warn(ctx, "开始请求接口获取数据", "Job", "客诉告警任务", "method", "POST", "url", url, "data", data)
	return zeusHttp.PostWithTracer(ctx, url, headers, bytesData)
}

func (f *FreshChatClient) GetFreshChatReportID(ctx context.Context) (*ReportID, error) {
	timeLayout := "2006-01-02T15:04:05.000Z"
	timeLocal := time.FixedZone("CST", 3600*8)
	time.Local = timeLocal
	endTime := time.Now().Local().UTC().Format(timeLayout)
	startTime := time.Now().Add(-time.Hour * 1).Local().UTC().Format(timeLayout)
	data := map[string]string{
		"start":  startTime,
		"end":    endTime,
		"event":  "Conversation-Created",
		"format": "csv",
	}

	response, err := f.HttpPost(ctx, data)
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("请求reportID接口失败: %s", err), "Job", "客诉告警任务")
		return nil, err
	}

	log.Warn(ctx, fmt.Sprintf("请求reportID接口成功，状态: %s", response.Status), "Job", "客诉告警任务")

	var result ReportID
	body, err := io.ReadAll(response.Body)
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("获取reportsID数据失败: %s", err), "Job", "客诉告警任务")
		return nil, err
	}
	defer response.Body.Close()

	err = json.Unmarshal(body, &result)
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("解析reportsID数据失败: %s", err), "Job", "客诉告警任务")
		return nil, err
	}
	log.Warn(ctx, fmt.Sprintf("解析reportsID数据成功: %s", result), "Job", "客诉告警任务")
	return &result, nil
}

func (f *FreshChatClient) GetFreshChatReportLinks(ctx context.Context, reportID *ReportID) (*Reports, error) {
	response, err := f.HttpGet(ctx, reportID.ID)
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("获取reportsLinks数据失败: %s", err), "Job", "客诉告警任务")
		return nil, err
	}
	defer response.Body.Close()

	body, _ := io.ReadAll(response.Body)
	if response.StatusCode != http.StatusOK {
		return nil, errors.New(string(body))
	}
	log.Warn(ctx, fmt.Sprintf("获取reportsLinks数据成功，状态: %s", response.Status), "Job", "客诉告警任务")

	var reports Reports
	err = json.Unmarshal(body, &reports)
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("[客诉告警任务]解析reportsLinks数据失败: %s", err), "Job", "客诉告警任务")
		return nil, err
	}
	log.Warn(ctx, fmt.Sprintf("[客诉告警任务]解析reportsLinks数据成功: %s", reports), "Job", "客诉告警任务")
	return &reports, nil
}

func CronGetCustomerComplaintTask(c *cron.Cron) {
	spec := "0 * * * *" // 每小时（整点）执行一次
	c.AddFunc(spec, func() {
		GetCustomerComplaint(context.Background())
	})
}
