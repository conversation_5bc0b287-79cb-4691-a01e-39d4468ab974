package services

import (
	"errors"
	"strconv"

	"zeus/common"
	"zeus/models"
	"zeus/services/game"

	"zeus/common/conf"

	"gorm.io/gorm"
)

func GetGameDBByProjectId(ProjectID int) (*gorm.DB, error) {
	pool := common.GetDBPool()
	db, ok := pool.Get(strconv.Itoa(ProjectID))
	if ok {
		return db, nil
	}

	p, err := models.GetProjectInfoById(common.GetInfoDB(), ProjectID)
	if err != nil {
		return nil, err
	}
	info := &common.DBInfo{
		Host:     conf.Conf.DB.Host,
		Port:     conf.Conf.DB.Port,
		Username: conf.Conf.DB.Username,
		Password: conf.Conf.DB.Password,
		Database: p.MysqlDatabase,
		Charset:  conf.Conf.DB.Charset,
	}
	err = pool.Add(strconv.Itoa(ProjectID), info)
	if err != nil {
		return nil, err
	}

	db, ok = pool.Get(strconv.Itoa(ProjectID))
	if !ok {
		return nil, errors.New("db not found")
	}

	return db, nil
}

func GetGameDBByAppID(appID string) (*gorm.DB, error) {
	g, err := game.GetGameByAppid(appID)
	if err != nil {
		return nil, err
	}

	return GetGameDBByProjectId(g.ProjectID)
}

func GetGameDBByXsollaInfo(XsollaProjectID string) (*gorm.DB, error) {
	projectId, err := game.GetProjectIdByXsollaProjectID(XsollaProjectID)
	if err != nil {
		return nil, err
	}

	return GetGameDBByProjectId(projectId)
}
