package services

import (
	"reflect"
	"testing"

	"zeus/common"
	"zeus/models"
	"zeus/services/game"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestDBPool(t *testing.T) {
	var pool *common.DBPool
	dbPool := make(map[string]interface{})
	ApplyMethod(reflect.TypeOf(pool), "Get", func(p *common.DBPool, k string) (*gorm.DB, bool) {
		_, ok := dbPool[k]
		return nil, ok
	})
	ApplyMethod(reflect.TypeOf(pool), "Add", func(p *common.DBPool, k string, v *common.DBInfo) error {
		dbPool[k] = ""
		return nil
	})
	ApplyFunc(models.GetProjectInfoById, func(db *gorm.DB, projectId int) (*models.Project, error) {
		p := models.Project{}
		return &p, nil
	})
	ApplyFunc(common.GetInfoDB, func() *gorm.DB {
		db := gorm.DB{}
		return &db
	})

	Convey("GetGameDBByAppID", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})

			_, output := GetGameDBByAppID("test_app_id")
			So(output, ShouldEqual, nil)
		})
	})

	Convey("GetGameDBByXsollaInfo", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(game.GetProjectIdByXsollaProjectID, func(XsollaMerchantID string) (int, error) {
				return 123, nil
			})

			_, output := GetGameDBByXsollaInfo("XsollaMerchantID")
			So(output, ShouldEqual, nil)
		})
	})
}
