package services

import (
	"zeus/models"
	"zeus/schema"
)

func UpdateDialTestingTarget(dbTarget *models.DialTestingTarget, target *schema.UpdateTarget) (*models.DialTestingTarget, error) {
	dbTarget.Name = target.Name
	dbTarget.Path = target.Path
	dbTarget.Protocol = target.Protocol
	dbTarget.Method = target.Method
	dbTarget.Content = target.Content
	dbTarget.Timeout = target.Timeout
	err := models.SaveDialTestingTarget(dbTarget)
	return dbTarget, err
}

func CreateDialTestingTarget(gameID int, target *schema.Targets) (*models.DialTestingTarget, error) {
	dbTarget := models.DialTestingTarget{}
	dbTarget.GameId = gameID
	dbTarget.Path = target.Path
	dbTarget.Name = target.Name
	dbTarget.Protocol = target.Protocol
	dbTarget.Method = target.Method
	dbTarget.Content = target.Content
	dbTarget.Timeout = target.Timeout
	err := models.CreateDialTestingTarget(&dbTarget)
	return &dbTarget, err
}
