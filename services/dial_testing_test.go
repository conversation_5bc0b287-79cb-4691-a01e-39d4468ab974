package services

import (
	"testing"

	"zeus/models"
	"zeus/schema"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestUpdateDialTesting(t *testing.T) {
	Convey("UpdateDialTestingTarget", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.SaveDialTestingTarget, func(*models.DialTestingTarget) error {
				return nil
			})
			output, err := UpdateDialTestingTarget(&models.DialTestingTarget{}, &schema.UpdateTarget{Targets: schema.Targets{Name: "test_name"}})
			So(output.Name, ShouldEqual, "test_name")
			So(err, ShouldBeNil)
		})
	})

	Convey("CreateDialTestingTarget", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.CreateDialTestingTarget, func(*models.DialTestingTarget) error {
				return nil
			})
			output, err := CreateDialTestingTarget(1, &schema.Targets{})
			So(output.GameId, ShouldEqual, 1)
			So(err, ShouldBeNil)
		})
	})
}
