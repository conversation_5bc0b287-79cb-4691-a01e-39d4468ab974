package download

import (
	"database/sql"
	"fmt"
	"os"

	"zeus/common/constants"
	"zeus/schema"
	"zeus/services"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
)

type Service struct {
	BasePath string
	FileName string
	FileType string
	Params   interface{}
}

var orderColumnMapping = map[string]string{
	"order_id":        "订单ID",
	"pay_id":          "交易单号",
	"pay_way":         "支付方式",
	"role_id":         "角色ID",
	"user_id":         "用户ID",
	"role_name":       "角色名称",
	"server_id":       "区服ID",
	"server_name":     "区服名称",
	"pay_status":      "支付状态",
	"device":          "设备",
	"notify_status":   "通知状态",
	"product_id":      "道具ID",
	"pay_product_id":  "支付道具ID",
	"order_attribute": "订单属性",
	"verify_status":   "验证状态",
	"price":           "价格",
	"account":         "用户账户",
	"platform":        "平台",
	"create_time":     "创建时间",
	"pay_time":        "支付时间",
	"notify_time":     "通知时间",
	"refund_time":     "退款时间",
}

var orderStatisticsMapping = map[string]string{
	"product_id":    "商品ID",
	"pay_date":      "支付时间",
	"paid_orders":   "已支付订单数",
	"paid_price":    "已支付总额",
	"refund_orders": "已退款订单数",
	"refund_price":  "已退款总额",
	"update_time":   "更新时间",
}

var subscriptionMapping = map[string]string{
	"order_id":        "订单ID",
	"pay_id":          "交易单号",
	"role_id":         "角色ID",
	"role_name":       "角色名称",
	"server_id":       "区服ID",
	"server_name":     "区服名称",
	"pay_status":      "支付状态",
	"device":          "设备",
	"notify_status":   "通知状态",
	"product_id":      "道具ID",
	"pay_product_id":  "支付道具ID",
	"order_attribute": "订单属性",
	"verify_status":   "验证状态",
	"price":           "价格",
	"account":         "用户账户",
	"platform":        "平台",
	"create_time":     "创建时间",
	"pay_time":        "支付时间",
	"notify_time":     "通知时间",
}

func NewService(fileName string, fileType string, params interface{}) *Service {
	return &Service{
		BasePath: "./download-files",
		FileName: fileName,
		FileType: fileType,
		Params:   params,
	}
}

func (s *Service) createTmpPath() {
	if _, err := os.Stat(s.BasePath); os.IsNotExist(err) {
		tmpDir, _ := os.MkdirTemp("", "order")
		s.BasePath = tmpDir
	}
}

type Data interface {
	Headers(interface{}, chan []interface{})
	Query(*gin.Context, int, int, interface{}) (*sql.Rows, []string)
}

type OrderDownload struct {
	Fields []string
}

func (o *OrderDownload) Query(c *gin.Context, pageNum int, pageSize int, params interface{}) (*sql.Rows, []string) {
	return services.GetDownloadOrder(c, pageNum, pageSize, params.(schema.OrderQueryParams)), params.(schema.OrderQueryParams).ChooseList
}

func (o *OrderDownload) Headers(params interface{}, ch chan []interface{}) {
	var header []interface{}
	for _, c := range params.(schema.OrderQueryParams).ChooseList {
		header = append(header, orderColumnMapping[c])
	}
	o.Fields = params.(schema.OrderQueryParams).ChooseList
	ch <- header
}

type OrderStatisticsDownload struct {
	Fields []string
}

func (os *OrderStatisticsDownload) Query(c *gin.Context, pageNum int, pageSize int, params interface{}) (*sql.Rows, []string) {
	return services.GetDownloadOrderStatistics(c, pageNum, pageSize, params.(schema.OrderStatisticsParams)), os.Fields
}

func (os *OrderStatisticsDownload) Headers(params interface{}, ch chan []interface{}) {
	var header []interface{}
	p := params.(schema.OrderStatisticsParams)
	fields := []string{"paid_orders", "paid_price", "refund_orders", "refund_price"}
	switch p.Dimension {
	case "product_id":
		fields = append([]string{"product_id"}, fields...)
	case "pay_date":
		fields = append([]string{"pay_date"}, fields...)
	case "all_dimension":
		fields = append(fields, "update_time")
		fields = append([]string{"product_id", "pay_date"}, fields...)
	}
	os.Fields = fields
	for _, c := range fields {
		header = append(header, orderStatisticsMapping[c])
	}
	ch <- header
}

type SubscriptionDownload struct {
	Fields []string
}

func (s *SubscriptionDownload) Query(c *gin.Context, pageNum int, pageSize int, params interface{}) (*sql.Rows, []string) {
	return services.GetDownloadSubscriptions(c, pageNum, pageSize, params.(schema.SubscriptionQueryParams)), s.Fields
}

func (s *SubscriptionDownload) Headers(params interface{}, ch chan []interface{}) {
	var header []interface{}
	for _, c := range params.(schema.SubscriptionQueryParams).ChooseList {
		header = append(header, subscriptionMapping[c])
	}
	s.Fields = params.(schema.SubscriptionQueryParams).ChooseList
	ch <- header
}

func loadDataToCh(c *gin.Context, data Data, ch chan []interface{}, params interface{}) {
	defer close(ch)

	pageSize := constants.QueryOrderMaxCount
	pageNum := 1

	data.Headers(params, ch)

	for {
		size := 0
		orderData, fields := data.Query(c, pageNum, pageSize, params)
		defer orderData.Close()

		valPointers := make([]interface{}, len(fields))

		for orderData.Next() {
			x := make([]interface{}, len(fields))
			for i := 0; i < len(fields); i++ {
				valPointers[i] = &x[i]
			}
			orderData.Scan(valPointers...)

			ch <- x
			size += 1
		}

		if size < constants.QueryOrderMaxCount {
			break
		}
		pageNum += 1
	}
}

func (s *Service) Download(c *gin.Context) (string, error) {
	s.createTmpPath()
	filePath := fmt.Sprintf("%s/%s", s.BasePath, s.FileName)
	f := excelize.NewFile()

	defer func() {
		if err := f.Close(); err != nil {
			return
		}
	}()

	sheetName := "Sheet1"
	idx := 1
	sw, _ := f.NewStreamWriter(sheetName)

	ch := make(chan []interface{})
	switch s.FileType {
	case "order":
		d := OrderDownload{}
		go loadDataToCh(c, &d, ch, s.Params)
	case "order_statistics":
		d := OrderStatisticsDownload{}
		go loadDataToCh(c, &d, ch, s.Params)
	case "subscription":
		d := SubscriptionDownload{}
		go loadDataToCh(c, &d, ch, s.Params)
	}

	for row := range ch {
		cell, err := excelize.CoordinatesToCellName(1, idx)
		if err != nil {
			return "", err
		}

		sw.SetRow(cell, row)
		idx += 1
	}
	sw.Flush()
	f.SaveAs(filePath)

	return filePath, nil
}
