package game

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"zeus/common"
	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/schema"

	"zeus/common/log"
)

type RedisKey struct {
	Key []string
	DB  *models.Game
}

func (r *RedisKey) GenerateRedisKeyByAppID(appid string) string {
	k := fmt.Sprintf("game_%s", appid)
	r.Key = append(r.Key, k)
	return k
}

func (r *RedisKey) GenerateRedisKeyByGameID(id int) string {
	k := fmt.Sprintf("game_%d", id)
	r.Key = append(r.Key, k)
	return k
}

func (r *RedisKey) CleanRedisGameInfo(ctx context.Context) {
	r.GenerateRedisKeyByGameID(r.DB.Id)
	r.GenerateRedisKeyByAppID(r.DB.Appid)
	for _, key := range r.Key {
		game := redis.Get(key)
		if game != "" {
			err := redis.Del(key)
			if err != nil {
				log.Error(ctx, "清除redis中game数据失败", "error", err.Error())
			}
		}
	}
}

func GetGames() []models.Game {
	db := common.GetInfoDB()
	var games []models.Game
	db.Find(&games)
	return games
}

func CreateGame(projectID int, game schema.GameInfoCreateForm) (*models.Game, error) {
	db := common.GetInfoDB()

	dbGame := models.Game{}
	dbGame.ProjectID = projectID
	dbGame.Name = game.Name
	dbGame.Appid = game.Appid
	dbGame.SecretKey = game.SecretKey
	dbGame.ServiceMail = game.ServiceMail
	dbGame.Platform = game.Platform
	dbGame.CreateTime = time.Now()
	err := models.CreateGame(db, &dbGame)
	return &dbGame, err
}

func UpdateGame(dbGame *models.Game, gameForm *schema.GameSummary) (*models.Game, string, error) {
	db := common.GetInfoDB()

	dbGame.Name = gameForm.Name
	dbGame.Type = gameForm.Type
	dbGame.Platform = gameForm.Platform
	dbGame.ServiceMail = gameForm.ServiceMail
	dbGame.Status = gameForm.Status
	funcName := "应用配置"
	dbGame.UpdateTime = time.Now()
	err := models.SaveGameInfo(db, dbGame)
	return dbGame, funcName, err
}

func GetGameInfoByAppId(appId string) schema.GameInfo {
	db := common.GetInfoDB()
	games := schema.GameInfo{}
	db.Where("appid = ?", appId).First(&games)
	return games
}

func GetProjectIdByXsollaProjectID(XsollaProjectID string) (int, error) {
	db := common.GetInfoDB()
	var setting models.Setting
	err := db.Where("name = ?", "xsolla_project_id").Where("value = ?", XsollaProjectID).Find(&setting).Error
	return setting.ProjectID, err
}

func GetGameIdsByXsollaProjectID(XsollaProjectID string) ([]int, error) {
	db := common.GetInfoDB()
	var settings []models.Setting

	var gameIds []int
	err := db.Where("name = ?", "xsolla_project_id").Where("value = ?", XsollaProjectID).Find(&settings).Error
	for _, s := range settings {
		gameIds = append(gameIds, s.GameId)
	}
	return gameIds, err
}

func GetGameByAppid(appid string) (*models.Game, error) {
	db := common.GetInfoDB()
	dbGame := models.Game{}

	RedisKey := new(RedisKey)
	key := RedisKey.GenerateRedisKeyByAppID(appid)

	game := redis.Get(key)
	if game != "" {
		err := json.Unmarshal([]byte(game), &dbGame)
		return &dbGame, err
	}

	err := db.Where("appid = ?", appid).First(&dbGame).Error
	if err == nil {
		data, _ := json.Marshal(&dbGame)
		_ = redis.Set(key, string(data), time.Second*constants.GameInfoTimeout)
	}

	return &dbGame, err
}

func GetGameInfoById(id int) (*models.Game, error) {
	db := common.GetInfoDB()
	dbGame := models.Game{}
	err := db.Model(&models.Game{}).Where("id = ?", id).First(&dbGame).Error

	return &dbGame, err
}

func GetGameById(id int) (*models.Game, error) {
	db := common.GetInfoDB()
	dbGame := models.Game{}

	RedisKey := new(RedisKey)
	key := RedisKey.GenerateRedisKeyByGameID(id)
	game := redis.Get(key)
	if game != "" {
		err := json.Unmarshal([]byte(game), &dbGame)
		return &dbGame, err
	}

	err := db.Model(&models.Game{}).Where("id = ?", id).First(&dbGame).Error
	if err == nil {
		data, _ := json.Marshal(&dbGame)
		_ = redis.Set(key, string(data), time.Second*constants.GameInfoTimeout)
	}

	return &dbGame, err
}

func QueryGamesByFilter(filter map[string]interface{}) ([]*models.Game, error) {
	db := common.GetInfoDB()
	return models.GetGames(db, filter)
}

func GetGameInfoByProjectID(id int) ([]schema.GameSummary, error) {
	db := common.GetInfoDB()
	var gameInfo []schema.GameSummary
	err := db.Model(&models.Game{}).Where("project_id = ?", id).Find(&gameInfo).Error

	return gameInfo, err
}

func GetSecretKey(appid string) (string, error) {
	game, err := GetGameByAppid(appid)
	if err != nil {
		return "", err
	}

	return game.SecretKey, err
}
