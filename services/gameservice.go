package services

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"time"

	"zeus/common/conf"
	"zeus/common/constants"
	"zeus/models"
	zeusHttp "zeus/pkg/http"
	"zeus/pkg/notify"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/services/settings/store"
	"zeus/utils"

	"zeus/common/log"

	"github.com/robfig/cron/v3"
	"gorm.io/gorm"
)

type NotifyOptions struct {
	IsAutoRetry bool
}

const messageTemplate = "## ❗ zeus通知发货失败：\n " +
	"* 应用： %s (%s) \n " +
	"* 订单ID: %s \n " +
	"* 渠道: %s \n " +
	"* 角色名称: %s \n " +
	"* 角色ID: %s \n " +
	"* 区服ID: %s \n " +
	"* 创建时间: %s \n " +
	"* 报错信息: %s"

const abnormalOrderMessageTemplate = "## ❗ Zeus疑似异常订单：\n " +
	"* 应用： %s \n " +
	"* 订单ID: %s \n " +
	"* 角色名称: %s \n " +
	"* 角色ID: %s \n " +
	"* 区服ID: %s \n " +
	"* 商品ID: %s \n " +
	"* 创建时间: %s \n " +
	"* 异常信息: %s"

const failedOrderMessageTemplate = "## ❗ Zeus订单支付失败：\n " +
	"* 订单ID: %s \n " +
	"* 支付渠道: %s \n " +
	"* 角色名称: %s \n " +
	"* 角色ID: %s \n " +
	"* 区服ID: %s \n " +
	"* 商品ID: %s \n " +
	"* 创建时间: %s \n " +
	"* 报错信息: %s"

func SendNotify(ctx context.Context, db *gorm.DB, order *models.Order, payNotifyKey string, isAutoRetry bool) error {
	if isAutoRetry {
		notifyInterval := getOrderNotifyInternal(order.NotifyCnt)
		newTime := order.NotifyTime.Add(time.Second * time.Duration(notifyInterval))
		if newTime.After(time.Now()) {
			log.Debug(ctx, "自动补单时间延长，该订单未到自动补单时间, 可等待自动补单或登录管理后台手动补单", "order", order)
			return nil
		}
	}
	// 未支付或者已经通知Game Server
	if order.PayStatus != constants.PaymentSuccess || order.NotifyStatus == constants.NotifySuccess {
		return errors.New("订单未支付或者已经通知Game Server，请登录管理后台查看订单支付状态")
	}
	// 通过订单ID加锁防止并发推送
	key := "order_id_" + order.OrderId
	err := redis.Lock(key, time.Second*30)
	if err != nil {
		log.Warn(ctx, err.Error(), "order", order)
		return nil
	}

	sign := utils.MD5(order.OrderId + order.ProductId + strconv.Itoa(order.UserId) + order.Extend + payNotifyKey)

	// 通知CP的信息
	notifyInfo := map[string]interface{}{
		"order_id":        order.OrderId,
		"product_id":      order.ProductId,
		"pay_time":        order.PayTime,
		"extend":          order.Extend,
		"user_id":         order.UserId,
		"order_attribute": order.OrderAttribute,
		"sign":            sign,
	}

	var channel string
	if payWay, ok := store.PaymentMapping[order.PayWay]; ok {
		channel = payWay.GetName()
	} else if order.PayWay == constants.PayWayWebPoints {
		channel = constants.PointsPayName
	} else {
		channel = "unknown"
	}

	notifyInfo["channel"] = channel
	notifyInfo["pay_amount"] = order.PayAmount
	notifyInfo["currency"] = order.PayCurrency

	if order.PayNotifyUrl == "" {
		log.Error(ctx, "游戏配置的通知url为空，无法发送通知Game Server请求", "notifyInfo", notifyInfo)
		return errors.New("支付回调Url为空，无法发送请求，请登录发行管理后台确认是否配置")
	}
	log.Debug(ctx, "发送通知Game Server请求", "pay_notify_url", order.PayNotifyUrl, "notifyInfo", notifyInfo)
	resp, _ := sendNotifyRequest(ctx, order.PayNotifyUrl, notifyInfo)
	notifyInfo["result"] = resp
	notifyInfo["pay_notify_url"] = order.PayNotifyUrl

	if notifyInfo["result"] == "success" {
		order.NotifyStatus = constants.NotifySuccess
	} else {
		notifyInfo["notify_cnt"] = order.NotifyCnt + 1
		order.NotifyStatus = constants.NotifyError
	}

	notifyInfoStr, _ := json.Marshal(notifyInfo)
	order.NotifyResult = string(notifyInfoStr) + "\n"
	order.NotifyCnt += 1
	order.NotifyTime = time.Now()

	if err := db.Updates(&order).Error; err != nil {
		return errors.New("通知Game Server完成，修改订单数据失败")
	}
	if resp != "success" {
		log.Error(ctx, "通知game server失败, 游戏服务端返回报错", "dbOrder", order, "err", resp)
		return fmt.Errorf("通知发货失败，服务端返回信息：%s, 请与游戏服务端确认", resp)
	}
	log.Debug(ctx, "通知game server成功，更新支付订单成功", "dbOrder", order)
	return nil
}

func SendSubscriptionNotify(ctx context.Context, db *gorm.DB, order *models.Subscription, payNotifyKey string, isAutoRetry bool, dbRenewSubscription *models.RenewSubscription) error {
	// 定时任务补单间隔周期越来越长
	if isAutoRetry {
		notifyInterval := getOrderNotifyInternal(order.NotifyCnt)
		newTime := order.NotifyTime.Add(time.Second * time.Duration(notifyInterval))
		if newTime.After(time.Now()) {
			log.Debug(ctx, "自动补单时间延长，该订单未到自动补单时间, 可等待自动补单或登录管理后台手动补单", "order", order)
			return nil
		}
	}

	if order.PayStatus != constants.PaymentSuccess || order.NotifyStatus == constants.NotifySuccess {
		return errors.New("订阅订单未支付或者已经通知Game Server, 请登录管理后台查看订单支付状态")
	}

	// 通过订单ID加锁防止并发推送
	key := "order_id_" + order.OrderId
	err := redis.Lock(key, time.Second*30)
	if err != nil {
		log.Warn(ctx, err.Error(), "order", order.OrderId)
		return nil
	}

	sign := utils.MD5(order.OrderId + order.ProductId + strconv.Itoa(order.UserId) + order.Extend + payNotifyKey)

	// 通知CP的信息
	notifyInfo := map[string]interface{}{
		"type":         "subscription",
		"order_id":     order.OrderId,
		"product_id":   order.ProductId,
		"pay_time":     order.PayTime,
		"extend":       order.Extend,
		"user_id":      order.UserId,
		"sign":         sign,
		"expires_date": order.ExpiresDate,
	}

	if order.PayNotifyUrl == "" {
		log.Error(ctx, "游戏配置的通知url为空，无法发送通知Game Server请求", "subscriptionNotifyInfo", notifyInfo)
		return errors.New("支付回调Url为空，无法发送请求，请登录发行管理后台确认是否配置")
	}

	log.Debug(ctx, "发送通知Game Server请求", "pay_notify_url", order.PayNotifyUrl, "subscriptionNotifyInfo", notifyInfo)
	resp, _ := sendNotifyRequest(ctx, order.PayNotifyUrl, notifyInfo)

	notifyInfo["result"] = resp
	notifyInfo["pay_notify_url"] = order.PayNotifyUrl
	if notifyInfo["result"] == "success" {
		order.NotifyStatus = constants.NotifySuccess
	} else {
		notifyInfo["notify_cnt"] = order.NotifyCnt + 1
		order.NotifyStatus = constants.NotifyError
	}

	order.NotifyCnt += 1
	order.NotifyTime = time.Now()

	if dbRenewSubscription.OrderId != "" {
		log.Debug(ctx, "开始更新续订数据", "notification_uuid", dbRenewSubscription.NotificationUUID)
		notifyInfoStr, _ := json.Marshal(notifyInfo)
		dbRenewSubscription.NotifyResult = string(notifyInfoStr)
		dbRenewSubscription.NotifyStatus = order.NotifyStatus
		err = models.SaveRenewSubscription(db, dbRenewSubscription)
		if err != nil {
			log.Error(ctx, "修改续订数据库失败", "dbRenewSubscription", dbRenewSubscription, "error", err.Error())
		}
	}

	if err := db.Updates(&order).Error; err != nil {
		log.Error(ctx, "通知Game Server完成，修改数据库失败", "order", order, "error", err.Error())
		return errors.New("通知Game Server完成，修改订单数据失败")
	}
	if resp != "success" {
		log.Error(ctx, "通知game server失败, 游戏服务端返回报错", "dbOrder", order, "err", resp)
		return fmt.Errorf("通知发货失败，服务端返回信息：%s, 请与游戏服务端确认", resp)
	}
	log.Debug(ctx, "通知game server成功，更新订阅订单成功", "dbOrder", order)
	return nil
}

func Notify(ctx context.Context, db *gorm.DB, dbOrder *models.Order, opts NotifyOptions) error {
	var paymentConfig payment.Payment
	converter.QueryByGameId(&paymentConfig, dbOrder.GameId)

	err := SendNotify(ctx, db, dbOrder, *paymentConfig.PayNotifyKey, opts.IsAutoRetry)
	if err != nil && !opts.IsAutoRetry {
		log.Error(ctx, "通知游戏服务发货失败", "error", err.Error())
		if *paymentConfig.DingTalkToken != "" {
			data := schema.MessageData{}
			data.GameId = dbOrder.GameId
			data.OrderId = dbOrder.OrderId
			data.Platform = dbOrder.Platform
			data.Channel = getChannelName(dbOrder.PayWay)
			data.RoleName = dbOrder.RoleName
			data.RoleId = dbOrder.RoleId
			data.ServerId = dbOrder.ServerId
			data.CreateTime = dbOrder.CreateTime.Format("2006-01-02 15:04:05")
			data.NotifyErr = err
			SendNotifyErrorMessage(ctx, *paymentConfig.DingTalkToken, data)
		}
	} else {
		log.Debug(ctx, "通知游戏服务发货成功")
	}

	if *paymentConfig.DingTalkToken != "" && !opts.IsAutoRetry {
		d := notify.DingTalk{AccessToken: *paymentConfig.DingTalkToken}
		for _, rule := range conf.Conf.OrderAlertRules {
			count := GetOrderCountByPayTime(ctx, db, dbOrder, time.Duration(rule.TimeInterval))
			if count >= int64(rule.Threshold) {
				err = fmt.Errorf("角色在%s内完成了超过%d笔订单，请确认订单是否异常", rule.Desc, count)
				dingTalkMessage := fmt.Sprintf(abnormalOrderMessageTemplate, dbOrder.Platform, dbOrder.OrderId,
					dbOrder.RoleName, dbOrder.RoleId, dbOrder.ServerId, dbOrder.ProductId, dbOrder.CreateTime, err)
				d.SendMarkDown(ctx, "支付订单疑似异常", dingTalkMessage)
				break
			}
		}
	}
	return err
}

func NotifySubscription(ctx context.Context, db *gorm.DB, order *models.Subscription, isAutoFix bool, dbRenewSubscription *models.RenewSubscription) error {
	g, err := game.GetGameById(order.GameId)
	if err != nil {
		return err
	}
	var pay payment.Payment
	converter.QueryByGameId(&pay, order.GameId)

	err = SendSubscriptionNotify(ctx, db, order, *pay.PayNotifyKey, isAutoFix, dbRenewSubscription)
	if err != nil && !isAutoFix {
		createTime := order.CreateTime.Format("2006-01-02 15:04:05")
		if dbRenewSubscription.OrderId != "" {
			createTime = dbRenewSubscription.CreateTime.Format("2006-01-02 15:04:05")
		}
		data := schema.MessageData{}
		data.GameId = order.GameId
		data.GameName = g.Name
		data.OrderId = order.OrderId
		data.Platform = order.Platform
		data.Channel = getSubscriptionChannel(order.Platform)
		data.RoleName = order.RoleName
		data.RoleId = order.RoleId
		data.ProductId = order.ProductId
		data.ServerId = order.ServerId
		data.CreateTime = createTime
		data.NotifyErr = err
		SendNotifyErrorMessage(ctx, *pay.DingTalkToken, data)
	}
	return err
}

func getChannelName(payWay int) string {
	var channel string
	switch payWay {
	case constants.PayWayApple:
		channel = constants.ApplePayLabel
	case constants.PayWayGoogle:
		channel = constants.GooglePayLabel
	case constants.PayWayWeChat:
		channel = constants.WechatPayLabel
	case constants.PayWayAli:
		channel = constants.AliPayLabel
	case constants.PayWayXsolla:
		channel = constants.XsollaPayLabel
	case constants.PayWayMyCard:
		channel = constants.MyCardPayName
	}
	channel = fmt.Sprintf("%s%s", channel, "支付")
	return channel
}

func SendNotifyErrorMessage(ctx context.Context, dingToken string, data schema.MessageData) {
	messageText := fmt.Sprintf(messageTemplate, data.GameName, data.Platform, data.OrderId, data.Channel, data.RoleName, data.RoleId, data.ServerId, data.CreateTime, data.NotifyErr)
	d := notify.DingTalk{AccessToken: dingToken}
	d.SendMarkDown(ctx, "支付通知失败", messageText)
}

func SendPayFailedMessage(ctx context.Context, dingToken string, data schema.MessageData) {
	messageText := fmt.Sprintf(failedOrderMessageTemplate, data.OrderId, data.Channel, data.RoleName, data.RoleId, data.ServerId, data.ProductId, data.CreateTime, data.NotifyErr)
	d := notify.DingTalk{AccessToken: dingToken}
	d.SendMarkDown(ctx, "订单支付失败", messageText)
}

func getOrderNotifyInternal(cnt int) int {
	orderNotifyIntervals := constants.OrderNotifyIntervals
	interval := orderNotifyIntervals[cnt+1]
	if interval == 0 {
		values := make([]int, 0, len(orderNotifyIntervals))
		for _, v := range orderNotifyIntervals {
			values = append(values, v)
		}
		sort.Ints(values)
		interval = values[len(values)-1]
	}
	return interval
}

func GetOrderCountByPayTime(ctx context.Context, db *gorm.DB, order *models.Order, timeInterval time.Duration) int64 {
	var orderCount int64
	if time.Now().Before(order.PayTime.Add(1 * time.Minute)) {
		db.Model(models.Order{}).Where("role_id = ?", order.RoleId).Where("pay_time >= ?", order.PayTime.Add(-timeInterval*time.Second)).Where(
			"pay_status = ?", constants.PaymentSuccess).Where("order_attribute = ?", constants.NormalOrder).Count(&orderCount)
	}
	return orderCount
}

func sendNotifyRequest(ctx context.Context, url string, notifyInfo map[string]interface{}) (response string, err error) {
	formData, _ := json.Marshal(notifyInfo)
	headers := map[string]string{"Content-Type": "application/json"}
	resp, err := zeusHttp.PostWithTracer(ctx, url, headers, formData)
	if err != nil {
		log.Error(ctx, err.Error())
		return "", err
	}
	buf := new(bytes.Buffer)
	buf.ReadFrom(resp.Body)
	respStr := buf.String()
	return respStr, err
}

func NotifyFix(ctx context.Context, fixOrdersNotify FixOrdersNotify) {
	games := game.GetGames()
	startTime := time.Now().Add(-time.Minute * 30) // Format("2006-01-02 15:04:05")
	endTime := time.Now().Add(-time.Minute * 1)

	for _, game := range games {
		if game.ProjectID == 0 {
			continue
		}

		db, err := GetGameDBByProjectId(game.ProjectID)
		if err != nil {
			continue
		}

		fixOrdersNotify.FixOrdersNotify(ctx, db, game, startTime, endTime)
	}
}

type FixOrdersNotify interface {
	FixOrdersNotify(ctx context.Context, db *gorm.DB, game models.Game, startTime time.Time, endTime time.Time)
}
type OrdersNotify struct {
	NotifyDones []string
}
type SubscriptionsNotify struct {
	NotifyDones []string
}

func (o *OrdersNotify) FixOrdersNotify(ctx context.Context, db *gorm.DB, game models.Game, startTime time.Time, endTime time.Time) {
	if utils.InArray(strconv.Itoa(game.Id), o.NotifyDones) {
		return
	}
	o.NotifyDones = append(o.NotifyDones, strconv.Itoa(game.Id))

	orders := make([]models.Order, 0)
	var count int64
	dbOrders := db.Model(models.Order{}).Where("pay_time >= ?", startTime).Where("pay_time <= ?", endTime).Where(
		"pay_status = ?", constants.PaymentSuccess).Where("notify_status <> ?", constants.NotifySuccess)
	dbOrders.Count(&count)
	dbOrders.Find(&orders)

	log.Debug(ctx, "支付自动补单结果统计", "game", game.Name, "fix_count", count)

	var notifyFailCount int
	for _, order := range orders {
		res := Notify(ctx, db, &order, NotifyOptions{IsAutoRetry: true})
		if res != nil {
			notifyFailCount += 1
		}
	}
	log.Debug(ctx, "支付自动补单任务", "action", constants.OrderFixClose, "game", game.Name, "notifyFailCount", notifyFailCount)
}

func (s *SubscriptionsNotify) FixOrdersNotify(ctx context.Context, db *gorm.DB, game models.Game, startTime time.Time, endTime time.Time) {
	if utils.InArray(strconv.Itoa(game.Id), s.NotifyDones) {
		return
	}
	s.NotifyDones = append(s.NotifyDones, strconv.Itoa(game.Id))

	var count int64
	subscriptionOrders := make([]models.Subscription, 0)
	dbOrders := db.Model(models.Subscription{}).Where("pay_time >= ?", startTime).Where("pay_time <= ?", endTime).Where(
		"pay_status = ?", constants.PaymentSuccess).Where("notify_status <> ?", constants.NotifySuccess)
	dbOrders.Count(&count)
	dbOrders.Find(&subscriptionOrders)

	log.Debug(ctx, "订阅自动补单结果统计", "game", game.Name, "fix_count", count)

	notifyFailCount := 0
	for _, order := range subscriptionOrders {
		renewSubscription := new(models.RenewSubscription)
		filter := map[string]interface{}{
			"order_id": order.OrderId,
		}
		renewSubscription, _ = models.GetLastedRenewSubscriptionByFilter(db, filter)
		if renewSubscription.OrderId == "" {
			log.Debug(ctx, "订阅自动补单,没有查询到续订信息", "filter", filter)
		}
		log.Debug(ctx, "订阅自动补单,查询到此订阅的续订信息", "filter", filter, "renewSubscription", renewSubscription)
		err := NotifySubscription(ctx, db, &order, true, renewSubscription)
		if err != nil {
			notifyFailCount += 1
		}
	}
	log.Debug(ctx, "订阅自动补单任务", "action", constants.OrderFixClose, "game", game.Name, "notifyFailCount", notifyFailCount)
}

func CronNotifyFixTask(c *cron.Cron) {
	spec := "*/1 * * * *" // 每1分钟执行一次
	c.AddFunc(spec, func() {
		ordersNotify := OrdersNotify{}
		NotifyFix(context.Background(), &ordersNotify)
	})
}

func CronSubscriptionNotifyFixTask(c *cron.Cron) {
	spec := "*/1 * * * *" // 每1分钟执行一次
	c.AddFunc(spec, func() {
		subscriptionsNotify := SubscriptionsNotify{}
		NotifyFix(context.Background(), &subscriptionsNotify)
	})
}

func getSubscriptionChannel(platform string) string {
	var channel string
	if platform == constants.PlatformAndroid {
		channel = constants.GooglePayLabel
	} else {
		channel = constants.ApplePayLabel
	}
	channel = fmt.Sprintf("%s订阅", channel)
	return channel
}
