package services

import (
	"context"
	"errors"
	"io"
	"net/http"
	"reflect"
	"strings"
	"testing"
	"time"

	"zeus/common/constants"
	"zeus/models"
	zeushttp "zeus/pkg/http"
	"zeus/pkg/redis"
	"zeus/services/game"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"
	"zeus/utils"

	. "github.com/agiledragon/gomonkey"
	"github.com/cjrd/allocate"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestGameService(t *testing.T) {
	ApplyFunc(converter.QueryByGameId, func(config schemas.BaseConfiger, _ int) {
		switch config.GetType() {
		case "Payment":
			paymentConfig := config.(*payment.Payment)
			isRechargeOpen := 0
			paymentConfig.IsRechargeOpen = &isRechargeOpen
			dingTalkToken := ""
			paymentConfig.DingTalkToken = &dingTalkToken
			payNotifyKey := ""
			paymentConfig.PayNotifyKey = &payNotifyKey
		case "XsollaPay":
			xsollaPayConfig := config.(*payment.XsollaPay)
			allocate.Zero(xsollaPayConfig)
		}
	})

	Convey("getOrderNotifyInternal", t, func() {
		Convey("one func for succ", func() {
			output := getOrderNotifyInternal(7)
			expected := 600
			So(output, ShouldEqual, expected)
		})
	})

	Convey("sendNotifyRequest", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(zeushttp.PostWithTracer, func(ctx context.Context, u string, headers map[string]string, data []byte, _ ...zeushttp.Option) (resp *http.Response, err error) {
				resp = &http.Response{}
				resp.Body = io.NopCloser(strings.NewReader("success"))
				return resp, nil
			})

			url := "http://127.0.0.1:8080/"
			notifyInfo := make(map[string]interface{})

			output, _ := sendNotifyRequest(context.Background(), url, notifyInfo)
			expected := "success"
			So(output, ShouldEqual, expected)
		})
	})

	Convey("Notify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
				return nil
			})
			ApplyFunc(redis.UnLock, func(key string) int64 {
				return 0
			})
			db := &gorm.DB{}
			ApplyMethod(reflect.TypeOf(db), "Updates", func(_ *gorm.DB, values interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				tx.Error = nil
				return
			})
			order := &models.Order{
				PayStatus:    constants.PaymentSuccess,
				PayNotifyUrl: "test_url",
			}
			output := Notify(context.Background(), db, order, NotifyOptions{})
			So(output, ShouldEqual, nil)
		})
	})

	Convey("Notify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
				return nil
			})
			ApplyFunc(redis.UnLock, func(key string) int64 {
				return 0
			})
			ApplyFunc(sendNotifyRequest, func(ctx context.Context, url string, notifyInfo map[string]interface{}) (response string, err error) {
				return "success", nil
			})
			db := &gorm.DB{}
			ApplyMethod(reflect.TypeOf(db), "Updates", func(_ *gorm.DB, values interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				tx.Error = nil
				return
			})
			order := &models.Order{
				PayStatus:    constants.PaymentSuccess,
				PayNotifyUrl: "test_url",
			}
			output := Notify(context.Background(), db, order, NotifyOptions{})
			So(output, ShouldEqual, nil)
		})
	})

	Convey("Notify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(game.GetGameById, func(id int) (*models.Game, error) {
				return &models.Game{}, nil
			})
			ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
				return nil
			})

			ApplyFunc(redis.UnLock, func(key string) int64 {
				return 0
			})

			ApplyFunc(getOrderNotifyInternal, func(cnt int) int {
				return 1
			})
			db := &gorm.DB{}
			ApplyMethod(reflect.TypeOf(db), "Updates", func(_ *gorm.DB, values interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				tx.Error = nil
				return
			})
			order := &models.Order{
				PayStatus:  constants.PaymentUnpaid,
				NotifyTime: time.Now().Add(-time.Minute * 10),
			}
			output := Notify(context.Background(), db, order, NotifyOptions{})
			So(output, ShouldBeError, errors.New("订单未支付或者已经通知Game Server，请登录管理后台查看订单支付状态"))
		})
	})
}

func TestNotifyFix(t *testing.T) {
	Convey("TestNotifyFix", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(game.GetGames, func() []models.Game {
				var data []models.Game
				data = append(data, models.Game{Id: 1, Name: "ace"})
				return data
			})
			ApplyFunc(utils.InArray, func(_ string, _ []string) bool {
				return false
			})
			ApplyFunc(GetGameDBByProjectId, func(ProjectID int) (*gorm.DB, error) {
				return &gorm.DB{}, nil
			})
			db := &gorm.DB{}
			ApplyMethod(reflect.TypeOf(db), "Model", func(_ *gorm.DB, value interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				return
			})
			ApplyMethod(reflect.TypeOf(db), "Where", func(_ *gorm.DB, query interface{}, args ...interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				return
			})
			ApplyMethod(reflect.TypeOf(db), "Count", func(_ *gorm.DB, count *int64) (tx *gorm.DB) {
				*count = 1
				tx = &gorm.DB{}
				return
			})
			ApplyMethod(reflect.TypeOf(db), "Find", func(_ *gorm.DB, dest interface{}, conds ...interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				return
			})

			ApplyMethod(reflect.TypeOf(db), "Find", func(_ *gorm.DB, dest interface{}, conds ...interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				return
			})

			on := OrdersNotify{}

			apply := ApplyMethod(reflect.TypeOf(&on), "FixOrdersNotify", func(_ *OrdersNotify, ctx context.Context,
				db *gorm.DB, game models.Game, startTime time.Time, endTime time.Time) {
			})
			defer apply.Reset()

			NotifyFix(context.Background(), &on)
		})
	})
}

func TestFixOrdersNotify(t *testing.T) {
	Convey("FixOrdersNotify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(game.GetGames, func() []models.Game {
				var data []models.Game
				data = append(data, models.Game{Id: 1, Name: "ace"})
				return data
			})
			ApplyFunc(utils.InArray, func(_ string, _ []string) bool {
				return false
			})
			ApplyFunc(GetGameDBByProjectId, func(ProjectID int) (*gorm.DB, error) {
				return &gorm.DB{}, nil
			})
			db := &gorm.DB{}
			ApplyMethod(reflect.TypeOf(db), "Model", func(_ *gorm.DB, value interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				return
			})
			ApplyMethod(reflect.TypeOf(db), "Where", func(_ *gorm.DB, query interface{}, args ...interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				return
			})
			ApplyMethod(reflect.TypeOf(db), "Count", func(_ *gorm.DB, count *int64) (tx *gorm.DB) {
				*count = 1
				tx = &gorm.DB{}
				return
			})
			ApplyMethod(reflect.TypeOf(db), "Find", func(_ *gorm.DB, dest interface{}, conds ...interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				return
			})

			ApplyMethod(reflect.TypeOf(db), "Find", func(_ *gorm.DB, dest interface{}, conds ...interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				return
			})

			on := OrdersNotify{NotifyDones: []string{"1"}}
			on.FixOrdersNotify(context.Background(), db, models.Game{Id: 1}, time.Now(), time.Now())
		})
	})
}

func TestNotifySubscription(t *testing.T) {
	mockQueryFunc := ApplyFunc(converter.QueryByGameId, func(config schemas.BaseConfiger, _ int) {
	})
	defer mockQueryFunc.Reset()

	ApplyFunc(converter.QueryByGameId, func(config schemas.BaseConfiger, _ int) {
		switch config.GetType() {
		case "Payment":
			paymentConfig := config.(*payment.Payment)
			isRechargeOpen := 0
			paymentConfig.IsRechargeOpen = &isRechargeOpen
			dingTalkToken := ""
			paymentConfig.DingTalkToken = &dingTalkToken
			payNotifyKey := ""
			paymentConfig.PayNotifyKey = &payNotifyKey
		case "XsollaPay":
			xsollaPayConfig := config.(*payment.XsollaPay)
			allocate.Zero(xsollaPayConfig)
		}
	})

	Convey("TestNotifySubscription", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(getOrderNotifyInternal, func(cnt int) int {
				return 1
			})
			sj := time.Time{}
			ApplyMethod(reflect.TypeOf(sj), "After", func(_ time.Time, u time.Time) bool {
				return false
			})
			ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
				return nil
			})
			ApplyFunc(redis.UnLock, func(key string) int64 {
				return 0
			})
			ApplyFunc(sendNotifyRequest, func(ctx context.Context, url string, notifyInfo map[string]interface{}) (response string, err error) {
				return "test", nil
			})

			db := &gorm.DB{}
			ApplyMethod(reflect.TypeOf(db), "Updates", func(_ *gorm.DB, values interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				tx.Error = errors.New("fail")
				return
			})
			order := &models.Subscription{
				PayStatus:        constants.PaymentSuccess,
				NotificationType: constants.SubscriptionOrderCancel,
				PayNotifyUrl:     "test_url",
			}
			renewSubscription := new(models.RenewSubscription)
			output := NotifySubscription(context.Background(), db, order, false, renewSubscription)
			if output != nil {
				So(output.Error(), ShouldEqual, "通知Game Server完成，修改订单数据失败")
			}
		})
	})
}
