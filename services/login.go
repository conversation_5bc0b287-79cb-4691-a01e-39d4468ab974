package services

import (
	"errors"
	"time"
	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/mapper"
	"zeus/schema/domain"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ErrorLoginFail struct {
	Message string
}

func (e ErrorLoginFail) Error() string {
	return e.Message
}

type ErrorAccountBlocked struct {
	Message string
}

func (e ErrorAccountBlocked) Error() string {
	return e.Message
}

func GetLoginErrorCode(err error) int {
	switch {
	case errors.As(err, &ErrorLoginFail{}):
		return controllers.ErrorLoginFail
	case errors.As(err, &ErrorAccountBlocked{}):
		return controllers.ErrorAccountBlocked
	default:
		return 0
	}
}

type Loginer interface {
	Login(c *gin.Context) (*models.User, error)
}

type EmailLoginServices struct {
	db       *gorm.DB
	APPID    string `json:"appid"`
	Device   string `json:"device"`
	Email    string `json:"email"`
	Password string `json:"password"`
}

func (e *EmailLoginServices) Map(v interface{}) (err error) {
	mapper.Map(e, v)
	e.db, err = GetGameDBByAppID(e.APPID)
	return
}

func (e *EmailLoginServices) Login(c *gin.Context) (*models.User, error) {
	filter := map[string]interface{}{
		"email":  e.Email,
		"pw_md5": e.Password,
	}
	dbUser, _ := QueryOneUser(c, filter)
	if dbUser.ID == 0 {
		return nil, ErrorLoginFail{"账号或密码错误"}
	}
	if dbUser.Status == constants.UserStatusBlocked {
		return dbUser, ErrorAccountBlocked{"账号已被封"}
	}

	dbUser.LoginTime = time.Now()
	dbUser.LoginIP = c.ClientIP()
	dbUser.LoginCnt += 1
	dbUser.Token = GenerateToken()

	_ = SaveUser(c, dbUser)

	RecordLoginDevice(c, dbUser, e.Device)
	return dbUser, nil
}

type PhoneLoginServices struct {
	db       *gorm.DB
	APPID    string `json:"appid"`
	Device   string `json:"device"`
	Phone    string `json:"phone"`
	Password string `json:"password"`
}

func (p *PhoneLoginServices) Map(v interface{}) (err error) {
	mapper.Map(p, v)
	p.db, err = GetGameDBByAppID(p.APPID)
	return
}

func (p *PhoneLoginServices) Login(c *gin.Context) (*models.User, error) {
	filter := map[string]interface{}{
		"phone":  p.Phone,
		"pw_md5": p.Password,
	}
	dbUser, _ := QueryOneUser(c, filter)
	if dbUser.ID == 0 {
		return nil, ErrorLoginFail{"账号或密码错误"}
	}
	if dbUser.Status == constants.UserStatusBlocked {
		return dbUser, ErrorAccountBlocked{"账号已被封"}
	}

	dbUser.LoginTime = time.Now()
	dbUser.LoginIP = c.ClientIP()
	dbUser.LoginCnt += 1
	dbUser.Token = GenerateToken()

	_ = SaveUser(c, dbUser)

	RecordLoginDevice(c, dbUser, p.Device)
	return dbUser, nil
}

type DeviceLoginServices struct {
	db      *gorm.DB
	APPID   string `json:"appid"`
	Device  string `json:"device"`
	Account string `json:"account"`
}

func (d *DeviceLoginServices) Map(v interface{}) (err error) {
	mapper.Map(d, v)
	d.db, err = GetGameDBByAppID(d.APPID)
	return
}

func (d *DeviceLoginServices) Login(c *gin.Context) (*models.User, error) {
	filter := map[string]interface{}{
		"account": d.Account,
	}
	dbUser, _ := QueryOneUser(c, filter)
	if dbUser.ID == 0 {
		return nil, ErrorLoginFail{"用户不存在"}
	}

	if dbUser.Status == constants.UserStatusBlocked {
		return dbUser, ErrorAccountBlocked{"账号已被封"}
	}

	if dbUser.Status == constants.UserStatusDeleted {
		return nil, ErrorLoginFail{"账号已删除"}
	}

	// TODO: 临时解决方案，后续需要将IsDeleted删除
	if dbUser.IsDeleted {
		dbUser.Status = constants.UserStatusDeleted
		_ = SaveUser(c, dbUser)
		return nil, ErrorLoginFail{"账号已删除"}
	}

	if !CheckLoginDevice(c, dbUser, d.Device) {
		return nil, ErrorLoginFail{"该设备不允许登录"}
	}

	dbUser.Status = constants.UserStatusNormal
	dbUser.LoginTime = time.Now()
	dbUser.LoginIP = c.ClientIP()
	dbUser.LoginCnt += 1
	dbUser.Token = GenerateToken()
	_ = SaveUser(c, dbUser)
	return dbUser, nil
}

func ChoiceLoginServices(r *domain.Login) Loginer {
	switch r.LoginType {
	case constants.LoginTypeEmail:
		els := new(EmailLoginServices)
		_ = els.Map(r)
		return els
	case constants.LoginTypePhone:
		pls := new(PhoneLoginServices)
		_ = pls.Map(r)
		return pls
	default:
		dls := new(DeviceLoginServices)
		_ = dls.Map(r)
		return dls
	}
}
