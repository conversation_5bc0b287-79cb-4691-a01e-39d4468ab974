package services

import (
	"reflect"
	"testing"

	"zeus/common/constants"
	"zeus/models"
	"zeus/schema/domain"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestLoginServices(t *testing.T) {
	c := &gin.Context{}
	apply1 := ApplyMethod(reflect.TypeOf(c), "ClientIP", func(_ *gin.Context) string {
		return "***************"
	})
	defer apply1.Reset()

	apply2 := ApplyFunc(GetGameDBByAppID, func(appID string) (*gorm.DB, error) {
		return &gorm.DB{}, nil
	})
	defer apply2.Reset()

	apply3 := ApplyFunc(SaveUser, func(c *gin.Context, user *models.User) error {
		return nil
	})
	defer apply3.Reset()

	apply4 := ApplyFunc(RecordLoginDevice, func(_ *gin.Context, _ *models.User, _ string) {
	})
	defer apply4.Reset()

	<PERSON><PERSON>("EmailLogin", t, func() {
		Convey("one func for succ", func() {
			apply4 := ApplyFunc(QueryOneUser, func(c *gin.Context, args map[string]interface{}) (*models.User, error) {
				return &models.User{ID: 1}, nil
			})
			defer apply4.Reset()

			v := &domain.Login{
				LoginType: constants.LoginTypeEmail,
				APPID:     "test_appid",
				Device:    "test_device",
				Email:     "email",
				Password:  "password",
			}
			els := ChoiceLoginServices(v)
			els.Login(c)
		})
	})

	Convey("PhoneLogin", t, func() {
		Convey("one func for succ", func() {
			apply5 := ApplyFunc(QueryOneUser, func(c *gin.Context, args map[string]interface{}) (*models.User, error) {
				return &models.User{ID: 1}, nil
			})
			defer apply5.Reset()
			ApplyFunc(RecordLoginDevice, func(_ *gin.Context, _ *models.User, _ string) {
			})

			v := &domain.Login{
				LoginType: constants.LoginTypePhone,
				APPID:     "test_appid",
				Device:    "test_device",
				Phone:     "phone",
				Password:  "password",
			}
			pls := ChoiceLoginServices(v)

			pls.Login(c)
		})
	})

	Convey("DeviceLogin", t, func() {
		Convey("one func for succ", func() {
			apply6 := ApplyFunc(QueryOneUser, func(c *gin.Context, args map[string]interface{}) (*models.User, error) {
				return &models.User{ID: 1}, nil
			})
			defer apply6.Reset()

			apply7 := ApplyFunc(CheckLoginDevice, func(_ *gin.Context, _ *models.User, _ string) bool {
				return true
			})
			defer apply7.Reset()

			v := &domain.Login{
				LoginType: constants.LoginTypeDevice,
				APPID:     "test_appid",
				Device:    "test_device",
				Account:   "account",
			}
			dls := ChoiceLoginServices(v)

			_, err := dls.Login(c)
			So(err, ShouldBeNil)
		})
	})
}
