package services

import (
	"time"

	"zeus/common"
	"zeus/middlewares/checkJWT"
	"zeus/models"
	"zeus/schema"

	"zeus/common/log"

	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"
)

func CreateOperation(operation schema.OperationCreate) (*models.Operation, error) {
	db := common.GetInfoDB()

	dbOperation := models.Operation{}
	dbOperation.User = operation.User
	dbOperation.ProjectId = operation.ProjectId
	dbOperation.Action = operation.Action
	dbOperation.Detail = operation.Detail
	dbOperation.Resource = operation.Resource
	dbOperation.ResourceName = operation.ResourceName
	dbOperation.ResourceFunc = operation.ResourceFunc
	dbOperation.OldData = operation.OldData
	dbOperation.NewData = operation.NewData
	dbOperation.Time = time.Now()
	err := models.CreateOperation(db, &dbOperation)
	return &dbOperation, err
}

func AddOperation(ctx *gin.Context, projectID int, resource string, resourceName string, resourceFunc string, action string, params interface{}, oldData interface{}, newData interface{}) {
	var operation schema.OperationCreate
	user, _ := ctx.Get("user")
	JWTUser := user.(*checkJWT.JWTUser)
	request := map[string]interface{}{
		"url":    ctx.Request.URL.Path,
		"params": params,
	}
	requestJson, _ := json.Marshal(request)
	oldDataJson, _ := json.Marshal(oldData)
	newDataJson, _ := json.Marshal(newData)
	operation.ProjectId = projectID
	operation.Action = action
	operation.Resource = resource
	operation.ResourceName = resourceName
	operation.ResourceFunc = resourceFunc
	operation.OldData = string(oldDataJson)
	operation.NewData = string(newDataJson)
	operation.User = JWTUser.Name
	operation.Detail = string(requestJson)
	_, err := CreateOperation(operation)
	if err != nil {
		log.Error(ctx, "添加操作记录失败", "error", err.Error())
	}
}

func GetOperation(filter map[string]interface{}, pageSize int, pageNum int, startTime string, endTime string) ([]*models.Operation, int64, error) {
	db := common.GetInfoDB()
	operation, total, err := models.GetOperationsByPage(db, pageSize, pageNum, filter, startTime, endTime)
	return operation, total, err
}
