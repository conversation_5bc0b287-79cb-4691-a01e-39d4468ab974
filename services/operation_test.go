package services

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"reflect"
	"testing"

	"zeus/common"
	"zeus/middlewares/checkJWT"
	"zeus/models"
	"zeus/schema"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestCreateOperation(t *testing.T) {
	apply1 := ApplyFunc(common.GetInfoDB, func() *gorm.DB {
		return &gorm.DB{}
	})
	defer apply1.Reset()

	apply2 := ApplyFunc(models.CreateOperation, func(db *gorm.DB, args *models.Operation) error {
		return nil
	})
	defer apply2.Reset()

	Convey("CreateOperation", t, func() {
		Convey("one func for succ", func() {
			v := schema.OperationCreate{
				User: "test_user",
			}
			operation, _ := CreateOperation(v)
			expected := "test_user"
			So(operation.User, ShouldEqual, expected)
		})
	})
}

func TestAddOperation(t *testing.T) {
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	c.Request, _ = http.NewRequest("GET", "/test/path", nil)

	apply1 := ApplyMethod(reflect.TypeOf(c), "Get", func(_ *gin.Context, key string) (value any, exists bool) {
		return &checkJWT.JWTUser{
			Name: "游基",
		}, true
	})
	defer apply1.Reset()
	apply2 := ApplyFunc(common.GetInfoDB, func() *gorm.DB {
		return &gorm.DB{}
	})
	defer apply2.Reset()

	apply3 := ApplyFunc(models.CreateOperation, func(db *gorm.DB, args *models.Operation) error {
		return errors.New("add fail")
	})
	defer apply3.Reset()

	Convey("AddOperation", t, func() {
		Convey("one func for fail", func() {
			AddOperation(c, 0, "resource", "resourceName",
				"resourceFunc", "action",
				"{name: tong}", "oldData", "newData")
		})
	})
}

func TestGetOperation(t *testing.T) {
	apply1 := ApplyFunc(common.GetInfoDB, func() *gorm.DB {
		return &gorm.DB{}
	})
	defer apply1.Reset()

	apply2 := ApplyFunc(models.GetOperationsByPage, func(db *gorm.DB, pageSize int, pageNum int, args map[string]interface{},
		startTime string, endTime string,
	) ([]*models.Operation, int64, error) {
		var data []*models.Operation
		data = append(data, &models.Operation{})
		return data, 1, nil
	})
	defer apply2.Reset()

	Convey("AddGetOperation", t, func() {
		Convey("one func for succ", func() {
			filter := map[string]interface{}{
				"name": "tong",
			}
			_, count, _ := GetOperation(filter, 1, 1, "startTime", "endTime")
			expected := 1
			So(count, ShouldEqual, expected)
		})
	})
}
