package services

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"time"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	zeusHttp "zeus/pkg/http"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/utils"
)

func generateStr(number int) string {
	letters := []rune("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
	rand.Seed(time.Now().UnixNano())

	b := make([]rune, number)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

func GenerateUUID() string {
	return uuid.New().String()
}

func GenerateOrderID(orderType int, tokenNumber int, extra ...string) string {
	orderIDPre := ""
	switch orderType {
	case constants.PayWayApple:
		orderIDPre = "i_"
	case constants.PayWayGoogle:
		orderIDPre = "a_"
	case constants.PayWayWeChat:
		orderIDPre = "w_"
	case constants.PayWayMiniGameWeChat:
		orderIDPre = "mini_w_"
	case constants.PayWayMiniGameDouyin:
		orderIDPre = "mini_d_"
	case constants.PayWayAli:
		orderIDPre = "ali_"
	case constants.GoogleSubscription:
		orderIDPre = "as_"
	case constants.IosSubscription:
		orderIDPre = "is_"
	case constants.PayWayXsolla:
		orderIDPre = "xs_"
	case constants.PayWayMyCard:
		orderIDPre = "mycard_"
	case constants.PayWayGash:
		orderIDPre = "gash_"
	case constants.PayWayHuawei:
		orderIDPre = "hw_"
	case constants.PayWayMock:
		orderIDPre = "mock_"
	case constants.PayWayWebPoints:
		orderIDPre = "wp_point_"
	}

	for _, ex := range extra {
		if ex != "" {
			orderIDPre += fmt.Sprintf("%s_", ex)
		}
	}

	var orderID string
	for {
		timeStr := time.Now().Format("20060102150405")
		strToken := generateStr(tokenNumber)
		orderID = orderIDPre + timeStr + "_" + strToken
		key := "generate_" + orderID
		err := redis.Lock(key, time.Second*5)
		if err == nil {
			break
		}
	}
	return orderID
}

func VerifyToAppstore(ctx context.Context, receipt string, isSandbox bool) *http.Response {
	var url string
	if isSandbox {
		url = constants.AppStoreUrlSandbox
	} else {
		url = constants.AppStoreUrlOnline
	}
	params := map[string]interface{}{
		"receipt-data": receipt,
	}
	bytesData, _ := json.Marshal(params)

	headers := map[string]string{"Content-Type": "application/json;charset=utf-8"}
	resp, err := zeusHttp.PostWithTracer(ctx, url, headers, bytesData, zeusHttp.WithTimeout(5*time.Second))
	if err != nil {
		return nil
	}
	return resp
}

func VerifyOnline(ctx context.Context, paper string, payType string, applePassword string) (map[string]interface{}, error) {
	var res *http.Response
	if payType == "order" {
		res = VerifyToAppstore(ctx, paper, false)
	} else {
		res = VerifySubToAppstore(ctx, paper, false, applePassword)
	}

	info := TransformResponseToMap(res)
	log.Debug(ctx, "IOS订阅向苹果服务器正式环境验证结果", "product_verify_result", info)
	return info, nil
}

func VerifySandBox(ctx context.Context, paper string, payType string, applePassword string) (map[string]interface{}, error) {
	var verifyRes *http.Response
	if payType == "order" {
		verifyRes = VerifyToAppstore(ctx, paper, true)
	} else {
		verifyRes = VerifySubToAppstore(ctx, paper, true, applePassword)
	}

	if verifyRes == nil {
		return nil, fmt.Errorf("%s 参数无效", paper)
	}
	info := TransformResponseToMap(verifyRes)
	log.Debug(ctx, "IOS订阅向苹果服务器沙盒环境验证结果", "sandbox_verify_result", info)

	return info, nil
}

func TransformResponseToMap(response *http.Response) map[string]interface{} {
	var result map[string]interface{}
	body, err := io.ReadAll(response.Body)
	if err == nil {
		json.Unmarshal(body, &result)
	}
	return result
}

func QueryOrder(c *gin.Context, filter map[string]interface{}) (*models.Order, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetOneOrder(db, filter)
}

func UpdatePayFailedMessage(c *gin.Context, orderId string, errorMessage string) {
	filter := map[string]interface{}{
		"order_id": orderId,
	}
	dbOrder, _ := QueryOrder(c, filter)
	if dbOrder.OrderId != "" && dbOrder.PayStatus == constants.PaymentUnpaid {
		_ = UpdateErrorMessage(c, errorMessage, *dbOrder)
	}
}

func QueryOrders(c *gin.Context, filter map[string]interface{}) ([]*models.Order, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetOrders(db, filter)
}

func GetOrderByOrderID(c *gin.Context, orderID string) (*models.Order, error) {
	db := c.MustGet("db").(*gorm.DB)
	filter := map[string]interface{}{
		"order_id": orderID,
	}

	return models.GetOneOrder(db, filter)
}

func SaveOrder(c *gin.Context, order *models.Order) error {
	log.Debug(c.Request.Context(), "保存订单信息", "data", order)
	db := c.MustGet("db").(*gorm.DB)
	err := models.SaveOrder(db, order)
	if err != nil {
		log.Error(c.Request.Context(), "保存订单失败", "error", err.Error())
		return err
	} else {
		return nil
	}
}

func CreateOrder(c *gin.Context, order *models.Order) error {
	log.Debug(c.Request.Context(), "开始创建订单", "data", order)
	db := c.MustGet("db").(*gorm.DB)
	err := models.CreateOrder(db, order)
	if err != nil {
		log.Error(c.Request.Context(), "创建订单失败", "error", err.Error())
	}
	return err
}

func CheckOrder(c *gin.Context, orderID string) (models.Order, error) {
	dbOrder, _ := GetOrderByOrderID(c, orderID)
	if dbOrder.OrderId == "" {
		return *dbOrder, errors.New(constants.OrderNotExitMessage)
	}
	return *dbOrder, nil
}

func UpdatePayOrder(c *gin.Context, order schema.OrderPayResult, dbOrder *models.Order, payWay int) error {
	log.Debug(c.Request.Context(), "更新订单信息", "data", order)

	dbOrder.PayId = order.PayID
	dbOrder.PayTime = time.Now()
	dbOrder.PayWay = payWay
	dbOrder.PayIp = c.ClientIP()
	dbOrder.PayCurrency = order.Currency
	dbOrder.PayAmount = order.Amount
	dbOrder.PayProductId = dbOrder.ProductId
	dbOrder.PayStatus = order.Status

	if err := SaveOrder(c, dbOrder); err != nil {
		log.Error(c.Request.Context(), "更新订单信息失败", "error", err.Error())
		return err
	}
	return nil
}

func GetOrderByPage(c *gin.Context, filter map[string]interface{}, pageSize int, pageNum int, OrderAttribute []string, startTime string, endTime string) ([]models.Order, int64, error) {
	db := c.MustGet("db").(*gorm.DB)

	orders, total, err := models.GetOrderByPage(db, pageSize, pageNum, OrderAttribute, filter, startTime, endTime)

	return orders, total, err
}

func GetDownloadOrderCount(c *gin.Context, params schema.OrderQueryParams) int {
	db := c.MustGet("db").(*gorm.DB)
	queryOptions := models.GetDownloadQueryOptions(params)
	return models.GetDownloadOrderCount(db, params.GameID, queryOptions)
}

func GetDownloadOrder(c *gin.Context, pageNum int, pageSize int, params schema.OrderQueryParams) *sql.Rows {
	db := c.MustGet("db").(*gorm.DB)
	queryOptions := models.GetDownloadQueryOptions(params)
	return models.GetDownloadOrder(db, params.GameID, pageNum, pageSize, params.ChooseList, queryOptions)
}

func GetDownloadSubscriptionCount(c *gin.Context, params schema.SubscriptionQueryParams) int {
	db := c.MustGet("db").(*gorm.DB)
	queryOptions := models.GetDownloadSubscriptionQueryOptions(params)
	return models.GetDownloadSubscriptionCount(db, params.GameID, queryOptions)
}

func GetDownloadSubscriptions(c *gin.Context, pageNum int, pageSize int, params schema.SubscriptionQueryParams) *sql.Rows {
	db := c.MustGet("db").(*gorm.DB)
	queryOptions := models.GetDownloadSubscriptionQueryOptions(params)
	return models.GetDownloadSubscriptionRows(db, params.GameID, pageNum, pageSize, params.ChooseList, queryOptions)
}

func GetDownloadOrderStatistics(c *gin.Context, pageNum int, pageSize int, params schema.OrderStatisticsParams) *sql.Rows {
	db := c.MustGet("db").(*gorm.DB)
	orders := GetOrderStatisticsRows(db, pageNum, pageSize, params)
	return orders
}

func UpdateErrorMessage(c *gin.Context, message string, dbOrder models.Order) error {
	dbOrder.PayStatus = constants.PaymentFailure
	dbOrder.PayErrorMessage = message
	err := SaveOrder(c, &dbOrder)

	var paymentConfig payment.Payment
	converter.QueryByGameId(&paymentConfig, dbOrder.GameId)
	if *paymentConfig.DingTalkToken != "" {
		data := schema.MessageData{}
		data.GameId = dbOrder.GameId
		data.OrderId = dbOrder.OrderId
		data.Platform = dbOrder.Platform
		data.Channel = getChannelName(dbOrder.PayWay)
		data.RoleName = dbOrder.RoleName
		data.RoleId = dbOrder.RoleId
		data.ServerId = dbOrder.ServerId
		data.CreateTime = dbOrder.CreateTime.Format("2006-01-02 15:04:05")
		data.NotifyErr = errors.New(message)
		SendPayFailedMessage(c.Request.Context(), *paymentConfig.DingTalkToken, data)
	}

	if err != nil {
		return err
	}
	return nil
}

func GetRefundOrder(c *gin.Context, gameID int, roleID string, roleName string, ServerID string, sortColumn string, sortRule string, pageNum int, pageSize int) ([]schema.RefundOrder, int64) {
	db := c.MustGet("db").(*gorm.DB)
	var orderData []schema.RefundOrder
	var total int64

	sqlString := "select a.*, role.role_name from (select role_id, server_id, server_name, " +
		"sum(case when pay_status = 1 or pay_status = 2 then 1 else 0 end) as total_orders, " +
		"sum(case when pay_status = 1 or pay_status = 2 then case when pay_way = 1 then price/7 when pay_way = 2 then price/700 when pay_currency = 'TWD' then price/30 when pay_currency = 'HKD' then price/7.8 else 0 end else 0 end) as total_price, " +
		"sum(case when pay_status = 2 then 1 else 0 end) as refund_orders, " +
		"sum(case when pay_status = 2 then case when pay_way = 1 then price/7 when pay_way = 2 then price/700 when pay_currency = 'TWD' then price/30 when pay_currency = 'HKD' then price/7.8 else 0 end else 0 end) as refund_price " +
		"from `order` where (role_id, server_id)  in (select role_id, server_id from `order` where pay_status = 2) "
	if gameID != 0 {
		sqlString = sqlString + fmt.Sprintf(" and game_id = %d", gameID)
	}
	if roleID != "" {
		sqlString = sqlString + fmt.Sprintf(" and role_id = %q", roleID)
	}
	if roleName != "" {
		sqlString = sqlString + " and role_name like '%" + roleName + "%' "
	}
	if ServerID != "" {
		sqlString = sqlString + fmt.Sprintf(" and server_id = %q", ServerID)
	}
	sqlString += " group by role_id, server_id, server_name ) as a left join role on a.role_id=role.role_id and a.server_id=role.server_id "
	if sortColumn != "" {
		sqlString += fmt.Sprintf(" order by %s ", sortColumn)
		if sortRule == "descend" {
			sqlString += "desc"
		} else {
			sqlString += "asc"
		}
	} else {
		sqlString += " order by refund_price desc"
	}

	db.Raw(sqlString).Count(&total)
	if pageNum != 0 && pageSize != 0 {
		offset := pageSize * (pageNum - 1)
		sqlString = sqlString + fmt.Sprintf(" limit %d offset %d", pageSize, offset)
	}
	db.Raw(sqlString).Scan(&orderData)
	if total == 0 {
		// 查询到1条数据时，total为0，判断len(orderData)再次确认
		total = int64(len(orderData))
	}

	return orderData, total
}

func GetRefundOrderInfo(c *gin.Context, gameID int, roleID string, ServerID string, pageNum int, pageSize int) ([]schema.RefundOrderInfo, int64) {
	db := c.MustGet("db").(*gorm.DB)
	var orderData []schema.RefundOrderInfo
	var total int64
	sqlString := fmt.Sprintf(
		"select order_id, pay_id, product_id, device, pay_way, price, pay_currency, pay_time "+
			"from `order` where game_id = %d and pay_status=2 and role_id = %q and server_id = %q  order by pay_time desc",
		gameID, roleID, ServerID)
	db.Raw(sqlString).Count(&total)
	if pageNum != 0 && pageSize != 0 {
		offset := pageSize * (pageNum - 1)
		sqlString = sqlString + fmt.Sprintf(" limit %d offset %d", pageSize, offset)
	}
	db.Raw(sqlString).Scan(&orderData)
	if total == 0 {
		// 查询到1条数据时，total为0，判断len(orderData)再次确认
		total = int64(len(orderData))
	}

	return orderData, total
}

func UpdateOrderPayStatus(ctx context.Context, db *gorm.DB, gameID int, transactionInfo *schema.SignedTransactionInfo) {
	log.Debug(ctx, "开始更新订单状态", "data", transactionInfo)
	filter := map[string]interface{}{
		"pay_id":  transactionInfo.TransactionId,
		"game_id": gameID,
	}
	dbOrder, _ := models.GetOneOrder(db, filter)
	if dbOrder.OrderId != "" {
		dbOrder.PayStatus = constants.PaymentRefund
		dbOrder.RefundTime = time.Unix(transactionInfo.RevocationDate/1000, 0)
		if err := db.Updates(&dbOrder).Error; err != nil {
			log.Error(ctx, "订单支付状态修改为退款失败", "error", err.Error())
		}
	}
	log.Debug(ctx, "更新订单支付状态成功")
}

func GetOrderByOpenId(db *gorm.DB, openId string) (*models.Order, error) {
	filter := map[string]interface{}{
		"open_id": openId,
		"pay_way": constants.PayWayMiniGameWeChat,
	}
	dbOrder, err := models.GetLatestOneOrder(db, filter)
	if err != nil {
		return nil, err
	}
	return dbOrder, nil
}

func CreateMiniGameOrder(c *gin.Context, orderID string, prepayId string, openId string, gameId int, user models.User, orderForm schema.WechatMiniGameExchange) error {
	dbOrder := &models.Order{}

	dbOrder.OrderId = orderID
	dbOrder.PrepayId = prepayId
	dbOrder.OpenId = openId
	dbOrder.Vip = utils.ConvertStringToInt(orderForm.Vip)
	dbOrder.Level = utils.ConvertStringToInt(orderForm.Level)
	dbOrder.GameId = gameId
	dbOrder.Account = user.Account
	dbOrder.ProductId = orderForm.ProductID
	dbOrder.UserId = user.ID
	dbOrder.RoleId = orderForm.RoleID
	dbOrder.Price = orderForm.Price
	dbOrder.Extend = orderForm.Extend
	dbOrder.Device = orderForm.Device
	dbOrder.RoleName = orderForm.RoleName
	dbOrder.PayNotifyUrl = orderForm.PayNotifyURL
	dbOrder.CreateTime = time.Now()
	dbOrder.Platform = orderForm.PlatForm
	dbOrder.ServerId = orderForm.ServerID
	dbOrder.ServerName = orderForm.ServerName
	dbOrder.PayWay = constants.PayWayMiniGameWeChat
	dbOrder.OrderAttribute = constants.NormalOrder
	if orderForm.Env == constants.TestAccount {
		dbOrder.OrderAttribute = orderForm.Env
	}

	err := CreateOrder(c, dbOrder)
	if err != nil {
		return err
	}
	return nil
}
