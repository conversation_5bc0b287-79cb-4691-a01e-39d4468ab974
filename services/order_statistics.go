package services

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"github.com/robfig/cron/v3"
	"gorm.io/gorm"

	"zeus/common/constants"
	"zeus/models"

	"github.com/gin-gonic/gin"
)

func CreateOrderStatistics(ctx context.Context, db *gorm.DB, orderStatistics []schema.OrderStatistics) error {
	if len(orderStatistics) > 0 {
		err := models.CreateOrderStatistics(db, orderStatistics)
		if err != nil {
			return err
		}
	}
	return nil
}

func GetOrderStatistics(ctx context.Context, db *gorm.DB, payWay int, game models.Game, startDate string, endDate string) []schema.OrderStatistics {
	var orderData []schema.OrderStatistics
	gameID := game.Id
	var sqlString string
	if payWay == constants.PayWayApple {
		sqlString = "SELECT * from (SELECT game_id, DATE_FORMAT(CONVERT_TZ(pay_time, '+08:00', '+0:00'), '%Y-%m-%d') pay_date, product_id, pay_way, " +
			"SUM(CASE WHEN pay_status = 1 THEN 1 ELSE 0 END) AS paid_orders, " +
			"SUM(CASE WHEN pay_status = 2 THEN 1 ELSE 0 END) AS refund_orders " +
			"FROM `order` WHERE verify_status = 1 AND pay_way = 1 AND order_attribute = 0 AND game_id = " + strconv.Itoa(gameID) +
			" GROUP BY game_id, pay_date, product_id, pay_way) as o"
	} else if payWay == constants.PayWayGoogle {
		sqlString = "SELECT * from (SELECT game_id, DATE_FORMAT(CONVERT_TZ(pay_time, '+08:00', '+0:00'), '%Y-%m-%d') pay_date, product_id, pay_way, " +
			"SUM(CASE WHEN pay_status = 1 THEN 1 ELSE 0 END) AS paid_orders, " +
			"SUM(CASE WHEN pay_status = 2 THEN 1 ELSE 0 END) AS refund_orders " +
			"FROM `order` WHERE verify_status = 1 AND pay_way = 2 AND order_attribute = 0 AND game_id = " + strconv.Itoa(gameID) +
			" GROUP BY game_id, pay_date, product_id, pay_way) as o"
	} else if payWay == constants.PayWayMyCard {
		sqlString = "SELECT * from (SELECT game_id, DATE_FORMAT(pay_time, '%Y-%m-%d') pay_date, product_id, pay_way, " +
			"SUM(CASE WHEN pay_status = 1 THEN 1 ELSE 0 END) AS paid_orders, " +
			"SUM(CASE WHEN pay_status = 2 THEN 1 ELSE 0 END) AS refund_orders " +
			"FROM `order` WHERE verify_status = 1 AND pay_way = 8 AND order_attribute in (0, 4) AND game_id = " + strconv.Itoa(gameID) +
			" GROUP BY game_id, pay_date, product_id, pay_way) as o"
	} else if payWay == constants.PayWayGash {
		sqlString = "SELECT * from (SELECT game_id, DATE_FORMAT(pay_time, '%Y-%m-%d') pay_date, product_id, pay_way, " +
			"SUM(CASE WHEN pay_status = 1 THEN 1 ELSE 0 END) AS paid_orders, " +
			"SUM(CASE WHEN pay_status = 2 THEN 1 ELSE 0 END) AS refund_orders " +
			"FROM `order` WHERE verify_status = 1 AND pay_way = 9 AND order_attribute in (0, 4) AND game_id = " + strconv.Itoa(gameID) +
			" GROUP BY game_id, pay_date, product_id, pay_way) as o"
	} else if payWay == constants.PayWayHuawei {
		// 与Google支付一致，price单位为美分
		sqlString = "SELECT * from (SELECT game_id, DATE_FORMAT(pay_time, '%Y-%m-%d') pay_date, product_id, pay_way, " +
			"SUM(CASE WHEN pay_status = 1 THEN 1 ELSE 0 END) AS paid_orders, " +
			"SUM(CASE WHEN pay_status = 2 THEN 1 ELSE 0 END) AS refund_orders " +
			"FROM `order` WHERE verify_status = 1 AND pay_way = 10 AND order_attribute = 0 AND game_id = " + strconv.Itoa(gameID) +
			" GROUP BY game_id, pay_date, product_id, pay_way) as o"
	} else if payWay == constants.PayWayMiniGameWeChat {
		sqlString = "SELECT * from (SELECT game_id, DATE_FORMAT(pay_time, '%Y-%m-%d') pay_date, product_id, pay_way, " +
			"SUM(CASE WHEN pay_status = 1 THEN 1 ELSE 0 END) AS paid_orders, " +
			"SUM(CASE WHEN pay_status = 2 THEN 1 ELSE 0 END) AS refund_orders " +
			"FROM `order` WHERE verify_status = 1 AND pay_way = 12 AND order_attribute = 0 AND game_id = " + strconv.Itoa(gameID) +
			" GROUP BY game_id, pay_date, product_id, pay_way) as o"
	} else if payWay == constants.PayWayWeChat {
		sqlString = "SELECT * from (SELECT game_id, DATE_FORMAT(pay_time, '%Y-%m-%d') pay_date, product_id, pay_way, " +
			"SUM(CASE WHEN pay_status = 1 THEN 1 ELSE 0 END) AS paid_orders, " +
			"SUM(CASE WHEN pay_status = 2 THEN 1 ELSE 0 END) AS refund_orders " +
			"FROM `order` WHERE verify_status = 1 AND pay_way = 3 AND order_attribute = 0 AND game_id = " + strconv.Itoa(gameID) +
			" GROUP BY game_id, pay_date, product_id, pay_way) as o"
	} else if payWay == constants.PayWayAli {
		sqlString = "SELECT * from (SELECT game_id, DATE_FORMAT(pay_time, '%Y-%m-%d') pay_date, product_id, pay_way, " +
			"SUM(CASE WHEN pay_status = 1 THEN 1 ELSE 0 END) AS paid_orders, " +
			"SUM(CASE WHEN pay_status = 2 THEN 1 ELSE 0 END) AS refund_orders " +
			"FROM `order` WHERE verify_status = 1 AND pay_way = 4 AND order_attribute = 0 AND game_id = " + strconv.Itoa(gameID) +
			" GROUP BY game_id, pay_date, product_id, pay_way) as o"
	} else if payWay == constants.PayWayMiniGameDouyin {
		sqlString = "SELECT * from (SELECT game_id, DATE_FORMAT(pay_time, '%Y-%m-%d') pay_date, product_id, pay_way, " +
			"SUM(CASE WHEN pay_status = 1 THEN 1 ELSE 0 END) AS paid_orders, " +
			"SUM(CASE WHEN pay_status = 2 THEN 1 ELSE 0 END) AS refund_orders " +
			"FROM `order` WHERE verify_status = 1 AND pay_way = 13 AND order_attribute = 0 AND game_id = " + strconv.Itoa(gameID) +
			" GROUP BY game_id, pay_date, product_id, pay_way) as o"
	} else {
		sqlString = ""
	}

	if sqlString != "" {
		var condition []string
		if startDate != "" {
			condition = append(condition, fmt.Sprintf(" o.pay_date >= %q ", startDate))
		}
		if endDate != "" {
			condition = append(condition, fmt.Sprintf(" o.pay_date <= %q ", endDate))
		}

		if len(condition) > 0 {
			sqlString += " where " + strings.Join(condition, "and")
		}

		db.Raw(sqlString).Scan(&orderData)
	}

	return orderData
}

func GetOrderStatisticsByDimension(c *gin.Context, p schema.OrderStatisticsParams) ([]schema.StatisticsData, int64) {
	db := c.MustGet("db").(*gorm.DB)
	var orderData []schema.StatisticsData
	var sql string
	switch p.Dimension {
	case "pay_date":
		if p.PayStartDate != "" && p.PayEndDate != "" {
			sql = fmt.Sprintf("select pay_date, sum(paid_orders) paid_orders, sum(refund_orders) refund_orders "+
				"from order_statistics where pay_way= %d and pay_date >= %q and pay_date <= %q and game_id=%d group by pay_date ", p.PayWay, p.PayStartDate, p.PayEndDate, p.GameID)
		} else {
			sql = fmt.Sprintf("select pay_date, sum(paid_orders) paid_orders, sum(refund_orders) refund_orders"+
				"from order_statistics where pay_way = %d and game_id=%d group by pay_date ", p.PayWay, p.GameID)
		}
	case "product_id":

		sql = fmt.Sprintf("select product_id, sum(paid_orders) paid_orders, sum(refund_orders) refund_orders"+
			"from order_statistics where pay_way = %d and game_id=%d group by product_id ", p.PayWay, p.GameID)
		if p.PayStartDate != "" && p.PayEndDate != "" {
			sql = fmt.Sprintf("select product_id, sum(paid_orders) paid_orders, sum(refund_orders) refund_orders "+
				"from order_statistics where pay_way = %d and pay_date >= %q and pay_date <= %q and game_id=%d group by product_id ", p.PayWay, p.PayStartDate, p.PayEndDate, p.GameID)
		}
	case "all_dimension":
		sql = fmt.Sprintf("select product_id, pay_date, sum(paid_orders) paid_orders, sum(refund_orders) refund_orders, update_time "+
			"from order_statistics where pay_way = %d and game_id=%d group by product_id, pay_date, update_time ", p.PayWay, p.GameID)
		if p.PayStartDate != "" && p.PayEndDate != "" {
			sql = fmt.Sprintf("select product_id, pay_date, sum(paid_orders) paid_orders, sum(refund_orders) refund_orders, update_time "+
				"from order_statistics where pay_way = %d and pay_date >= %q and pay_date <= %q and game_id=%d group by product_id, pay_date, update_time ", p.PayWay, p.PayStartDate, p.PayEndDate, p.GameID)
		}
	case "all_total":
		sql = fmt.Sprintf("select sum(paid_orders) paid_orders, sum(refund_orders) refund_orders "+
			"from order_statistics where pay_way = %d and game_id=%d", p.PayWay, p.GameID)
		if p.PayStartDate != "" && p.PayEndDate != "" {
			sql = fmt.Sprintf("select sum(paid_orders) paid_orders, sum(paid_price) paid_price, sum(refund_orders) refund_orders, sum(refund_price) refund_price "+
				"from order_statistics where pay_way = %d and pay_date >= %q and pay_date <= %q and game_id=%d", p.PayWay, p.PayStartDate, p.PayEndDate, p.GameID)
		}
	}
	if p.Field != "" {
		sql += fmt.Sprintf(" order by %s ", p.Field)
		if p.Order == "ascend" {
			sql += "asc"
		} else {
			sql += "desc"
		}
	}
	var total int64
	db.Raw(sql).Count(&total)
	if p.PageNumber != 0 && p.PageSize != 0 {
		offset := p.PageSize * (p.PageNumber - 1)
		sql = sql + fmt.Sprintf(" limit %d offset %d", p.PageSize, offset)
	}
	db.Raw(sql).Scan(&orderData)
	if total == 0 {
		total = int64(len(orderData))
	}
	return orderData, total
}

func GetOrderStatisticsRows(db *gorm.DB, pageNum int, pageSize int, p schema.OrderStatisticsParams) *sql.Rows {
	var sql string
	switch p.Dimension {
	case "pay_date":
		if p.PayStartDate != "" && p.PayEndDate != "" {
			sql = fmt.Sprintf("select pay_date, sum(paid_orders) paid_orders, sum(paid_price) paid_price, sum(refund_orders) refund_orders, sum(refund_price) refund_price "+
				"from order_statistics where pay_way= %d and pay_date >= %q and pay_date <= %q and game_id=%d group by pay_date ", p.PayWay, p.PayStartDate, p.PayEndDate, p.GameID)
		} else {
			sql = fmt.Sprintf("select pay_date, sum(paid_orders) paid_orders, sum(paid_price) paid_price, sum(refund_orders) refund_orders, sum(refund_price) refund_price "+
				"from order_statistics where pay_way = %d and game_id=%d group by pay_date ", p.PayWay, p.GameID)
		}
	case "product_id":
		sql = fmt.Sprintf("select product_id, sum(paid_orders) paid_orders, sum(paid_price) paid_price, sum(refund_orders) refund_orders, sum(refund_price) refund_price "+
			"from order_statistics where pay_way = %d and game_id=%d group by product_id ", p.PayWay, p.GameID)
		if p.PayStartDate != "" && p.PayEndDate != "" {
			sql = fmt.Sprintf("select product_id, sum(paid_orders) paid_orders, sum(paid_price) paid_price, sum(refund_orders) refund_orders, sum(refund_price) refund_price "+
				"from order_statistics where pay_way = %d and pay_date >= %q and pay_date <= %q and game_id=%d group by product_id ", p.PayWay, p.PayStartDate, p.PayEndDate, p.GameID)
		}
	case "all_dimension":
		sql = fmt.Sprintf("select product_id, pay_date, sum(paid_orders) paid_orders, sum(paid_price) paid_price, sum(refund_orders) refund_orders, sum(refund_price) refund_price, update_time "+
			"from order_statistics where pay_way = %d and game_id=%d group by product_id, pay_date, update_time ", p.PayWay, p.GameID)
		if p.PayStartDate != "" && p.PayEndDate != "" {
			sql = fmt.Sprintf("select product_id, pay_date, sum(paid_orders) paid_orders, sum(paid_price) paid_price, sum(refund_orders) refund_orders, sum(refund_price) refund_price, update_time "+
				"from order_statistics where pay_way = %d and pay_date >= %q and pay_date <= %q and game_id=%d group by product_id, pay_date, update_time ", p.PayWay, p.PayStartDate, p.PayEndDate, p.GameID)
		}
	case "all_total":
		sql = fmt.Sprintf("select sum(paid_orders) paid_orders, sum(paid_price) paid_price, sum(refund_orders) refund_orders, sum(refund_price) refund_price "+
			"from order_statistics where pay_way = %d and game_id=%d", p.PayWay, p.GameID)
		if p.PayStartDate != "" && p.PayEndDate != "" {
			sql = fmt.Sprintf("select sum(paid_orders) paid_orders, sum(paid_price) paid_price, sum(refund_orders) refund_orders, sum(refund_price) refund_price "+
				"from order_statistics where pay_way = %d and pay_date >= %q and pay_date <= %q and game_id=%d", p.PayWay, p.PayStartDate, p.PayEndDate, p.GameID)
		}
	}
	if p.Field != "" {
		sql += fmt.Sprintf(" order by %s ", p.Field)
		if p.Order == "ascend" {
			sql += "asc"
		} else {
			sql += "desc"
		}
	}
	offset := pageSize * (pageNum - 1)
	sql = sql + fmt.Sprintf(" limit %d offset %d", pageSize, offset)
	rows, _ := db.Raw(sql).Rows()
	return rows
}

func DeleteAllOrderStatistics(db *gorm.DB, gameID int, startDate string, endDate string) error {
	return models.DeleteOrderStatistics(db, gameID, startDate, endDate)
}

func OrderStatisticsTask(ctx context.Context, gameID int, startDate string, endDate string) {
	games := game.GetGames()

	for _, game := range games {
		if gameID != 0 && game.Id != gameID {
			continue
		}
		var config payment.Payment
		converter.QueryByGameId(&config, game.Id)

		if *config.IsRechargeOpen != constants.IsRechargeOpen {
			continue
		}
		key := fmt.Sprintf("order_statistics_token_%s", game.Appid)
		err := redis.Lock(key, time.Second*60)
		if err != nil {
			continue
		}

		db, err := GetGameDBByProjectId(game.ProjectID)
		if err != nil {
			continue
		}

		err = DeleteAllOrderStatistics(db, game.Id, startDate, endDate)
		if err != nil {
			return
		}

		for _, payWay := range []int{constants.PayWayApple, constants.PayWayGoogle, constants.PayWayGash, constants.PayWayMyCard, constants.PayWayHuawei, constants.PayWayMiniGameWeChat, constants.PayWayAli, constants.PayWayWeChat, constants.PayWayMiniGameDouyin} {
			orderDB := GetOrderStatistics(ctx, db, payWay, game, startDate, endDate)
			CreateOrderStatistics(ctx, db, orderDB)
		}
	}
}

func CronOrderStatisticsTask(c *cron.Cron) {
	spec := "0 13 * * *" // 每天的13点执行
	c.AddFunc(spec, func() {
		startDate := time.Now().AddDate(0, 0, -3).Format("2006-01-02")
		endDate := time.Now().Format("2006-01-02")

		OrderStatisticsTask(context.Background(), 0, startDate, endDate)
	})
}
