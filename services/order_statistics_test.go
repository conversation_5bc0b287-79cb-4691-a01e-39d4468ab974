package services

import (
	"context"
	"database/sql"
	"reflect"
	"testing"

	"zeus/common/constants"
	"zeus/models"
	"zeus/schema"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestGetOrderStatisticsByDimension(t *testing.T) {
	db := &gorm.DB{}

	c := &gin.Context{}
	mockMustGetMethod := ApplyMethod(reflect.TypeOf(c), "MustGet", func(_ *gin.Context, _ string) any {
		return db
	})
	defer mockMustGetMethod.Reset()

	mockRawMethod := ApplyMethod(reflect.TypeOf(db), "Raw", func(_ *gorm.DB, _ string, _ ...interface{}) (tx *gorm.DB) {
		return db
	})
	defer mockRawMethod.Reset()

	mockCountMethod := ApplyMethod(reflect.TypeOf(db), "Count", func(_ *gorm.DB, count *int64) (tx *gorm.DB) {
		return db
	})
	defer mockCountMethod.Reset()

	mockScanMethod := ApplyMethod(reflect.TypeOf(db), "Scan", func(_ *gorm.DB, dest interface{}) (tx *gorm.DB) {
		return db
	})
	defer mockScanMethod.Reset()

	Convey("GetOrderStatisticsByDimension", t, func() {
		Convey("one func for succ", func() {
			testData := []schema.OrderStatisticsParams{
				{
					Dimension:    "pay_date",
					PayStartDate: "2023-01-01",
					PayEndDate:   "2023-12-01",
					Order:        "ascend",
					Field:        "data",
					PageSize:     10,
					PageNumber:   1,
				},
				{
					Dimension: "pay_date",
				},
				{
					Dimension:    "product_id",
					PayStartDate: "2023-01-01",
					PayEndDate:   "2023-12-01",
				},
				{
					Dimension:    "all_dimension",
					PayStartDate: "2023-01-01",
					PayEndDate:   "2023-12-01",
				},
				{
					Dimension:    "all_total",
					PayStartDate: "2023-01-01",
					PayEndDate:   "2023-12-01",
				},
			}

			for _, data := range testData {
				_, actual := GetOrderStatisticsByDimension(c, data)
				So(actual, ShouldEqual, 0)
			}
		})
	})
}

func TestGetOrderStatisticsRows(t *testing.T) {
	db := &gorm.DB{}

	mockRawMethod := ApplyMethod(reflect.TypeOf(db), "Raw", func(_ *gorm.DB, _ string, _ ...interface{}) (tx *gorm.DB) {
		return db
	})
	defer mockRawMethod.Reset()

	mockScanMethod := ApplyMethod(reflect.TypeOf(db), "Rows", func(_ *gorm.DB) (*sql.Rows, error) {
		return &sql.Rows{}, nil
	})
	defer mockScanMethod.Reset()

	Convey("GetOrderStatisticsRows", t, func() {
		Convey("one func for succ", func() {
			testData := []schema.OrderStatisticsParams{
				{
					Dimension:    "pay_date",
					PayStartDate: "2023-01-01",
					PayEndDate:   "2023-12-01",
					Order:        "ascend",
					Field:        "data",
					PageSize:     10,
					PageNumber:   1,
				},
				{
					Dimension: "pay_date",
				},
				{
					Dimension:    "product_id",
					PayStartDate: "2023-01-01",
					PayEndDate:   "2023-12-01",
				},
				{
					Dimension:    "all_dimension",
					PayStartDate: "2023-01-01",
					PayEndDate:   "2023-12-01",
				},
				{
					Dimension:    "all_total",
					PayStartDate: "2023-01-01",
					PayEndDate:   "2023-12-01",
				},
			}

			for _, data := range testData {
				_ = GetOrderStatisticsRows(db, 1, 10, data)
			}
		})
	})
}

func TestGetOrderStatistics(t *testing.T) {
	db := &gorm.DB{}

	mockRawMethod := ApplyMethod(reflect.TypeOf(db), "Raw", func(_ *gorm.DB, _ string, _ ...interface{}) (tx *gorm.DB) {
		return db
	})
	defer mockRawMethod.Reset()

	mockScanMethod := ApplyMethod(reflect.TypeOf(db), "Scan", func(_ *gorm.DB, dest interface{}) (tx *gorm.DB) {
		return db
	})
	defer mockScanMethod.Reset()

	Convey("GetOrderStatistics", t, func() {
		Convey("one func for succ", func() {
			payWays := []int{constants.PayWayApple, constants.PayWayGoogle, constants.PayWayMyCard, constants.PayWayGash}
			game := models.Game{}
			startDate := "2023-08-01"
			endDate := "2023-08-30"

			ctx := context.Background()
			for _, payWay := range payWays {
				actual := GetOrderStatistics(ctx, db, payWay, game, startDate, endDate)
				So(len(actual), ShouldEqual, 0)
			}
		})
	})
}
