package services

import (
	"context"
	"encoding/json"
	"errors"
	"io"
	"math/rand"
	"net/http"
	"net/http/httptest"
	"reflect"
	"strings"
	"testing"
	"time"

	"zeus/common/constants"
	"zeus/models"
	zeusHttp "zeus/pkg/http"
	"zeus/pkg/redis"
	"zeus/schema"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestOrder(t *testing.T) {
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	c.Request = new(http.Request)
	c.Set("db", &gorm.DB{})

	ApplyMethod(reflect.TypeOf(c), "ClientIP", func(_ *gin.Context) string {
		return "127.0.0.1"
	})

	Convey("generateStr", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(rand.Intn, func(n int) int {
				return 0
			})
			output := generateStr(5)
			expected := "00000"
			So(output, ShouldEqual, expected)
		})
	})

	Convey("GenerateOrderID", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(rand.Intn, func(n int) int {
				return 0
			})

			ApplyFunc(redis.Lock, func(key string, duration time.Duration) error {
				return nil
			})
			var sj time.Time
			ApplyMethod(reflect.TypeOf(sj), "Format", func(_ time.Time, _ string) string {
				return "20221201000000"
			})

			output := GenerateOrderID(constants.PayWayXsolla, 5)

			expected := "xs_20221201000000_00000"
			So(output, ShouldEqual, expected)
		})
	})

	Convey("VerifyToAppstore", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(rand.Intn, func(n int) int {
				return 0
			})
			ApplyFunc(zeusHttp.PostWithTracer, func(ctx context.Context, url string, headers map[string]string, data []byte, _ ...zeusHttp.Option) (resp *http.Response, err error) {
				return &http.Response{}, errors.New("请求失败")
			})

			receipt := "test_receipt"
			output := VerifyToAppstore(context.Background(), receipt, true)
			So(output, ShouldEqual, nil)
		})
	})

	Convey("QueryOrder", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetOneOrder, func(db *gorm.DB, args map[string]interface{}) (*models.Order, error) {
				return &models.Order{Id: 11}, nil
			})

			filter := make(map[string]interface{})
			output, _ := QueryOrder(c, filter)
			So(output.Id, ShouldEqual, 11)
		})
	})

	Convey("GetOrderByOrderID", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetOneOrder, func(db *gorm.DB, args map[string]interface{}) (*models.Order, error) {
				return &models.Order{OrderId: "1111"}, nil
			})
			orderID := "1111"
			output, _ := GetOrderByOrderID(c, orderID)
			So(output.OrderId, ShouldEqual, "1111")
		})
	})

	Convey("SaveOrder", t, func() {
		Convey("one func for fail", func() {
			ApplyFunc(models.SaveOrder, func(db *gorm.DB, args *models.Order) error {
				return errors.New("保存失败")
			})
			o := &models.Order{}
			err := SaveOrder(c, o)
			So(err.Error(), ShouldEqual, "保存失败")
		})
	})

	Convey("CreateOrder", t, func() {
		Convey("one func for fail", func() {
			ApplyFunc(models.CreateOrder, func(db *gorm.DB, args *models.Order) error {
				return errors.New("创建失败")
			})
			o := &models.Order{}
			err := CreateOrder(c, o)
			So(err.Error(), ShouldEqual, "创建失败")
		})
	})

	Convey("CheckOrder", t, func() {
		Convey("one func for succ", func() {
			orderID := "1111"
			o, _ := CheckOrder(c, orderID)
			So(o.OrderId, ShouldEqual, "1111")
		})
	})

	Convey("UpdatePayOrder", t, func() {
		Convey("one func for succ", func() {
			order := schema.OrderPayResult{}
			dbOrder := models.Order{OrderId: "1111"}
			payWay := 1
			UpdatePayOrder(c, order, &dbOrder, payWay)
			So(dbOrder.OrderId, ShouldEqual, "1111")
		})
	})
}

func TestOrderVerify(t *testing.T) {
	ApplyFunc(VerifyToAppstore, func(ctx context.Context, receipt string, isSandbox bool) *http.Response {
		httpResp := &http.Response{}
		resp := map[string]string{"data": "test_data"}
		body, _ := json.Marshal(resp)
		httpResp.Body = io.NopCloser(strings.NewReader(string(body)))
		return httpResp
	})

	Convey("VerifyOnline", t, func() {
		Convey("one func for succ", func() {
			output, _ := VerifyOnline(context.Background(), "paper", "order", "")
			So(output["data"], ShouldEqual, "test_data")
		})
	})

	Convey("VerifySandBox", t, func() {
		Convey("one func for succ", func() {
			output, _ := VerifySandBox(context.Background(), "paper", "order", "")
			So(output["data"], ShouldEqual, "test_data")
		})
	})
}
