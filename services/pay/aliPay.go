package pay

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	"zeus/pkg/ali"
	"zeus/schema"
	"zeus/services"
	"zeus/utils"

	"github.com/gin-gonic/gin"
	"github.com/smartwalle/alipay/v3"
)

type AliOrder struct {
	appid string
}

func NewAliOrder(appid string) *AliOrder {
	return &AliOrder{appid}
}

func CreateAliOrder(c *gin.Context, user models.User, game models.Game, order schema.AliExchangeForm) (*models.Order, string, error) {
	log.Debug(c.Request.Context(), "开始创建支付宝订单", "data", order)

	orderModel, orderStr, err := ali.AliPay(c, order)
	if err != nil {
		return nil, "", err
	}

	orderModel.GameId = game.Id
	orderModel.Account = user.Account
	orderModel.UserId = user.ID
	orderModel.RoleId = order.RoleID
	orderModel.Extend = order.Extend
	orderModel.Device = order.Device
	orderModel.RoleName = order.RoleName
	orderModel.CreateTime = time.Now()
	orderModel.PayNotifyUrl = order.PayNotifyURL
	orderModel.Vip = utils.ConvertStringToInt(order.Vip)
	orderModel.Level = utils.ConvertStringToInt(order.Level)
	orderModel.Platform = order.PlatForm
	orderModel.ServerId = order.ServerID
	orderModel.ServerName = order.ServerName

	err = services.CreateOrder(c, orderModel)
	if err != nil {
		return orderModel, orderStr, err
	}

	return orderModel, orderStr, nil
}

func (ao *AliOrder) VerifyPayOrder(c *gin.Context, dbOrder *models.Order) (schema.OrderPayResult, error) {
	var OrderPayResult schema.OrderPayResult
	aliOrderResult, err := ali.QueryAliPayTrade(c, *dbOrder, ao.appid)
	if err != nil {
		errorMessage := fmt.Sprintf("报错信息：%s, 支付宝支付订单认证失败导致无法完成支付操作, 请确认支付宝支付相关参数配置是否正确", err)
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			return OrderPayResult, err
		}
		return OrderPayResult, errors.New(errorMessage)
	}
	switch aliOrderResult.TradeStatus {
	case alipay.TradeStatusSuccess:
		OrderPayResult.Status = constants.PaymentSuccess
	case alipay.TradeStatusClosed:
		OrderPayResult.Status = constants.PaymentUnpaid
	}
	amount, _ := strconv.ParseFloat(aliOrderResult.TotalAmount, 64)
	OrderPayResult.Amount = amount * 100 // 由单位为元转换为分，存入数据库
	OrderPayResult.Currency = aliOrderResult.PayCurrency
	OrderPayResult.PayID = aliOrderResult.TradeNo
	return OrderPayResult, nil
}
