package apple

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"golang.org/x/exp/slices"
	"gorm.io/gorm"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/apple"
	zeusHttp "zeus/pkg/http"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"

	"zeus/common/log"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
)

type MyClaims struct {
	Bid string `json:"bid"`
	jwt.StandardClaims
}

type NotificationInfoClaims struct {
	*schema.NotificationInfo
	jwt.StandardClaims
}

type SignedTransactionClaims struct {
	*schema.SignedTransactionInfo
	jwt.StandardClaims
}

type SignedPayload struct {
	SignedPayload          string `json:"signedPayload"`
	FirstSendAttemptResult string `json:"firstSendAttemptResult"`
}
type NotificationHistoryResponse struct {
	NotificationHistory []SignedPayload `json:"notificationHistory"`
	PaginationToken     string          `json:"paginationToken"`
	HasMore             bool            `json:"hasMore"`
}

type AppleClient struct {
	ISS    string `json:"iss"`
	BID    string `json:"bid"`
	KeyID  string `json:"key_id"`
	Secret string `json:"secret"`
}

type InAppsHostoryResponse struct {
	Revision           string   `json:"revision"`
	BundleId           string   `json:"bundleId"`
	Environment        string   `json:"environment"`
	HasMore            bool     `json:"hasMore"`
	SignedTransactions []string `json:"signedTransactions"`
}

type InAppsTransactionsResponse struct {
	SignedTransactionInfo string `json:"signedTransactionInfo"`
}

type TransactionInfo struct {
	BundleId              string `json:"bundleId"`
	Environment           string `json:"environment"`
	InAppOwnershipType    string `json:"inAppOwnershipType"`
	OriginalPurchaseDate  int64  `json:"originalPurchaseDate"`
	OriginalTransactionId string `json:"originalTransactionId"`
	ProductId             string `json:"productId"`
	PurchaseDate          int64  `json:"purchaseDate"`
	Quantity              string `json:"quantity"`
	SignedDate            string `json:"signedDate"`
	Storefront            string `json:"storefront"`
	StorefrontId          string `json:"storefrontId"`
	TransactionId         string `json:"transactionId"`
	TransactionReason     string `json:"transactionReason"`
	Type                  string `json:"type"`
}

type IOSTransaction struct {
	Status             int      `json:"status"`
	SignedTransactions []string `json:"signedTransactions"`
}

type TransactionOrder struct {
	OrderId       string    `json:"order_id"`
	PayId         string    `json:"pay_id"`
	TransactionID string    `json:"transaction_id"`
	ProductID     string    `json:"product_id"`
	ServerId      string    `json:"server_id"`
	ServerName    string    `json:"server_name"`
	CreateTime    time.Time `json:"create_time"`
	PayTime       time.Time `json:"pay_time"`
	PayStatus     int       `json:"pay_status"`
	NotifyStatus  int       `json:"notify_status"`
	OverTime      bool      `json:"over_time"`
}

type TransactionInfoClaims struct {
	TransactionInfo
	jwt.StandardClaims
}

func (c *AppleClient) HttpGet(ctx context.Context, url string) (*http.Response, error) {
	headers := map[string]string{
		"Accept":        "application/json",
		"Authorization": fmt.Sprintf("Bearer %s", apple.GenerateJWTToken(c.ISS, c.BID, c.KeyID, c.Secret, "appstoreconnect-v1")),
	}

	log.Debug(ctx, "开始请求接口获取数据", "method", "GET", "url", url)
	return zeusHttp.GetWithTracer(ctx, url, headers)
}

func (c *AppleClient) HttpPost(ctx context.Context, url string, data map[string]string) (*http.Response, error) {
	bytesData, _ := json.Marshal(data)
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": fmt.Sprintf("Bearer %s", apple.GenerateJWTToken(c.ISS, c.BID, c.KeyID, c.Secret, "appstoreconnect-v1")),
	}

	log.Debug(ctx, "开始请求接口获取数据", "url", url, "method", "POST", "data", data)
	return zeusHttp.PostWithTracer(ctx, url, headers, bytesData)
}

func VerifyIOSOrderWithTransactionID(ctx context.Context, appleISS string, appleKeyID string, appleBundleId string, appleSecret string, originTransactionID string) (schema.SignedTransactionInfo, error) {
	ac := AppleClient{
		ISS:    appleISS,
		KeyID:  appleKeyID,
		BID:    appleBundleId,
		Secret: appleSecret,
	}

	isSandBox := false
	transactionInfo, err := ac.VerifyOrderWithOriginTransactionID(ctx, originTransactionID, isSandBox)
	if transactionInfo.TransactionId == "" {
		isSandBox = true
		transactionInfo, err = ac.VerifyOrderWithOriginTransactionID(ctx, originTransactionID, isSandBox)
	}
	return transactionInfo, err
}

func (c *AppleClient) VerifyOrderWithOriginTransactionID(ctx context.Context, originTransactionID string, isSandbox bool) (schema.SignedTransactionInfo, error) {
	var url string
	var transactionInfo schema.SignedTransactionInfo
	if isSandbox {
		url = fmt.Sprintf("%s/%s", constants.AppleSandboxInAppsV1TransactionsUrl, originTransactionID)
	} else {
		url = fmt.Sprintf("%s/%s", constants.AppleInAppsV1TransactionsUrl, originTransactionID)
	}

	resp, err := c.HttpGet(ctx, url)
	if err != nil {
		return transactionInfo, err
	}

	body, _ := io.ReadAll(resp.Body)
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return transactionInfo, errors.New(string(body))
	}
	var info InAppsTransactionsResponse
	json.Unmarshal(body, &info)
	signedTransactionParse, _ := jwt.ParseWithClaims(info.SignedTransactionInfo, &SignedTransactionClaims{}, func(token *jwt.Token) (i interface{}, err error) {
		return c.Secret, nil
	})
	if payload, ok := signedTransactionParse.Claims.(*SignedTransactionClaims); ok {
		log.Debug(ctx, "与Apple校验订单订单结果", "transaction_id", originTransactionID, "result", payload)
		if payload == nil {
			return transactionInfo, errors.New("parse signedPayload error")
		}
		return *payload.SignedTransactionInfo, nil
	}
	return transactionInfo, nil
}

type NotificationCallbackClaims struct {
	*schema.NotificationCallbackParams
	jwt.StandardClaims
}

func (c *AppleClient) ParseNotifyPayload(ctx context.Context, tokenString string) (*schema.NotificationCallbackParams, error) {
	notificationCallbackInfo, _ := jwt.ParseWithClaims(tokenString, &NotificationCallbackClaims{}, func(token *jwt.Token) (i interface{}, err error) {
		return c.Secret, nil
	})
	if notificationCallbackClaims, ok := notificationCallbackInfo.Claims.(*NotificationCallbackClaims); ok {
		if notificationCallbackClaims.NotificationCallbackParams == nil {
			return nil, errors.New("parse signedPayload error")
		}
		return notificationCallbackClaims.NotificationCallbackParams, nil
	}
	return nil, errors.New("parse callback notification error")
}

func (c *AppleClient) parsePayload(ctx context.Context, tokenString string) (*schema.NotificationInfo, error) {
	// 解析 第一层 signedPayload
	notificationInfo, _ := jwt.ParseWithClaims(tokenString, &NotificationInfoClaims{}, func(token *jwt.Token) (i interface{}, err error) {
		return c.Secret, nil
	})

	if notificationInfoClaims, ok := notificationInfo.Claims.(*NotificationInfoClaims); ok {
		log.Debug(ctx, "开始解析第一层signedPayload", "signedPayload", tokenString)

		if notificationInfoClaims.NotificationInfo == nil {

			log.Error(ctx, "解析第一层signedPayload失败", "signedPayload", tokenString)

			return nil, errors.New("parse signedPayload error")
		}
		signedTransactionInfo := notificationInfoClaims.NotificationInfo.Data.SignedTransactionInfo

		log.Debug(ctx, "解析第一层signedPayload完成", "notificationInfo", "notificationInfoClaims.NotificationInfo")

		// 解析 第二层 signedTransactionInfo
		signedTransactionParse, _ := jwt.ParseWithClaims(signedTransactionInfo, &SignedTransactionClaims{}, func(token *jwt.Token) (i interface{}, err error) {
			return c.Secret, nil
		})

		log.Debug(ctx, "开始解析第二层signedTransactionInfo", "signedTransactionInfo", signedTransactionInfo)

		if transactionInfoClaims, ok := signedTransactionParse.Claims.(*SignedTransactionClaims); ok {
			if transactionInfoClaims.SignedTransactionInfo == nil {
				log.Error(ctx, "解析第二层signedTransactionInfo失败", "signedTransactionInfo", signedTransactionInfo)

				return nil, errors.New("parse signedTransactionInfo error")
			}
			notificationInfoClaims.NotificationInfo.Data.SignedTransactionInfoParse = transactionInfoClaims.SignedTransactionInfo

			log.Debug(ctx, "解析第二层signedTransactionInfo完成", "SignedTransactionInfo", transactionInfoClaims.SignedTransactionInfo)

		}
		return notificationInfoClaims.NotificationInfo, nil
	}
	return nil, errors.New("parse jws error")
}

func (c *AppleClient) GetTransactionResult(ctx context.Context, paginationToken string, data map[string]string) (*NotificationHistoryResponse, error) {
	url := constants.AppleUrlNotificationHistory

	if paginationToken != "" {
		url += fmt.Sprintf("?paginationToken=%s", paginationToken)
	}

	response, _ := c.HttpPost(ctx, url, data)
	defer response.Body.Close()

	var result *NotificationHistoryResponse
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(body, &result)
	if err != nil {
		log.Debug(ctx, "获取苹果通知历史订单数据失败", "error", err.Error())
		return nil, err
	}
	log.Debug(ctx, "获取苹果通知历史订单数据成功", "data", data)
	return result, nil
}

func (c *AppleClient) ChangeOrderPayStatus(ctx context.Context, db *gorm.DB, gameID int, payloads []SignedPayload) []string {
	var orderIds []string
	for _, notificationData := range payloads {
		signedPayload := notificationData.SignedPayload
		transactionInfo, err := c.parsePayload(ctx, signedPayload)
		if err != nil {
			log.Error(ctx, "解析苹果signedPayload失败", "error", err.Error())
			continue
		}
		log.Debug(ctx, "解析苹果signedPayload成功", "transactionInfo", transactionInfo, "signedPayload", signedPayload)
		if transactionInfo != nil && transactionInfo.NotificationType == constants.NotificationTypeRefund {
			services.UpdateOrderPayStatus(ctx, db, gameID, transactionInfo.Data.SignedTransactionInfoParse)
			orderIds = append(orderIds, transactionInfo.Data.SignedTransactionInfoParse.TransactionId)
		}
	}
	return orderIds
}

// UpdateAppleRefundOrder 获取ios历史通知订单
func UpdateAppleRefundOrder(ctx context.Context, gameId int, appleClient AppleClient, startDate string, endDate string) ([]string, error) {
	var orderIds []string
	if appleClient.ISS == "" {
		return orderIds, errors.New("当前项目缺少请求苹果服务器的认证信息")
	}

	dbGame, _ := game.GetGameById(gameId)
	db, _ := services.GetGameDBByProjectId(dbGame.ProjectID)
	params := map[string]string{
		"notificationType": constants.NotificationTypeRefund,
	}

	if startDate != "" {
		startDateTime, _ := time.Parse("2006-01-02", startDate)
		params["startDate"] = strconv.FormatInt(startDateTime.UnixMilli(), 10)
	}

	if endDate != "" {
		endDateTime, _ := time.Parse("2006-01-02", endDate)
		params["endDate"] = strconv.FormatInt(endDateTime.UnixMilli(), 10)
	}

	var payloads []SignedPayload
	var paginationToken string
	for {
		result, err := appleClient.GetTransactionResult(ctx, paginationToken, params)
		if err != nil {
			return orderIds, err
		}
		payloads = append(payloads, result.NotificationHistory...)
		if result.HasMore {
			paginationToken = result.PaginationToken
		} else {
			break
		}
	}
	orderIds = appleClient.ChangeOrderPayStatus(ctx, db, dbGame.Id, payloads)
	return orderIds, nil
}

func CheckAppleOrder(transactionInfo schema.SignedTransactionInfo, dbOrder *models.Order, bid string) error {
	if transactionInfo.AppAccountToken != dbOrder.OrderId {
		return fmt.Errorf("支付票据内的订单ID为%s, 与SDK传入的订单ID%s不一致", transactionInfo.AppAccountToken, dbOrder.OrderId)
	}

	if transactionInfo.ProductId != dbOrder.ProductId {
		return fmt.Errorf("实际支付的商品为%s, 与订单商品%s不一致", transactionInfo.ProductId, dbOrder.ProductId)
	}

	if transactionInfo.OriginalPurchaseDate < dbOrder.CreateTime.UnixMilli() {
		return fmt.Errorf("支付票据内支付时间 %s, 比订单创建时间 %s早",
			time.Unix(transactionInfo.OriginalPurchaseDate/1000, 0).In(time.FixedZone("CST", 8*3600)),
			dbOrder.CreateTime)
	}

	if transactionInfo.RevocationDate > 0 {
		return fmt.Errorf("支付票据状态为已退款 , 退款时间为:%s",
			time.Unix(transactionInfo.RevocationDate/1000, 0).In(time.FixedZone("CST", 8*3600)))
	}

	if bid != transactionInfo.BundleId {
		return fmt.Errorf("当前订单的BID为%s, 与项目配置的%s不一致", transactionInfo.BundleId, bid)
	}

	return nil
}

func GetOrderByOrderID(ctx context.Context, appleClient AppleClient, purchaseOrderID string) ([]TransactionInfo, error) {
	if appleClient.ISS == "" {
		return nil, errors.New("当前项目缺少请求苹果服务器的认证信息, 请前往支付服务页面配置")
	}
	transactionInfo, err := appleClient.GetTransactionByPurchaseOrderID(ctx, purchaseOrderID)
	if err != nil {
		return nil, err
	}

	parseTransactionInfo := appleClient.ParseTransactionInfo(transactionInfo)
	return parseTransactionInfo, nil
}

func (c *AppleClient) GetTransactionByPurchaseOrderID(ctx context.Context, purchaseOrderID string) (*IOSTransaction, error) {
	resp, err := c.HttpGet(ctx, constants.AppleInAppsV1LookUpUrl+purchaseOrderID)
	if err != nil {
		return nil, err
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var result IOSTransaction
	err = json.Unmarshal(body, &result)
	if err != nil {
		errMessage := "根据ios票据获取订单信息失败"
		log.Error(ctx, errMessage, "purchaseOrderID", purchaseOrderID, "error", err.Error())
		return nil, errors.New(errMessage)
	}
	return &result, nil
}

func GetOrderByTransactionInfo(ctx *gin.Context, transactions []TransactionInfo, purchaseOrderID string, roleId string) []TransactionOrder {
	var transactionOrders []TransactionOrder
	for _, t := range transactions {
		iosProductId := t.ProductId
		iosTransactionId := t.TransactionId
		iosPurchaseDate := t.PurchaseDate

		log.Debug(ctx, "根据ios票据查询订单", "roleId", roleId, "productId", iosProductId, "purchaseOrderID", purchaseOrderID, "transactionId", t)
		filter := map[string]interface{}{
			"product_id": iosProductId,
			"role_id":    roleId,
		}
		dbOrders, _ := services.QueryOrders(ctx, filter)
		for _, dbOrder := range dbOrders {
			if dbOrder.PayId == iosTransactionId {
				log.Debug(ctx, "根据ios票据查询订单成功", "order_id", dbOrder.OrderId, "purchaseOrderID", purchaseOrderID)

				transactionOrder := TransactionOrder{}
				transactionOrder.OrderId = dbOrder.OrderId
				transactionOrder.PayId = dbOrder.PayId
				transactionOrder.ProductID = iosProductId
				transactionOrder.TransactionID = dbOrder.PayId
				transactionOrder.ServerId = dbOrder.ServerId
				transactionOrder.ServerName = dbOrder.ServerName
				transactionOrder.CreateTime = dbOrder.CreateTime
				transactionOrder.PayTime = time.Unix(iosPurchaseDate/1000, 0)
				transactionOrder.PayStatus = dbOrder.PayStatus
				transactionOrder.NotifyStatus = dbOrder.NotifyStatus
				transactionOrders = append(transactionOrders, transactionOrder)
				break
			} else if dbOrder.PayId == "" {
				if dbOrder.CreateTime.UnixMilli() < iosPurchaseDate {
					transactionOrder := TransactionOrder{}
					purchaseDate := time.Unix(iosPurchaseDate/1000, 0).In(time.FixedZone("CST", 8*3600))

					timeDiff := purchaseDate.Sub(dbOrder.CreateTime).Seconds()
					if timeDiff >= 30 {
						transactionOrder.OverTime = true
					}

					log.Debug(ctx, "根据ios票据查询支付时间接近的订单成功", "order_id", dbOrder.Id, "purchaseOrderId", purchaseOrderID)

					transactionOrder.OrderId = dbOrder.OrderId
					transactionOrder.TransactionID = t.TransactionId
					transactionOrder.ProductID = iosProductId
					transactionOrder.ServerId = dbOrder.ServerId
					transactionOrder.ServerName = dbOrder.ServerName
					transactionOrder.CreateTime = dbOrder.CreateTime
					transactionOrder.PayTime = time.Unix(iosPurchaseDate/1000, 0)
					transactionOrder.PayStatus = dbOrder.PayStatus
					transactionOrder.NotifyStatus = dbOrder.NotifyStatus
					transactionOrders = append(transactionOrders, transactionOrder)
					break
				}
			}
		}
	}
	return transactionOrders
}

func (c *AppleClient) ParseTransactionInfo(transaction *IOSTransaction) []TransactionInfo {
	if transaction.Status != 0 {
		return []TransactionInfo{}
	}

	var TransactionInfos []TransactionInfo

	for _, signedTransaction := range transaction.SignedTransactions {
		transactionInfo, _ := jwt.ParseWithClaims(signedTransaction, &TransactionInfoClaims{}, func(token *jwt.Token) (i interface{}, err error) {
			return c.Secret, nil
		})
		if transactionInfoClaims, ok := transactionInfo.Claims.(*TransactionInfoClaims); ok {
			TransactionInfos = append(TransactionInfos, transactionInfoClaims.TransactionInfo)
		}
	}
	return TransactionInfos
}

type CallbackNotification struct {
	SignedPayload string `json:"signedPayload"`
}

// SubscriptionCallbackDecoded 参数文档: https://developer.apple.com/documentation/appstoreservernotifications/responsebodyv2decodedpayload
type SubscriptionCallbackDecoded struct {
	NotificationType string                   `json:"notificationType"`
	Subtype          string                   `json:"subtype"`
	Data             SubscriptionCallbackData `json:"data"`
	Summary          string                   `json:"summary"`
	Version          string                   `json:"version"`
	SignedDate       string                   `json:"signedDate"`
	NotificationUUID string                   `json:"notificationUUID"`
}

type SubscriptionCallbackData struct {
	AppAppleId            string `json:"appAppleId"`
	BundleId              string `json:"bundleId"`
	BundleVersion         string `json:"bundleVersion"`
	Environment           string `json:"environment"`
	SignedRenewalInfo     string `json:"signedRenewalInfo"`
	SignedTransactionInfo string `json:"signedTransactionInfo"`
	Status                string `json:"status"`
}

type CallbackRenewalInfoDecoded struct {
	AutoRenewProductId          string `json:"autoRenewProductId"`
	AutoRenewStatus             string `json:"autoRenewStatus"`
	ExpirationIntent            string `json:"expirationIntent"`
	GracePeriodExpiresDate      int    `json:"gracePeriodExpiresDate"`
	IsInBillingRetryPeriod      string `json:"isInBillingRetryPeriod"`
	OfferIdentifier             string `json:"offerIdentifier"`
	OfferType                   string `json:"offerType"`
	OriginalTransactionId       string `json:"originalTransactionId"`
	PriceIncreaseStatus         string `json:"priceIncreaseStatus"`
	ProductId                   string `json:"productId"`
	RecentSubscriptionStartDate int    `json:"recentSubscriptionStartDate"`
	RenewalDate                 int    `json:"renewalDate"`
	SignedDate                  int    `json:"signedDate"`
}

type CallbackTransactionInfoDecoded struct {
	AppAccountToken             string `json:"appAccountToken"`
	BundleId                    string `json:"bundleId"`
	Environment                 string `json:"environment"`
	ExpiresDate                 int    `json:"expiresDate"`
	InAppOwnershipType          string `json:"inAppOwnershipType"`
	IsUpgraded                  string `json:"isUpgraded"`
	OfferIdentifier             string `json:"offerIdentifier"`
	OriginalPurchaseDate        int    `json:"offerType"`
	OriginalTransactionId       string `json:"originalTransactionId"`
	ProductId                   string `json:"productId"`
	PurchaseDate                int    `json:"purchaseDate"`
	Quantity                    string `json:"quantity"`
	RevocationDate              int    `json:"revocationDate"`
	RevocationReason            string `json:"revocationReason"`
	SignedDate                  int    `json:"signedDate"`
	Storefront                  string `json:"storefront"`
	StorefrontId                string `json:"storefrontId"`
	SubscriptionGroupIdentifier string `json:"subscriptionGroupIdentifier"`
	TransactionId               string `json:"transactionId"`
	TransactionReason           string `json:"transactionReason"`
	Type                        string `json:"type"`
	WebOrderLineItemId          string `json:"webOrderLineItemId"`
}

type CallbackNotificationInfo struct {
	NotificationType      string `json:"notificationType"`
	BundleId              string `json:"bundleId"`
	Environment           string `json:"environment"`
	AppAccountToken       string `json:"appAccountToken"`
	ExpiresDate           int    `json:"expiresDate"`
	OriginalTransactionId string `json:"originalTransactionId"`
	TransactionId         string `json:"transactionId"`
	ProductId             string `json:"productId"`
	TransactionReason     string `json:"transactionReason"`
	Type                  string `json:"type"`
	NotificationUUID      string `json:"notificationUUID"`
}

type CallbackNotificationClaims struct {
	SubscriptionCallbackDecoded
	jwt.StandardClaims
}

type NotificationClaims struct {
	CallbackTransactionInfoDecoded
	jwt.StandardClaims
}

func (c *AppleClient) ParseSubscriptionCallbackNotification(ctx context.Context, notification CallbackNotification) (*CallbackNotificationInfo, error) {
	var NotificationInfo *CallbackNotificationInfo
	NotificationInfo = new(CallbackNotificationInfo)

	callbackNotificationInfo := new(SubscriptionCallbackDecoded)
	callbackNotification, _ := jwt.ParseWithClaims(notification.SignedPayload, &CallbackNotificationClaims{}, func(token *jwt.Token) (i interface{}, err error) {
		return c.Secret, nil
	})
	if notificationInfoClaims, ok := callbackNotification.Claims.(*CallbackNotificationClaims); ok {
		callbackNotificationInfo = &notificationInfoClaims.SubscriptionCallbackDecoded
	}
	log.Debug(ctx, "IOS订阅通知回调数据解析", "callbackNotificationInfo", callbackNotificationInfo)
	availableNotificationTypes := []string{"REFUND", "DID_RENEW"}
	if !slices.Contains(availableNotificationTypes, callbackNotificationInfo.NotificationType) {
		log.Debug(ctx, fmt.Sprintf("非IOS自动续订或订阅退款类型(DID_RENEW或REFUND)"), "NotificationType", callbackNotificationInfo.NotificationType)

		return NotificationInfo, errors.New("非IOS自动续订或订阅退款类型")
	}

	SignedTransactionInfo := callbackNotificationInfo.Data.SignedTransactionInfo
	callbackNotificationDataInfo := new(CallbackTransactionInfoDecoded)
	callbackNotificationData, _ := jwt.ParseWithClaims(SignedTransactionInfo, &NotificationClaims{}, func(token *jwt.Token) (i interface{}, err error) {
		return c.Secret, nil
	})
	if notificationInfoClaims, ok := callbackNotificationData.Claims.(*NotificationClaims); ok {
		callbackNotificationDataInfo = &notificationInfoClaims.CallbackTransactionInfoDecoded
	}

	log.Debug(ctx, "IOS订阅通知回调transaction数据解析", "callbackNotificationDataInfo", callbackNotificationDataInfo)

	NotificationInfo.NotificationType = callbackNotificationInfo.NotificationType
	NotificationInfo.BundleId = callbackNotificationDataInfo.BundleId
	NotificationInfo.Environment = callbackNotificationDataInfo.Environment
	NotificationInfo.AppAccountToken = callbackNotificationDataInfo.AppAccountToken
	NotificationInfo.ExpiresDate = callbackNotificationDataInfo.ExpiresDate
	NotificationInfo.OriginalTransactionId = callbackNotificationDataInfo.OriginalTransactionId
	NotificationInfo.TransactionId = callbackNotificationDataInfo.TransactionId
	NotificationInfo.ProductId = callbackNotificationDataInfo.ProductId
	NotificationInfo.TransactionReason = callbackNotificationDataInfo.TransactionReason
	NotificationInfo.Type = callbackNotificationDataInfo.Type
	NotificationInfo.NotificationUUID = callbackNotificationInfo.NotificationUUID

	log.Debug(ctx, "IOS订阅通知回调主要参数", "NotificationInfo", NotificationInfo)

	return NotificationInfo, nil
}
