package pay

import (
	"errors"

	"zeus/common/constants"
	"zeus/models"
	"zeus/schema"
	"zeus/services/pay/douyin"
	"zeus/services/pay/wechat"
	"zeus/services/pay/wechat/minigame"

	"github.com/gin-gonic/gin"
)

type Order interface {
	VerifyPayOrder(c *gin.Context, dbOrder *models.Order) (schema.OrderPayResult, error)
}

func GetOrder(payWay int64, appid string) (order Order, err error) {
	switch payWay {
	case constants.PayWayWeChat:
		order = wechat.NewWeChatOrder(appid)
	case constants.PayWayAli:
		order = NewAliOrder(appid)
	case constants.PayWayMiniGameWeChat:
		order = minigame.NewJsapiOrder(appid)
	case constants.PayWayMiniGameDouyin:
		order = douyin.NewDouyinOrder(appid)
	default:
		err = errors.New("invalid pay way")
	}
	return order, err
}
