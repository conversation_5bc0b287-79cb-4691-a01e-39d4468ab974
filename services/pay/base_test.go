package pay

import (
	"testing"

	"zeus/common/constants"
	"zeus/services/pay/wechat"

	. "github.com/smartystreets/goconvey/convey"
)

func TestGetOrder(t *testing.T) {
	Convey("TestGetOrder", t, func() {
		Convey("TestGetOrder With WeChat", func() {
			appid := "test_appid"
			order, err := GetOrder(constants.PayWayWeChat, appid)
			So(order, ShouldResemble, wechat.NewWeChatOrder(appid))
			So(err, ShouldBeNil)
		})

		Convey("TestGetOrder With Available AliPay", func() {
			appid := "test_appid"
			order, err := GetOrder(constants.PayWayAli, appid)
			So(order, ShouldResemble, NewAliOrder(appid))
			So(err, ShouldBeNil)
		})

		Convey("TestGetOrder With Invaild pay way", func() {
			appid := "test_appid"
			order, err := GetOrder(constants.PayWayApple, appid)
			So(order, ShouldBeNil)
			So(err, ShouldNotBeNil)
		})
	})
}
