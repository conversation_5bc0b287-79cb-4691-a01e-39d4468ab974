package douyin

import (
	"time"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	"zeus/pkg/douyin"
	"zeus/schema"
	"zeus/services"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
	"zeus/utils"

	"github.com/gin-gonic/gin"
)

type DouyinOrder struct {
	appid string
}

func NewDouyinOrder(appid string) *DouyinOrder {
	return &DouyinOrder{appid}
}

func CreateDouyinOrder(c *gin.Context, user models.User, game models.Game, order schema.DouyinMiniGameExchange) (schema.DouyinOrder, error) {
	log.Debug(c.Request.Context(), "开始创建抖音订单", "data", order)
	var douyinOrder schema.DouyinOrder

	detail, _ := services.GetUserBindDouyinDetail(c, user.ID)

	dbOrder := &models.Order{}

	dbOrder.OrderId = services.GenerateOrderID(constants.PayWayMiniGameDouyin, constants.OrderIDTokenSize)
	dbOrder.GameId = game.Id
	dbOrder.Account = user.Account
	dbOrder.UserId = user.ID
	dbOrder.RoleId = order.RoleID
	dbOrder.OpenId = detail.OpenId
	dbOrder.ProductId = order.ProductID
	dbOrder.Price = order.Price
	dbOrder.Extend = order.Extend
	dbOrder.Device = order.Device
	dbOrder.RoleName = order.RoleName
	dbOrder.CreateTime = time.Now()
	dbOrder.PayNotifyUrl = order.PayNotifyURL
	dbOrder.Vip = utils.ConvertStringToInt(order.Vip)
	dbOrder.Level = utils.ConvertStringToInt(order.Level)
	dbOrder.Platform = order.PlatForm
	dbOrder.ServerId = order.ServerID
	dbOrder.ServerName = order.ServerName

	err := services.CreateOrder(c, dbOrder)
	if err != nil {
		return douyinOrder, err
	}

	douyinOrder.OrderID = dbOrder.OrderId

	return douyinOrder, nil
}

func (ao *DouyinOrder) VerifyPayOrder(c *gin.Context, dbOrder *models.Order) (schema.OrderPayResult, error) {
	var config payment.MiniGameDouyinPay
	converter.QueryByAppid(&config, ao.appid)

	var orderPayResult schema.OrderPayResult
	amt := int64(dbOrder.Price / 100)
	client := douyin.Client{Appid: *config.DouyinAppId, Secret: *config.DouyinSecret, SignKey: *config.DouyinSignKey}
	billNo, err := client.WalletGamePay(c.Request.Context(), dbOrder.OpenId, dbOrder.ServerId, amt, dbOrder.OrderId)
	if err != nil {
		return orderPayResult, err
	}
	orderPayResult.Amount = dbOrder.Price
	orderPayResult.PayID = billNo
	orderPayResult.Currency = "CNY"
	orderPayResult.Status = 1

	return orderPayResult, nil
}
