package pay

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	zeusHttp "zeus/pkg/http"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"zeus/common/constants"
	"zeus/pkg/redis"

	"zeus/common/log"
)

func getGoogleAccessToken(appid string) string {
	key := redis.GetRedisKey("token", appid)
	accessToken := redis.Get(key)
	return accessToken
}

func getAccessTokenFromGoogle(ctx context.Context, appid string, orderID string) (string, error) {
	log.Debug(ctx, "开始获取 google access token ")
	token := getGoogleAccessToken(appid)
	if token == "" {
		var config payment.GooglePay
		converter.QueryByAppid(&config, appid)

		params := AccessTokenForm{
			RefreshToken: *config.GoogleRefreshToken,
			ClientId:     *config.GoogleClientId,
			ClientSecret: *config.GoogleClientSecret,
			GrantType:    "refresh_token",
		}
		paramsData, _ := json.Marshal(params)
		headers := map[string]string{"Content-Type": "application/json;charset=utf-8"}
		resp, err := zeusHttp.PostWithTracer(ctx, constants.GoogleOauth2Url, headers, paramsData)
		if err == nil && resp.StatusCode == http.StatusOK {
			var respResult AccessTokenResponse
			body, _ := io.ReadAll(resp.Body)
			_ = json.Unmarshal(body, &respResult)

			token = respResult.AccessToken
			if token != "" {
				redis.Set(redis.GetRedisKey("token", appid), token, time.Second*constants.RedisKeyTimeOut)
			} else {
				log.Error(ctx, "获取 google access token 失败", "appid", appid, "order_id", orderID, "params", params, "result", respResult)
				return "", errors.New("get google access token error")
			}
		} else {
			return "", err
		}
	}
	return token, nil
}

func GetPurchaseInfo(ctx context.Context, appid string, info map[string]interface{}, isNormal bool, orderID string) (map[string]interface{}, error) {
	log.Debug(ctx, "开始校验Google支付信息", "order_id", orderID, "data", info)
	token, err := getAccessTokenFromGoogle(ctx, appid, orderID)
	var url string
	if isNormal {
		url = fmt.Sprintf(constants.GooglePurchaseProductUrl, info["packageName"], info["productId"], info["purchaseToken"], "", token)
	} else {
		url = fmt.Sprintf(constants.GooglePurchaseSubUrl, info["packageName"], info["productId"], info["purchaseToken"], "", token)
	}

	headers := map[string]string{"Content-Type": "application/json"}
	resp, err := zeusHttp.GetWithTracer(ctx, url, headers)
	if err != nil {
		log.Error(ctx, "校验Google支付信息失败", "error", err.Error(), "url", url, "order_id", orderID)
		return nil, err
	}
	respResult := map[string]interface{}{}
	body, _ := io.ReadAll(resp.Body)
	json.Unmarshal(body, &respResult)

	log.Debug(ctx, "校验Google支付信息成功", "respResult", respResult)

	return respResult, nil
}
