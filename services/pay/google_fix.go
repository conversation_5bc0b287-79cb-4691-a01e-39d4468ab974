package pay

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	zeusHttp "zeus/pkg/http"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"zeus/common/constants"
	"zeus/pkg/redis"

	"zeus/common/log"
)

type GoogleClient struct {
	RefreshToken string `json:"refresh_token"`
	ClientId     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	RedirectUri  string `json:"redirect_uri"`
}

type AccessTokenForm struct {
	RefreshToken string `json:"refresh_token"`
	ClientId     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	GrantType    string `json:"grant_type"`
}

type AccessTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int64  `json:"expires_in"`
	Scope       string `json:"scope"`
	TokenType   string `json:"token_type"`
}

type AccessTokenResponseError struct {
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
}

func (g *GoogleClient) Sign() string {
	data := make([]byte, 0)
	data = append(data, g.ClientId...)
	data = append(data, g.RefreshToken...)
	data = append(data, g.ClientSecret...)
	m := md5.Sum(data)
	sign := hex.EncodeToString(m[:])
	return sign
}

func CheckSettingParams(c *gin.Context, params string) error {
	var config *payment.GooglePay
	err := json.Unmarshal([]byte(params), &config)
	if err != nil {
		return err
	}
	googleClient := GoogleClient{
		RefreshToken: *config.GoogleRefreshToken,
		ClientId:     *config.GoogleClientId,
		ClientSecret: *config.GoogleClientSecret,
		RedirectUri:  *config.GoogleRedirectUri,
	}
	err = googleClient.SendGetAccessTokenRequest(c.Request.Context())
	if err != nil {
		return err
	}
	return nil
}

func (g *GoogleClient) SendGetAccessTokenRequest(ctx context.Context) error {
	form := AccessTokenForm{
		RefreshToken: g.RefreshToken,
		ClientId:     g.ClientId,
		ClientSecret: g.ClientSecret,
		GrantType:    "refresh_token",
	}
	paramsData, _ := json.Marshal(form)

	headers := map[string]string{"Content-Type": "application/json"}
	resp, err := zeusHttp.PostWithTracer(ctx, constants.GoogleOauth2Url, headers, paramsData)
	if err != nil {
		log.Error(ctx, "获取 google access token 失败", "error", err.Error())
		return err
	}
	if resp.StatusCode == http.StatusUnauthorized || resp.StatusCode == http.StatusBadRequest {
		log.Error(ctx, "获取 google access token 失败", "status_code", resp.StatusCode)
		return errors.New("get google access token error")
	}
	return nil
}

func (g *GoogleClient) getAccessToken(ctx context.Context) (string, error) {
	cacheKey := redis.GetRedisKey("token_", g.Sign())
	token := redis.Get(cacheKey)
	if token != "" {
		log.Debug(ctx, "从缓存中获取Google Access Token", "token", token)
		return token, nil
	}

	form := AccessTokenForm{
		RefreshToken: g.RefreshToken,
		ClientId:     g.ClientId,
		ClientSecret: g.ClientSecret,
		GrantType:    "refresh_token",
	}
	paramsData, _ := json.Marshal(form)

	headers := map[string]string{"Content-Type": "application/json"}
	resp, err := zeusHttp.PostWithTracer(ctx, constants.GoogleOauth2Url, headers, paramsData)
	if err != nil {
		log.Error(ctx, "获取 google access token 失败", "error", err.Error())
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		var respResult AccessTokenResponseError
		body, _ := io.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &respResult)
		log.Error(ctx, "获取 google access token 失败", "status_code", resp.StatusCode)
		return "", errors.New("get google access token error")
	}

	var respResult AccessTokenResponse
	body, _ := io.ReadAll(resp.Body)
	_ = json.Unmarshal(body, &respResult)
	token = respResult.AccessToken
	_ = redis.Set(cacheKey, token, time.Duration(respResult.ExpiresIn)*time.Second)
	log.Debug(ctx, "设置Google Access Token到缓存中", "token", token, "cacheKey", cacheKey)
	return token, nil
}

type GooglePurchaseClient struct {
	GoogleClient GoogleClient
	PackageName  string
}

type GooglePurchaseResponse struct {
	PurchaseTimeMillis          string `json:"purchaseTimeMillis"`
	PurchaseState               int    `json:"purchaseState"`
	ConsumptionState            int    `json:"consumptionState"`
	DeveloperPlayload           string `json:"developerPlayload"`
	OrderID                     string `json:"orderID"`
	PurchaseType                string `json:"purchaseType"`
	AcknowledgementState        int    `json:"acknowledgementState"`
	Kind                        string `json:"kind"`
	ObfuscatedExternalAccountId string `json:"obfuscatedExternalAccountId"`
	RegionCode                  string `json:"regionCode"`
}

type GoogleVoidedpurchasesResponse struct {
	VoidedPurchases []GoogleVoidedPurchase `json:"voidedPurchases"`
}

type GoogleVoidedPurchase struct {
	PurchaseToken      string `json:"purchaseToken"`
	PurchaseTimeMillis string `json:"purchaseTimeMillis"`
	VoidedTimeMillis   string `json:"voidedTimeMillis"`
	OrderId            string `json:"orderId"`
	VoidedSource       int    `json:"voidedSource"`
	VoidedReason       int    `json:"voidedReason"`
	Kind               string `json:"kind"`
}

func (p *GooglePurchaseResponse) ToString() string {
	b, err := json.Marshal(p)
	if err != nil {
		return ""
	}
	return string(b)
}

func (p *GooglePurchaseClient) Get(ctx context.Context, productId string, purchaseToken string, isNormal bool) (GooglePurchaseResponse, error) {
	var respResult GooglePurchaseResponse

	accessToken, err := p.GoogleClient.getAccessToken(ctx)
	if err != nil {
		return respResult, err
	}
	var url string
	if isNormal {
		url = fmt.Sprintf(constants.GooglePurchaseProductUrl,
			p.PackageName, productId, purchaseToken, "", accessToken)
	} else {
		url = fmt.Sprintf(constants.GooglePurchaseSubUrl,
			p.PackageName, productId, purchaseToken, "", accessToken)
	}

	headers := map[string]string{}
	resp, err := zeusHttp.GetWithTracer(ctx, url, headers)
	if err != nil {
		return respResult, err
	}

	if resp.StatusCode != http.StatusOK {
		return respResult, errors.New("票据信息异常，未获取到订单信息")
	}
	body, _ := io.ReadAll(resp.Body)
	_ = json.Unmarshal(body, &respResult)
	return respResult, nil
}

func (p *GooglePurchaseClient) Acknowledge(ctx context.Context, productId string, purchaseToken string) (GooglePurchaseResponse, error) {
	log.Debug(ctx, "Start Request Google Acknowledge interface", "product_id", productId, "purchase_token", purchaseToken)
	var respResult GooglePurchaseResponse

	accessToken, err := p.GoogleClient.getAccessToken(ctx)
	if err != nil {
		return respResult, err
	}

	url := fmt.Sprintf(constants.GooglePurchaseProductUrl,
		p.PackageName, productId, purchaseToken, ":acknowledge", accessToken)

	headers := map[string]string{"Content-Type": "application/json"}
	resp, err := zeusHttp.PostWithTracer(ctx, url, headers, []byte(""))

	if err == nil {
		body, _ := io.ReadAll(resp.Body)
		json.Unmarshal(body, &respResult)
		return respResult, nil
	} else {
		return respResult, err
	}
}

func (p *GooglePurchaseClient) Consume(ctx context.Context, productId string, purchaseToken string) (GooglePurchaseResponse, error) {
	log.Debug(ctx, "Start Request Google consume interface", "product_id", productId, "purchase_token", purchaseToken)
	var respResult GooglePurchaseResponse

	accessToken, err := p.GoogleClient.getAccessToken(ctx)
	if err != nil {
		return respResult, err
	}

	url := fmt.Sprintf(constants.GooglePurchaseProductUrl,
		p.PackageName, productId, purchaseToken, ":consume", accessToken)

	headers := map[string]string{"Content-Type": "application/json"}

	resp, err := zeusHttp.PostWithTracer(ctx, url, headers, []byte(""))
	if err == nil {
		body, _ := io.ReadAll(resp.Body)
		json.Unmarshal(body, &respResult)
		return respResult, nil
	} else {
		return respResult, err
	}
}

func CheckPurchaseInfo(ctx context.Context, gameId int, productId, purchaseToken string) (GooglePurchaseResponse, error) {
	var orderInfo GooglePurchaseResponse

	var config payment.GooglePay
	converter.QueryByGameId(&config, gameId)

	googleClient := GoogleClient{
		RefreshToken: *config.GoogleRefreshToken,
		ClientId:     *config.GoogleClientId,
		ClientSecret: *config.GoogleClientSecret,
		RedirectUri:  *config.GoogleRedirectUri,
	}

	if *config.GooglePayPackageName == "" {
		return orderInfo, errors.New("当前项目下未配置PackageName")
	}

	p := GooglePurchaseClient{
		GoogleClient: googleClient,
		PackageName:  *config.GooglePayPackageName,
	}
	orderInfo, err := p.Get(ctx, productId, purchaseToken, true)
	if err != nil {
		return orderInfo, err
	}

	if orderInfo.PurchaseState == 1 {
		return orderInfo, fmt.Errorf("当前订单未支付")
	} else {
		if orderInfo.AcknowledgementState != 1 {
			p.Acknowledge(ctx, productId, purchaseToken)
		}

		if orderInfo.ConsumptionState != 1 {
			p.Consume(ctx, productId, purchaseToken)
		}
	}

	return orderInfo, nil
}

func (p *GooglePurchaseClient) GetRefundHistory(ctx context.Context, startTime string, endTime string) (GoogleVoidedpurchasesResponse, error) {
	var respResult GoogleVoidedpurchasesResponse

	accessToken, err := p.GoogleClient.getAccessToken(ctx)
	if err != nil {
		return respResult, err
	}

	url := fmt.Sprintf(constants.GooglePurchaseVoidedpurchasesUrl, p.PackageName, accessToken)

	if startTime != "" {
		url += fmt.Sprintf("&startTime=%s", startTime)
	}

	if endTime != "" {
		url += fmt.Sprintf("&endTime=%s", endTime)
	}

	resp, err := zeusHttp.GetWithTracer(ctx, url, map[string]string{})
	if err == nil {
		body, _ := io.ReadAll(resp.Body)
		json.Unmarshal(body, &respResult)
		return respResult, nil
	} else {
		return respResult, err
	}
}
