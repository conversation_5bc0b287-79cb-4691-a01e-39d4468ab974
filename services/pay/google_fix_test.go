package pay

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"reflect"
	"testing"
	"time"

	zeusHttp "zeus/pkg/http"
	"zeus/pkg/redis"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestGoogleClient(t *testing.T) {
	client := GoogleClient{
		RefreshToken: "RefreshToken",
		ClientId:     "ClientId",
		ClientSecret: "ClientSecret",
		RedirectUri:  "RedirectUri",
	}

	redisGetRedisKeyMockFunc := ApplyFunc(redis.GetRedisKey, func(_ string, _ string) string {
		return "sdk_token_appid"
	})
	defer redisGetRedisKeyMockFunc.Reset()

	Convey("getAccessToken", t, func() {
		Convey("通过redis获取到token", func() {
			redisGetMockFunc := ApplyFunc(redis.Get, func(_ string) string {
				return "test_data"
			})
			defer redisGetMockFunc.Reset()
			output, _ := client.getAccessToken(context.Background())
			expected := "test_data"
			So(output, ShouldEqual, expected)
		})

		Convey("通过google获取到token,获取成功", func() {
			redisGetMockFunc := ApplyFunc(redis.Get, func(_ string) string {
				return ""
			})
			defer redisGetMockFunc.Reset()

			redisSetMockFunc := ApplyFunc(redis.Set, func(_ string, _ interface{}, _ time.Duration) error {
				return nil
			})
			defer redisSetMockFunc.Reset()

			PostWithTracerMockFunc := ApplyFunc(zeusHttp.PostWithTracer, func(_ context.Context, _ string, _ map[string]string,
				_ []byte, _ ...zeusHttp.Option,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusOK}
				data := AccessTokenResponse{
					AccessToken: "AccessToken",
				}
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return
			})
			defer PostWithTracerMockFunc.Reset()

			output, _ := client.getAccessToken(context.Background())
			expected := "AccessToken"
			So(output, ShouldEqual, expected)
		})

		Convey("通过google获取到token,获取失败", func() {
			redisGetMockFunc := ApplyFunc(redis.Get, func(_ string) string {
				return ""
			})
			defer redisGetMockFunc.Reset()

			PostWithTracerMockFunc := ApplyFunc(zeusHttp.PostWithTracer, func(_ context.Context, _ string, _ map[string]string,
				_ []byte, _ ...zeusHttp.Option,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusBadRequest}
				data := AccessTokenResponseError{
					Error: "get google access token error",
				}
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return
			})
			defer PostWithTracerMockFunc.Reset()

			_, err := client.getAccessToken(context.Background())
			output := err.Error()
			expected := "get google access token error"
			So(output, ShouldEqual, expected)
		})
	})
}

func TestGooglePurchaseClient(t *testing.T) {
	client := GooglePurchaseClient{
		GoogleClient: GoogleClient{
			RefreshToken: "RefreshToken",
			ClientId:     "ClientId",
			ClientSecret: "ClientSecret",
			RedirectUri:  "RedirectUri",
		},
		PackageName: "PackageName",
	}

	ctx := context.Background()

	redisGetMockFunc := ApplyFunc(redis.Get, func(_ string) string {
		return "test_data"
	})
	defer redisGetMockFunc.Reset()

	Convey("Get", t, func() {
		Convey("成功", func() {
			PostWithTracerMockFunc := ApplyFunc(zeusHttp.GetWithTracer, func(_ context.Context, _ string,
				_ map[string]string,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusOK}
				var data GooglePurchaseResponse
				data.OrderID = "orderID"
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return
			})
			defer PostWithTracerMockFunc.Reset()

			resp, _ := client.Get(ctx, "productId", "purchaseToken", true)
			output := resp.OrderID
			expected := "orderID"
			So(output, ShouldEqual, expected)
		})

		Convey("失败", func() {
			PostWithTracerMockFunc := ApplyFunc(zeusHttp.GetWithTracer, func(_ context.Context, _ string,
				_ map[string]string,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusBadRequest}
				return
			})
			defer PostWithTracerMockFunc.Reset()

			_, err := client.Get(ctx, "productId", "purchaseToken", true)
			output := err.Error()
			expected := "票据信息异常，未获取到订单信息"
			So(output, ShouldEqual, expected)
		})
	})

	Convey("Acknowledge", t, func() {
		Convey("成功", func() {
			PostWithTracerMockFunc := ApplyFunc(zeusHttp.PostWithTracer, func(_ context.Context, _ string, _ map[string]string,
				_ []byte, _ ...zeusHttp.Option,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusOK}
				var data GooglePurchaseResponse
				data.OrderID = "orderID"
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return
			})
			defer PostWithTracerMockFunc.Reset()

			resp, _ := client.Acknowledge(ctx, "productId", "purchaseToken")
			output := resp.OrderID
			expected := "orderID"
			So(output, ShouldEqual, expected)
		})
	})

	Convey("Consume", t, func() {
		Convey("成功", func() {
			PostWithTracerMockFunc := ApplyFunc(zeusHttp.PostWithTracer, func(_ context.Context, _ string, _ map[string]string,
				_ []byte, _ ...zeusHttp.Option,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusOK}
				var data GooglePurchaseResponse
				data.OrderID = "orderID"
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return
			})
			defer PostWithTracerMockFunc.Reset()

			resp, _ := client.Consume(ctx, "productId", "purchaseToken")
			output := resp.OrderID
			expected := "orderID"
			So(output, ShouldEqual, expected)
		})
	})

	Convey("GetRefundHistory", t, func() {
		Convey("成功", func() {
			PostWithTracerMockFunc := ApplyFunc(zeusHttp.PostWithTracer, func(_ context.Context, _ string, _ map[string]string,
				_ []byte, _ ...zeusHttp.Option,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusOK}
				var data GoogleVoidedpurchasesResponse
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return
			})
			defer PostWithTracerMockFunc.Reset()

			_, output := client.GetRefundHistory(ctx, "startTime", "endTime")
			So(output, ShouldEqual, nil)
		})
	})
}

func TestCheckPurchaseInfo(t *testing.T) {
	ctx := context.Background()

	QueryMockFunc := ApplyFunc(converter.QueryByGameId, func(config schemas.BaseConfiger, _ int) {
		switch config.GetType() {
		case "PaymentGooglePay":
			GooglePayConfig := config.(*payment.GooglePay)
			googleRefreshToken := "GoogleRefreshToken"
			GooglePayConfig.GoogleRefreshToken = &googleRefreshToken
			googlePayConfig := "GooglePayConfig"
			GooglePayConfig.GooglePayKey = &googlePayConfig
			googleClientId := "GoogleClientId"
			GooglePayConfig.GoogleClientId = &googleClientId
			googleClientSecret := "GoogleClientSecret"
			GooglePayConfig.GoogleClientSecret = &googleClientSecret
			googleRedirectUri := "GoogleRedirectUri"
			GooglePayConfig.GoogleRedirectUri = &googleRedirectUri
			googlePayPackageName := "GooglePayPackageName"
			GooglePayConfig.GooglePayPackageName = &googlePayPackageName
		}
	})
	defer QueryMockFunc.Reset()

	Convey("CheckPurchaseInfo", t, func() {
		Convey("成功", func() {
			var googleClient *GooglePurchaseClient
			GoogleClientGetMockFunc := ApplyMethod(reflect.TypeOf(googleClient), "Get", func(
				_ *GooglePurchaseClient, _ context.Context, _ string, _ string, _ bool,
			) (GooglePurchaseResponse, error) {
				return GooglePurchaseResponse{OrderID: "orderID"}, nil
			})
			defer GoogleClientGetMockFunc.Reset()

			GoogleClientAcknowledgeMockFunc := ApplyMethod(reflect.TypeOf(googleClient), "Acknowledge",
				func(_ *GooglePurchaseClient, _ context.Context, _ string, _ string) (GooglePurchaseResponse, error) {
					return GooglePurchaseResponse{}, nil
				})
			defer GoogleClientAcknowledgeMockFunc.Reset()

			GoogleClientConsumeMockFunc := ApplyMethod(reflect.TypeOf(googleClient), "Consume",
				func(_ *GooglePurchaseClient, _ context.Context, _ string, _ string) (GooglePurchaseResponse, error) {
					return GooglePurchaseResponse{}, nil
				})
			defer GoogleClientConsumeMockFunc.Reset()

			resp, _ := CheckPurchaseInfo(ctx, 10001, "productId", "purchaseToken")
			output := resp.OrderID
			expected := "orderID"
			So(output, ShouldEqual, expected)
		})
	})
}
