package pay

import (
	"context"
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"time"

	"zeus/common/log"
	"zeus/models"
	zeusHttp "zeus/pkg/http"
	"zeus/pkg/redis"
)

type HuaweiPayClient struct {
	ClientID     string
	ClientSecret string
	PublicKey    string
}

type AccessTokenResult struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
}

type VerifyResponse struct {
	ResponseCode       string `json:"responseCode"`
	PurchaseTokenData  string `json:"purchaseTokenData"`
	DataSignature      string `json:"dataSignature"`
	SignatureAlgorithm string `json:"signatureAlgorithm"`
}

type HuaweiPurchaseData struct {
	ApplicationId      int64  `json:"applicationId"`
	AutoRenewing       bool   `json:"autoRenewing"`
	OrderId            string `json:"orderId"`
	Kind               *int   `json:"kind"`
	PackageName        string `json:"packageName"`
	ProductId          string `json:"productId"`
	ProductName        string `json:"productName"`
	PurchaseTime       int64  `json:"purchaseTime"`
	PurchaseState      *int   `json:"purchaseState"`
	DeveloperPayload   string `json:"developerPayload"`
	DeveloperChallenge string `json:"developerChallenge"`
	ConsumptionState   int    `json:"consumptionState"`
	Confirmed          int    `json:"confirmed"`
	PurchaseToken      string `json:"purchaseToken"`
	PurchaseType       *int   `json:"purchaseType"`
	Currency           string `json:"currency"`
	Price              int    `json:"price"`
	Country            string `json:"country"`
	PayType            string `json:"payType"`
	PayOrderId         string `json:"payOrderId"`
}

const (
	GetHuaweiAccessTokenUrl = "https://oauth-login.cloud.huawei.com/oauth2/v3/token"
	HuaweiVerifyTokenUrl    = "https://orders-drcn.iap.hicloud.com/applications/purchases/tokens/verify"
	huaweiTokenKey          = "huawei_token"
)

func (h *HuaweiPurchaseData) ToString() string {
	b, err := json.Marshal(h)
	if err != nil {
		return ""
	}
	return string(b)
}

func (h *HuaweiPayClient) GetHuaweiAccessToken(ctx context.Context, dbGame *models.Game, sendNewRequest bool) (string, error) {
	key := redis.GetRedisKey(huaweiTokenKey, dbGame.Appid)
	accessToken := redis.Get(key)

	if accessToken == "" || sendNewRequest == true {
		getTokenUrl := fmt.Sprintf("%s?client_id=%s&client_secret=%s&grant_type=client_credentials", GetHuaweiAccessTokenUrl, h.ClientID, h.ClientSecret)
		headers := map[string]string{"Content-Type": "application/x-www-form-urlencoded"}

		resp, err := zeusHttp.GetWithTracer(ctx, getTokenUrl, headers)
		if err == nil {
			respResult := AccessTokenResult{}
			body, _ := io.ReadAll(resp.Body)
			json.Unmarshal(body, &respResult)
			if respResult.AccessToken != "" {
				authString := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("APPAT:%s", respResult.AccessToken)))
				accessToken = fmt.Sprintf("Basic %s", authString)
				redis.Set(redis.GetRedisKey(huaweiTokenKey, dbGame.Appid), accessToken, time.Second*time.Duration(respResult.ExpiresIn))
			} else {
				log.Error(ctx, "获取华为access_token失败", "appid", dbGame.Appid, "result", respResult)
				return "", errors.New("get huawei access token error")
			}
		} else {
			return "", err
		}

	}
	return accessToken, nil
}

func (h *HuaweiPayClient) VerifyHuaweiOrder(ctx context.Context, dbGame *models.Game, purchaseToken string, productId string) (HuaweiPurchaseData, error) {
	var purchaseData HuaweiPurchaseData

	token, err := h.GetHuaweiAccessToken(ctx, dbGame, false)
	if err != nil {
		return purchaseData, err
	}
	bodyMap := map[string]interface{}{
		"purchaseToken": purchaseToken,
		"productId":     productId,
	}
	bodyString, _ := json.Marshal(bodyMap)

	headers := map[string]string{
		"Authorization": token,
		"Content-Type":  "application/json",
	}
	response, _ := zeusHttp.PostWithTracer(ctx, HuaweiVerifyTokenUrl, headers, bodyString)

	// 401 需要重新获取access_token
	if response.StatusCode == 401 {
		token, err = h.GetHuaweiAccessToken(ctx, dbGame, true)
		headers := map[string]string{
			"Authorization": token,
			"Content-Type":  "application/json",
		}
		response, _ = zeusHttp.PostWithTracer(ctx, HuaweiVerifyTokenUrl, headers, bodyString)
	}

	var verifyResponse VerifyResponse
	body, _ := io.ReadAll(response.Body)
	err = json.Unmarshal(body, &verifyResponse)
	if err != nil {
		return purchaseData, err
	}

	// 校验订单数据签名
	err = h.VerifyOrderSignature(verifyResponse.PurchaseTokenData, verifyResponse.DataSignature)
	if err != nil {
		return purchaseData, errors.New("订单数据验签失败")
	}

	// 解析订单数据
	err = json.Unmarshal([]byte(verifyResponse.PurchaseTokenData), &purchaseData)
	return purchaseData, err
}

func (h *HuaweiPayClient) VerifyOrderSignature(content string, sign string) error {
	publicKeyByte, err := base64.StdEncoding.DecodeString(h.PublicKey)
	if err != nil {
		return err
	}
	pub, err := x509.ParsePKIXPublicKey(publicKeyByte)
	if err != nil {
		return err
	}

	hashed := sha256.Sum256([]byte(content))
	signature, err := base64.StdEncoding.DecodeString(sign)
	if err != nil {
		return err
	}

	return rsa.VerifyPKCS1v15(pub.(*rsa.PublicKey), crypto.SHA256, hashed[:], signature)
}
