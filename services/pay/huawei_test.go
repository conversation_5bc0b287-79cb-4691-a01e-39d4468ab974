package pay

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"reflect"
	"testing"
	"time"

	"zeus/models"
	zeusHttp "zeus/pkg/http"
	"zeus/pkg/redis"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestHuaweiPay(t *testing.T) {
	Convey("ToString", t, func() {
		Convey("one func for succ", func() {
			h := HuaweiPurchaseData{OrderId: "test_order_id"}
			output := h.ToString()
			expected := `{"applicationId":0,"autoRenewing":false,"orderId":"test_order_id","kind":null,"packageName":"","productId":"","productName":"","purchaseTime":0,"purchaseState":null,"developerPayload":"","developerChallenge":"","consumptionState":0,"confirmed":0,"purchaseToken":"","purchaseType":null,"currency":"","price":0,"country":"","payType":"","payOrderId":""}`
			So(output, ShouldEqual, expected)
		})

		Convey("one func for with error", func() {
			jsonMarshalMockFunc := ApplyFunc(json.Marshal, func(_ any) ([]uint8, error) {
				return nil, errors.New("error")
			})

			defer jsonMarshalMockFunc.Reset()

			h := HuaweiPurchaseData{OrderId: "test_order_id"}
			output := h.ToString()
			expected := ""
			So(output, ShouldEqual, expected)
		})
	})

	Convey("GetHuaweiAccessToken", t, func() {
		Convey("Get token from cache", func() {
			ApplyFunc(redis.GetRedisKey, func(strType string, key string) string {
				return "sdk_token_appid"
			})
			ApplyFunc(redis.Get, func(_ string) string {
				return "test_data"
			})
			g := &models.Game{}
			h := HuaweiPayClient{}
			output, err := h.GetHuaweiAccessToken(context.Background(), g, false)
			expected := "test_data"
			So(output, ShouldEqual, expected)
			So(err, ShouldBeNil)
		})

		Convey("Get token from Huawei Server", func() {
			ApplyFunc(redis.GetRedisKey, func(strType string, key string) string {
				return "sdk_token_appid"
			})
			ApplyFunc(redis.Get, func(_ string) string {
				return ""
			})

			ApplyFunc(redis.Set, func(_ string, _ any, _ time.Duration) error {
				return nil
			})

			GetWithTracerMockFunc := ApplyFunc(zeusHttp.GetWithTracer, func(_ context.Context, _ string,
				_ map[string]string,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusOK}
				var data AccessTokenResult
				data.AccessToken = "test_data"
				data.ExpiresIn = 180
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return
			})
			defer GetWithTracerMockFunc.Reset()
			g := &models.Game{}
			h := HuaweiPayClient{}
			output, err := h.GetHuaweiAccessToken(context.Background(), g, false)
			expected := "Basic QVBQQVQ6dGVzdF9kYXRh"
			So(output, ShouldEqual, expected)
			So(err, ShouldBeNil)
		})

		Convey("Get token from Huawei Server Failed 1", func() {
			ApplyFunc(redis.GetRedisKey, func(strType string, key string) string {
				return "sdk_token_appid"
			})
			ApplyFunc(redis.Get, func(_ string) string {
				return ""
			})

			ApplyFunc(redis.Set, func(_ string, _ any, _ time.Duration) error {
				return nil
			})

			GetWithTracerMockFunc := ApplyFunc(zeusHttp.GetWithTracer, func(_ context.Context, _ string,
				_ map[string]string,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusOK}
				var data AccessTokenResult
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return
			})
			defer GetWithTracerMockFunc.Reset()
			g := &models.Game{}
			h := HuaweiPayClient{}
			output, err := h.GetHuaweiAccessToken(context.Background(), g, false)
			So(output, ShouldEqual, "")
			So(err, ShouldNotBeNil)
		})

		Convey("Get token from Huawei Server Failed 2", func() {
			ApplyFunc(redis.GetRedisKey, func(strType string, key string) string {
				return "sdk_token_appid"
			})
			ApplyFunc(redis.Get, func(_ string) string {
				return ""
			})

			GetWithTracerMockFunc := ApplyFunc(zeusHttp.GetWithTracer, func(_ context.Context, _ string,
				_ map[string]string,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusOK}
				var data AccessTokenResult
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return resp, errors.New("Request failed")
			})
			defer GetWithTracerMockFunc.Reset()
			g := &models.Game{}
			h := HuaweiPayClient{}
			output, err := h.GetHuaweiAccessToken(context.Background(), g, false)
			So(output, ShouldEqual, "")
			So(err, ShouldNotBeNil)
		})
	})

	Convey("TestVerifyHuaweiOrder", t, func() {
		Convey("Get Huawei AccessToken Failed", func() {
			var client *HuaweiPayClient
			GetHuaweiAccessTokenMock := ApplyMethod(reflect.TypeOf(client), "GetHuaweiAccessToken", func(
				_ *HuaweiPayClient, _ context.Context, _ *models.Game, _ bool,
			) (string, error) {
				return "", errors.New("Get Access Token Failed")
			})

			defer GetHuaweiAccessTokenMock.Reset()

			h := HuaweiPayClient{}
			_, err := h.VerifyHuaweiOrder(context.Background(), &models.Game{}, "", "")
			So(err, ShouldNotBeNil)
		})

		Convey("Get Huawei", func() {
			var client *HuaweiPayClient
			GetHuaweiAccessTokenMock := ApplyMethod(reflect.TypeOf(client), "GetHuaweiAccessToken", func(
				_ *HuaweiPayClient, _ context.Context, _ *models.Game, _ bool,
			) (string, error) {
				return "test_access_token", nil
			})

			PostWithTracerMockFunc := ApplyFunc(zeusHttp.PostWithTracer, func(_ context.Context, _ string, _ map[string]string,
				_ []byte, _ ...zeusHttp.Option,
			) (resp *http.Response, err error) {
				resp = &http.Response{StatusCode: http.StatusOK}
				data := VerifyResponse{}
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return
			})
			defer PostWithTracerMockFunc.Reset()

			defer GetHuaweiAccessTokenMock.Reset()

			h := HuaweiPayClient{}
			_, err := h.VerifyHuaweiOrder(context.Background(), &models.Game{}, "", "")
			So(err, ShouldNotBeNil)
		})
	})
}
