package pay

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/ali"
	zeusHttp "zeus/pkg/http"
	"zeus/pkg/redis"
	"zeus/pkg/wechat"
	"zeus/schema"
	"zeus/services"
	"zeus/services/game"
	wechatService "zeus/services/pay/wechat"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	"github.com/smartwalle/alipay/v3"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
)

func TestGooglePay(t *testing.T) {
	Convey("getGoogleAccessToken", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(redis.GetRedisKey, func(strType string, key string) string {
				return "sdk_token_appid"
			})
			ApplyFunc(redis.Get, func(_ string) string {
				return "test_data"
			})
			appid := "test_appid"
			output := getGoogleAccessToken(appid)
			expected := "test_data"
			So(output, ShouldEqual, expected)
		})
	})

	Convey("getAccessTokenFromGoogle", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})

			ApplyFunc(zeusHttp.PostWithTracer, func(_ context.Context, _ string, headers map[string]string, _ []byte, _ ...zeusHttp.Option) (*http.Response, error) {
				resp := &http.Response{}
				respResult := make(map[string]string)
				respResult["access_token"] = "test_access_token"
				body, _ := json.Marshal(respResult)

				resp.Body = io.NopCloser(strings.NewReader(string(body)))
				return resp, nil
			})

			ApplyFunc(redis.Set, func(key string, data interface{}, duration time.Duration) error {
				return nil
			})

			appid := "test_appid"
			orderID := "test_order_id"
			output, _ := getAccessTokenFromGoogle(context.Background(), appid, orderID)
			expected := "test_data"
			So(output, ShouldEqual, expected)
		})
	})

	Convey("GetPurchaseInfo", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(zeusHttp.GetWithTracer, func(_ context.Context, _ string,
				_ map[string]string,
			) (resp *http.Response, err error) {
				resp = &http.Response{}
				respResult := make(map[string]string)
				respResult["data"] = "test_data"
				body, _ := json.Marshal(respResult)
				resp.Body = io.NopCloser(strings.NewReader(string(body)))
				return
			})

			appid := "test_appid"
			info := make(map[string]interface{})
			info["packageName"] = "packageName"
			info["productId"] = "productId"
			info["purchaseToken"] = "purchaseToken"
			orderID := "test_order_id"
			output, _ := GetPurchaseInfo(context.Background(), appid, info, true, orderID)

			expected := "test_data"
			So(output["data"], ShouldEqual, expected)
		})
	})
}

func TestGetAccessTokenFromGoogleFail(t *testing.T) {
	QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
		switch config.GetType() {
		case "PaymentGooglePay":
			GooglePayConfig := config.(*payment.GooglePay)
			googleRefreshToken := "GoogleRefreshToken"
			GooglePayConfig.GoogleRefreshToken = &googleRefreshToken
			googlePayConfig := "GooglePayConfig"
			GooglePayConfig.GooglePayKey = &googlePayConfig
			googleClientId := "GoogleClientId"
			GooglePayConfig.GoogleClientId = &googleClientId
			googleClientSecret := "GoogleClientSecret"
			GooglePayConfig.GoogleClientSecret = &googleClientSecret
			googleRedirectUri := "GoogleRedirectUri"
			GooglePayConfig.GoogleRedirectUri = &googleRedirectUri
			googlePayPackageName := "GooglePayPackageName"
			GooglePayConfig.GooglePayPackageName = &googlePayPackageName
		}
	})
	defer QueryMockFunc.Reset()

	Convey("getAccessTokenFromGoogle", t, func() {
		Convey("one func for fail", func() {
			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})

			ApplyFunc(zeusHttp.PostWithTracer, func(_ context.Context, _ string, headers map[string]string, _ []byte, _ ...zeusHttp.Option) (*http.Response, error) {
				resp := &http.Response{}
				var respResult AccessTokenResponse
				respResult.AccessToken = ""
				body, _ := json.Marshal(respResult)

				resp.Body = io.NopCloser(strings.NewReader(string(body)))
				resp.StatusCode = 200
				return resp, nil
			})

			ApplyFunc(getGoogleAccessToken, func(appid string) string {
				return ""
			})

			ApplyFunc(redis.Set, func(key string, data interface{}, duration time.Duration) error {
				return nil
			})

			appid := "test_appid"
			orderID := "test_order_id"
			_, output := getAccessTokenFromGoogle(context.Background(), appid, orderID)
			expected := "get google access token error"
			So(output.Error(), ShouldEqual, expected)
		})
	})
}

func TestGooglePayFail(t *testing.T) {
	Convey("GetPurchaseInfo", t, func() {
		Convey("one func for fail", func() {
			func1 := ApplyFunc(getAccessTokenFromGoogle, func(ctx context.Context, appid string, orderID string) (string, error) {
				return "sss", nil
			})
			defer func1.Reset()

			ApplyFunc(zeusHttp.GetWithTracer, func(_ context.Context, _ string,
				_ map[string]string,
			) (*http.Response, error) {
				resp := &http.Response{StatusCode: http.StatusOK}
				var data GooglePurchaseResponse
				data.OrderID = "orderID"
				jsonData, _ := json.Marshal(data)
				resp.Body = io.NopCloser(bytes.NewReader(jsonData))
				return resp, nil
			})

			appid := "test_appid"
			info := make(map[string]interface{})
			info["packageName"] = "packageName"
			info["productId"] = "productId"
			info["purchaseToken"] = "purchaseToken"
			orderID := "test_order_id"
			output, _ := GetPurchaseInfo(context.Background(), appid, info, true, orderID)
			So(output["data"], ShouldEqual, nil)
		})
	})
}

func TestAliPay(t *testing.T) {
	Convey("CreateAliOrder", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(ali.AliPay, func(_ *gin.Context, order schema.AliExchangeForm) (*models.Order, string, error) {
				return &models.Order{}, "pay_url", nil
			})
			ApplyFunc(services.CreateOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			c, _ := gin.CreateTestContext(httptest.NewRecorder())
			c.Request = new(http.Request)
			user := models.User{}
			g := models.Game{}
			order := schema.AliExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					PlatForm:   "PlatForm",
					ServerID:   "ServerID",
					ServerName: "ServerName",
					Vip:        "1",
					Level:      "10",
				},
			}

			_, output, _ := CreateAliOrder(c, user, g, order)
			expected := "pay_url"
			So(output, ShouldEqual, expected)
		})
	})

	Convey("VerifyPayOrder", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(ali.QueryAliPayTrade, func(ctx context.Context, order models.Order, appid string) (*alipay.TradeQueryRsp, error) {
				a := &alipay.TradeQueryRsp{}
				a.TradeStatus = alipay.TradeStatusSuccess
				return a, nil
			})

			appid := "test_appid"
			aliOrder := NewAliOrder(appid)

			c := &gin.Context{}
			dbOrder := models.Order{}
			output, _ := aliOrder.VerifyPayOrder(c, &dbOrder)
			expected := constants.PaymentSuccess
			So(output.Status, ShouldEqual, expected)
		})
	})
}

func TestWeChatPay(t *testing.T) {
	Convey("CreateWeChatOrder", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(wechat.WechatPay, func(_ *gin.Context, order schema.WeChatExchangeForm) (*schema.WeChatOrder, *models.Order, error) {
				return &schema.WeChatOrder{
					OrderID: "test_order_id",
				}, &models.Order{}, nil
			})
			ApplyFunc(services.CreateOrder, func(c *gin.Context, order *models.Order) error {
				return nil
			})

			c, _ := gin.CreateTestContext(httptest.NewRecorder())
			c.Request = new(http.Request)
			user := models.User{}
			g := models.Game{}
			order := schema.WeChatExchangeForm{
				BaseOrderForm: schema.BaseOrderForm{
					PlatForm:   "PlatForm",
					ServerID:   "ServerID",
					ServerName: "ServerName",
					Vip:        "1",
					Level:      "10",
				},
			}

			output, _ := wechatService.CreateWeChatOrder(c, user, g, order)
			expected := "test_order_id"
			So(output.OrderID, ShouldEqual, expected)
		})
	})

	Convey("VerifyPayOrder", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(wechat.QueryWechatPayTrade, func(ctx context.Context, order models.Order, appid string) (*payments.Transaction, error) {
				w := &payments.Transaction{}
				s := "SUCCESS"
				t := "testTransactionId"
				total := int64(100)
				currency := "RNY"
				w.TradeState = &s
				amount := payments.TransactionAmount{}
				amount.Total = &total
				amount.Currency = &currency
				w.Amount = &amount
				w.TransactionId = &t
				return w, nil
			})

			appid := "test_appid"
			weChatOrder := wechatService.NewWeChatOrder(appid)

			c, _ := gin.CreateTestContext(httptest.NewRecorder())
			c.Request = new(http.Request)
			dbOrder := models.Order{}
			output, _ := weChatOrder.VerifyPayOrder(c, &dbOrder)
			expected := constants.PaymentSuccess
			So(output.Status, ShouldEqual, expected)
		})
	})
}
