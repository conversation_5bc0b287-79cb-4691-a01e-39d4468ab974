package callback

import (
	"zeus/services/pay"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"zeus/common/constants"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
)

type AlipayNotifyForm struct {
	OrderId string `json:"out_trade_no" form:"out_trade_no"`
}

type AlipayCallback struct {
	Appid string
}

func (v *AlipayCallback) GetPayWay() int {
	return constants.PayWayAli
}

func (v *AlipayCallback) GetOrder(c *gin.Context) (OrderResult, error) {
	orderResult := OrderResult{}

	notifyForm := AlipayNotifyForm{}
	if err := c.ShouldBind(&notifyForm); err != nil {
		return orderResult, err
	}

	dbOrder, err := services.CheckOrder(c, notifyForm.OrderId)
	if err != nil {
		return orderResult, errors.Errorf("支付宝回调，%s订单不存在", notifyForm.OrderId)
	}

	orderResult.DBOrder = &dbOrder

	return orderResult, nil
}

func (v *AlipayCallback) Verify(c *gin.Context, dbOrder *models.Order) (*schema.OrderPayResult, error) {
	aliOrder, _ := pay.GetOrder(constants.PayWayAli, v.Appid)
	OrderPayResult, err := aliOrder.VerifyPayOrder(c, dbOrder)
	if err != nil {
		return nil, err
	}
	return &OrderPayResult, nil
}
