package callback

import (
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/douyin"
	"zeus/schema"
	"zeus/services"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
)

type MiniGameDouyinCallback struct {
	Appid string
}

func checkDouyinPayConfigByAppid(appid string) (payment.MiniGameDouyinPay, error) {
	var config payment.MiniGameDouyinPay
	converter.QueryByAppid(&config, appid)

	if *config.DouyinAppId == "" || *config.DouyinSecret == "" || *config.DouyinSignKey == "" || *config.OpenStatus != constants.IsRechargeOpen {
		return config, errors.New("抖音小游戏支付参数配置不完整或支付已关闭")
	}
	return config, nil
}

func (v *MiniGameDouyinCallback) GetPayWay() int {
	return constants.PayWayMiniGameDouyin
}

type OrderSuccessPayInfo struct {
	Appid          string `json:"appid"`            // 小游戏appid
	CpOrderNo      string `json:"cp_orderno"`       // 开发者自定义订单号
	CpExtra        string `json:"cp_extra"`         // 开发者传的额外参数
	OrderNoChannel string `json:"order_no_channel"` // 小游戏后台交易单号
}

type OrderInfo struct {
	Timestamp string `json:"timestamp"` // 时间戳
	Nonce     string `json:"nonce"`     // 随机数
	Msg       string `json:"msg"`       // 包体
	Signature string `json:"signature"` // 根据token生成的签名
}

func (v *MiniGameDouyinCallback) GetOrder(c *gin.Context) (OrderResult, error) {
	var orderResult OrderResult
	var oi OrderInfo
	err := c.BindJSON(&oi)
	if err != nil {
		return orderResult, err
	}

	var ospi OrderSuccessPayInfo
	err = json.Unmarshal([]byte(oi.Msg), &ospi)

	if err != nil {
		return orderResult, err
	}

	// 校验支付配置

	dbOrder, err := services.CheckOrder(c, ospi.CpOrderNo)
	if err != nil {
		return orderResult, errors.Errorf("抖音小游戏支付回调，%s订单不存在", ospi.CpOrderNo)
	}

	config, err := checkDouyinPayConfigByAppid(v.Appid)
	if err != nil {
		return orderResult, err
	}

	// 需要在抖音支付后台配置 1 人民币 = 10 游戏币
	amt := int64(dbOrder.Price / 10)
	client := douyin.Client{Appid: *config.DouyinAppId, Secret: *config.DouyinSecret, SignKey: *config.DouyinSignKey}
	client.WalletGamePay(c.Request.Context(), dbOrder.OpenId, dbOrder.ServerId, amt, dbOrder.OrderId)

	// 返回订单及支付信息
	orderResult.DBOrder = &dbOrder
	orderResult.PayId = ospi.OrderNoChannel
	orderResult.PayStatus = constants.PaymentSuccess
	virtualPaymentNotifyJson, err := json.Marshal(oi)
	if err != nil {
		virtualPaymentNotifyJson = []byte("")
	}
	orderResult.Receipt = string(virtualPaymentNotifyJson)

	return orderResult, nil
}

func (v *MiniGameDouyinCallback) Verify(c *gin.Context, dbOrder *models.Order) (*schema.OrderPayResult, error) {
	config, err := checkDouyinPayConfigByAppid(v.Appid)
	if err != nil {
		return nil, err
	}
	client := douyin.Client{Appid: *config.DouyinAppId, Secret: *config.DouyinSecret, SignKey: *config.DouyinSignKey}
	result, _ := client.QueryPayState(c.Request.Context(), dbOrder.OrderId)
	if result != "success" {
		return nil, errors.New("Order unpaid")
	}
	return nil, nil
}
