package callback

import (
	"encoding/json"

	"zeus/services/pay/wechat/minigame"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/wechat"
	"zeus/schema"
	"zeus/services"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
)

type MiniGamePayCallback struct {
	Appid string
}

func checkWechatMiniGameConfigByAppid(appid string) (payment.MiniGameWeChatPay, error) {
	var config payment.MiniGameWeChatPay
	converter.QueryByAppid(&config, appid)

	if *config.Appid == "" || *config.MchAPIKey == "" || *config.MchID == "" || *config.MchSN == "" || *config.ApiClientKey == "" || *config.OpenStatus != constants.IsRechargeOpen {
		return config, errors.New("微信小游戏支付参数配置不完整或支付已关闭")
	}
	return config, nil
}

func (v *MiniGamePayCallback) GetPayWay() int {
	return constants.PayWayMiniGameWeChat
}

func (v *MiniGamePayCallback) GetOrder(c *gin.Context) (OrderResult, error) {
	orderResult := OrderResult{}

	// 校验支付配置
	config, err := checkWechatMiniGameConfigByAppid(v.Appid)
	if err != nil {
		return orderResult, err
	}

	// 解析微信请求数据
	transaction, err := wechat.DecodeWechatCallbackRequest(c, *config.ApiClientKey, *config.MchSN, *config.MchID, *config.MchAPIKey)
	if err != nil {
		return orderResult, errors.Errorf("微信小游戏支付回调解析数据失败, %s", err)
	}
	transactionJson, err := json.Marshal(transaction)
	if err != nil {
		transactionJson = []byte("")
	}

	// 根据订单ID获取订单
	dbOrder, err := services.CheckOrder(c, *transaction.OutTradeNo)
	if err != nil {
		return orderResult, errors.Errorf("微信小游戏支付回调，%s订单不存在", *transaction.OutTradeNo)
	}

	// 返回订单及支付信息
	orderResult.DBOrder = &dbOrder
	orderResult.Receipt = string(transactionJson)

	return orderResult, nil
}

func (v *MiniGamePayCallback) Verify(c *gin.Context, dbOrder *models.Order) (*schema.OrderPayResult, error) {
	wechatMiniGame := minigame.NewJsapiOrder(v.Appid)
	OrderPayResult, err := wechatMiniGame.VerifyPayOrder(c, dbOrder)
	if err != nil {
		return nil, err
	}
	return &OrderPayResult, nil
}
