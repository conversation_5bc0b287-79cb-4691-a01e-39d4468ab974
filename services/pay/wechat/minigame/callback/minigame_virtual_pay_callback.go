package callback

import (
	"encoding/json"

	"zeus/services/pay/wechat/minigame"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"zeus/common/constants"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
)

type MiniGameVirtualPayCallback struct {
	Appid string
}

func checkWechatVirtualPayConfigByAppid(appid string) (payment.MiniGameWeChatPay, error) {
	var config payment.MiniGameWeChatPay
	converter.QueryByAppid(&config, appid)

	if *config.Appid == "" || *config.VirtualPaymentSandboxAppKey == "" || *config.VirtualPaymentAppKey == "" || *config.VirtualPaymentOfferId == "" || *config.OpenStatus != constants.IsRechargeOpen {
		return config, errors.New("微信小游戏支付参数配置不完整或支付已关闭")
	}
	return config, nil
}

func (v *MiniGameVirtualPayCallback) GetPayWay() int {
	return constants.PayWayMiniGameWeChat
}

func (v *MiniGameVirtualPayCallback) GetOrder(c *gin.Context) (OrderResult, error) {
	orderResult := OrderResult{}

	// 校验支付配置
	config, err := checkWechatVirtualPayConfigByAppid(v.Appid)
	if err != nil {
		return orderResult, err
	}

	// 解析数据
	var virtualPaymentNotify schema.VirtualPaymentNotify
	err = c.ShouldBindJSON(&virtualPaymentNotify)
	if err != nil {
		return orderResult, err
	}

	notifyInfo := schema.MiniGamePayload{}
	notifyInfoPayload := virtualPaymentNotify.MiniGame.Payload
	err = json.Unmarshal([]byte(notifyInfoPayload), &notifyInfo)
	if err != nil {
		return orderResult, err
	}

	// 校验签名
	appKey := *config.VirtualPaymentAppKey
	if notifyInfo.Env == constants.TestAccount {
		appKey = *config.VirtualPaymentSandboxAppKey
	}
	sig := minigame.WechatMiniGameCalcPaySig(virtualPaymentNotify.Event, notifyInfoPayload, appKey)
	if sig != virtualPaymentNotify.MiniGame.PayEventSig {
		return orderResult, errors.New("签名校验未通过")
	}

	// 根据订单ID获取订单
	dbOrder, err := services.CheckOrder(c, notifyInfo.OutTradeNo)
	if err != nil {
		return orderResult, errors.Errorf("微信小游戏支付回调，%s订单不存在", notifyInfo.OutTradeNo)
	}

	// 校验订单信息
	if dbOrder.OpenId != notifyInfo.OpenId {
		return orderResult, errors.New("openId校验失败")
	}
	if dbOrder.ProductId != notifyInfo.GoodsInfo.ProductId {
		return orderResult, errors.New("productId校验失败")
	}

	// 返回订单及支付信息
	orderResult.DBOrder = &dbOrder
	orderResult.PayId = notifyInfo.WeChatPayInfo.TransactionId
	orderResult.PayAmount = float64(notifyInfo.GoodsInfo.ActualPrice)
	orderResult.PayStatus = constants.PaymentSuccess
	orderResult.Currency = constants.PayCurrencyCNY
	virtualPaymentNotifyJson, err := json.Marshal(virtualPaymentNotify)
	if err != nil {
		virtualPaymentNotifyJson = []byte("")
	}
	orderResult.Receipt = string(virtualPaymentNotifyJson)

	return orderResult, nil
}

func (v *MiniGameVirtualPayCallback) Verify(c *gin.Context, dbOrder *models.Order) (*schema.OrderPayResult, error) {
	return nil, nil
}
