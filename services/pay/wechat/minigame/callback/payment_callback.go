package callback

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
)

type PaymentCallbacker interface {
	GetPayWay() int
	GetOrder(c *gin.Context) (OrderResult, error)
	Verify(c *gin.Context, dbOrder *models.Order) (*schema.OrderPayResult, error)
}

type OrderResult struct {
	DBOrder   *models.Order
	PayId     string
	PayAmount float64
	Currency  string
	PayStatus int
	Receipt   string
}

func updateDBOrder(c *gin.Context, orderResult OrderResult, dbOrder *models.Order, payWay int) error {
	order := schema.OrderPayResult{
		PayID:    orderResult.PayId,
		Currency: orderResult.Currency,
		Amount:   orderResult.PayAmount,
		Status:   orderResult.PayStatus,
	}

	dbOrder.Receipt = orderResult.Receipt
	err := services.UpdatePayOrder(c, order, dbOrder, payWay)
	if err != nil {
		return err
	}
	return nil
}

func PaymentCallbackHandler(c *gin.Context, payment PaymentCallbacker) (int, error) {
	// 解析数据、校验数据、查询订单
	orderResult, err := payment.GetOrder(c)
	if err != nil {
		return controllers.ErrorOrderSign, err
	}
	dbOrder := orderResult.DBOrder

	// 订单已支付
	if dbOrder.PayStatus == constants.PaymentSuccess {
		return controllers.ErrorOrderExists, nil
	}

	// 向三方校验订单
	OrderVerifyResult, err := payment.Verify(c, dbOrder)
	if err != nil {
		return controllers.ErrorOrderSign, err
	}
	if OrderVerifyResult != nil {
		orderResult.PayId = OrderVerifyResult.PayID
		orderResult.PayAmount = OrderVerifyResult.Amount
		orderResult.Currency = OrderVerifyResult.Currency
		orderResult.PayStatus = OrderVerifyResult.Status
	}

	// 保存订单
	err = updateDBOrder(c, orderResult, dbOrder, payment.GetPayWay())
	if err != nil {
		return controllers.ErrorDBError, err
	}

	// 发货
	if orderResult.PayStatus == constants.PaymentSuccess {
		db := c.MustGet("db").(*gorm.DB)
		services.Notify(c.Request.Context(), db, dbOrder, services.NotifyOptions{})
	}
	return controllers.Success, nil
}
