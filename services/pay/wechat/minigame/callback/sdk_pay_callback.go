package callback

import (
	"encoding/json"

	"zeus/services/pay"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/wechat"
	"zeus/schema"
	"zeus/services"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"
)

type SDKPayCallback struct {
	Appid string
}

func checkWechatSDKConfigByAppid(appid string) (payment.WeChatPay, error) {
	var config payment.WeChatPay
	converter.QueryByAppid(&config, appid)

	if *config.WechatAppid == "" || *config.WechatMchID == "" || *config.WechatMchSN == "" || *config.WechatMchAPIKey == "" || *config.OpenStatus != constants.IsRechargeOpen {
		return config, errors.New("微信支付参数配置不完整或支付已关闭")
	}
	return config, nil
}

func (v *SDKPayCallback) GetPayWay() int {
	return constants.PayWayWeChat
}

func (v *SDKPayCallback) GetOrder(c *gin.Context) (OrderResult, error) {
	orderResult := OrderResult{}

	// 校验支付配置
	config, err := checkWechatSDKConfigByAppid(v.Appid)
	if err != nil {
		return orderResult, err
	}

	// 解析微信请求数据
	transaction, err := wechat.DecodeWechatCallbackRequest(c, *config.WechatApiClientKey, *config.WechatMchSN, *config.WechatMchID, *config.WechatMchAPIKey)
	if err != nil {
		return orderResult, errors.Errorf("微信支付回调解析数据失败, %s", err)
	}
	transactionJson, err := json.Marshal(transaction)
	if err != nil {
		transactionJson = []byte("")
	}

	// 根据订单ID获取订单
	dbOrder, err := services.CheckOrder(c, *transaction.OutTradeNo)
	if err != nil {
		return orderResult, errors.Errorf("微信支付回调，%s订单不存在", *transaction.OutTradeNo)
	}

	// 返回订单及支付信息
	orderResult.DBOrder = &dbOrder
	orderResult.Receipt = string(transactionJson)

	return orderResult, nil
}

func (v *SDKPayCallback) Verify(c *gin.Context, dbOrder *models.Order) (*schema.OrderPayResult, error) {
	wechatOrder, _ := pay.GetOrder(constants.PayWayWeChat, v.Appid)
	OrderPayResult, err := wechatOrder.VerifyPayOrder(c, dbOrder)
	if err != nil {
		return nil, err
	}
	return &OrderPayResult, nil
}
