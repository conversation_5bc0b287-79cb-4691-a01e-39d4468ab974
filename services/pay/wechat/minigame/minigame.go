package minigame

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"

	"zeus/pkg/wechat/jsapi"

	"github.com/gin-gonic/gin"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	"zeus/schema"
	"zeus/services"
	"zeus/services/settings/payment"
)

type MiniGameWeChatJsapiOrder struct {
	appid string
}

func NewJsapiOrder(appid string) *MiniGameWeChatJsapiOrder {
	return &MiniGameWeChatJsapiOrder{appid: appid}
}

func CreateJsapiOrder(c *gin.Context, config payment.MiniGameWeChatPay, user models.User, game models.Game, orderForm schema.WechatMiniGameExchange) (schema.PrepayOrderResult, error) {
	log.Debug(c.Request.Context(), "开始创建微信小游戏Jsapi订单", "data", orderForm)

	prepayOrderResult := schema.PrepayOrderResult{
		PrepayId: "",
		OrderId:  "",
	}

	if *config.ApiClientKey == "" || *config.Appid == "" || *config.MchID == "" || *config.MchSN == "" || *config.MchAPIKey == "" {
		log.Error(c.Request.Context(), "微信小游戏jsapi支付参数配置不完整")
		return prepayOrderResult, errors.New("微信小游戏jsapi支付参数配置不完整")
	}

	detail, err := services.GetUserBindWechatDetail(c, user.ID)
	if err != nil {
		return prepayOrderResult, err
	}

	wechatClient := jsapi.Client{
		WechatApiClientKey:         *config.ApiClientKey,
		WechatAppid:                *config.Appid,
		MchID:                      *config.MchID,
		MchAPIv3Key:                *config.MchAPIKey,
		MchCertificateSerialNumber: *config.MchSN,
	}

	orderID := services.GenerateOrderID(constants.PayWayMiniGameWeChat, constants.OrderIDTokenSize, "J")
	prepayOrder := jsapi.PrepayOrder{
		OrderID:   orderID,
		Price:     int(orderForm.Price),
		OpenId:    detail.OpenId,
		NotifyUrl: fmt.Sprintf("https://%s/callback/minigame/wechat/notify/%s", c.Request.Host, orderForm.AppID),
	}
	if orderForm.ProductName != "" {
		prepayOrder.ProductName = orderForm.ProductName
	} else {
		prepayOrder.ProductName = orderForm.ProductID
	}

	result, _, err := wechatClient.CreatePrepay(c, prepayOrder)
	if err != nil {
		return prepayOrderResult, err
	}

	if result.PrepayId != nil {
		prepayOrderResult.PrepayId = *result.PrepayId
		prepayOrderResult.OrderId = orderID
	} else {
		return prepayOrderResult, errors.New("微信小游戏jsapi预支付订单ID为空")
	}

	detail, err = services.GetUserBindWechatDetail(c, user.ID)
	if err != nil {
		return prepayOrderResult, err
	}

	err = services.CreateMiniGameOrder(c, orderID, *result.PrepayId, detail.OpenId, game.Id, user, orderForm)
	if err != nil {
		return prepayOrderResult, err
	}

	return prepayOrderResult, nil
}

func (wo *MiniGameWeChatJsapiOrder) VerifyPayOrder(c *gin.Context, dbOrder *models.Order) (schema.OrderPayResult, error) {
	var OrderPayResult schema.OrderPayResult

	wechatMiniGameOrderResult, err := jsapi.QueryWechatPayTrade(c, dbOrder.OrderId, wo.appid)
	if err != nil || wechatMiniGameOrderResult == nil {
		errorMessage := fmt.Sprintf("报错信息：%s, 微信小游戏支付订单认证失败导致无法完成支付操作, 请确认微信小游戏ios支付相关参数配置是否正确", err)
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			return OrderPayResult, err
		}
		return OrderPayResult, errors.New(errorMessage)
	}

	log.Debug(c.Request.Context(), "校验微信小游戏订单信息完成", "data", wechatMiniGameOrderResult, "order_id", dbOrder.OrderId)

	switch *wechatMiniGameOrderResult.TradeState {
	case "SUCCESS":
		OrderPayResult.Status = constants.PaymentSuccess
	case "REVOKED":
		OrderPayResult.Status = constants.PaymentUnpaid
	case "CLOSED":
		OrderPayResult.Status = constants.PaymentFailure
	}

	if wechatMiniGameOrderResult.Amount != nil {
		OrderPayResult.Amount = float64(*wechatMiniGameOrderResult.Amount.Total)
		OrderPayResult.Currency = ""
		if wechatMiniGameOrderResult.Amount.Currency != nil {
			OrderPayResult.Currency = *wechatMiniGameOrderResult.Amount.Currency
		}
	}

	OrderPayResult.PayID = ""
	if wechatMiniGameOrderResult.TransactionId != nil {
		OrderPayResult.PayID = *wechatMiniGameOrderResult.TransactionId
	}
	return OrderPayResult, err
}

func CreateVirtualPayOrder(c *gin.Context, config payment.MiniGameWeChatPay, user models.User, game models.Game, orderForm schema.WechatMiniGameExchange) (schema.VirtualPaymentResult, error) {
	log.Debug(c.Request.Context(), "开始创建微信小游戏虚拟支付订单", "data", orderForm)

	virtualPaymentResult := schema.VirtualPaymentResult{
		SignData:  "",
		Signature: "",
		PaySig:    "",
	}

	if *config.VirtualPaymentOfferId == "" || *config.Appid == "" || *config.VirtualPaymentAppKey == "" || *config.VirtualPaymentSandboxAppKey == "" {
		log.Error(c.Request.Context(), "微信小游戏虚拟支付参数配置不完整")
		return virtualPaymentResult, errors.New("微信小游戏虚拟支付参数配置不完整")
	}

	detail, err := services.GetUserBindWechatDetail(c, user.ID)
	if err != nil {
		return virtualPaymentResult, err
	}

	orderID := services.GenerateOrderID(constants.PayWayMiniGameWeChat, constants.OrderIDTokenSize, "V")
	signData := schema.VirtualPaymentSignData{
		Mode:         "goods",
		OfferId:      *config.VirtualPaymentOfferId,
		BuyQuantity:  1,
		Env:          orderForm.Env,
		CurrencyType: constants.PayCurrencyCNY,
		Platform:     orderForm.PlatForm,
		ProductId:    orderForm.ProductID,
		GoodsPrice:   int(orderForm.Price),
		OutTradeNo:   orderID,
	}
	err = services.CreateMiniGameOrder(c, orderID, "", detail.OpenId, game.Id, user, orderForm)
	if err != nil {
		return virtualPaymentResult, err
	}

	signDataJson, _ := json.Marshal(signData)
	virtualPaymentResult.SignData = string(signDataJson)
	appKey := *config.VirtualPaymentAppKey
	if signData.Env == constants.TestAccount {
		appKey = *config.VirtualPaymentSandboxAppKey
	}
	virtualPaymentResult.PaySig = WechatMiniGameCalcPaySig("requestMidasPaymentGameItem", string(signDataJson), appKey)
	virtualPaymentResult.Signature = WechatMiniGameCalcSignature(string(signDataJson), detail.SessionKey)

	return virtualPaymentResult, nil
}

func WechatMiniGameCalcPaySig(uri, body, appKey string) string {
	h := hmac.New(sha256.New, []byte(appKey))
	h.Write([]byte(uri + "&" + body))
	return hex.EncodeToString(h.Sum(nil))
}

func WechatMiniGameCalcSignature(body, sessionKey string) string {
	h := hmac.New(sha256.New, []byte(sessionKey))
	h.Write([]byte(body))
	return hex.EncodeToString(h.Sum(nil))
}
