package wechat

import (
	"errors"
	"fmt"
	"time"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/wechat"
	"zeus/schema"
	"zeus/services"
	"zeus/utils"

	"zeus/common/log"

	"github.com/gin-gonic/gin"
)

type WeChatOrder struct {
	appid string
}

func NewWeChatOrder(appid string) *WeChatOrder {
	return &WeChatOrder{appid: appid}
}

func CreateWeChatOrder(c *gin.Context, user models.User, game models.Game, order schema.WeChatExchangeForm) (*schema.WeChatOrder, error) {
	log.Debug(c.Request.Context(), "开始创建微信订单", "data", order)

	wechatResult, orderModel, err := wechat.WechatPay(c, order)
	if err != nil {
		return nil, err
	}
	orderModel.Vip = utils.ConvertStringToInt(order.Vip)
	orderModel.Level = utils.ConvertStringToInt(order.Level)
	price := order.Price
	orderModel.GameId = game.Id
	orderModel.Account = user.Account
	orderModel.UserId = user.ID
	orderModel.RoleId = order.RoleID
	orderModel.Price = price
	orderModel.Extend = order.Extend
	orderModel.Device = order.Device
	orderModel.RoleName = order.RoleName
	orderModel.PayNotifyUrl = order.PayNotifyURL
	orderModel.CreateTime = time.Now()
	orderModel.Platform = order.PlatForm
	orderModel.ServerId = order.ServerID
	orderModel.ServerName = order.ServerName

	err = services.CreateOrder(c, orderModel)
	if err != nil {
		return wechatResult, err
	}
	return wechatResult, err
}

func (wo *WeChatOrder) VerifyPayOrder(c *gin.Context, dbOrder *models.Order) (schema.OrderPayResult, error) {
	var OrderPayResult schema.OrderPayResult
	wechatOrderResult, err := wechat.QueryWechatPayTrade(c, *dbOrder, wo.appid)
	if err != nil {
		errorMessage := fmt.Sprintf("微信支付订单认证失败导致无法完成支付操作, 请确认微信支付相关参数配置是否正确, %s", err)
		err := services.UpdateErrorMessage(c, errorMessage, *dbOrder)
		if err != nil {
			return OrderPayResult, err
		}
		return OrderPayResult, errors.New(errorMessage)
	}

	log.Debug(c.Request.Context(), "校验微信订单信息完成", "data", wechatOrderResult, "order_id", dbOrder.OrderId)

	switch *wechatOrderResult.TradeState {
	case "SUCCESS":
		OrderPayResult.Status = constants.PaymentSuccess
	case "REVOKED":
		OrderPayResult.Status = constants.PaymentUnpaid
	case "CLOSED":
		OrderPayResult.Status = constants.PaymentFailure
	}

	if wechatOrderResult.Amount != nil {
		OrderPayResult.Amount = float64(*wechatOrderResult.Amount.Total)
		OrderPayResult.Currency = ""
		if wechatOrderResult.Amount.Currency != nil {
			OrderPayResult.Currency = *wechatOrderResult.Amount.Currency
		}
	}

	OrderPayResult.PayID = ""
	if wechatOrderResult.TransactionId != nil {
		OrderPayResult.PayID = *wechatOrderResult.TransactionId
	}
	return OrderPayResult, err
}
