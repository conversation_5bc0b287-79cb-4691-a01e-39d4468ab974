package services

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/robfig/cron/v3"
	"gorm.io/gorm"
	"time"
	"zeus/common/constants"

	"zeus/common/log"
	"zeus/models"
)

const ChooseRangeTime int = 1
const LoopByCalendarMonth int = 2

func generateNextMonthRangeTime(currentTime time.Time) (time.Time, time.Time) {
	newMonth := currentTime.AddDate(0, 0, 10)

	startTime := time.Date(newMonth.Year(), newMonth.Month(), 1, 0, 0, 0, 0, newMonth.Location())
	endTime := time.Date(newMonth.Year(), newMonth.Month(), 1, 23, 59, 59, 0, newMonth.Location()).AddDate(0, 1, -1)
	return startTime, endTime
}

func GetPointStrategyRecordRangeTimeByType(db *gorm.DB, recordId int, recordType int, rangeTime []string) (time.Time, time.Time, error) {
	var startTime time.Time
	var endTime time.Time

	if recordType == LoopByCalendarMonth {
		latestStrategies, err := models.GetLatestWebPayPointStrategyRecord(db, recordId)
		if err != nil {
			return startTime, endTime, err
		}
		startTime, endTime = generateNextMonthRangeTime(latestStrategies.EndTime)
		return startTime, endTime, nil
	} else if recordType == ChooseRangeTime {
		if len(rangeTime) == 2 {
			startTime, err := time.ParseInLocation(constants.DateTimeMinuteOnly, rangeTime[0], time.Local)
			endTime, err := time.ParseInLocation(constants.DateTimeMinuteOnly, rangeTime[1], time.Local)
			if err != nil {
				return startTime, endTime, err
			}
			return startTime, endTime, nil
		} else {
			return startTime, endTime, errors.New("缺少range_time参数")
		}
	} else {
		return startTime, endTime, errors.New("暂时不支持此类型！")
	}
}

func CheckStrategyRecord(db *gorm.DB, recordId int, startTime time.Time, endTime time.Time) error {
	strategies, err := models.GetWebPayPointStrategyRecordByEndTime(db, recordId, startTime)
	if err != nil {
		return err
	}

	for _, v := range strategies {
		if v.EndTime.After(startTime) && v.StartTime.Before(endTime) {
			return errors.New("时间冲突，请修改！")
		}
	}
	return nil
}

func GetReceivedPoints(db *gorm.DB, roleId string, serverId string, currentPointStrategyRecordId int) ([]string, error) {
	var receivedPoints = make([]string, 0)

	pointsExchangeRecords, err := models.GetPointsExchangeRecordsByRoleIdAndServerId(db, roleId, serverId, currentPointStrategyRecordId)
	if err != nil {
		return receivedPoints, err
	}

	for _, r := range pointsExchangeRecords {
		receivedPoints = append(receivedPoints, string(rune(r.Points)))
	}
	return receivedPoints, nil
}

func addPointStrategyRecord(ctx context.Context) {
	projects, _ := GetProjects()

	for _, project := range projects {
		db, err := GetGameDBByProjectId(project.ID)
		if err != nil {
			continue
		}

		latestStrategyRecord, err := models.GetLatestWebPayPointStrategyRecord(db, 0)
		if err != nil {
			log.Error(ctx, "查询累计充值记录出错", "project_name", project.Name)
			continue
		}
		if latestStrategyRecord.Type == LoopByCalendarMonth {
			startTime, endTime := generateNextMonthRangeTime(latestStrategyRecord.EndTime)

			pointsAndProducts := make([]models.PointsAndProducts, 0)
			pointStrategies, _, err := models.GetWebPayPointStrategy(db)
			for _, pointStrategy := range pointStrategies {
				p := models.PointsAndProducts{
					Points:  pointStrategy.Points,
					Product: pointStrategy.ProductId,
				}
				pointsAndProducts = append(pointsAndProducts, p)
			}

			products, err := json.Marshal(pointsAndProducts)

			newPointStrategyRecordInfo := &models.WebPayPointStrategyRecord{
				Name:        latestStrategyRecord.Name,
				Description: latestStrategyRecord.Description,
				Products:    string(products),
				StartTime:   startTime,
				EndTime:     endTime,
				Picture:     latestStrategyRecord.Picture,
				Type:        latestStrategyRecord.Type,
			}

			err = models.CreateWebPayPointStrategyRecord(db, newPointStrategyRecordInfo)
			if err != nil {
				log.Error(ctx, "添加累计充值记录出错", "project_name", project.Name, "newPointStrategyRecordInfo", newPointStrategyRecordInfo)
				continue
			}
			log.Info(ctx, "自动添加累计充值记录", "project_name", project.Name, "newPointStrategyRecordInfo", newPointStrategyRecordInfo)
		}
	}
}

func CronPointStrategyRecordTask(c *cron.Cron) {
	spec := "0 0 25 * *"
	c.AddFunc(spec, func() {
		addPointStrategyRecord(context.Background())
	})
}
