package services

import (
	"time"

	"zeus/common"
	"zeus/models"
	"zeus/schema"
)

func GetProjects() ([]*models.Project, error) {
	db := common.GetInfoDB()
	projects, err := models.GetProject(db)
	return projects, err
}

func CreateProject(project schema.ProjectCreateForm) (*models.Project, error) {
	db := common.GetInfoDB()

	dbProject := models.Project{}
	dbProject.Name = project.Name
	dbProject.Alias = project.Alias
	dbProject.Type = project.Type
	dbProject.Status = project.Status
	dbProject.MysqlDatabase = project.MysqlDatabase
	dbProject.CreateTime = time.Now()
	err := models.CreateProject(db, &dbProject)
	return &dbProject, err
}

func UpdateProject(dbProject *models.Project, projectForm *schema.ProjectSummary) (*models.Project, error) {
	db := common.GetInfoDB()
	dbProject.Name = projectForm.Name
	dbProject.Alias = projectForm.Alias
	dbProject.Type = projectForm.Type
	dbProject.Status = projectForm.Status
	dbProject.MysqlDatabase = projectForm.MysqlDatabase
	dbProject.UpdateTime = time.Now()
	err := models.SaveProject(db, dbProject)

	pool := common.GetDBPool()
	pool.Delete(projectForm.Name)

	return dbProject, err
}

func DeleteProject(dbProject *models.Project) error {
	db := common.GetInfoDB()
	err := models.DeleteProject(db, dbProject)
	return err
}

func GetProjectById(id int) (*models.Project, error) {
	db := common.GetInfoDB()
	dbProject := models.Project{}
	err := db.Model(&models.Project{}).Where("id = ?", id).First(&dbProject).Error

	return &dbProject, err
}

func GetProjectByAlias(alias string) (*models.Project, error) {
	db := common.GetInfoDB()
	dbProject := models.Project{}
	err := db.Model(&models.Project{}).Where("alias = ?", alias).Where("status != 0").First(&dbProject).Error

	return &dbProject, err
}
