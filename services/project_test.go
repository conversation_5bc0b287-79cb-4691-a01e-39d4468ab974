package services

import (
	"testing"

	"zeus/common"
	"zeus/models"
	"zeus/schema"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestGetProjects(t *testing.T) {
	apply1 := ApplyFunc(common.GetInfoDB, func() *gorm.DB {
		return &gorm.DB{}
	})
	defer apply1.Reset()

	apply2 := ApplyFunc(models.GetProject, func(db *gorm.DB) ([]*models.Project, error) {
		var projects []*models.Project
		projects = append(projects, &models.Project{
			Name: "MyTools",
		})
		return projects, nil
	})
	defer apply2.Reset()

	Convey("GetProjects", t, func() {
		Convey("one func for succ", func() {
			projects, _ := GetProjects()
			expected := "MyTools"
			So(projects[0].Name, ShouldEqual, expected)
		})
	})
}

func TestCreateProject(t *testing.T) {
	apply1 := ApplyFunc(common.GetInfoDB, func() *gorm.DB {
		return &gorm.DB{}
	})
	defer apply1.Reset()

	apply2 := ApplyFunc(models.CreateProject, func(db *gorm.DB, args *models.Project) error {
		return nil
	})
	defer apply2.Reset()

	Convey("CreateProject", t, func() {
		Convey("one func for succ", func() {
			v := schema.ProjectCreateForm{
				Name: "我的世界",
			}
			project, _ := CreateProject(v)
			expected := "我的世界"
			So(project.Name, ShouldEqual, expected)
		})
	})
}

func TestUpdateProject(t *testing.T) {
	apply1 := ApplyFunc(common.GetInfoDB, func() *gorm.DB {
		return &gorm.DB{}
	})
	defer apply1.Reset()

	apply2 := ApplyFunc(models.SaveProject, func(db *gorm.DB, args *models.Project) error {
		return nil
	})
	defer apply2.Reset()

	Convey("UpdateProject", t, func() {
		Convey("one func for succ", func() {
			oldV := &models.Project{
				Name: "老对世界",
			}

			v := &schema.ProjectSummary{
				Name: "我的世界",
			}

			project, _ := UpdateProject(oldV, v)
			expected := "我的世界"
			So(project.Name, ShouldEqual, expected)
		})
	})
}
