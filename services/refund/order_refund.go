package refund

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/pay"
	"zeus/services/pay/apple"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"zeus/common/log"

	"github.com/robfig/cron/v3"
)

func GetAndUpdateGooglePayVoidedPurchases(ctx context.Context, gameId int, startDate string, endDate string) ([]string, error) {
	var orderIds []string
	dbGame, _ := game.GetGameById(gameId)

	var base payment.Payment
	converter.QueryByGameId(&base, gameId)

	var config payment.GooglePay
	converter.QueryByGameId(&config, gameId)

	if *config.OpenStatus != constants.IsRechargeOpen {
		return orderIds, errors.New("当前项目未开放GooglePlay支付")
	}

	googleClient := pay.GoogleClient{
		RefreshToken: *config.GoogleRefreshToken,
		ClientId:     *config.GoogleClientId,
		ClientSecret: *config.GoogleClientSecret,
		RedirectUri:  *config.GoogleRedirectUri,
	}

	if *config.GooglePayPackageName == "" {
		return orderIds, errors.New("当前项目下未配置PackageName")
	}

	p := pay.GooglePurchaseClient{
		GoogleClient: googleClient,
		PackageName:  *config.GooglePayPackageName,
	}

	var startTime, endTime string

	if startDate != "" {
		startDateTime, _ := time.Parse("2006-01-02", startDate)
		startTime = strconv.FormatInt(startDateTime.UnixMilli(), 10)
	}

	if endDate != "" {
		endDateTime, _ := time.Parse("2006-01-02", endDate)
		endTime = strconv.FormatInt(endDateTime.UnixMilli(), 10)
	}

	voidedpurchases, err := p.GetRefundHistory(ctx, startTime, endTime)
	if err != nil {
		return []string{}, err
	}

	db, _ := services.GetGameDBByProjectId(dbGame.ProjectID)
	for _, order := range voidedpurchases.VoidedPurchases {
		dbOrder, err := models.GetOrderByPayId(db, order.OrderId)
		if err != nil {
			log.Warn(ctx, "Zeus内未发现订单", "order_id", order.OrderId)
			continue
		}
		log.Debug(ctx, "更新订单支付状态", "order_id", order.OrderId)
		voidedTimeMillis, _ := strconv.ParseInt(order.VoidedTimeMillis, 10, 64)
		dbOrder.PayStatus = constants.PaymentRefund
		dbOrder.RefundTime = time.Unix(voidedTimeMillis/1000, 0)
		models.SaveOrder(db, &dbOrder)
		orderIds = append(orderIds, order.OrderId)
	}

	return orderIds, nil
}

func OrderRefundTask(ctx context.Context, gameId int, startDate string, endDate string) {
	games := game.GetGames()

	for _, game := range games {
		if gameId != 0 && game.Id != gameId {
			continue
		}
		var config payment.Payment
		converter.QueryByGameId(&config, gameId)

		if *config.IsRechargeOpen != constants.IsRechargeOpen {
			continue
		}

		key := fmt.Sprintf("RefundTask %d", game.Id)
		err := redis.Lock(key, time.Second*30)
		if err != nil {
			continue
		}

		applePayConfig := new(payment.ApplePay)
		converter.QueryByGameId(applePayConfig, game.Id)
		if *applePayConfig.OpenStatus == constants.IsRechargeOpen {
			log.Debug(ctx, "开始处理项目的苹果退款", "game_name", game.Name)

			appleClient := apple.AppleClient{
				ISS:    *applePayConfig.AppleISS,
				KeyID:  *applePayConfig.AppleKeyID,
				BID:    *applePayConfig.AppleBundleId,
				Secret: *applePayConfig.AppleSecret,
			}
			appleRefundOrderIds, _ := apple.UpdateAppleRefundOrder(ctx, game.Id, appleClient, startDate, endDate)
			log.Debug(ctx, "开始处理项目的苹果退款", "game_name", game.Name, "count", len(appleRefundOrderIds), "order_ids", appleRefundOrderIds)
		}

		googlePayConfig := new(payment.GooglePay)
		converter.QueryByGameId(googlePayConfig, game.Id)
		if *googlePayConfig.OpenStatus == constants.IsRechargeOpen {
			log.Debug(ctx, "开始处理项目的Google退款", "game_name", game.Name)
			googleRefundOrderIds, _ := GetAndUpdateGooglePayVoidedPurchases(ctx, game.Id, startDate, endDate)
			log.Debug(ctx, "开始处理项目的GooglePay退款", "game_name", game.Name, "count", len(googleRefundOrderIds), "order_ids", googleRefundOrderIds)
		}
	}
}

func CronScanRefundOrderTask(c *cron.Cron) {
	spec := "0 */1 * * *" // 每天的0点10分执行
	c.AddFunc(spec, func() {
		startDate := time.Now().AddDate(0, 0, -3).Format("2006-01-02")
		endDate := time.Now().Format("2006-01-02")
		OrderRefundTask(context.Background(), 0, startDate, endDate)
	})
}
