package services

import (
	"fmt"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/mapper"
	"zeus/schema/domain"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetRecentlyLoggedInUnBindUser 通过Device获取最近登录的未绑定三方账号的zeus用户
func GetRecentlyLoggedInUnBindUser(c *gin.Context, device string) (*models.User, error) {
	db := c.MustGet("db").(*gorm.DB)
	gameID := c.MustGet("gameID")
	user, err := models.GetRecentlyLoggedInUnboundUserByRegisterDevice(db, device, gameID)
	if err != nil {
		return nil, err
	}
	return user, nil
}

type Registrar interface {
	Register(c *gin.Context) (*models.User, error)
}

type EmailRegisterServices struct {
	db         *gorm.DB
	APPID      string `json:"appid"`
	Device     string `json:"device"`
	Platform   string `json:"platform"`
	MobileInfo string `json:"mobile_info"`
	Email      string `json:"email"`
	Password   string `json:"password"`
	Name       string `json:"name"`
}

func (e *EmailRegisterServices) Map(v interface{}) (err error) {
	mapper.Map(e, v)
	e.db, err = GetGameDBByAppID(e.APPID)
	return
}

func (e *EmailRegisterServices) emailIsExists() bool {
	args := make(map[string]interface{})
	args["email"] = e.Email
	user, _ := models.GetOneUser(e.db, args)
	if user.ID == 0 {
		return false
	}
	return true
}

func (e *EmailRegisterServices) Register(c *gin.Context) (*models.User, error) {
	if e.emailIsExists() {
		return nil, fmt.Errorf("邮箱:%s 已经注册过", e.Email)
	}
	us := new(UserServices)
	us.Map(e)
	dbUser, err := us.CreateUser(c)
	return dbUser, err
}

type PhoneRegisterServices struct {
	db         *gorm.DB
	APPID      string `json:"appid"`
	Device     string `json:"device"`
	Platform   string `json:"platform"`
	MobileInfo string `json:"mobile_info"`
	Phone      string `json:"phone"`
	Password   string `json:"password"`
	Name       string `json:"name"`
}

func (p *PhoneRegisterServices) Map(v interface{}) (err error) {
	mapper.Map(p, v)
	p.db, err = GetGameDBByAppID(p.APPID)
	return
}

func (p *PhoneRegisterServices) phoneIsExists() bool {
	args := make(map[string]interface{})
	args["phone"] = p.Phone
	user, _ := models.GetOneUser(p.db, args)
	if user.ID == 0 {
		return false
	}
	return true
}

func (p *PhoneRegisterServices) Register(c *gin.Context) (*models.User, error) {
	if p.phoneIsExists() {
		return nil, fmt.Errorf("电话:%s 已经注册过", p.Phone)
	}
	us := new(UserServices)
	us.Map(p)
	dbUser, err := us.CreateUser(c)
	return dbUser, err
}

type DeviceRegisterServices struct {
	db         *gorm.DB
	APPID      string `json:"appid"`
	Device     string `json:"device"`
	Platform   string `json:"platform"`
	ForceRenew bool   `json:"force_renew"`
	MobileInfo string `json:"mobile_info"`
}

func (d *DeviceRegisterServices) Map(v interface{}) (err error) {
	mapper.Map(d, v)
	d.db, err = GetGameDBByAppID(d.APPID)
	return
}

func (d *DeviceRegisterServices) Register(c *gin.Context) (*models.User, error) {
	// NotForceCreation为false时强制创建一个账号
	if !d.ForceRenew {
		user, err := GetRecentlyLoggedInUnBindUser(c, d.Device)
		if err != nil {
			return nil, err
		}
		if user.ID != 0 {
			return user, nil
		}
	}
	us := new(UserServices)
	us.Map(d)
	dbUser, err := us.CreateUser(c)
	return dbUser, err
}

func ChoiceRegisterServices(r *domain.Register) Registrar {
	switch r.RegisterType {
	case constants.RegisterTypeEmail:
		ers := new(EmailRegisterServices)
		_ = ers.Map(r)
		return ers
	case constants.RegisterTypePhone:
		prs := new(PhoneRegisterServices)
		_ = prs.Map(r)
		return prs
	default:
		drs := new(DeviceRegisterServices)
		_ = drs.Map(r)
		return drs
	}
}
