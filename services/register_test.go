package services

import (
	"reflect"
	"testing"

	"zeus/common/constants"
	"zeus/models"
	"zeus/schema/domain"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestRegister(t *testing.T) {
	c := &gin.Context{}
	c.Set("db", &gorm.DB{})
	c.Set("gameID", "1")

	Convey("GetRecentlyLoggedInUnBindUser", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetRecentlyLoggedInUnboundUserByRegisterDevice,
				func(_ *gorm.DB, _ string, _ any) (*models.User, error) {
					user := &models.User{ID: 11}
					return user, nil
				})

			output, _ := GetRecentlyLoggedInUnBindUser(c, "test_device")
			expected := 11
			So(output.ID, ShouldEqual, expected)
		})
	})
}

func TestRegisterServices(t *testing.T) {
	apply1 := ApplyFunc(models.GetOneUser, func(db *gorm.DB, args map[string]interface{}) (*models.User, error) {
		return &models.User{}, nil
	})
	defer apply1.Reset()

	us := new(UserServices)
	apply2 := ApplyMethod(reflect.TypeOf(us), "CreateUser", func(_ *UserServices, c *gin.Context) (*models.User, error) {
		return &models.User{Name: "saber"}, nil
	})
	defer apply2.Reset()

	apply3 := ApplyFunc(GetGameDBByAppID, func(appID string) (*gorm.DB, error) {
		return &gorm.DB{}, nil
	})
	defer apply3.Reset()

	c := &gin.Context{}

	Convey("TestEmailRegister", t, func() {
		Convey("one func for succ", func() {
			v := &domain.Register{
				RegisterType: constants.RegisterTypeEmail,
			}
			ers := ChoiceRegisterServices(v)
			user, _ := ers.Register(c)
			expected := "saber"
			So(user.Name, ShouldEqual, expected)
		})
	})

	Convey("TestPhoneRegister", t, func() {
		Convey("one func for succ", func() {
			v := &domain.Register{
				RegisterType: constants.RegisterTypePhone,
			}
			prs := ChoiceRegisterServices(v)
			user, _ := prs.Register(c)
			expected := "saber"
			So(user.Name, ShouldEqual, expected)
		})
	})

	Convey("TestDeviceRegister", t, func() {
		Convey("one func for succ", func() {
			v := &domain.Register{
				RegisterType: constants.RegisterTypeDevice,
				ForceRenew:   true,
			}
			drs := ChoiceRegisterServices(v)
			user, _ := drs.Register(c)
			expected := "saber"
			So(user.Name, ShouldEqual, expected)
		})

		Convey("two func for succ", func() {
			apply := ApplyFunc(GetRecentlyLoggedInUnBindUser, func(c *gin.Context, device string) (*models.User, error) {
				return &models.User{
					ID:   1,
					Name: "那鲁托",
				}, nil
			})
			defer apply.Reset()
			v := &domain.Register{
				RegisterType: constants.RegisterTypeDevice,
				ForceRenew:   false,
			}
			drs := ChoiceRegisterServices(v)
			user, _ := drs.Register(c)
			expected := "那鲁托"
			So(user.Name, ShouldEqual, expected)
		})
	})
}
