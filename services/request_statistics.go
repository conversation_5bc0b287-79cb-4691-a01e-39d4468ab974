package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"zeus/common"
	"zeus/common/log"
	"zeus/middlewares/counter"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services/game"

	"github.com/robfig/cron/v3"
)

func GetRequestStatistics() map[string]uint64 {
	request := make(map[string]uint64)
	for _, c := range counter.ReqeustConterMap {
		request[fmt.Sprintf("%v,%v,%v", c.AppId, c.<PERSON>questP<PERSON>, c.RequestDate)] = c.Get()
	}
	return request
}

func UpdateRequestStatistics(ctx context.Context) error {
	key := fmt.Sprintf("RequestCountTask")
	err := redis.Lock(key, time.Minute*1)
	if err != nil {
		log.Warn(ctx, err.Error())
	}
	defer redis.UnLock(key)

	for _, c := range counter.ReqeustConterMap {
		err := SaveRequestStatisticsToDB(c.AppId, c.<PERSON>, c.<PERSON>, c.<PERSON>())
		if err == nil {
			c.Reset()
		} else {
			return err
		}
	}
	return nil
}

func SaveRequestStatisticsToDB(appid string, requestPath string, requestDate string, count uint64) error {
	if appid != "" && requestPath != "" && requestDate != "" {
		filter := map[string]interface{}{
			"appid":        appid,
			"request_path": requestPath,
			"request_date": requestDate,
		}

		dbGame, err := game.GetGameByAppid(appid)
		if err != nil {
			return err
		}

		dbRequest, _ := models.GetRequestStatistics(filter)
		if dbRequest.Id == 0 {
			requestStatistics := models.RequestStatistics{}
			requestStatistics.AppId = appid
			requestStatistics.ProjectId = dbGame.ProjectID
			requestStatistics.RequestPath = requestPath
			requestStatistics.RequestDate = requestDate
			requestStatistics.RequestCount = int64(count)
			err = models.CreateRequestStatistics(&requestStatistics)
			if err != nil {
				return err
			}
		} else {
			dbRequest.RequestCount = dbRequest.RequestCount + int64(count)
			dbRequest.RequestDate = requestDate
			err = models.SaveRequestStatistics(dbRequest)
			if err != nil {
				return err
			}
		}
	} else {
		errMessage := fmt.Sprintf("[request_statistics] 请求计数数据不完整, appid: %s, requestPath: %s, requestDate %s, 请求数: %d", appid, requestPath, requestDate, count)
		return errors.New(errMessage)
	}
	return nil
}

func GetRequestPercentageData(p schema.QueryRequestPercentageForm) []schema.RequestPercentage {
	requestData := make([]schema.RequestPercentage, 0)
	if p.StartDate != "" && p.EndDate != "" {
		db := common.GetInfoDB()
		sql := "select project.alias as project_alias, request_statistics.request_count, " +
			"concat(FORMAT(request_statistics.request_count / (select sum(request_count) from request_statistics " +
			"where project_id in (select id from project where status = 1)  and request_date >= '" + p.StartDate +
			"' and request_date <= '" + p.EndDate + "') * 100, 2), '%') as percentage " +
			"from (select project_id, sum(request_count) as request_count from request_statistics " +
			"where project_id in (select id from project where status = 1) and request_date >= '" + p.StartDate + "' " +
			"and request_date <= '" + p.EndDate + "' group by project_id) as request_statistics" +
			" inner join project on request_statistics.project_id = project.id order by request_statistics.request_count desc"

		db.Raw(sql).Scan(&requestData)
	}
	return requestData
}

func GetRequestStatisticsData(p schema.RequestStatisticsForm) ([]schema.RequestStatistics, int64, error) {
	db := common.GetInfoDB()

	var requestData []schema.RequestStatistics
	var sql string
	dbGame, err := game.GetGameInfoById(p.GameID)
	if err != nil {
		return requestData, 0, err
	}
	appid := dbGame.Appid
	switch p.Dimension {
	case "request_path":
		sql = fmt.Sprintf("select request_path, sum(request_count) request_count "+
			"from request_statistics where appid=%q group by request_path ", appid)
		if p.RequestDateStart != "" && p.RequestDateEnd != "" {
			sql = fmt.Sprintf("select request_path, sum(request_count) request_count "+
				"from request_statistics where appid=%q and request_date >= %q and request_date <= %q group by request_path ", appid, p.RequestDateStart, p.RequestDateEnd)
		}
	case "request_date":
		sql = fmt.Sprintf("select request_date, sum(request_count) request_count "+
			"from request_statistics where appid=%q group by request_date ", appid)
		if p.RequestDateStart != "" && p.RequestDateEnd != "" {
			sql = fmt.Sprintf("select request_date, sum(request_count) request_count "+
				"from request_statistics where appid=%q and request_date >= %q and request_date <= %q group by request_date ", appid, p.RequestDateStart, p.RequestDateEnd)
		}
	case "all_dimension":
		sql = fmt.Sprintf("select request_path, request_date, sum(request_count) request_count "+
			"from request_statistics where appid=%q group by request_path, request_date ", appid)
		if p.RequestDateStart != "" && p.RequestDateEnd != "" {
			sql = fmt.Sprintf("select request_path, request_date, sum(request_count) request_count "+
				"from request_statistics where appid=%q and request_date >= %q and request_date <= %q group by request_path, request_date ", appid, p.RequestDateStart, p.RequestDateEnd)
		}
	}

	if p.Field != "" {
		sql += fmt.Sprintf(" order by %s ", p.Field)
		if p.Order == "ascend" {
			sql += "asc"
		} else {
			sql += "desc"
		}
	}
	var total int64
	db.Raw(sql).Count(&total)
	if p.PageNumber != 0 && p.PageSize != 0 {
		offset := p.PageSize * (p.PageNumber - 1)
		sql = sql + fmt.Sprintf(" limit %d offset %d", p.PageSize, offset)
	}
	db.Raw(sql).Scan(&requestData)
	if total == 0 {
		// 查询到1条数据时，total为0，判断len(orderData)再次确认
		total = int64(len(requestData))
	}

	return requestData, total, nil
}

func CronRequestStatisticsTask(c *cron.Cron) {
	spec := "*/1 * * * *" // 每1分钟执行一次
	c.AddFunc(spec, func() {
		UpdateRequestStatistics(context.Background())
	})
}
