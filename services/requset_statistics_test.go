package services

import (
	"context"
	"reflect"
	"testing"
	"time"

	"zeus/common"
	"zeus/middlewares/counter"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/schema"
	"zeus/services/game"

	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestGetRequestPercentageData(t *testing.T) {
	Convey("GetRequestPercentageData", t, func() {
		Convey("one func for succ", func() {
			mockGetInfoDBFunc := ApplyFunc(common.GetInfoDB, func() *gorm.DB {
				return &gorm.DB{}
			})
			defer mockGetInfoDBFunc.Reset()
			db := &gorm.DB{}
			mockRawMethod := ApplyMethod(reflect.TypeOf(db), "Raw", func(_ *gorm.DB,
				sql string, values ...interface{},
			) (tx *gorm.DB) {
				return db
			})
			defer mockRawMethod.Reset()
			mockScanMethod := ApplyMethod(reflect.TypeOf(db), "Scan", func(_ *gorm.DB, dest interface{}) (tx *gorm.DB) {
				data := dest.(*[]schema.RequestPercentage)
				*data = append(*data, schema.RequestPercentage{ProjectAlias: "ace"})
				return db
			})
			defer mockScanMethod.Reset()

			v := schema.QueryRequestPercentageForm{
				StartDate: "2023-08-01",
				EndDate:   "2023-09-01",
			}
			result := GetRequestPercentageData(v)
			actual := result[0].ProjectAlias
			expected := "ace"
			So(actual, ShouldEqual, expected)
		})
	})
}

func TestUpdateRequestStatistics(t *testing.T) {
	mockLockFunc := ApplyFunc(redis.Lock, func(_ string, _ time.Duration) error {
		return nil
	})
	defer mockLockFunc.Reset()
	mockUnLockFunc := ApplyFunc(redis.UnLock, func(key string) int64 {
		return 0
	})
	defer mockUnLockFunc.Reset()

	Convey("UpdateRequestStatistics", t, func() {
		Convey("one func for succ", func() {
			counter.ReqeustConterMap.GetOrCreateCounter("test_appid_1", "/test", "2023-08-01")
			counter.ReqeustConterMap.GetOrCreateCounter("test_appid_2", "/test", "2023-08-01")
			mockGetGameByAppidFunc := ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
				return &models.Game{ProjectID: 1}, nil
			})
			defer mockGetGameByAppidFunc.Reset()
			mockGetRequestStatisticsFunc := ApplyFunc(models.GetRequestStatistics,
				func(args map[string]interface{}) (*models.RequestStatistics, error) {
					appid := args["appid"].(string)
					if appid == "test_appid_1" {
						return &models.RequestStatistics{}, nil
					}
					return &models.RequestStatistics{Id: 1}, nil
				})
			defer mockGetRequestStatisticsFunc.Reset()
			mockCreateRequestStatisticsFunc := ApplyFunc(models.CreateRequestStatistics,
				func(_ *models.RequestStatistics) error {
					return nil
				})
			defer mockCreateRequestStatisticsFunc.Reset()

			mockSaveRequestStatisticsFunc := ApplyFunc(models.SaveRequestStatistics,
				func(_ *models.RequestStatistics) error {
					return nil
				})
			defer mockSaveRequestStatisticsFunc.Reset()
			actual := UpdateRequestStatistics(context.Background())
			So(actual, ShouldEqual, nil)
		})
	})
}

func TestGetRequestStatisticsData(t *testing.T) {
	mockGetInfoDBFunc := ApplyFunc(common.GetInfoDB, func() *gorm.DB {
		return &gorm.DB{}
	})
	defer mockGetInfoDBFunc.Reset()

	mockGetGameInfoByIdFunc := ApplyFunc(game.GetGameInfoById, func(id int) (*models.Game, error) {
		return &models.Game{}, nil
	})
	defer mockGetGameInfoByIdFunc.Reset()

	db := &gorm.DB{}
	mockRawMethod := ApplyMethod(reflect.TypeOf(db), "Raw", func(_ *gorm.DB, _ string, _ ...interface{}) (tx *gorm.DB) {
		return db
	})
	defer mockRawMethod.Reset()

	mockCountMethod := ApplyMethod(reflect.TypeOf(db), "Count", func(_ *gorm.DB, count *int64) (tx *gorm.DB) {
		return db
	})
	defer mockCountMethod.Reset()

	mockScanMethod := ApplyMethod(reflect.TypeOf(db), "Scan", func(_ *gorm.DB, dest interface{}) (tx *gorm.DB) {
		return db
	})
	defer mockScanMethod.Reset()

	Convey("GetRequestStatisticsData", t, func() {
		Convey("one func for succ", func() {
			testData := []schema.RequestStatisticsForm{
				{
					Dimension:        "request_path",
					RequestDateStart: "2023-01-01",
					RequestDateEnd:   "2023-12-01",
					Order:            "ascend",
					Field:            "data",
					PageSize:         10,
					PageNumber:       1,
				},
				{
					Dimension:        "request_path",
					RequestDateStart: "2023-01-01",
					RequestDateEnd:   "2023-12-01",
				},
				{
					Dimension:        "request_path",
					RequestDateStart: "2023-01-01",
					RequestDateEnd:   "2023-12-01",
				},
			}

			for _, data := range testData {
				_, actual, _ := GetRequestStatisticsData(data)
				So(actual, ShouldEqual, 0)
			}
		})
	})
}
