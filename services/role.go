package services

import (
	"zeus/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func GetRoleByRoleID(c *gin.Context, roleID string) (*models.Role, error) {
	db := c.MustGet("db").(*gorm.DB)
	args := map[string]interface{}{
		"role_id": roleID,
	}
	return models.QueryRole(db, args)
}

func GetRoleByRoleIDAndServerID(c *gin.Context, gameID int, roleID string, serverID string) (*models.Role, error) {
	db := c.MustGet("db").(*gorm.DB)
	args := map[string]interface{}{
		"game_id":   gameID,
		"role_id":   roleID,
		"server_id": serverID,
	}
	return models.QueryRole(db, args)
}

func GetRoleByRoleId(c *gin.Context, roleId string) ([]models.Role, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetRoles(db, roleId)
}

func GetRoleByRoleName(c *gin.Context, roleName string) ([]models.Role, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.QueryRoleByName(db, roleName)
}

func QueryAllRole(c *gin.Context, filter map[string]interface{}) ([]models.Role, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetAllRole(db, filter)
}

func SaveRole(c *gin.Context, role *models.Role) error {
	db := c.MustGet("db").(*gorm.DB)
	return models.SaveRole(db, role)
}

func GetRolesByUserIDs(c *gin.Context, gameID int, userIDs []int) (models.Roles, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetRolesByUserIDs(db, gameID, userIDs)
}
