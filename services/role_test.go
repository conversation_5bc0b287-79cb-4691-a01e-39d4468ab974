package services

import (
	"testing"

	"zeus/models"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestRole(t *testing.T) {
	c := &gin.Context{}
	c.Set("db", &gorm.DB{})

	<PERSON>vey("GetRoleByRoleID", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.QueryRole, func(db *gorm.DB, args map[string]interface{}) (*models.Role, error) {
				return &models.Role{RoleId: "1111"}, nil
			})

			output, _ := GetRoleByRoleID(c, "1111")
			expected := "1111"
			So(output.RoleId, ShouldEqual, expected)
		})
	})

	Convey("GetRoleByRoleIDAndServerID", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.QueryRole, func(db *gorm.DB, args map[string]interface{}) (*models.Role, error) {
				return &models.Role{RoleId: "test_role_id"}, nil
			})

			output, _ := GetRoleByRoleIDAndServerID(c, 0, "test_role_id", "test_server_id")
			expected := "test_role_id"
			So(output.RoleId, ShouldEqual, expected)
		})
	})

	Convey("GetRoleByRoleName", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.QueryRoleByName, func(db *gorm.DB, roleName string) ([]models.Role, error) {
				return []models.Role{{RoleName: roleName}}, nil
			})

			output, _ := GetRoleByRoleName(c, "test_role_name")
			expected := "test_role_name"
			So(output[0].RoleName, ShouldEqual, expected)
		})
	})

	Convey("QueryAllRole", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetAllRole, func(db *gorm.DB, args map[string]interface{}) ([]models.Role, error) {
				return []models.Role{{RoleName: "test_role_name"}}, nil
			})

			output, _ := QueryAllRole(c, map[string]interface{}{})
			expected := "test_role_name"
			So(output[0].RoleName, ShouldEqual, expected)
		})
	})

	Convey("SaveRole", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.SaveRole, func(db *gorm.DB, role *models.Role) error {
				return nil
			})
			r := &models.Role{}
			output := SaveRole(c, r)
			So(output, ShouldEqual, nil)
		})
	})

	Convey("GetRolesByUserIDs", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetRolesByUserIDs, func(db *gorm.DB, _ int, _ []int) (models.Roles, error) {
				return []models.Role{{RoleName: "test_role_name"}}, nil
			})
			output, _ := GetRolesByUserIDs(c, 1, []int{1, 2, 3})
			So(output[0].RoleName, ShouldEqual, "test_role_name")
		})
	})
}
