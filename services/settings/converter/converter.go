package converter

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"zeus/common"
	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/redis"
	"zeus/services/settings/field"

	"github.com/cjrd/allocate"
)

func StructConverter(from, to any) error {
	data, err := json.Marshal(from)
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &to)
	return err
}

type Filter struct {
	Appid     string `json:"appid,omitempty"`
	ProjectId int    `json:"project_id,string,omitempty"`
	GameId    int    `json:"game_id,string,omitempty"`
}

func query(config field.BaseConfiger, filter Filter) bool {
	allocate.Zero(config)
	var hasEmpty bool
	db := common.GetInfoDB()
	result := make(map[string]interface{})
	kinds := make(map[string]reflect.Kind)
	keys := make([]string, 0)
	v := reflect.ValueOf(config).Elem()
	for j := 0; j < v.NumField(); j++ {
		f := v.Field(j)
		tag := strings.Split(v.Type().Field(j).Tag.Get("json"), ",")[0]
		if f.Kind() == reflect.Ptr {
			kinds[tag] = f.Elem().Kind()
		} else {
			kinds[tag] = f.Kind()
		}
		keys = append(keys, tag)
	}
	var ss []models.Setting
	query := db.Model(&models.Setting{}).Where("name in (?)", keys)
	queryArgs := make(map[string]string)
	StructConverter(filter, &queryArgs)

	if config.GetType() != "any" {
		queryArgs["type"] = config.GetType()
	}

	query.Where(queryArgs).Find(&ss)

	if len(keys) != len(ss) {
		hasEmpty = true
	}

	for _, s := range ss {
		switch kinds[s.Name] {
		case reflect.Bool:
			result[s.Name], _ = strconv.ParseBool(s.Value)
		case reflect.Float64:
			result[s.Name], _ = strconv.ParseFloat(s.Value, 64)
		case reflect.Int:
			result[s.Name], _ = strconv.ParseInt(s.Value, 10, 64)
		case reflect.Map:
			tmp := make(map[string]any)
			json.Unmarshal([]byte(s.Value), &tmp)
			result[s.Name] = tmp
		case reflect.Slice:
			tmp := make([]any, 0)
			json.Unmarshal([]byte(s.Value), &tmp)
			result[s.Name] = tmp
		default:
			result[s.Name] = s.Value
		}
	}
	dbByte, _ := json.Marshal(result)
	json.Unmarshal(dbByte, &config)
	return hasEmpty
}

func Query(config field.BaseConfiger, filter Filter) {
	cacheKey := fmt.Sprintf("settings_%d_%d_%s_%s", filter.ProjectId, filter.GameId, filter.Appid, config.GetType())

	configInfo := redis.Get(cacheKey)
	if configInfo != "" {
		json.Unmarshal([]byte(configInfo), &config)
	}

	hasEmpty := query(config, filter)

	if config.GetType() != "any" && !hasEmpty {
		dbByte, _ := json.Marshal(config)
		redis.Set(cacheKey, string(dbByte), time.Second*constants.GameInfoTimeout)
	}
}

func Save(config field.BaseConfiger, filter Filter) {
	db := common.GetInfoDB()
	configInfo := make(map[string]interface{})
	StructConverter(config, &configInfo)
	for k, v := range configInfo {
		new := make(map[string]interface{})

		new["appid"] = filter.Appid
		new["project_id"] = filter.ProjectId
		new["game_id"] = filter.GameId
		new["name"] = k
		new["type"] = config.GetType()

		var data string
		if v == nil {
			data = ""
		} else if reflect.TypeOf(v).Kind() == reflect.Map {
			dataByte, _ := json.Marshal(v)
			data = string(dataByte)
		} else if reflect.TypeOf(v).Kind() == reflect.Slice {
			dataByte, _ := json.Marshal(v)
			data = string(dataByte)
		} else {
			data = fmt.Sprint(v)
		}

		var ns models.Setting
		db.Where(new).First(&ns)

		if ns.ID != 0 {
			if ns.Value == data {
				continue
			}
			ns.Value = data
		} else {
			new["value"] = data
			newByte, _ := json.Marshal(new)
			json.Unmarshal(newByte, &ns)
		}
		db.Save(&ns)
	}
	cacheKey := fmt.Sprintf("settings_%d_%d_%s_%s", filter.ProjectId, filter.GameId, filter.Appid, config.GetType())
	redis.Del(cacheKey)
}

func QueryOnSave(config field.BaseConfiger, filter Filter, defaultObj any) {
	if query(config, filter) && defaultObj != nil {
		StructConverter(defaultObj, &config)
		Save(config, filter)
	}
}

func QueryByGameId(config field.BaseConfiger, gameId int) {
	filter := Filter{
		GameId: gameId,
	}
	Query(config, filter)
}

func QueryByProjectId(config field.BaseConfiger, projectId int) {
	filter := Filter{
		ProjectId: projectId,
	}
	Query(config, filter)
}

func QueryByAppid(config field.BaseConfiger, appid string) {
	filter := Filter{
		Appid: appid,
	}
	Query(config, filter)
}

func ConvertToConfig(c field.BaseConfiger) []field.ConfigField {
	v := reflect.ValueOf(c).Elem()
	configFields := make([]field.ConfigField, 0)
	for j := 0; j < v.NumField(); j++ {
		f := v.Field(j)
		r, _ := strconv.ParseBool(v.Type().Field(j).Tag.Get("required"))
		if v.Type().Field(j).Tag.Get("label") == "" {
			continue
		}

		var nameKey string
		if v.Type().Field(j).Tag.Get("name") != "" {
			nameKey = v.Type().Field(j).Tag.Get("name")
		} else {
			nameKey = strings.Split(v.Type().Field(j).Tag.Get("json"), ",")[0]
		}
		configItem := field.ConfigField{
			Name:        nameKey,
			Label:       v.Type().Field(j).Tag.Get("label"),
			Type:        v.Type().Field(j).Tag.Get("type"),
			Required:    r,
			Value:       f.Interface(),
			Placeholder: v.Type().Field(j).Tag.Get("placeholder"),
		}

		if v.Type().Field(j).Tag.Get("options") != "" {
			options := make([]field.Option, 0)
			Invoke(f, v.Type().Field(j).Tag.Get("options"), &options)
			configItem.Options = options
		}

		configFields = append(configFields, configItem)
	}

	return configFields
}

func Invoke(any reflect.Value, name string, args ...interface{}) {
	inputs := make([]reflect.Value, len(args))
	for i := range args {
		inputs[i] = reflect.ValueOf(args[i])
	}
	any.MethodByName(name).Call(inputs)
}
