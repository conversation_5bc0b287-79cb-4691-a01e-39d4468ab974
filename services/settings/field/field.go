package field

import "zeus/pkg/thirdlogin"

type BaseConfiger interface {
	GetLabel() string
	GetType() string
}

type PaymentConfiger interface {
	BaseConfiger
	GetId() int
	GetName() string
	GetPlatform() string
	GetOpenStatus() int
	AllowChecked() bool
}

type ThirdLoginConfiger interface {
	BaseConfiger
	GetName() string
	GetLabel() string
	GetOpenStatus() int
	GetAbbrName() string
	GetPlatform() string
	GetBindSwitchId() int
	NewClient() thirdlogin.ThirdLoginer
}

type MiniGameThirdLoginConfiger interface {
	BaseConfiger
	GetName() string
	GetLabel() string
	GetOpenStatus() int
	GetAbbrName() string
	GetPlatform() string
	GetBindSwitchId() int
	NewClient() thirdlogin.MiniGameThirdLoginer
}

type Option struct {
	Label string `json:"label"`
	Value string `json:"value"`
}
type ConfigField struct {
	Label       string   `json:"label"`
	Name        string   `json:"name"`
	Type        string   `json:"type"`
	Value       any      `json:"value"`
	Required    bool     `json:"required"`
	Placeholder string   `json:"placeholder,omitempty"`
	Options     []Option `json:"options,omitempty"`
}
