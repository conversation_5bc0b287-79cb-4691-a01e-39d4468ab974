package settings

import (
	_ "zeus/services/settings/other"
	_ "zeus/services/settings/payment"
	"zeus/services/settings/store"
	_ "zeus/services/settings/thirdlogin"
)

func init() {
	for _, v := range store.ThirdLoginSettings {
		store.Settings[v.GetType()] = v
		store.ThirdLoginSwitchMapping[v.GetBindSwitchId()] = v
	}

	for _, v := range store.MiniGameThirdLoginSettings {
		store.Settings[v.GetType()] = v
		store.MiniGameThirdLoginSwitchMapping[v.GetBindSwitchId()] = v
	}

	for _, v := range store.PaySettings {
		store.Settings[v.GetType()] = v
		store.PaymentMapping[v.GetId()] = v
	}

	for _, v := range store.OtherSettings {
		store.Settings[v.GetType()] = v
	}
}
