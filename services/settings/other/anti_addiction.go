package other

import "zeus/services/settings/store"

type AntiAddictionConfig struct {
	CheckStrategy           *int    `json:"check_strategy,omitempty" label:"校验策略" type:"checkbox" options:"1:阿里云;2:中宣部;3:模拟校验" required:"true"`
	PublicityAppId          *string `json:"publicity_app_id,omitempty" label:"中宣部AppId" type:"input" required:"true"`
	PublicitySecretKey      *string `json:"publicity_secret_key,omitempty" label:"中宣部SecretKey" type:"input" required:"true"`
	PublicityBizId          *string `json:"publicity_biz_id,omitempty" label:"中宣部BizId" type:"input" required:"true"`
	PublicityExpireDatetime *string `json:"publicity_expire_datetime,omitempty"`
}

func (anti AntiAddictionConfig) GetLabel() string {
	return "实名认证"
}

func (anti AntiAddictionConfig) GetType() string {
	return "AntiAddiction"
}

func init() {
	store.OtherSettings = append(store.OtherSettings, AntiAddictionConfig{})
}
