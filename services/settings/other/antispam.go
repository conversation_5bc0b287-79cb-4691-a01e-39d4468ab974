package other

import "zeus/services/settings/store"

type AntispamConfig struct {
	AntispamSecretID     *string `json:"antispam_secret_id,omitempty" label:"SecretId" type:"input" required:"true"`
	AntispamSecretKey    *string `json:"antispam_secret_key,omitempty" label:"SecretKey" type:"input" required:"true"`
	AntispamBusinessID   *string `json:"antispam_business_id,omitempty" label:"BusinessId" type:"input" required:"true"`
	AntispamType         *int    `json:"antispam_type,omitempty" label:"Type" type:"input" required:"true"`
	AntispamWeChatAppId  *string `json:"antispam_wechat_appid,omitempty" label:"WeChatAppId" type:"input" required:"true"`
	AntispamWeChatSecret *string `json:"antispam_wechat_secret,omitempty" label:"WeChatSecret" type:"input" required:"true"`
}

func (anti AntispamConfig) GetLabel() string {
	return "屏蔽字"
}

func (anti AntispamConfig) GetType() string {
	return "Antispam"
}

func init() {
	store.OtherSettings = append(store.OtherSettings, AntispamConfig{})
}
