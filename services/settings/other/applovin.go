package other

import "zeus/services/settings/store"

type ApplovinConfig struct {
	EventKey string `json:"event_key,omitempty" label:"ApplovinEventKey" type:"input" required:"true"`
}

func (anti ApplovinConfig) GetLabel() string {
	return "广告变现"
}

func (anti ApplovinConfig) GetType() string {
	return "Applovin"
}

func init() {
	store.OtherSettings = append(store.OtherSettings, ApplovinConfig{})
}
