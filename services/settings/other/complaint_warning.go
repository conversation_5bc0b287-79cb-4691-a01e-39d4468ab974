package other

import "zeus/services/settings/store"

type ComplaintWarningConfig struct {
	FreshchatUrl           *string `json:"freshchat_url,omitempty" label:"FreshchatUrl" type:"input" required:"true"`
	FreshchatToken         *string `json:"freshchat_token,omitempty" label:"FreshchatToken" type:"input" required:"true"`
	HourlyWarningSwitch    *bool   `json:"hourly_warning_switch,omitempty" label:"HourlyWarningSwitch" type:"input" required:"true"`
	HourlyWarningThreshold *int    `json:"hourly_warning_threshold,omitempty" label:"HourlyWarningThreshold" type:"input" required:"true"`
	HourlyDingTalkToken    *string `json:"hourly_dingtalk_token,omitempty" label:"HourlyDingTalkToken" type:"input" required:"true"`
	DailyWarningSwitch     *bool   `json:"daily_warning_switch,omitempty" label:"DailyWarningSwitch" type:"input" required:"true"`
	DailyWarningThreshold  *int    `json:"daily_warning_threshold,omitempty" label:"DailyWarningThreshold" type:"input" required:"true"`
	DailyDingTalkToken     *string `json:"daily_dingtalk_token,omitempty" label:"DailyDingTalkToken" type:"input" required:"true"`
	PhoneNumber            *string `json:"phone_number,omitempty" label:"PhoneNumber" type:"input" required:"true"`
}

func (complaint ComplaintWarningConfig) GetLabel() string {
	return "客诉预警"
}

func (complaint ComplaintWarningConfig) GetType() string {
	return "ComplaintWarning"
}

func init() {
	store.OtherSettings = append(store.OtherSettings, ComplaintWarningConfig{})
}
