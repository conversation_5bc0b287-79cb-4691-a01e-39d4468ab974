package other

import "zeus/services/settings/store"

type DataCollectionConfig struct {
	SamplingRate *float64 `json:"sampling_rate,omitempty" label:"采样率" type:"input" required:"true"`
}

func (anti DataCollectionConfig) GetLabel() string {
	return "数据打点"
}

func (anti DataCollectionConfig) GetType() string {
	return "DataCollection"
}

func init() {
	store.OtherSettings = append(store.OtherSettings, DataCollectionConfig{})
}
