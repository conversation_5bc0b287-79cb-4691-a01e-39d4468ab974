package other

import "zeus/services/settings/store"

type FreshdeskConfig struct {
	FreshdeskAppkey *string `json:"freshdesk_appkey,omitempty" label:"FreshdeskAppkey" type:"input" required:"true"`
	FreshdeskDomain *string `json:"freshdesk_domain,omitempty" label:"FreshdeskDomain" type:"input" required:"true"`
	FreshdeskAppid  *string `json:"freshdesk_appid,omitempty" label:"FreshdeskAppid" type:"input" required:"true"`
}

func (anti FreshdeskConfig) GetLabel() string {
	return "Freshdesk"
}

func (anti FreshdeskConfig) GetType() string {
	return "Freshdesk"
}

func init() {
	store.OtherSettings = append(store.OtherSettings, FreshdeskConfig{})
}
