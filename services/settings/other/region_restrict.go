package other

import (
	"zeus/services/settings/store"
)

type RegionRestrictConfig struct {
	IsEnabled   *bool   `json:"is_enabled,omitempty" label:"IsEnable" type:"input" required:"true"`
	RegionCodes *string `json:"region_codes,omitempty" label:"RegionCodes" type:"input" required:"true"`
	IpWhiteList *string `json:"ip_white_list,omitempty" label:"IpWhiteList" type:"input" required:"true"`
	IpBlackList *string `json:"ip_black_list,omitempty" label:"IpBlackList" type:"input" required:"false"`
	ISP         *string `json:"isp,omitempty" label:"ISP" type:"input" required:"false"`
}

func (anti RegionRestrictConfig) GetLabel() string {
	return "RegionRestrict"
}

func (anti RegionRestrictConfig) GetType() string {
	return "RegionRestrict"
}

func init() {
	store.OtherSettings = append(store.OtherSettings, RegionRestrictConfig{})
}
