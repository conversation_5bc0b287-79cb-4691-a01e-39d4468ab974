package other

import "zeus/services/settings/store"

type DialTestingConfig struct {
	DialTestingSamplingRate *float64 `json:"dial_testing_sampling_rate,omitempty"`
	DialTestingWhiteList    *string  `json:"dial_testing_white_list,omitempty"`
}

func (dial DialTestingConfig) GetLabel() string {
	return "拨测"
}

func (dial DialTestingConfig) GetType() string {
	return "DialTesting"
}

type LogCollectionConfig struct {
	LogCollectionSamplingRate *float64 `json:"log_collection_sampling_rate,omitempty"`
	LogCollectionWhiteList    *string  `json:"log_collection_white_list,omitempty"`
}

func (log LogCollectionConfig) GetLabel() string {
	return "日志收集"
}

func (log LogCollectionConfig) GetType() string {
	return "LogCollection"
}

func init() {
	store.OtherSettings = append(store.OtherSettings, DialTestingConfig{})
	store.OtherSettings = append(store.OtherSettings, LogCollectionConfig{})
}
