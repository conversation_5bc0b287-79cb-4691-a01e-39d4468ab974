package payment

import (
	"zeus/common/constants"
	"zeus/services/settings/store"
)

type ApplePay struct {
	OpenStatus    *int    `json:"open_status,omitempty"`
	AppleBundleId *string `json:"apple_bundle_id,omitempty" label:"AppleBundleId" type:"input" required:"true"`
	AppleISS      *string `json:"apple_iss,omitempty" label:"AppleISS" type:"input" required:"true"`
	AppleKeyID    *string `json:"apple_key_id,omitempty" label:"AppleKey" type:"input" required:"true"`
	AppleSecret   *string `json:"apple_secret,omitempty" label:"AppleSecret" type:"text-area" required:"true"`
	ApplePassword *string `json:"apple_password,omitempty" label:"ApplePassword" type:"input"`
}

func (c ApplePay) GetId() int {
	return constants.PayWayApple
}

func (c ApplePay) GetName() string {
	return constants.PayWayAppleString
}

func (c ApplePay) GetLabel() string {
	return "苹果支付"
}

func (c ApplePay) GetOpenStatus() int {
	if c.OpenStatus != nil {
		return *c.OpenStatus
	} else {
		return 0
	}
}

func (c ApplePay) GetType() string {
	return "PaymentApplePay"
}

func (s ApplePay) GetPlatform() string {
	return constants.PlatformIOS
}

func (c ApplePay) AllowChecked() bool {
	return false
}

func init() {
	store.PaySettings = append(store.PaySettings, ApplePay{})
}
