package payment

import (
	"zeus/common/constants"
	"zeus/services/settings/store"
)

type MockPay struct {
	OpenStatus *int `json:"open_status,omitempty"`
}

func (m MockPay) GetId() int {
	return constants.PayWayMock
}

func (m MockPay) GetName() string {
	return constants.PayWayMockString
}

func (m MockPay) GetLabel() string {
	return "模拟支付"
}

func (m MockPay) GetOpenStatus() int {
	if m.OpenStatus != nil {
		return *m.OpenStatus
	} else {
		return 0
	}
}

func (m MockPay) GetType() string {
	return "PaymentMockPay"
}

func (m MockPay) GetPlatform() string {
	return constants.PlatformAll
}

func (c MockPay) AllowChecked() bool {
	return false
}

func init() {
	store.PaySettings = append(store.PaySettings, MockPay{})
}
