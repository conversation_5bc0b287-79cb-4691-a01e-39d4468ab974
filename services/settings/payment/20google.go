package payment

import (
	"zeus/common/constants"
	"zeus/services/settings/store"
)

type GooglePay struct {
	OpenStatus           *int    `json:"open_status,omitempty"`
	GooglePayKey         *string `json:"google_pay_key,omitempty" label:"GooglePayKey" type:"text-area" required:"true"`
	GoogleClientId       *string `json:"google_client_id,omitempty" label:"GoogleClientId" type:"input" required:"true"`
	GoogleClientSecret   *string `json:"google_client_secret,omitempty" label:"GoogleClientSecret" type:"input" required:"true"`
	GoogleRedirectUri    *string `json:"google_redirect_uri,omitempty" label:"GoogleRedirectUri" type:"input" required:"true"`
	GoogleRefreshToken   *string `json:"google_refresh_token,omitempty" label:"GoogleRefreshToken" type:"text-area" required:"true"`
	GooglePayPackageName *string `json:"google_pay_package_name,omitempty" label:"GooglePlay包名" type:"input" required:"false"`
}

func (c GooglePay) GetId() int {
	return constants.PayWayGoogle
}

func (c GooglePay) GetName() string {
	return constants.PayWayGoogleString
}

func (c GooglePay) GetLabel() string {
	return "谷歌支付"
}

func (c GooglePay) GetOpenStatus() int {
	if c.OpenStatus != nil {
		return *c.OpenStatus
	} else {
		return 0
	}
}

func (c GooglePay) GetType() string {
	return "PaymentGooglePay"
}

func (s GooglePay) GetPlatform() string {
	return constants.PlatformAndroid
}

func (c GooglePay) AllowChecked() bool {
	return true
}

func init() {
	store.PaySettings = append(store.PaySettings, GooglePay{})
}
