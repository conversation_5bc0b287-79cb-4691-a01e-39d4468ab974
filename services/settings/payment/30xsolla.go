package payment

import (
	"zeus/common/constants"
	"zeus/services/settings/store"
)

type XsollaPay struct {
	OpenStatus      *int    `json:"open_status,omitempty"`
	XsollaProjectID *string `json:"xsolla_project_id,omitempty" label:"XsollaProjectID" type:"input" required:"true"`
	XsollaSecretKey *string `json:"xsolla_secret_key,omitempty" label:"XsollaSecretKey" type:"input" required:"true"`
	PayNotifyUrl    *string `json:"pay_notify_url,omitempty" name:"pay_notify_url" label:"支付回调地址" type:"input" required:"true"`
}

func (c XsollaPay) GetId() int {
	return constants.PayWayXsolla
}

func (c XsollaPay) GetName() string {
	return constants.PayWayXsollaString
}

func (c XsollaPay) GetLabel() string {
	return "Xsolla支付"
}

func (c XsollaPay) GetOpenStatus() int {
	if c.OpenStatus != nil {
		return *c.OpenStatus
	} else {
		return 0
	}
}

func (c XsollaPay) GetType() string {
	return "PaymentXsolla"
}

func (s XsollaPay) GetPlatform() string {
	return constants.PlatformAll
}

func (c XsollaPay) AllowChecked() bool {
	return false
}

func init() {
	store.PaySettings = append(store.PaySettings, XsollaPay{})
}
