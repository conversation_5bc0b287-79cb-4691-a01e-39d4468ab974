package payment

import (
	"database/sql/driver"
	"encoding/json"
	"strings"

	"zeus/common/constants"
	"zeus/services/settings/store"
)

type MyCardConfig struct {
	ServiceID   string `json:"service_id"`
	Secret      string `json:"secret"`
	SandBoxMode bool   `json:"sandbox_mode"`
}

type MyCardConfigs map[string]MyCardConfig

func (m MyCardConfigs) Value() (driver.Value, error) {
	if m == nil {
		return "", nil
	}
	d, err := json.Marshal(m)
	return string(d), err
}

func (m MyCardConfigs) String() string {
	if m == nil {
		return ""
	}
	d, _ := json.Marshal(m)
	return string(d)
}

func (m *MyCardConfigs) Scan(v interface{}) error {
	if len(v.([]byte)) == 0 {
		return nil
	}
	return json.Unmarshal(v.([]byte), m)
}

func (m MyCardConfigs) GetConfigByRegion(region string) *MyCardConfig {
	config, ok := m[strings.ToLower(region)]
	if ok {
		return &config
	}
	return nil
}

type MyCardPay struct {
	OpenStatus    *int           `json:"open_status,omitempty"`
	MyCardConfigs *MyCardConfigs `json:"mycard_configs,omitempty" label:"MyCardConfigs" type:"text-area-json" required:"true" placeholder:""`
}

func (c MyCardPay) GetId() int {
	return constants.PayWayMyCard
}

func (c MyCardPay) GetName() string {
	return constants.PayWayMyCardString
}

func (c MyCardPay) GetLabel() string {
	return "MyCard支付"
}

func (c MyCardPay) GetOpenStatus() int {
	if c.OpenStatus != nil {
		return *c.OpenStatus
	} else {
		return 0
	}
}

func (c MyCardPay) GetType() string {
	return "PaymentMyCard"
}

func (s MyCardPay) GetPlatform() string {
	return constants.PlatformAll
}

func (c MyCardPay) AllowChecked() bool {
	return false
}

func init() {
	store.PaySettings = append(store.PaySettings, MyCardPay{})
}
