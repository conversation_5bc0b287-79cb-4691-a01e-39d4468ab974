package payment

import (
	"database/sql/driver"
	"encoding/json"
	"strings"

	"zeus/common/constants"
	"zeus/services/settings/store"
)

type GashConfig struct {
	ServiceID   string `json:"service_id"`
	Region      string `json:"region"`
	Key         string `json:"key"`
	Vi          string `json:"vi"`
	Password    string `json:"password"`
	SandBoxMode bool   `json:"sand_box_mode"`
}

type GashConfigs map[string]GashConfig

func (g GashConfigs) Value() (driver.Value, error) {
	if g == nil {
		return "", nil
	}
	d, err := json.Marshal(g)
	return string(d), err
}

func (g GashConfigs) String() string {
	if g == nil {
		return ""
	}
	d, _ := json.Marshal(g)
	return string(d)
}

func (g *GashConfigs) Scan(v interface{}) error {
	if len(v.([]byte)) == 0 {
		return nil
	}
	return json.Unmarshal(v.([]byte), g)
}

func (g GashConfigs) GetConfigByRegion(region string) *GashConfig {
	config, ok := g[strings.ToLower(region)]
	if ok {
		return &config
	}
	return nil
}

type GashPay struct {
	OpenStatus  *int         `json:"open_status,omitempty"`
	GashConfigs *GashConfigs `json:"gash_configs,omitempty" label:"GashConfigs" type:"text-area-json" required:"true"`
}

func (c GashPay) GetId() int {
	return constants.PayWayGash
}

func (c GashPay) GetName() string {
	return constants.PayWayGashString
}

func (c GashPay) GetLabel() string {
	return "Gash支付"
}

func (c GashPay) GetOpenStatus() int {
	if c.OpenStatus != nil {
		return *c.OpenStatus
	} else {
		return 0
	}
}

func (c GashPay) GetType() string {
	return "PaymentGash"
}

func (s GashPay) GetPlatform() string {
	return constants.PlatformAll
}

func (c GashPay) AllowChecked() bool {
	return false
}

func init() {
	store.PaySettings = append(store.PaySettings, GashPay{})
}
