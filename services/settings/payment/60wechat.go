package payment

import (
	"zeus/common/constants"
	"zeus/services/settings/store"
)

type WeChatPay struct {
	OpenStatus         *int    `json:"open_status,omitempty"`
	WechatAppid        *string `json:"wechat_appid,omitempty" label:"WechatAppid" type:"input" required:"true" binding:"required"`
	WechatApiClientKey *string `json:"wechat_api_client_key,omitempty" label:"WechatApiClientKey" type:"text-area" required:"true"`
	WechatMchID        *string `json:"wechat_mch_id,omitempty" label:"WechatMchID" type:"input" required:"true"`
	WechatMchSN        *string `json:"wechat_mch_sn,omitempty" label:"WechatMchSN" type:"input" required:"true"`
	WechatMchAPIKey    *string `json:"wechat_mch_api_key,omitempty" label:"WechatMchAPIKey" type:"input" required:"true"`
}

func (c WeChatPay) GetId() int {
	return constants.PayWayWeChat
}

func (c WeChatPay) GetName() string {
	return constants.PayWayWeChatString
}

func (c WeChatPay) GetLabel() string {
	return "微信支付"
}

func (c WeChatPay) GetOpenStatus() int {
	if c.OpenStatus != nil {
		return *c.OpenStatus
	} else {
		return 0
	}
}

func (c WeChatPay) GetType() string {
	return "PaymentWeChatPay"
}

func (s WeChatPay) GetPlatform() string {
	return constants.PlatformAll
}

func (c WeChatPay) AllowChecked() bool {
	return false
}

func init() {
	store.PaySettings = append(store.PaySettings, WeChatPay{})
}
