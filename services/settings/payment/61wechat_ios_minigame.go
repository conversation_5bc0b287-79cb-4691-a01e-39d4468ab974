package payment

import (
	"zeus/common/constants"
	"zeus/services/settings/store"
)

type MiniGameWeChatPay struct {
	OpenStatus                  *int    `json:"open_status,omitempty"`
	Appid                       *string `json:"appid,omitempty" label:"WechatAppid" type:"input" required:"true" binding:"required"`
	ApiClientKey                *string `json:"api_client_key,omitempty" label:"WechatApiClientKey" type:"text-area" required:"true"`
	MchID                       *string `json:"mch_id,omitempty" label:"WechatMchID" type:"input" required:"true"`
	MchSN                       *string `json:"mch_sn,omitempty" label:"WechatMchSN" type:"input" required:"true"`
	MchAPIKey                   *string `json:"mch_api_key,omitempty" label:"WechatMchAPIKey" type:"input" required:"true"`
	VirtualPaymentOfferId       *string `json:"virtual_payment_offer_id,omitempty" label:"虚拟支付OfferId" type:"input" required:"true"`
	VirtualPaymentAppKey        *string `json:"virtual_payment_app_key,omitempty" label:"虚拟支付AppKey" type:"input" required:"true"`
	VirtualPaymentSandboxAppKey *string `json:"virtual_payment_sandbox_app_key,omitempty" label:"虚拟支付沙盒AppKey" type:"input" required:"true"`
}

func (c MiniGameWeChatPay) GetId() int {
	return constants.PayWayMiniGameWeChat
}

func (c MiniGameWeChatPay) GetName() string {
	return constants.PayWayMiniGameWeChatString
}

func (c MiniGameWeChatPay) GetLabel() string {
	return "微信小游戏支付"
}

func (c MiniGameWeChatPay) GetOpenStatus() int {
	if c.OpenStatus != nil {
		return *c.OpenStatus
	} else {
		return 0
	}
}

func (c MiniGameWeChatPay) GetType() string {
	return "PaymentMiniGameWeChatPay"
}

func (c MiniGameWeChatPay) GetPlatform() string {
	return constants.PlatformAll
}

func (c MiniGameWeChatPay) AllowChecked() bool {
	return false
}

func init() {
	store.PaySettings = append(store.PaySettings, MiniGameWeChatPay{})
}
