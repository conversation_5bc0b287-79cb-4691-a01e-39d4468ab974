package payment

import (
	"zeus/common/constants"
	"zeus/services/settings/store"
)

type AliPay struct {
	OpenStatus    *int    `json:"open_status,omitempty"`
	AliPrivate<PERSON><PERSON> *string `json:"ali_private_key,omitempty" label:"AliPrivateKey" type:"input" required:"true"`
	AliAppId      *string `json:"ali_app_id,omitempty" label:"AliAppId" type:"input" required:"true"`
	AliPubKey     *string `json:"ali_pub_key,omitempty" label:"AliPubKey" type:"input" required:"true"`
}

func (c AliPay) GetId() int {
	return constants.PayWayAli
}

func (c AliPay) GetName() string {
	return constants.PayWayAliString
}

func (c AliPay) GetLabel() string {
	return "支付宝支付"
}

func (c AliPay) GetOpenStatus() int {
	if c.OpenStatus != nil {
		return *c.OpenStatus
	} else {
		return 0
	}
}

func (c AliPay) GetType() string {
	return "PaymentAliPay"
}

func (s AliPay) GetPlatform() string {
	return constants.PlatformAll
}

func (c AliPay) AllowChecked() bool {
	return false
}

func init() {
	store.PaySettings = append(store.PaySettings, AliPay{})
}
