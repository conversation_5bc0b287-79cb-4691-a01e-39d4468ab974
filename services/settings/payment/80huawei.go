package payment

import (
	"zeus/common/constants"
	"zeus/services/settings/store"
)

type HuaweiPay struct {
	OpenStatus         *int    `json:"open_status,omitempty"`
	HuaweiPackageName  *string `json:"huawei_package_name,omitempty" label:"HuaweiPayPackage" type:"input" required:"true"`
	HuaweiClientId     *string `json:"huawei_client_id,omitempty" label:"HuaweiClientId" type:"input" required:"true"`
	HuaweiClientSecret *string `json:"huawei_client_secret,omitempty" label:"HuaweiClientSecret" type:"input" required:"true"`
	HuaweiPublicKey    *string `json:"huawei_public_key,omitempty" label:"HuaweiPublicKey" type:"text-area" required:"true"`
}

func (h HuaweiPay) GetId() int {
	return constants.PayWayHuawei
}

func (h HuaweiPay) GetName() string {
	return constants.PayWayHuaweiString
}

func (h HuaweiPay) GetLabel() string {
	return "华为支付"
}

func (h HuaweiPay) GetOpenStatus() int {
	if h.OpenStatus != nil {
		return *h.OpenStatus
	} else {
		return 0
	}
}

func (h HuaweiPay) GetType() string {
	return "PaymentHuaweiPay"
}

func (s HuaweiPay) GetPlatform() string {
	return constants.PlatformAndroid
}

func (h HuaweiPay) AllowChecked() bool {
	return false
}

func init() {
	store.PaySettings = append(store.PaySettings, HuaweiPay{})
}
