package payment

import (
	"zeus/common/constants"
	"zeus/services/settings/store"
)

type MiniGameDouyinPay struct {
	OpenStatus    *int    `json:"open_status,omitempty"`
	DouyinAppId   *string `json:"DouyinAppId,omitempty" label:"DouyinAppId" type:"input" required:"true" binding:"required"`
	DouyinSecret  *string `json:"DouyinSecret,omitempty" label:"DouyinSecret" type:"input" required:"true" binding:"required"`
	DouyinSignKey *string `json:"DouyinSignKey,omitempty" label:"DouyinSignKey" type:"input" required:"true" binding:"required"`
}

func (h MiniGameDouyinPay) GetId() int {
	return constants.PayWayMiniGameDouyin
}

func (h MiniGameDouyinPay) GetName() string {
	return constants.PayWayDouyinString
}

func (h MiniGameDouyinPay) GetLabel() string {
	return "抖音小游戏支付"
}

func (h MiniGameDouyinPay) GetOpenStatus() int {
	if h.OpenStatus != nil {
		return *h.OpenStatus
	} else {
		return 0
	}
}

func (h MiniGameDouyinPay) GetType() string {
	return "PaymentDouyinPay"
}

func (s MiniGameDouyinPay) GetPlatform() string {
	return constants.PlatformAndroid
}

func (h MiniGameDouyinPay) AllowChecked() bool {
	return false
}

func init() {
	store.PaySettings = append(store.PaySettings, MiniGameDouyinPay{})
}
