package payment

import "zeus/services/settings/store"

type Payment struct {
	DingTalkToken  *string `json:"ding_talk_token,omitempty" name:"ding_talk_token" label:"钉钉机器人Token" type:"text" required:"true"`
	IsRechargeOpen *int    `json:"is_recharge_open,omitempty" name:"is_recharge_open" label:"是否开启支付" type:"checkbox" required:"true"`
	PayNotifyKey   *string `json:"pay_notify_key,omitempty" name:"pay_notify" label:"" type:"text" required:"true"`
}

func (p Payment) GetLabel() string {
	return "支付基础信息"
}

func (p Payment) GetType() string {
	return "Payment"
}

func init() {
	store.Settings[Payment{}.GetType()] = Payment{}
}
