package store

import "zeus/services/settings/field"

var Settings = make(map[string]field.BaseConfiger)

var OtherSettings = make([]field.BaseConfiger, 0)

var (
	ThirdLoginSettings      = make([]field.ThirdLoginConfiger, 0)
	ThirdLoginSwitchMapping = make(map[int]field.ThirdLoginConfiger)
)

var (
	MiniGameThirdLoginSettings      = make([]field.MiniGameThirdLoginConfiger, 0)
	MiniGameThirdLoginSwitchMapping = make(map[int]field.MiniGameThirdLoginConfiger)
)

var (
	PaySettings    = make([]field.PaymentConfiger, 0)
	PaymentMapping = make(map[int]field.PaymentConfiger)
)
