package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type ThirdLoginApple struct {
	OpenStatus    *int    `json:"open_status,omitempty"`
	AppleBundleId *string `json:"apple_bundle_id,omitempty" label:"AppleBundleId" type:"input" required:"true"`
	AppleISS      *string `json:"apple_iss,omitempty" label:"AppleISS" type:"input" required:"true"`
	AppleKeyID    *string `json:"apple_key_id,omitempty" label:"AppleKey" type:"input" required:"true"`
	AppleSecret   *string `json:"apple_secret,omitempty" label:"AppleSecret" type:"text-area" required:"true"`
}

func (s ThirdLoginApple) GetName() string {
	return "ThirdLoginApple"
}

func (s ThirdLoginApple) GetLabel() string {
	return "Apple登录"
}

func (s ThirdLoginApple) GetType() string {
	return "ThirdLoginApple"
}

func (s ThirdLoginApple) GetBindSwitchId() int {
	return constants.AppleID
}

func (s ThirdLoginApple) GetBindSwitchName() string {
	return "is_ai_login"
}

func (s ThirdLoginApple) GetBindName() string {
	return "is_ai_bind"
}

func (s ThirdLoginApple) GetAbbrName() string {
	return "ai"
}

func (s ThirdLoginApple) GetPlatform() string {
	return constants.PlatformIOS
}

func (s ThirdLoginApple) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s ThirdLoginApple) NewClient() thirdlogin.ThirdLoginer {
	return thirdlogin.Apple{}
}

func init() {
	store.ThirdLoginSettings = append(store.ThirdLoginSettings, ThirdLoginApple{})
}
