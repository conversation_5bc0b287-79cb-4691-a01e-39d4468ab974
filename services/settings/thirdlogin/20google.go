package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type ThirdLoginGoogle struct {
	OpenStatus     *int    `json:"open_status,omitempty"`
	GoogleClientId *string `json:"google_client_id,omitempty" label:"Google ID" type:"input" required:"true"`
}

func (s ThirdLoginGoogle) GetName() string {
	return "ThirdLoginGoogle"
}

func (s ThirdLoginGoogle) GetLabel() string {
	return "Google登录"
}

func (s ThirdLoginGoogle) GetType() string {
	return "ThirdLoginGoogle"
}

func (s ThirdLoginGoogle) GetBindSwitchId() int {
	return constants.GoogleID
}

func (s ThirdLoginGoogle) GetAbbrName() string {
	return "gi"
}

func (s ThirdLoginGoogle) GetPlatform() string {
	return constants.PlatformAndroid
}

func (s ThirdLoginGoogle) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s ThirdLoginGoogle) NewClient() thirdlogin.ThirdLoginer {
	return thirdlogin.Google{}
}

func init() {
	store.ThirdLoginSettings = append(store.ThirdLoginSettings, ThirdLoginGoogle{})
}
