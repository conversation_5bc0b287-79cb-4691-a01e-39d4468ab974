package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type ThirdLoginFacebook struct {
	OpenStatus    *int    `json:"open_status,omitempty"`
	FbAppid       *string `json:"fb_appid,omitempty" label:"Facebook App ID" type:"input" required:"true"`
	FbSecret      *string `json:"fb_secret,omitempty" label:"Facebook Secret" type:"input" required:"true"`
	FbClientToken *string `json:"fb_client_token,omitempty" label:"Facebook Client Token" type:"input" required:"true"`
}

func (s ThirdLoginFacebook) GetName() string {
	return "ThirdLoginFacebook"
}

func (s ThirdLoginFacebook) GetLabel() string {
	return "Facebook登录"
}

func (s ThirdLoginFacebook) GetType() string {
	return "ThirdLoginFacebook"
}

func (s ThirdLoginFacebook) GetBindSwitchId() int {
	return constants.Facebook
}

func (s ThirdLoginFacebook) GetAbbrName() string {
	return "fb"
}

func (s ThirdLoginFacebook) GetPlatform() string {
	return constants.PlatformAll
}

func (s ThirdLoginFacebook) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s ThirdLoginFacebook) NewClient() thirdlogin.ThirdLoginer {
	return thirdlogin.FaceBook{
		AppID:  *s.FbAppid,
		Secret: *s.FbSecret,
	}
}

func init() {
	store.ThirdLoginSettings = append(store.ThirdLoginSettings, ThirdLoginFacebook{})
}
