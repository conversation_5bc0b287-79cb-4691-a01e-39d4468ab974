package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type ThirdLoginTwitter struct {
	OpenStatus *int    `json:"open_status,omitempty"`
	TwKey      *string `json:"twitter_key,omitempty" label:"Twitter Key" type:"input" required:"true"`
	TwSecret   *string `json:"twitter_secret,omitempty" label:"Twitter Secret" type:"input" required:"true"`
}

func (s ThirdLoginTwitter) GetName() string {
	return "ThirdLoginTwitter"
}

func (s ThirdLoginTwitter) GetLabel() string {
	return "Twitter登录"
}

func (s ThirdLoginTwitter) GetType() string {
	return "ThirdLoginTwitter"
}

func (s ThirdLoginTwitter) GetBindSwitchId() int {
	return constants.Twitter
}

func (s ThirdLoginTwitter) GetAbbrName() string {
	return "tw"
}

func (s ThirdLoginTwitter) GetPlatform() string {
	return constants.PlatformAll
}

func (s ThirdLoginTwitter) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s ThirdLoginTwitter) NewClient() thirdlogin.ThirdLoginer {
	return thirdlogin.Twitter{Key: *s.TwKey, Secret: *s.TwSecret}
}

func init() {
	store.ThirdLoginSettings = append(store.ThirdLoginSettings, ThirdLoginTwitter{})
}
