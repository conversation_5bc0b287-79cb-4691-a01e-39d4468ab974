package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type ThirdLoginLine struct {
	OpenStatus *int    `json:"open_status,omitempty"`
	LineAppId  *string `json:"line_appid,omitempty" label:"Line App ID" type:"input" required:"true"`
}

func (s ThirdLoginLine) GetName() string {
	return "ThirdLoginLine"
}

func (s ThirdLoginLine) GetLabel() string {
	return "Line登录"
}

func (s ThirdLoginLine) GetType() string {
	return "ThirdLoginLine"
}

func (s ThirdLoginLine) GetBindSwitchId() int {
	return constants.Line
}

func (s ThirdLoginLine) GetAbbrName() string {
	return "line"
}

func (s ThirdLoginLine) GetPlatform() string {
	return constants.PlatformAll
}

func (s ThirdLoginLine) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s ThirdLoginLine) NewClient() thirdlogin.ThirdLoginer {
	return thirdlogin.Line{}
}

func init() {
	store.ThirdLoginSettings = append(store.ThirdLoginSettings, ThirdLoginLine{})
}
