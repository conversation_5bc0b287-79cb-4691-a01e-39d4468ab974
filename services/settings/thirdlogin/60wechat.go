package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type ThirdLoginWeChat struct {
	OpenStatus   *int    `json:"open_status,omitempty"`
	WechatAppid  *string `json:"wechat_appid,omitempty" label:"WechatAppid" type:"input" required:"true"`
	WechatSecret *string `json:"wechat_secret,omitempty" label:"WechatSecret" type:"input" required:"true"`
}

func (s ThirdLoginWeChat) GetName() string {
	return "ThirdLoginWeChat"
}

func (s ThirdLoginWeChat) GetLabel() string {
	return "微信登录"
}

func (s ThirdLoginWeChat) GetType() string {
	return "ThirdLoginWeChat"
}

func (s ThirdLoginWeChat) GetBindSwitchId() int {
	return constants.Wechat
}

func (s ThirdLoginWeChat) GetAbbrName() string {
	return "wechat"
}

func (s ThirdLoginWeChat) GetPlatform() string {
	return constants.PlatformAll
}

func (s ThirdLoginWeChat) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s ThirdLoginWeChat) NewClient() thirdlogin.ThirdLoginer {
	return thirdlogin.Wechat{
		AppId:  *s.WechatAppid,
		Secret: *s.WechatSecret,
	}
}

func init() {
	store.ThirdLoginSettings = append(store.ThirdLoginSettings, ThirdLoginWeChat{})
}
