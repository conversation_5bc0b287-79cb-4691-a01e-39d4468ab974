package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type ThirdLoginQQ struct {
	OpenStatus *int    `json:"open_status,omitempty"`
	QQAppId    *string `json:"qq_appid,omitempty" label:"QQAppId" type:"input" required:"true"`
}

func (s ThirdLoginQQ) GetName() string {
	return "ThirdLoginQQ"
}

func (s ThirdLoginQQ) GetLabel() string {
	return "QQ登录"
}

func (s ThirdLoginQQ) GetType() string {
	return "ThirdLoginQQ"
}

func (s ThirdLoginQQ) GetBindSwitchId() int {
	return constants.QQ
}

func (s ThirdLoginQQ) GetAbbrName() string {
	return "qq"
}

func (s ThirdLoginQQ) GetPlatform() string {
	return constants.PlatformAll
}

func (s ThirdLoginQQ) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s ThirdLoginQQ) NewClient() thirdlogin.ThirdLoginer {
	return thirdlogin.QQ{}
}

func init() {
	store.ThirdLoginSettings = append(store.ThirdLoginSettings, ThirdLoginQQ{})
}
