package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type ThirdLoginWeChatMiniGame struct {
	OpenStatus   *int    `json:"open_status,omitempty"`
	WechatAppid  *string `json:"wechat_appid,omitempty" label:"WechatAppid" type:"input" required:"true"`
	WechatSecret *string `json:"wechat_secret,omitempty" label:"WechatSecret" type:"input" required:"true"`
}

func (s ThirdLoginWeChatMiniGame) GetName() string {
	return "ThirdLoginWeChatMiniGame"
}

func (s ThirdLoginWeChatMiniGame) GetLabel() string {
	return "微信小游戏登录"
}

func (s ThirdLoginWeChatMiniGame) GetType() string {
	return "ThirdLoginWeChatMiniGame"
}

func (s ThirdLoginWeChatMiniGame) GetBindSwitchId() int {
	return constants.Wechat
}

func (s ThirdLoginWeChatMiniGame) GetAbbrName() string {
	return "wechatminigame"
}

func (s ThirdLoginWeChatMiniGame) GetPlatform() string {
	return constants.PlatformAll
}

func (s ThirdLoginWeChatMiniGame) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s ThirdLoginWeChatMiniGame) NewClient() thirdlogin.MiniGameThirdLoginer {
	return thirdlogin.Wechat{
		AppId:  *s.WechatAppid,
		Secret: *s.WechatSecret,
	}
}

func init() {
	store.MiniGameThirdLoginSettings = append(store.MiniGameThirdLoginSettings, ThirdLoginWeChatMiniGame{})
}
