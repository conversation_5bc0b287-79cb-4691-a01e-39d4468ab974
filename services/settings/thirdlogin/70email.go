package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type Email struct {
	OpenStatus *int `json:"open_status,omitempty"`
}

func (s Email) GetName() string {
	return "ThirdLoginEmail"
}

func (s Email) GetLabel() string {
	return "邮箱登录"
}

func (s Email) GetType() string {
	return "ThirdLoginEmail"
}

func (s Email) GetBindSwitchId() int {
	return constants.Email
}

func (s Email) GetAbbrName() string {
	return "email"
}

func (s Email) GetPlatform() string {
	return constants.PlatformAll
}

func (s Email) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s Email) NewClient() thirdlogin.ThirdLoginer {
	return thirdlogin.Email{}
}

func init() {
	store.ThirdLoginSettings = append(store.ThirdLoginSettings, Email{})
}
