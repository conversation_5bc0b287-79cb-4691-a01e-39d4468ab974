package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type NumberVerify struct {
	OpenStatus             *int    `json:"open_status,omitempty"`
	NumberVerifySecretID   *string `json:"number_verify_secret_id,omitempty" label:"SecretId" type:"input" required:"true"`
	NumberVerifySecretKey  *string `json:"number_verif_secret_key,omitempty" label:"SecretKey" type:"input" required:"true"`
	NumberVerifyBusinessID *string `json:"number_verify_business_id,omitempty" label:"BusinessId" type:"input" required:"true"`
}

func (s NumberVerify) GetName() string {
	return "ThirdLoginNumberVerify"
}

func (s NumberVerify) GetLabel() string {
	return "号码认证"
}

func (s NumberVerify) GetType() string {
	return "ThirdLoginNumberVerify"
}

func (s NumberVerify) GetBindSwitchId() int {
	return constants.Mobile
}

func (s NumberVerify) GetAbbrName() string {
	return "numberverify"
}

func (s NumberVerify) GetPlatform() string {
	return constants.PlatformAll
}

func (s NumberVerify) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s NumberVerify) NewClient() thirdlogin.ThirdLoginer {
	return thirdlogin.NumberVerify{}
}

func init() {
	store.ThirdLoginSettings = append(store.ThirdLoginSettings, NumberVerify{})
}
