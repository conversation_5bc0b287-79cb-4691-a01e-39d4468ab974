package thirdlogin

import (
	"encoding/json"

	"zeus/common/conf"
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/field"
	"zeus/services/settings/store"
)

type AreaCodes []string

func (a AreaCodes) GetOptions(options *[]field.Option) {
	temp := make([]field.Option, 0)
	for _, area := range conf.Conf.SMS.Areas {
		temp = append(temp, field.Option{Label: area.AreaName, Value: area.AreaCode})
	}
	data, _ := json.Marshal(temp)
	json.Unmarshal(data, options)
}

type Mobile struct {
	OpenStatus *int       `json:"open_status,omitempty"`
	AreaCodes  *AreaCodes `json:"area_codes,omitempty" label:"支持区域" type:"multiple-select" options:"GetOptions" required:"true"`
}

func (s Mobile) GetName() string {
	return "ThirdLoginMobile"
}

func (s Mobile) GetLabel() string {
	return "短信登录"
}

func (s Mobile) GetType() string {
	return "ThirdLoginMobile"
}

func (s Mobile) GetBindSwitchId() int {
	return constants.Mobile
}

func (s Mobile) GetAbbrName() string {
	return "mobile"
}

func (s Mobile) GetPlatform() string {
	return constants.PlatformAll
}

func (s Mobile) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s Mobile) NewClient() thirdlogin.ThirdLoginer {
	return thirdlogin.Mobile{}
}

func init() {
	store.ThirdLoginSettings = append(store.ThirdLoginSettings, Mobile{})
}
