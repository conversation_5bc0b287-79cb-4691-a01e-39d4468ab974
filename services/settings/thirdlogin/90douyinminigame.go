package thirdlogin

import (
	"zeus/common/constants"
	"zeus/pkg/thirdlogin"
	"zeus/services/settings/store"
)

type ThirdLoginDouyinMinigame struct {
	OpenStatus   *int    `json:"open_status,omitempty"`
	DouyinAppid  *string `json:"douyin_appid,omitempty" label:"DouyinAppid" type:"input" required:"true"`
	DouyinSecret *string `json:"douyin_secret,omitempty" label:"DouyinSecret" type:"input" required:"true"`
}

func (s ThirdLoginDouyinMinigame) GetName() string {
	return "ThirdLoginDouyinMinigame"
}

func (s ThirdLoginDouyinMinigame) GetLabel() string {
	return "抖音小游戏登录"
}

func (s ThirdLoginDouyinMinigame) GetType() string {
	return "ThirdLoginDouyinMinigame"
}

func (s ThirdLoginDouyinMinigame) GetBindSwitchId() int {
	return constants.Douyin
}

func (s ThirdLoginDouyinMinigame) GetAbbrName() string {
	return "douyinminigame"
}

func (s ThirdLoginDouyinMinigame) GetPlatform() string {
	return constants.PlatformAll
}

func (s ThirdLoginDouyinMinigame) GetOpenStatus() int {
	if s.OpenStatus == nil {
		return 0
	} else {
		return *s.OpenStatus
	}
}

func (s ThirdLoginDouyinMinigame) NewClient() thirdlogin.MiniGameThirdLoginer {
	return thirdlogin.Douyin{
		AppId:  *s.DouyinAppid,
		Secret: *s.DouyinSecret,
	}
}

func init() {
	store.MiniGameThirdLoginSettings = append(store.MiniGameThirdLoginSettings, ThirdLoginDouyinMinigame{})
}
