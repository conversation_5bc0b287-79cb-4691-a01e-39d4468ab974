package thirdlogin

import "zeus/services/settings/store"

type ThirdLogin struct {
	BoundMaxCount        *int    `json:"bound_max_count"`
	GameServerAddr       *string `json:"game_server_addr"`
	CancelGameServerAddr *string `json:"cancel_game_server_addr"`
}

func (a ThirdLogin) GetLabel() string {
	return "登录基础信息"
}

func (a ThirdLogin) GetType() string {
	return "ThirdLogin"
}

func init() {
	store.Settings[ThirdLogin{}.GetType()] = ThirdLogin{}
}
