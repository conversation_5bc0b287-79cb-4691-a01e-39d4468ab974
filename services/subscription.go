package services

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"zeus/common/constants"
	"zeus/models"
	zeusHttp "zeus/pkg/http"
	"zeus/schema"

	"zeus/common/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func CreateSubscription(c *gin.Context, subscription *models.Subscription) error {
	log.Debug(c.Request.Context(), "开始创建订阅信息", "data", subscription)
	db := c.MustGet("db").(*gorm.DB)
	err := models.CreateSubscription(db, subscription)
	if err != nil {
		log.Error(c.Request.Context(), "开始创建订阅失败", "error", err.Error())
	}
	return err
}

func GetSubscriptionByExtendAndPayStatus(c *gin.Context, extend, payStatus interface{}) (*models.Subscription, error) {
	db := c.MustGet("db").(*gorm.DB)
	filter := map[string]interface{}{
		"extend":     extend,
		"pay_status": payStatus,
	}
	return models.GetOneSubscription(db, filter)
}

func QuerySubscription(c *gin.Context, filter map[string]interface{}) (*models.Subscription, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetOneSubscription(db, filter)
}

func SaveSubscription(c *gin.Context, order *models.Subscription) error {
	log.Debug(c.Request.Context(), "开始保存订阅信息", "data", order)
	db := c.MustGet("db").(*gorm.DB)
	err := models.SaveSubscription(db, order)
	if err != nil {
		log.Error(c.Request.Context(), "保存订阅信息失败", "error", err.Error())
	}

	return err
}

func EditNewSubscription(c *gin.Context, order *models.Subscription, data schema.PurchaseData, info map[string]interface{}, orderForm schema.GoogleSubscriptionVerify) error {
	dataJson, _ := json.Marshal(data)
	infoJson, _ := json.Marshal(info)
	payAmount, _ := strconv.ParseFloat(orderForm.PayAmount, 64)

	order.OriginReceipt = string(dataJson)
	order.OriginReceiptInfo = string(infoJson)
	order.PayProductId = data.ProductID
	order.PayTime = time.Now()
	order.PayStatus = constants.PaymentSuccess
	order.PayCurrency = orderForm.PayCurrency
	order.PayAmount = payAmount
	order.OriginalTransactionId = data.PurchaseToken
	order.ProductId = data.ProductID
	order.TransactionId = data.PurchaseToken
	order.PayId = data.OrderID
	order.LatestReceipt = string(dataJson)
	order.LatestReceiptInfo = string(infoJson)
	expiryTimeMillis, _ := strconv.Atoi(info["expiryTimeMillis"].(string))
	order.ExpiresDate = expiryTimeMillis / 1000
	order.NotifyStatus = constants.SubscriptionOrderNormal

	return SaveSubscription(c, order)
}

func GetSubscriptionStatus(data map[string]interface{}) int {
	if data["cancellation_date_pst"] != "" {
		return constants.SubscriptionOrderCancel
	}
	if data["expires_date"].(int64) <= time.Now().Unix()*1000 {
		return constants.SubscriptionOrderExpire
	}
	return constants.SubscriptionOrderNormal
}

func GetSubscriptionByPage(c *gin.Context, filter map[string]interface{}, pageSize int, pageNum int, startTime string, endTime string) ([]models.Subscription, int64, error) {
	db := c.MustGet("db").(*gorm.DB)

	orders, total, err := models.GetSubscriptionByPage(db, pageSize, pageNum, filter, startTime, endTime)
	return orders, total, err
}

func VerifySubToAppstore(ctx context.Context, receipt string, isSandbox bool, applePassword string) *http.Response {
	var url string
	if isSandbox {
		url = constants.AppStoreUrlSandbox
	} else {
		url = constants.AppStoreUrlOnline
	}
	params := map[string]interface{}{
		"receipt-data":             receipt,
		"password":                 applePassword,
		"exclude-old-transactions": false,
	}
	bytesData, _ := json.Marshal(params)
	headers := map[string]string{"Content-Type": "application/json"}
	resp, err := zeusHttp.PostWithTracer(ctx, url, headers, bytesData)
	if err != nil {
		return nil
	}

	return resp
}

func CreateRenewSubscription(db *gorm.DB, gameID int, RenewSubscriptionInfo *schema.RenewSubscriptionCreate) (*models.RenewSubscription, error) {
	renewSubscription := models.RenewSubscription{}
	renewSubscription.GameId = gameID
	renewSubscription.PayWay = RenewSubscriptionInfo.PayWay
	renewSubscription.OriginalTransactionId = RenewSubscriptionInfo.OriginalTransactionId
	renewSubscription.TransactionId = RenewSubscriptionInfo.TransactionId
	renewSubscription.OrderId = RenewSubscriptionInfo.OrderId
	renewSubscription.NotificationInfo = RenewSubscriptionInfo.NotificationInfo
	renewSubscription.NotificationType = RenewSubscriptionInfo.NotificationType
	renewSubscription.NotificationUUID = RenewSubscriptionInfo.NotificationUUID
	renewSubscription.ExpiresDate = RenewSubscriptionInfo.ExpiresDate
	renewSubscription.CreateTime = time.Now()
	err := models.CreateRenewSubscription(db, &renewSubscription)
	return &renewSubscription, err
}
