package services

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"zeus/common/constants"
	"zeus/models"
	zeusHttp "zeus/pkg/http"
	"zeus/schema"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestSubscription(t *testing.T) {
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	c.Request = new(http.Request)
	c.Set("db", &gorm.DB{})

	Convey("CreateSubscription", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.CreateSubscription, func(db *gorm.DB, _ *models.Subscription) error {
				return nil
			})
			subscription := &models.Subscription{}

			output := CreateSubscription(c, subscription)
			So(output, ShouldEqual, nil)
		})
	})

	Convey("GetSubscriptionByExtendAndPayStatus", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetOneSubscription, func(db *gorm.DB, args map[string]interface{}) (*models.Subscription, error) {
				return &models.Subscription{Id: 11}, nil
			})
			extend := "sss"
			payStatus := 1

			output, _ := GetSubscriptionByExtendAndPayStatus(c, extend, payStatus)
			So(output.Id, ShouldEqual, 11)
		})
	})

	Convey("QuerySubscription", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetOneSubscription, func(db *gorm.DB, args map[string]interface{}) (*models.Subscription, error) {
				return &models.Subscription{Id: 11}, nil
			})

			args := make(map[string]interface{})

			output, _ := QuerySubscription(c, args)
			So(output.Id, ShouldEqual, 11)
		})
	})

	Convey("SaveSubscription", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.SaveSubscription, func(db *gorm.DB, args *models.Subscription) error {
				return nil
			})
			order := &models.Subscription{}
			output := SaveSubscription(c, order)
			So(output, ShouldEqual, nil)
		})
	})

	Convey("EditNewSubscription", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.SaveSubscription, func(db *gorm.DB, args *models.Subscription) error {
				return nil
			})
			order := &models.Subscription{}
			data := schema.PurchaseData{}
			info := make(map[string]interface{})
			info["expiryTimeMillis"] = "100"
			orderForm := schema.GoogleSubscriptionVerify{}

			output := EditNewSubscription(c, order, data, info, orderForm)
			So(output, ShouldEqual, nil)
		})
	})

	Convey("GetSubscriptionStatus", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.SaveSubscription, func(db *gorm.DB, args *models.Subscription) error {
				return nil
			})

			data := make(map[string]interface{})
			data["cancellation_date_pst"] = ""
			data["expires_date"] = int64(100)

			output := GetSubscriptionStatus(data)
			expected := constants.SubscriptionOrderExpire
			So(output, ShouldEqual, expected)
		})
	})

	Convey("VerifySubToAppstore", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(zeusHttp.PostWithTracer, func(_ context.Context, _ string, _ map[string]string,
				_ []byte, _ ...zeusHttp.Option,
			) (resp *http.Response, err error) {
				return &http.Response{StatusCode: http.StatusOK}, nil
			})
			ctx := context.Background()
			resp := VerifySubToAppstore(ctx, "receipt", false, "applePassword")
			output := resp.StatusCode
			expected := http.StatusOK
			So(output, ShouldEqual, expected)
		})
	})
}
