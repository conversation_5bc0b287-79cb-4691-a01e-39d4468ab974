package third_account

import (
	"errors"
	"reflect"
	"time"

	"zeus/common/constants"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/thirdlogin"
	"zeus/schema"
	"zeus/schema/request"
	"zeus/schema/response"
	"zeus/services"
	"zeus/services/account"
	"zeus/services/settings/converter"
	"zeus/services/settings/field"
	"zeus/services/settings/store"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
)

func createUserBindThird(ctx *gin.Context, userId int, loginType int, userInfo thirdlogin.UserInfo) error {
	userBindThird := &models.UserBindThird{}
	userBindThird.UnionID = userInfo.UnionID
	userBindThird.UserID = userId
	userBindThird.Type = loginType
	userBindThird.Status = constants.BoundStatus
	userBindThird.UserName = userInfo.UserName
	nowTime := time.Now()
	userBindThird.CreateTime = nowTime
	userBindThird.UpdateTime = nowTime

	return services.SaveBindThird(ctx, userBindThird)
}

func createUser(ctx *gin.Context, req request.ThirdLogin) (*models.User, error) {
	us := new(services.UserServices)
	us.Map(req)
	return us.CreateUser(ctx)
}

func ThirdLogin(ctx *gin.Context, gameId int, userInfo thirdlogin.UserInfo, req request.ThirdLogin) (response.ThirdLogins, error) {
	users, _ := services.GetUserWithThirdBindAccount(ctx, userInfo.UnionID, req.Type, gameId)
	db := ctx.MustGet("db").(*gorm.DB)

	switch len(users) {
	case 0:
		user, err := createUser(ctx, req)
		if err != nil {
			return nil, errors.New("创建新用户失败")
		}

		createUserBindThird(ctx, user.ID, req.Type, userInfo)

		r := response.ThirdLogins{response.ThirdLogin{
			ID:        user.ID,
			Account:   user.Account,
			Token:     user.Token,
			LoginTime: schema.DateTime{Time: user.LoginTime},
		}}
		return r, nil
	case 1:
		user := users[0]
		user.LoginTime = time.Now()
		user.Token = services.GenerateToken()
		err := services.SaveUser(ctx, &user)
		if err != nil {
			return nil, errors.New("用户更新失败")
		}

		err = models.UpdateThirdUserName(db, userInfo.UnionID, req.Type, constants.BoundStatus, userInfo.UserName)
		if err != nil {
			return nil, errors.New("更新绑定信息失败")
		}

		services.RecordLoginDevice(ctx, &user, req.Device)
		r := response.ThirdLogins{response.ThirdLogin{
			ID:        user.ID,
			Account:   user.Account,
			Token:     user.Token,
			LoginTime: schema.DateTime{Time: user.LoginTime},
		}}
		return r, nil
	default:
		for _, user := range users {
			services.RecordLoginDevice(ctx, &user, req.Device)
		}
		roles, _ := services.GetRolesByUserIDs(ctx, gameId, users.GetIDs())
		err := models.UpdateThirdUserName(db, userInfo.UnionID, req.Type, constants.BoundStatus, userInfo.UserName)
		if err != nil {
			return nil, errors.New("更新绑定信息失败")
		}
		userBindThirds := response.NewThirdLogins(users, roles)
		return userBindThirds, nil
	}
}

func GetThirdUserInfo(c *gin.Context, appid string, form request.ThirdForm) (thirdlogin.UserInfo, int, error) {
	provider, ok := store.ThirdLoginSwitchMapping[form.Type]
	if !ok {
		return thirdlogin.UserInfo{}, controllers.ErrorParamInvalid, errors.New("不支持的三方类型")
	}

	var userInfo thirdlogin.UserInfo
	switch form.Type {
	case constants.Wechat:
		config := reflect.New(reflect.TypeOf(provider)).Interface().(field.ThirdLoginConfiger)
		converter.QueryByAppid(config.(field.BaseConfiger), appid)
		client := config.NewClient()
		var err error
		if userInfo, err = client.Login(c.Request.Context(), form.Token, form.SecretToken); err != nil {
			return userInfo, controllers.ErrorThirdLogin, err
		}
	case constants.Email:
		server := account.NewServer(c)
		account, err := server.FindByUnionId(form.UnionId)
		if err != nil {
			return thirdlogin.UserInfo{}, controllers.ErrorParamInvalid, errors.New("无效的UnionId")
		}
		userInfo.UnionID = form.UnionId
		userInfo.UserName = *account.Email
	case constants.Mobile:
		server := account.NewServer(c)
		account, err := server.FindByUnionId(form.UnionId)
		if err != nil {
			return thirdlogin.UserInfo{}, controllers.ErrorParamInvalid, errors.New("无效的UnionId")
		}
		userInfo.UnionID = form.UnionId
		userInfo.UserName = *account.Phone
	default:
		if form.UnionId == "" {
			return thirdlogin.UserInfo{}, controllers.ErrorDBError, errors.New("无效的UnionId")
		} else {
			userInfo.UnionID = form.UnionId
			userInfo.UserName = form.UserName
		}
	}
	return userInfo, controllers.Success, nil
}

func GetMiniGameThirdInfo(c *gin.Context, appid string, form request.MiniGameThirdLoginForm) (map[string]interface{}, int, error) {
	provider, ok := store.MiniGameThirdLoginSwitchMapping[form.Type]
	if !ok {
		return nil, controllers.ErrorParamInvalid, errors.New("不支持的三方类型")
	}

	var userInfo map[string]interface{}
	config := reflect.New(reflect.TypeOf(provider)).Interface().(field.MiniGameThirdLoginConfiger)
	converter.QueryByAppid(config.(field.BaseConfiger), appid)
	client := config.NewClient()
	var err error
	if userInfo, err = client.MiniGameLogin(c.Request.Context(), form.Code); err != nil {
		return userInfo, controllers.ErrorThirdLogin, err
	}

	return userInfo, controllers.Success, nil
}
