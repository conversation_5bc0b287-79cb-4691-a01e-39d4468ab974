package third_account

import (
	"context"
	"errors"
	"net/http"
	"reflect"
	"testing"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/controllers"
	"zeus/models"
	"zeus/pkg/thirdlogin"
	"zeus/schema/request"
	"zeus/services"
	"zeus/services/account"
	"zeus/services/game"
	_ "zeus/services/settings"
	"zeus/services/settings/converter"
	settings_schema "zeus/services/settings/field"
	settings_thirdlogin "zeus/services/settings/thirdlogin"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestThirdAccount(t *testing.T) {
	c := &gin.Context{Request: &http.Request{}}
	c.Set("db", &gorm.DB{})
	mockFunc1 := ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{}, nil
	})
	defer mockFunc1.Reset()

	mockFunc2 := ApplyFunc(services.RecordLoginDevice, func(_ *gin.Context, _ *models.User, _ string) {
	})
	defer mockFunc2.Reset()

	mockFuncLog := ApplyFunc(log.Debug, func(_ context.Context, _ string, _ ...any) {
	})
	defer mockFuncLog.Reset()

	Convey("TestThirdLogin", t, func() {
		Convey("当该三方账号没有绑定zeus账号", func() {
			mockFunc2 := ApplyFunc(services.GetUserWithThirdBindAccount, func(_ *gin.Context, _ string, _, _ int) (models.Users, error) {
				return models.Users{}, nil
			})
			defer mockFunc2.Reset()

			mockFunc3 := ApplyFunc(services.SaveBindThird, func(_ *gin.Context, _ *models.UserBindThird) error {
				return nil
			})
			defer mockFunc3.Reset()

			us := services.UserServices{}
			mockMethod := ApplyMethod(reflect.TypeOf(&us), "CreateUser", func(_ *services.UserServices, c *gin.Context) (*models.User, error) {
				return &models.User{Account: "111222"}, nil
			})
			defer mockMethod.Reset()

			users, _ := ThirdLogin(c, 1003, thirdlogin.UserInfo{}, request.ThirdLogin{})
			actual := users[0].Account
			expected := "111222"
			So(actual, ShouldEqual, expected)
		})

		Convey("当该三方账号绑定一个zeus账号", func() {
			mockFunc2 := ApplyFunc(services.GetUserWithThirdBindAccount, func(_ *gin.Context, _ string, _, _ int) (models.Users, error) {
				return models.Users{models.User{Account: "111222"}}, nil
			})
			defer mockFunc2.Reset()

			mockFunc3 := ApplyFunc(services.SaveUser, func(_ *gin.Context, _ *models.User) error {
				return nil
			})
			defer mockFunc3.Reset()

			mockFunc6 := ApplyFunc(models.UpdateThirdUserName, func(_ *gorm.DB, _ string, _ int, _ int, _ string) error {
				return nil
			})
			defer mockFunc6.Reset()

			users, _ := ThirdLogin(c, 1003, thirdlogin.UserInfo{}, request.ThirdLogin{})
			actual := users[0].Account
			expected := "111222"
			So(actual, ShouldEqual, expected)
		})

		Convey("当该三方账号绑定多个zeus账号", func() {
			mockFunc2 := ApplyFunc(services.GetUserWithThirdBindAccount, func(_ *gin.Context, _ string, _, _ int) (models.Users, error) {
				return models.Users{models.User{Account: "111222"}, models.User{Account: "333222"}}, nil
			})
			defer mockFunc2.Reset()

			mockFunc3 := ApplyFunc(services.SaveUser, func(_ *gin.Context, _ *models.User) error {
				return nil
			})
			defer mockFunc3.Reset()

			mockFunc4 := ApplyFunc(services.GetRolesByUserIDs, func(_ *gin.Context, _ int, _ []int) (models.Roles, error) {
				return models.Roles{models.Role{RoleId: "tom111"}}, nil
			})
			defer mockFunc4.Reset()

			mockFunc6 := ApplyFunc(models.UpdateThirdUserName, func(_ *gorm.DB, _ string, _ int, _ int, _ string) error {
				return nil
			})
			defer mockFunc6.Reset()

			users, _ := ThirdLogin(c, 1003, thirdlogin.UserInfo{}, request.ThirdLogin{})
			actual := users[1].Roles[0].RoleID
			expected := "tom111"
			So(actual, ShouldEqual, expected)
		})
	})

	Convey("GetThirdUnionId", t, func() {
		Convey("获取三方平台绑定ID", func() {
			userInfo, errorNo, err := GetThirdUserInfo(c, "test_appid", request.ThirdForm{Type: 1, UnionId: "test_union_id"})
			So(userInfo.UnionID, ShouldEqual, "test_union_id")
			So(errorNo, ShouldEqual, controllers.Success)
			So(err, ShouldBeNil)
		})

		Convey("获取三方平台绑定ID, UnionID为空", func() {
			userInfo, errorNo, err := GetThirdUserInfo(c, "test_appid", request.ThirdForm{Type: 1})
			So(userInfo.UnionID, ShouldEqual, "")
			So(errorNo, ShouldEqual, controllers.ErrorDBError)
			So(err, ShouldNotBeNil)
		})

		Convey("账号体系手机登录", func() {
			server := account.Server{}
			phone := "**********"
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "FindByUnionId", func(_ *account.Server, _ string) (*models.Account, error) {
				return &models.Account{UnionId: "test_union_id", Phone: &phone}, nil
			})
			defer mockMethod.Reset()

			userInfo, errorNo, _ := GetThirdUserInfo(c, "test_appid", request.ThirdForm{Type: 10, UnionId: "test_union_id"})
			So(userInfo.UnionID, ShouldEqual, "test_union_id")
			So(userInfo.UserName, ShouldEqual, phone)
			So(errorNo, ShouldEqual, controllers.Success)
		})

		Convey("账号体系邮箱登录", func() {
			server := account.Server{}
			email := "<EMAIL>"
			mockMethod := ApplyMethod(reflect.TypeOf(&server), "FindByUnionId", func(_ *account.Server, _ string) (*models.Account, error) {
				return &models.Account{UnionId: "test_union_id", Email: &email}, nil
			})
			defer mockMethod.Reset()

			userInfo, errorNo, _ := GetThirdUserInfo(c, "test_appid", request.ThirdForm{Type: 11, UnionId: "test_union_id"})
			So(userInfo.UnionID, ShouldEqual, "test_union_id")
			So(userInfo.UserName, ShouldEqual, email)
			So(errorNo, ShouldEqual, controllers.Success)
		})

		Convey("无效的平台信息", func() {
			userInfo, errorNo, err := GetThirdUserInfo(c, "test_appid", request.ThirdForm{Type: 999})
			So(userInfo.UnionID, ShouldEqual, "")
			So(errorNo, ShouldEqual, controllers.ErrorParamInvalid)
			So(err, ShouldNotBeNil)
		})

		Convey("微信平台，请求微信获取UnionID", func() {
			QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config settings_schema.BaseConfiger, _ string) {
				switch config.GetType() {
				case "ThirdLoginWeChat":
					weChatPayConfig := config.(*settings_thirdlogin.ThirdLoginWeChat)
					wechatAppid := "WechatAppid"
					weChatPayConfig.WechatAppid = &wechatAppid
					wechatSecret := "test_wechat_secret"
					weChatPayConfig.WechatSecret = &wechatSecret
				}
			})
			defer QueryMockFunc.Reset()

			ApplyMethod(reflect.TypeOf(&thirdlogin.Wechat{}), "Login", func(_ *thirdlogin.Wechat, _ context.Context, _, _ string) (thirdlogin.UserInfo, error) {
				return thirdlogin.UserInfo{UnionID: "test_union_id"}, nil
			})

			userInfo, errorNo, err := GetThirdUserInfo(c, "test_appid", request.ThirdForm{Type: constants.Wechat})
			So(userInfo.UnionID, ShouldEqual, "test_union_id")
			So(errorNo, ShouldEqual, controllers.Success)
			So(err, ShouldBeNil)
		})

		Convey("微信平台，请求微信获取UnionID失败", func() {
			QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config settings_schema.BaseConfiger, _ string) {
				switch config.GetType() {
				case "ThirdLoginWeChat":
					weChatPayConfig := config.(*settings_thirdlogin.ThirdLoginWeChat)
					wechatAppid := "WechatAppid"
					weChatPayConfig.WechatAppid = &wechatAppid
					wechatSecret := "test_wechat_secret"
					weChatPayConfig.WechatSecret = &wechatSecret
				}
			})
			defer QueryMockFunc.Reset()

			ApplyMethod(reflect.TypeOf(&thirdlogin.Wechat{}), "Login", func(_ *thirdlogin.Wechat, _ context.Context, _, _ string) (thirdlogin.UserInfo, error) {
				return thirdlogin.UserInfo{UnionID: "test_union_id"}, errors.New("Login Wechat Failed")
			})

			userInfo, errorNo, err := GetThirdUserInfo(c, "test_appid", request.ThirdForm{Type: constants.Wechat})
			So(userInfo.UnionID, ShouldEqual, "test_union_id")
			So(errorNo, ShouldEqual, controllers.ErrorThirdLogin)
			So(err, ShouldNotBeNil)
		})
	})
}
