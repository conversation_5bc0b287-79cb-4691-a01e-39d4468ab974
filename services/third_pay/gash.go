package third_pay

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
	"net/url"
	"time"

	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	"zeus/pkg/gash"
	"zeus/schema/request"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"gorm.io/gorm"
)

type GashPayClient struct{}

func (m *GashPayClient) CreateOrder(ctx context.Context, g *models.Game, v *request.ThirdPayOrder, o *models.Order) (map[string]interface{}, error) {
	var gashConfig payment.GashPay
	converter.QueryByGameId(&gashConfig, g.Id)

	config := gashConfig.GashConfigs.GetConfigByRegion(v.Region)
	if config == nil {
		return nil, errors.New("缺少该地区对应的gash商户信息")
	}
	facReturnURL := fmt.Sprintf("%v?project_id=%v&pay_way=%v",
		v.PayReturnUrl, v.ProjectID, constants.PayWayGash)
	gashOrder := gash.Order{
		MsgType:       gash.MsgTypeOrderRequest,
		PCode:         gash.PCodeOrder,
		CID:           config.ServiceID,
		COID:          o.OrderId,
		CUID:          v.Currency,
		Amount:        v.Price,
		ProductName:   v.ProductName,
		ReturnURL:     gash.Cdata{Value: facReturnURL},
		OrderType:     gash.UnspecifiedPaymentChannels,
		UserAccountID: v.RoleID,
	}

	client := gash.NewClient(config.Key, config.Vi, config.Password, config.SandBoxMode)
	transactionXML := client.Pay(&gashOrder)
	payUrl := ""
	if config.SandBoxMode {
		payUrl = gash.BaseUrlTest + gash.PayPath
		o.OrderAttribute = constants.TestAccount
	} else {
		payUrl = gash.BaseUrl + gash.PayPath
		o.OrderAttribute = constants.NormalOrder
	}

	var extend Extend
	extend.OrderId = o.OrderId
	extend.RoleId = o.RoleId
	extend.ServerId = o.ServerId
	extend.Account = o.Account
	extend.ProductId = o.ProductId
	extend.PayWay = constants.GashPayName
	extend.Region = v.Region
	data, _ := json.Marshal(extend)
	o.Extend = string(data)

	respData := map[string]interface{}{
		"pay_url": payUrl,
		"data":    transactionXML,
	}
	return respData, nil
}

func (m *GashPayClient) CreateOrderV2(
	ctx context.Context,
	g *models.Game,
	v *request.ThirdPayOrderV2,
	product *models.Product,
	o *models.Order,
	inSandBoxPayment bool,
) (map[string]interface{}, error) {
	var gashConfig payment.GashPay
	converter.QueryByGameId(&gashConfig, g.Id)

	var config *payment.GashConfig

	region := v.Region
	if inSandBoxPayment {
		region = fmt.Sprintf("%s_sandbox", region)
	}

	config = gashConfig.GashConfigs.GetConfigByRegion(region)

	if config == nil {
		return nil, errors.New("缺少该地区对应的gash商户信息")
	}

	facReturnURL := fmt.Sprintf("%v?project_id=%v&pay_way=%v",
		v.PayReturnUrl, v.ProjectId, constants.PayWayGash)
	gashOrder := gash.Order{
		MsgType:       gash.MsgTypeOrderRequest,
		PCode:         gash.PCodeOrder,
		CID:           config.ServiceID,
		COID:          o.OrderId,
		CUID:          v.Currency,
		Amount:        product.Prices.GetPrice(v.Currency),
		ProductName:   product.GameProductId,
		ReturnURL:     gash.Cdata{Value: facReturnURL},
		OrderType:     gash.UnspecifiedPaymentChannels,
		UserAccountID: v.RoleId,
	}

	client := gash.NewClient(config.Key, config.Vi, config.Password, config.SandBoxMode)
	transactionXML := client.Pay(&gashOrder)
	payUrl := ""
	if config.SandBoxMode || inSandBoxPayment {
		payUrl = gash.BaseUrlTest + gash.PayPath
		o.OrderAttribute = constants.TestAccount
	} else {
		payUrl = gash.BaseUrl + gash.PayPath
		o.OrderAttribute = constants.NormalOrder
	}

	var extend Extend
	extend.OrderId = o.OrderId
	extend.RoleId = o.RoleId
	extend.ServerId = o.ServerId
	extend.Account = o.Account
	extend.ProductId = o.ProductId
	extend.PayWay = constants.GashPayName
	extend.Region = region
	data, _ := json.Marshal(extend)
	o.Extend = string(data)

	respData := map[string]interface{}{
		"pay_url": payUrl,
		"data":    transactionXML,
	}
	return respData, nil
}

func (m *GashPayClient) CheckAndPaymentConfirmAndNotify(ctx context.Context, db *gorm.DB, values interface{}) error {
	v, err := url.ParseQuery(values.(string))
	if err != nil {
		return err
	}

	data := v.Get("data")
	decodedBytes, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return err
	}
	resp := gash.PaymentConfirmResp{}
	err = xml.Unmarshal(decodedBytes, &resp)
	if err != nil {
		return err
	}
	if resp.RCode != gash.ReqSuccess {
		return errors.New("订单支付失败！")
	}

	order, err := models.GetOrderByOrderID(db, resp.COID)
	if err != nil {
		return err
	}
	if order.PayStatus == constants.PaymentSuccess {
		return nil
	}

	var extend Extend
	err = json.Unmarshal([]byte(order.Extend), &extend)
	if err != nil {
		return err
	}

	var gashConfig payment.GashPay
	converter.QueryByGameId(&gashConfig, order.GameId)

	config := gashConfig.GashConfigs.GetConfigByRegion(extend.Region)
	if config == nil {
		return errors.New("缺少该地区对应的gash商户信息")
	}

	req := gash.PaymentConfirmReq{
		MsgType: gash.MsgTypeRequestFundsReq,
		PCode:   gash.PCodeOrder,
		CID:     resp.CID,
		CUID:    resp.CUID,
		COID:    resp.COID,
		PAID:    resp.PAID,
		Amount:  resp.Amount,
	}

	client := gash.NewClient(config.Key, config.Vi, config.Password, config.SandBoxMode)
	if !client.CheckNotifyInfo(&resp) {
		return errors.New("非法的通知信息")
	}

	paymentConfirmResp, err := client.PaymentConfirm(&req)
	if err != nil {
		return err
	}

	if paymentConfirmResp.RCode != gash.ReqSuccess {
		log.Error(ctx, "异常", "order_id", order.OrderId, "error", paymentConfirmResp.RCode)
		return errors.New(paymentConfirmResp.RCode)
	}

	order.PayStatus = constants.PaymentSuccess
	order.PayTime = time.Now()
	order.PayId = paymentConfirmResp.RRN
	order.VerifyStatus = constants.VerifyPassed
	err = models.SaveOrder(db, order)
	if err != nil {
		return err
	}
	_ = services.Notify(ctx, db, order, services.NotifyOptions{})
	return nil
}

func (m *GashPayClient) CheckAndPaymentConfirmAndNotifyV2(ctx context.Context, db *gorm.DB, values interface{}) error {
	v, err := url.ParseQuery(values.(string))
	if err != nil {
		return err
	}

	data := v.Get("data")
	decodedBytes, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return err
	}
	resp := gash.PaymentConfirmResp{}
	err = xml.Unmarshal(decodedBytes, &resp)
	if err != nil {
		return err
	}
	if resp.RCode != gash.ReqSuccess {
		return errors.New("订单支付失败！")
	}

	order, err := models.GetOrderByOrderID(db, resp.COID)
	if err != nil {
		return err
	}
	if order.PayStatus == constants.PaymentSuccess {
		return nil
	}

	var extend Extend
	err = json.Unmarshal([]byte(order.Extend), &extend)
	if err != nil {
		return err
	}

	var gashConfig payment.GashPay
	converter.QueryByGameId(&gashConfig, order.GameId)

	config := gashConfig.GashConfigs.GetConfigByRegion(extend.Region)
	if config == nil {
		return errors.New("缺少该地区对应的gash商户信息")
	}

	req := gash.PaymentConfirmReq{
		MsgType: gash.MsgTypeRequestFundsReq,
		PCode:   gash.PCodeOrder,
		CID:     resp.CID,
		CUID:    resp.CUID,
		COID:    resp.COID,
		PAID:    resp.PAID,
		Amount:  resp.Amount,
	}

	client := gash.NewClient(config.Key, config.Vi, config.Password, config.SandBoxMode)
	if !client.CheckNotifyInfo(&resp) {
		return errors.New("非法的通知信息")
	}

	paymentConfirmResp, err := client.PaymentConfirm(&req)
	if err != nil {
		return err
	}

	if paymentConfirmResp.RCode != gash.ReqSuccess {
		log.Error(ctx, "异常", "order_id", order.OrderId, "error", paymentConfirmResp.RCode)
		return errors.New(paymentConfirmResp.RCode)
	}

	purchaseRecord, err := models.GetPurchaseRecordByOrderId(db, order.OrderId)
	if err != nil {
		return errors.New("交易记录不存在！")
	}

	order.PayId = paymentConfirmResp.RRN
	order.PayTime = time.Now()
	if purchaseRecord.PayStatus == constants.PaymentFailure {
		order.PayStatus = constants.PaymentFailure
		order.VerifyStatus = constants.VerifyFailed
		order.PayErrorMessage = "支付订单已取消"
		err = models.SaveOrder(db, order)
		if err != nil {
			return err
		}
		return errors.New("支付订单已取消")
	} else {
		purchaseRecord.PayStatus = constants.PaymentSuccess
		purchaseRecord.UpdateTime = time.Now()
		err = models.SavePurchaseRecord(db, purchaseRecord)
		if err != nil {
			return err
		}
		order.PayStatus = constants.PaymentSuccess
		order.VerifyStatus = constants.VerifyPassed
		err = models.SaveOrder(db, order)
		if err != nil {
			return err
		}
		_ = services.Notify(ctx, db, order, services.NotifyOptions{})
	}
	return nil
}

func (m *GashPayClient) checkOrder(configs payment.GashConfigs, order *models.Order) (*gash.Client, *gash.PaymentConfirmResp, error) {
	for _, v := range configs {
		client := gash.NewClient(v.Key, v.Vi, v.Password, v.SandBoxMode)
		gashOrder, _ := client.CheckOrder(v.ServiceID, order.OrderId, order.PayCurrency, order.Price)
		if gashOrder.RCode == gash.ReqSuccess && gashOrder.PayRCode == gash.ReqSuccess {
			return client, gashOrder, nil
		}
	}
	return nil, nil, errors.New("补单失败，订单未支付！")
}

func (m *GashPayClient) FixOrder(gameId int, order *models.Order) error {
	g, _ := game.GetGameById(gameId)
	var gashConfig payment.GashPay
	converter.QueryByGameId(&gashConfig, g.Id)

	client, gashOrder, err := m.checkOrder(*gashConfig.GashConfigs, order)
	if err != nil {
		return err
	}

	req := gash.PaymentConfirmReq{
		MsgType: gash.MsgTypeRequestFundsReq,
		PCode:   gash.PCodeOrder,
		CID:     gashOrder.CID,
		CUID:    gashOrder.CUID,
		COID:    gashOrder.COID,
		PAID:    gashOrder.PAID,
		Amount:  gashOrder.Amount,
	}

	paymentConfirmResp, err := client.PaymentConfirm(&req)
	if err != nil {
		return err
	}

	if paymentConfirmResp.RCode != gash.ReqSuccess && paymentConfirmResp.RCode != gash.RepeatSettle {
		return errors.New("补单失败，请款失败！")
	}

	order.PayStatus = constants.PaymentSuccess
	order.PayTime = time.Now()
	order.PayId = paymentConfirmResp.RRN
	order.VerifyStatus = constants.VerifyPassed
	return nil
}
