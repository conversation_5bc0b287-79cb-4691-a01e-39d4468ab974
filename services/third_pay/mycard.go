package third_pay

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/mycard"
	"zeus/schema/request"
	"zeus/services"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"zeus/common/log"

	"gorm.io/gorm"
)

type MyCardPayClient struct{}

func (m *MyCardPayClient) CreateOrder(ctx context.Context, g *models.Game, v *request.ThirdPayOrder, o *models.Order) (map[string]interface{}, error) {
	facReturnURL := fmt.Sprintf("%v?project_id=%v&pay_way=%v", v.PayReturnUrl, v.ProjectID, constants.PayWayMyCard)

	var mycardConfig payment.MyCardPay
	converter.QueryByGameId(&mycardConfig, g.Id)

	config := mycardConfig.MyCardConfigs.GetConfigByRegion(v.Region)
	if config == nil {
		return nil, fmt.Errorf("缺少該地區對應的mycard商戶信息, Region: %s", v.Region)
	}

	myCardOrder := &mycard.Order{
		FacServiceId: config.ServiceID,
		FacTradeSeq:  o.OrderId,
		FacGameId:    strconv.Itoa(g.Id),
		FacGameName:  g.Name,
		TradeType:    mycard.TradeTypeWeb,
		ServerId:     v.ServerID,
		CustomerId:   v.RoleID,
		ProductName:  v.ProductName,
		Amount:       strconv.FormatFloat(v.Price, 'f', 2, 64),
		Currency:     v.Currency,
		SandBoxMode:  strconv.FormatBool(config.SandBoxMode),
		FacReturnURL: facReturnURL,
	}
	client := mycard.NewClient(config.Secret, config.SandBoxMode)
	resp, err := client.Pay(ctx, myCardOrder)
	if err != nil {
		return nil, err
	}
	if resp.ReturnCode != mycard.ReqSuccess {
		return nil, fmt.Errorf("code: %v,msg: %v", resp.ReturnCode, resp.ReturnMsg)
	}
	if config.SandBoxMode {
		o.OrderAttribute = constants.TestAccount
	} else {
		o.OrderAttribute = constants.NormalOrder
	}

	receipt := make(map[string]string)
	receipt["AuthCode"] = resp.AuthCode
	receiptData, _ := json.Marshal(receipt)
	o.Receipt = string(receiptData)

	var extend Extend
	extend.OrderId = o.OrderId
	extend.RoleId = o.RoleId
	extend.ServerId = o.ServerId
	extend.Account = o.Account
	extend.ProductId = o.ProductId
	extend.PayWay = constants.MyCardPayName
	extend.Region = v.Region
	data, _ := json.Marshal(extend)
	o.Extend = string(data)

	respData := map[string]interface{}{
		"pay_url": resp.TransactionUrl,
	}
	return respData, nil
}

func (m *MyCardPayClient) CreateOrderV2(
	ctx context.Context,
	g *models.Game,
	v *request.ThirdPayOrderV2,
	product *models.Product,
	o *models.Order,
	inSandBoxPayment bool,
) (map[string]interface{}, error) {
	facReturnURL := fmt.Sprintf("%v?project_id=%v&pay_way=%v", v.PayReturnUrl, v.ProjectId, constants.PayWayMyCard)

	var mycardConfig payment.MyCardPay
	converter.QueryByGameId(&mycardConfig, g.Id)

	var config *payment.MyCardConfig
	region := v.Region
	if inSandBoxPayment {
		region = fmt.Sprintf("%s_sandbox", region)
	}

	config = mycardConfig.MyCardConfigs.GetConfigByRegion(region)
	if config == nil {
		return nil, fmt.Errorf("缺少該地區對應的mycard商戶信息, Region: %s", v.Region)
	}

	myCardOrder := &mycard.Order{
		FacServiceId: config.ServiceID,
		FacTradeSeq:  o.OrderId,
		FacGameId:    strconv.Itoa(g.Id),
		FacGameName:  g.Name,
		TradeType:    mycard.TradeTypeWeb,
		ServerId:     v.ServerId,
		CustomerId:   v.RoleId,
		ProductName:  product.GameProductId,
		Amount:       strconv.FormatFloat(product.Prices.GetPrice(v.Currency), 'f', 2, 64),
		Currency:     v.Currency,
		SandBoxMode:  strconv.FormatBool(config.SandBoxMode),
		FacReturnURL: facReturnURL,
	}

	client := mycard.NewClient(config.Secret, config.SandBoxMode)
	resp, err := client.Pay(ctx, myCardOrder)
	if err != nil {
		return nil, err
	}
	if resp.ReturnCode != mycard.ReqSuccess {
		return nil, fmt.Errorf("code: %v,msg: %v", resp.ReturnCode, resp.ReturnMsg)
	}
	if config.SandBoxMode || inSandBoxPayment {
		o.OrderAttribute = constants.TestAccount
	} else {
		if v.ActivityId != 0 {
			o.OrderAttribute = constants.ActivityOrder
		} else {
			o.OrderAttribute = constants.NormalOrder
		}
	}

	receipt := make(map[string]string)
	receipt["AuthCode"] = resp.AuthCode
	receiptData, _ := json.Marshal(receipt)
	o.Receipt = string(receiptData)

	var extend Extend
	extend.OrderId = o.OrderId
	extend.RoleId = o.RoleId
	extend.ServerId = o.ServerId
	extend.Account = o.Account
	extend.ProductId = o.ProductId
	extend.PayWay = constants.MyCardPayName
	extend.Region = region
	data, _ := json.Marshal(extend)
	o.Extend = string(data)

	respData := map[string]interface{}{
		"pay_url": resp.TransactionUrl,
	}
	return respData, nil
}

func (m *MyCardPayClient) CheckAndPaymentConfirmAndNotify(ctx context.Context, db *gorm.DB, values interface{}) error {
	v, _ := url.ParseQuery(values.(string))
	facTradeSeq := v.Get("FacTradeSeq")
	order, err := models.GetOrderByOrderID(db, facTradeSeq)
	if err != nil {
		return err
	}

	if order.PayStatus == constants.PaymentSuccess {
		return nil
	}

	var extend Extend
	err = json.Unmarshal([]byte(order.Extend), &extend)
	if err != nil {
		return err
	}

	var mycardConfig payment.MyCardPay
	converter.QueryByGameId(&mycardConfig, order.GameId)

	config := mycardConfig.MyCardConfigs.GetConfigByRegion(extend.Region)
	if config == nil {
		log.Error(ctx, "缺少該地區對應的mycard商戶信息", "order_id", order.OrderId)
		return errors.New("缺少該地區對應的mycard商戶信息")
	}

	if err = checkNotifyHash(v, config.Secret); err != nil {
		log.Error(ctx, "校验订单Hash失败", "error", err.Error(), "order_id", order.OrderId)
		return err
	}

	receipt := make(map[string]string)
	json.Unmarshal([]byte(order.Receipt), &receipt)

	authCode, ok := receipt["AuthCode"]
	if !ok {
		log.Error(ctx, "验证失败缺少AuthCode", "order_id", order.OrderId)
		return errors.New("验证失败缺少AuthCode")
	}
	client := mycard.NewClient(config.Secret, config.SandBoxMode)
	verifyResp, err := client.Verify(ctx, authCode)
	if err != nil {
		log.Error(ctx, "验证订单失败", "error", err.Error(), "order_id", order.OrderId)
		return err
	}

	if verifyResp.ReturnCode != mycard.ReqSuccess || verifyResp.PayResult != mycard.TradeSuccess {
		var returnMsg string
		var err error
		returnMsg, err = url.QueryUnescape(v.Get("ReturnMsg"))
		if err != nil {
			returnMsg = "未知異常"
		}
		log.Error(ctx, "订单支付失败", "error", returnMsg, "order_id", order.OrderId)
		return errors.New(returnMsg)
	}

	paymentConfirmResp, err := client.PaymentConfirm(ctx, authCode)
	if err != nil {
		return err
	}

	if paymentConfirmResp.ReturnCode != mycard.ReqSuccess {
		log.Error(ctx, "请款失败", "error", err.Error(), "order_id", order.OrderId)
		return errors.New(paymentConfirmResp.ReturnMsg)
	}

	receipt["ReturnCode"] = verifyResp.ReturnCode
	receipt["PayResult"] = verifyResp.PayResult
	receipt["FacTradeSeq"] = verifyResp.FacTradeSeq
	receipt["PaymentType"] = verifyResp.PaymentType
	receipt["Amount"] = verifyResp.Amount
	receipt["Currency"] = verifyResp.Currency
	receipt["MyCardTradeNo"] = verifyResp.MyCardTradeNo
	receipt["MyCardType"] = verifyResp.MyCardType
	receipt["PromoCode"] = verifyResp.PromoCode
	receipt["TradeSeq"] = paymentConfirmResp.TradeSeq
	data, _ := json.Marshal(receipt)

	order.PayId = verifyResp.MyCardTradeNo
	order.Receipt = string(data)
	order.PayStatus = constants.PaymentSuccess
	order.VerifyStatus = constants.VerifyPassed
	order.PayTime = time.Now()
	err = models.SaveOrder(db, order)
	if err != nil {
		return err
	}
	services.Notify(ctx, db, order, services.NotifyOptions{})
	return nil
}

func (m *MyCardPayClient) CheckAndPaymentConfirmAndNotifyV2(ctx context.Context, db *gorm.DB, values interface{}) error {
	v, _ := url.ParseQuery(values.(string))
	facTradeSeq := v.Get("FacTradeSeq")
	order, err := models.GetOrderByOrderID(db, facTradeSeq)
	if err != nil {
		return err
	}

	if order.PayStatus == constants.PaymentSuccess {
		return nil
	}

	var extend Extend
	err = json.Unmarshal([]byte(order.Extend), &extend)
	if err != nil {
		return err
	}

	var mycardConfig payment.MyCardPay
	converter.QueryByGameId(&mycardConfig, order.GameId)

	config := mycardConfig.MyCardConfigs.GetConfigByRegion(extend.Region)
	if config == nil {
		log.Error(ctx, "缺少該地區對應的mycard商戶信息", "order_id", order.OrderId)
		return errors.New("缺少該地區對應的mycard商戶信息")
	}

	if err = checkNotifyHash(v, config.Secret); err != nil {
		log.Error(ctx, "校验订单Hash失败", "error", err.Error(), "order_id", order.OrderId)
		return err
	}

	receipt := make(map[string]string)
	json.Unmarshal([]byte(order.Receipt), &receipt)

	authCode, ok := receipt["AuthCode"]
	if !ok {
		log.Error(ctx, "验证失败缺少AuthCode", "order_id", order.OrderId)
		return errors.New("验证失败缺少AuthCode")
	}
	client := mycard.NewClient(config.Secret, config.SandBoxMode)
	verifyResp, err := client.Verify(ctx, authCode)
	if err != nil {
		log.Error(ctx, "验证订单失败", "error", err.Error(), "order_id", order.OrderId)
		return err
	}

	if verifyResp.ReturnCode != mycard.ReqSuccess || verifyResp.PayResult != mycard.TradeSuccess {
		var returnMsg string
		var err error
		returnMsg, err = url.QueryUnescape(v.Get("ReturnMsg"))
		if err != nil {
			returnMsg = "未知異常"
		}
		log.Error(ctx, "订单支付失败", "error", returnMsg, "order_id", order.OrderId)
		return errors.New(returnMsg)
	}

	paymentConfirmResp, err := client.PaymentConfirm(ctx, authCode)
	if err != nil {
		return err
	}

	if paymentConfirmResp.ReturnCode != mycard.ReqSuccess {
		log.Error(ctx, "请款失败", "error", err.Error(), "order_id", order.OrderId)
		return errors.New(paymentConfirmResp.ReturnMsg)
	}

	receipt["ReturnCode"] = verifyResp.ReturnCode
	receipt["PayResult"] = verifyResp.PayResult
	receipt["FacTradeSeq"] = verifyResp.FacTradeSeq
	receipt["PaymentType"] = verifyResp.PaymentType
	receipt["Amount"] = verifyResp.Amount
	receipt["Currency"] = verifyResp.Currency
	receipt["MyCardTradeNo"] = verifyResp.MyCardTradeNo
	receipt["MyCardType"] = verifyResp.MyCardType
	receipt["PromoCode"] = verifyResp.PromoCode
	receipt["TradeSeq"] = paymentConfirmResp.TradeSeq
	data, _ := json.Marshal(receipt)

	purchaseRecord, err := models.GetPurchaseRecordByOrderId(db, order.OrderId)
	if err != nil {
		return errors.New("交易记录不存在！")
	}
	order.PayId = verifyResp.MyCardTradeNo
	order.Receipt = string(data)
	order.PayTime = time.Now()
	if purchaseRecord.PayStatus == constants.PaymentFailure {
		order.PayStatus = constants.PaymentFailure
		order.VerifyStatus = constants.VerifyFailed
		order.PayErrorMessage = "支付订单已取消"
		err = models.SaveOrder(db, order)
		if err != nil {
			return err
		}
		return errors.New("支付订单已取消")
	} else {
		purchaseRecord.PayStatus = constants.PaymentSuccess
		purchaseRecord.UpdateTime = time.Now()
		err = models.SavePurchaseRecord(db, purchaseRecord)
		if err != nil {
			return err
		}
		order.PayStatus = constants.PaymentSuccess
		order.VerifyStatus = constants.VerifyPassed
		order.PayTime = time.Now()
		err = models.SaveOrder(db, order)
		if err != nil {
			return err
		}
		services.Notify(ctx, db, order, services.NotifyOptions{})
	}
	return nil
}

func checkNotifyHash(values url.Values, secret string) error {
	hash := values.Get("ReturnCode") + values.Get("PayResult") + values.Get("FacTradeSeq") +
		values.Get("PaymentType") + values.Get("Amount") + values.Get("Currency") +
		values.Get("MyCardTradeNo") + values.Get("MyCardType") + values.Get("PromoCode") + secret
	hash = url.QueryEscape(hash)
	hash = strings.ToLower(hash)
	buff := sha256.Sum256([]byte(hash))
	hash = strings.ToLower(fmt.Sprintf("%x", buff))
	if hash != values.Get("Hash") {
		return errors.New("hash value invalid")
	}
	return nil
}
