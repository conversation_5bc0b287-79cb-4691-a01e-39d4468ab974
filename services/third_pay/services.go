package third_pay

import (
	"context"
	"crypto/sha256"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"zeus/common/conf"
	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	"zeus/schema/request"
	"zeus/services"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"github.com/robfig/cron/v3"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ThirdPayClient interface {
	CreateOrder(ctx context.Context, g *models.Game, v *request.ThirdPayOrder, o *models.Order) (map[string]interface{}, error)
	CreateOrderV2(ctx context.Context, g *models.Game, v *request.ThirdPayOrderV2, product *models.Product, o *models.Order, inWhiteList bool) (map[string]interface{}, error)
	CheckAndPaymentConfirmAndNotify(ctx context.Context, db *gorm.DB, values interface{}) error
	CheckAndPaymentConfirmAndNotifyV2(ctx context.Context, db *gorm.DB, values interface{}) error
}

func NewThirdPayClient(clientType int) ThirdPayClient {
	switch clientType {
	case constants.PayWayMyCard:
		return &MyCardPayClient{}
	case constants.PayWayGash:
		return &GashPayClient{}
	}
	return nil
}

type Extend struct {
	OrderId   string `json:"order_id"`
	ProductId string `json:"product_id"`
	RoleId    string `json:"role_id"`
	ServerId  string `json:"server_id"`
	Account   string `json:"account"`
	PayWay    string `json:"pay_way"`
	Region    string `json:"region,omitempty"`
}

type RoleInfo struct {
	ServerId          string `json:"server_id"`
	ServerName        string `json:"server_name"`
	RoleId            string `json:"role_id"`
	RoleName          string `json:"role_name"`
	Level             string `json:"level"`
	PreviewEnabled    bool   `json:"preview_enabled"`
	SandboxPayEnabled bool   `json:"sandbox_pay_enabled"`
}

type Services struct {
	db        *gorm.DB
	ProjectID string
}

func NewServices(c *gin.Context, projectID string) *Services {
	db := c.MustGet("db").(*gorm.DB)
	return &Services{
		db:        db,
		ProjectID: projectID,
	}
}

func (s *Services) GetRoleInfos(roleID string) ([]RoleInfo, error) {
	roleInfos := make([]RoleInfo, 0)
	roles, err := models.GetRoles(s.db, roleID)
	if err != nil {
		return roleInfos, err
	}

	for _, role := range roles {
		previewEnabled := false
		sandboxPayEnabled := false
		userInWhiteList, _ := models.GetWebPayWhiteListByRoleId(s.db, roleID, role.ServerId)
		if userInWhiteList.ID != 0 {
			previewEnabled = userInWhiteList.PreviewEnabled
			sandboxPayEnabled = userInWhiteList.SandboxPayEnabled

		}
		roleInfos = append(roleInfos, RoleInfo{
			ServerId:          role.ServerId,
			ServerName:        role.ServerName,
			RoleId:            role.RoleId,
			RoleName:          role.RoleName,
			Level:             strconv.Itoa(role.Level),
			PreviewEnabled:    previewEnabled,
			SandboxPayEnabled: sandboxPayEnabled,
		})
	}

	return roleInfos, nil
}

func (s *Services) CreateOrder(c *gin.Context, v *request.ThirdPayOrder) (map[string]interface{}, error) {
	role, err := models.GetRoleByServerIdAndRoleId(s.db, v.RoleID, v.ServerID)
	if err != nil {
		return nil, err
	}
	user, err := models.GetUserByID(s.db, role.UserID)
	if err != nil {
		return nil, err
	}
	dbGame, err := game.GetGameById(user.GameID)
	if err != nil {
		return nil, err
	}
	// 校验sign
	err = checkOrderSign(v, conf.Conf.ThirdPaySecret)
	if err != nil {
		return nil, err
	}

	// 构建订单
	var order models.Order
	order.OrderId = services.GenerateOrderID(v.PayWay, constants.OrderIDTokenSize)
	order.GameId = role.GameID
	order.Level = role.Level
	order.RoleName = role.RoleName
	order.RoleId = role.RoleId
	order.ServerId = role.ServerId
	order.ServerName = role.ServerName
	order.UserId = role.UserID
	order.Platform = role.Platform
	order.Vip = role.Vip
	order.Account = user.Account
	order.PayIp = c.ClientIP()
	order.PayWay = v.PayWay
	order.PayStatus = constants.PaymentUnpaid
	order.CreateTime = time.Now()
	order.ProductId = v.ProductID
	order.PayProductId = v.ProductID
	order.PayAmount = v.Price
	order.PayNotifyUrl = v.PayNotifyUrl
	order.PayCurrency = v.Currency
	order.Price = v.Price

	payClient := NewThirdPayClient(v.PayWay)
	if payClient == nil {
		return nil, errors.New("缺少对应的支付方式")
	}

	respData, err := payClient.CreateOrder(c.Request.Context(), dbGame, v, &order)
	if err != nil {
		return nil, err
	}

	err = models.CreateOrder(s.db, &order)
	return respData, err
}

func (s *Services) CreateOrderV2(c *gin.Context, v *request.ThirdPayOrderV2) (map[string]interface{}, error) {
	// 校验sign
	err := checkOrderSignV2(v, conf.Conf.ThirdPaySecret)
	if err != nil {
		return nil, err
	}
	payClient := NewThirdPayClient(v.PayWay)
	if payClient == nil {
		return nil, errors.New("缺少對應的支付方式")
	}

	product, err := models.GetProductById(s.db, v.ProductId)
	if err != nil {
		return nil, errors.New("商品不存在")
	}

	role, err := models.GetRoleByServerIdAndRoleId(s.db, v.RoleId, v.ServerId)
	if err != nil {
		return nil, err
	}
	user, err := models.GetUserByID(s.db, role.UserID)
	if err != nil {
		return nil, err
	}
	dbGame, err := game.GetGameById(user.GameID)
	if err != nil {
		return nil, err
	}

	userWhiteList, _ := models.GetWebPayWhiteListByRoleId(s.db, v.RoleId, v.ServerId)

	isSupportCumulativePurchase := false
	if v.ActivityId != 0 {
		activity, err := models.GetActivityById(s.db, v.ActivityId)
		if err != nil {
			return nil, errors.New("活動不存在")
		}
		isSupportCumulativePurchase = activity.IsSupportCumulativePurchase
		nowTime := time.Now()
		if activity.EndTime.Before(nowTime) {
			return nil, errors.New("活动已结束")
		}
		if activity.StartTime.After(nowTime) && !userWhiteList.PreviewEnabled {
			return nil, errors.New("活動不存在")
		}
		if activity.ActivityProducts.GetProduct(v.ProductId) == nil {
			return nil, errors.New("商品無效")
		}

		record, err := models.GetUnpaidRecord(
			s.db, v.RoleId, v.ServerId, v.ActivityId, v.ProductId,
		)
		if err == nil {
			if record.CreateTime.Before(time.Now().Add(-time.Second * constants.PaymentTimeout)) {
				record.PayStatus = constants.PaymentFailure
				_ = models.SavePurchaseRecord(s.db, record)
				o, err := models.GetOrderByOrderID(s.db, record.OrderId)
				if err == nil {
					o.PayStatus = constants.PaymentFailure
					_ = models.SaveOrder(s.db, o)
				}
			} else {
				return nil, errors.New("訂單正在交易中，請稍後再試")
			}
		}
		count, err := models.GetRoleActivityProductPurchaseCount(
			s.db, v.RoleId, v.ServerId, v.ActivityId, v.ProductId,
		)
		if err != nil {
			return nil, err
		}
		productLimits := activity.ActivityProducts.GetProduct(v.ProductId).Limit
		if productLimits != 0 && count >= productLimits {
			return nil, errors.New("商品已售罄")
		}

	}

	// 构建订单
	var order models.Order
	order.OrderId = services.GenerateOrderID(v.PayWay, constants.OrderIDTokenSize)
	order.GameId = role.GameID
	order.Level = role.Level
	order.RoleName = role.RoleName
	order.RoleId = role.RoleId
	order.ServerId = role.ServerId
	order.ServerName = role.ServerName
	order.UserId = role.UserID
	order.Platform = role.Platform
	order.Vip = role.Vip
	order.Account = user.Account
	order.PayIp = c.ClientIP()
	order.PayWay = v.PayWay
	order.PayStatus = constants.PaymentUnpaid
	order.CreateTime = time.Now()
	order.ProductId = product.GameProductId
	order.PayProductId = product.GameProductId
	order.PayAmount = product.Prices.GetPrice(v.Currency)
	order.PayNotifyUrl = v.PayNotifyUrl
	order.PayCurrency = v.Currency
	order.Price = product.Prices.GetPrice(v.Currency)

	respData, err := payClient.CreateOrderV2(c.Request.Context(), dbGame, v, product, &order, userWhiteList.SandboxPayEnabled)
	if err != nil {
		return nil, err
	}

	if err = models.CreateOrder(s.db, &order); err != nil {
		return nil, err
	}

	points := 0
	currentPointStrategyRecordID := 0
	if isSupportCumulativePurchase == true {
		currentPointStrategyRecord, err := models.GetCurrentWebPayPointStrategyRecord(s.db)
		if err == nil {
			currentPointStrategyRecordID = currentPointStrategyRecord.Id
		}
		points = product.Points
	}
	if err = models.CreatePurchaseRecord(s.db, &models.PurchaseRecord{
		OrderId:               order.OrderId,
		RoleId:                v.RoleId,
		ServerId:              v.ServerId,
		ActivityId:            v.ActivityId,
		ProductId:             v.ProductId,
		PayStatus:             constants.PaymentUnpaid,
		Points:                points,
		PointStrategyRecordId: currentPointStrategyRecordID,
	}); err != nil {
		return nil, err
	}

	return respData, nil
}

func (s *Services) Notify(ctx context.Context, payWay int, v interface{}) error {
	payClient := NewThirdPayClient(payWay)
	if payClient == nil {
		return errors.New("不支援的支付方式")
	}
	return payClient.CheckAndPaymentConfirmAndNotify(ctx, s.db, v)
}

func (s *Services) NotifyV2(ctx context.Context, payWay int, v interface{}) error {
	payClient := NewThirdPayClient(payWay)
	if payClient == nil {
		return errors.New("不支援的支付方式")
	}
	return payClient.CheckAndPaymentConfirmAndNotifyV2(ctx, s.db, v)
}

func (s *Services) Query(v *request.ThirdPayQuery) ([]models.Order, error) {
	orders := make([]models.Order, 0)
	if v.PayID != "" {
		order, err := models.GetOrderByPayIDAndPayStatus(s.db, v.PayID, constants.PaymentSuccess)
		if err == nil {
			orders = append(orders, *order)
			return orders, nil
		}
	}
	if v.StartDateTime != "" {
		return models.GetOrdersByTime(s.db, v.PayWay, constants.PaymentSuccess, v.StartDateTime, v.EndDateTime)
	}
	return orders, nil
}

func checkOrderSign(v *request.ThirdPayOrder, secret string) error {
	nowTime := time.Now().Unix()
	timestamp, _ := strconv.ParseInt(v.Timestamp, 10, 64)
	c := nowTime - timestamp
	if c > constants.OrderHashTimeout || c < 0 {
		return errors.New("order timeout")
	}

	sign := v.ProjectID + v.RoleID + v.ProductID + v.ProductName + strconv.FormatFloat(v.Price, 'f', 2, 64) +
		v.Currency + v.PayNotifyUrl + v.PayReturnUrl + v.Timestamp + secret
	sign = url.QueryEscape(sign)
	sign = strings.ToLower(sign)
	buff := sha256.Sum256([]byte(sign))
	sign = strings.ToLower(fmt.Sprintf("%x", buff))
	if sign != v.Sign {
		return errors.New("sign value invalid")
	}
	return nil
}

func checkOrderSignV2(v *request.ThirdPayOrderV2, secret string) error {
	nowTime := time.Now().Unix()
	timestamp, _ := strconv.ParseInt(v.Timestamp, 10, 64)
	c := nowTime - timestamp
	if c > constants.OrderHashTimeout || c < 0 {
		return errors.New("order timeout")
	}

	sign := v.ProjectId + v.RoleId + strconv.Itoa(v.ProductId) + strconv.Itoa(v.ActivityId) +
		v.Currency + v.PayNotifyUrl + v.PayReturnUrl + v.Timestamp + secret
	sign = url.QueryEscape(sign)
	sign = strings.ToLower(sign)
	buff := sha256.Sum256([]byte(sign))
	sign = strings.ToLower(fmt.Sprintf("%x", buff))
	if sign != v.Sign {
		return errors.New("sign value invalid")
	}
	return nil
}

func GetGashOrdersOneHourAgo(db *gorm.DB) ([]models.Order, error) {
	nowTime := time.Now()
	start := nowTime.Add(-(2 * time.Hour)).Format("2006-01-02 15:00:00")
	end := nowTime.Add(-time.Hour).Format("2006-01-02 15:00:00")
	return models.GetOrdersByTime(db, constants.PayWayGash, constants.PaymentUnpaid, start, end)
}

func fixGashOrders(ctx context.Context, db *gorm.DB, dbGame models.Game, orders []models.Order) {
	client := GashPayClient{}
	fixCount := 0
	for i := range orders {
		if err := client.FixOrder(orders[i].GameId, &orders[i]); err == nil {
			_ = models.SaveOrder(db, &orders[i])
			fixCount++
		}
	}
	log.Debug(ctx, "gash自动补单结果统计", "game", dbGame.Name, "fix_count", fixCount)
}

func FixGashOrder(ctx context.Context) {
	games := game.GetGames()
	for _, g := range games {
		if g.ProjectID == 0 {
			continue
		}

		var gashConfig payment.GashPay
		converter.QueryByGameId(&gashConfig, g.Id)

		if *gashConfig.OpenStatus == 0 {
			continue
		}

		db, _ := services.GetGameDBByProjectId(g.ProjectID)
		orders, _ := GetGashOrdersOneHourAgo(db)
		go fixGashOrders(ctx, db, g, orders)
	}
}

func CronFixGashOrderTask(c *cron.Cron) {
	spec := "0 * * * *" // 每1小时执行一次
	c.AddFunc(spec, func() {
		FixGashOrder(context.Background())
	})
}
