package services

import (
	"context"
	"errors"
	"net/http"
	"time"

	"zeus/common"
	"zeus/common/constants"
	"zeus/common/log"
	"zeus/models"
	"zeus/pkg/apple"
	zeusHttp "zeus/pkg/http"
	"zeus/schema"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/thirdlogin"

	"github.com/gin-gonic/gin"

	"github.com/robfig/cron/v3"
	"gorm.io/gorm"
)

type UnregisterServices struct {
	gameDB *gorm.DB
}

func InitUnregisterServices() *UnregisterServices {
	s := &UnregisterServices{}
	s.gameDB = common.GetInfoDB()
	return s
}

func (s *UnregisterServices) CreateOrUpdateUnregister(c *gin.Context, gameId int, account string, userStatus int) error {
	args := map[string]interface{}{
		"account": account,
		"game_id": gameId,
	}

	ac := models.GetUnregister(s.gameDB, args)
	ac.GameId = gameId
	ac.Account = account
	if ac.Status == constants.UnregisterStatusDeleted {
		return nil
	}
	db := c.MustGet("db").(*gorm.DB)
	user, err := models.GetUserByAccount(db, account)
	if err != nil {
		return err
	}
	user.Status = userStatus
	err = models.SaveUser(db, user)
	if err != nil {
		return err
	}

	ac.Status = constants.UnregisterStatusCoolingOffPeriod
	ac.CancellationTime = time.Now()
	err = models.SaveUnregister(s.gameDB, ac)
	return err
}

func (s *UnregisterServices) CreateOrUpdateUnregisterWithAppid(c *gin.Context, appid, account string) error {
	g, _ := game.GetGameByAppid(appid)
	return s.CreateOrUpdateUnregister(c, g.Id, account, constants.UserStatusDeleting)
}

func (s *UnregisterServices) RevokeAppleToken(ctx context.Context, appid, token string) error {
	var config thirdlogin.ThirdLoginApple
	converter.QueryByAppid(&config, appid)

	appleClient := apple.NewAuthClient(*config.AppleISS, *config.AppleBundleId, *config.AppleKeyID, *config.AppleSecret)
	resp, err := appleClient.RevokeToken(ctx, token)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return errors.New("调用苹果服务异常")
	}
	return nil
}

func (s *UnregisterServices) WithdrawUnregisterNotify(ctx context.Context) {
	accounts := models.GetUnregisterByStatus(s.gameDB, constants.UnregisterStatusCoolingOffPeriod)
	for _, a := range accounts {
		dbGame, _ := game.GetGameById(a.GameId)
		gdb, err := GetGameDBByProjectId(dbGame.ProjectID)
		var config thirdlogin.ThirdLogin
		converter.QueryByGameId(&config, a.GameId)

		if err != nil {
			log.Error(ctx, "database connection failed", "game", dbGame.Name)
			return
		}
		user, _ := models.GetUserByAccount(gdb, a.Account)
		if a.CancellationTime.Unix() > user.LoginTime.Unix() {
			continue
		}
		s.withdrawUnregisterNotify(&a, *config.CancelGameServerAddr, user)
	}
}

func (s *UnregisterServices) withdrawUnregisterNotify(a *models.Unregister, notifyUrl string, user *models.User) {
	var data WithdrawUnregisterResp
	data.Account = user.Account
	data.LoginTime = schema.DateTime{Time: user.LoginTime}
	var err error
	if notifyUrl != "" {
		err = s.sendNotify(notifyUrl, data)
	}
	a.ActualProcessingTime = time.Now()
	if err != nil {
		a.Status = constants.UnregisterStatusProcessingFailed
		_ = models.SaveUnregister(s.gameDB, a)
	} else {
		a.Status = constants.UnregisterStatusDeletedDeleteCancel
		_ = models.SaveUnregister(s.gameDB, a)
	}
}

func (s *UnregisterServices) UnregisterNotify(ctx context.Context) {
	t := time.Now().AddDate(0, 0, -CoolingOffPeriod)
	y, m, d := t.Date()
	t = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)
	accounts := models.GetUnregisterByStatusAndTime(s.gameDB, constants.UnregisterStatusCoolingOffPeriod, t)
	for _, a := range accounts {
		dbGame, _ := game.GetGameById(a.GameId)
		var config thirdlogin.ThirdLogin
		converter.QueryByGameId(&config, dbGame.Id)

		gdb, err := GetGameDBByProjectId(dbGame.ProjectID)
		if err != nil {
			log.Error(ctx, "database connection failed", "game", dbGame.Name)
			return
		}
		user, _ := models.GetUserByAccount(gdb, a.Account)
		if a.CancellationTime.Unix() < user.LoginTime.Unix() {
			continue
		}
		s.unregisterNotify(ctx, gdb, &a, *config.CancelGameServerAddr, user)
	}
}

func (s *UnregisterServices) UnregisterNotifyWithAccount(ctx context.Context, gameId int, account string) {
	dbGame, _ := game.GetGameById(gameId)
	var config thirdlogin.ThirdLogin
	converter.QueryByGameId(&config, dbGame.Id)

	args := map[string]interface{}{
		"account": account,
		"game_id": gameId,
	}
	ac := models.GetUnregister(s.gameDB, args)

	gdb, err := GetGameDBByProjectId(dbGame.ProjectID)
	if err != nil {
		log.Error(ctx, "database connection failed", "game", dbGame.Name)
		return
	}
	user, _ := models.GetUserByAccount(gdb, account)
	s.unregisterNotify(ctx, gdb, ac, *config.CancelGameServerAddr, user)
}

func (s *UnregisterServices) unregisterNotify(ctx context.Context, db *gorm.DB, a *models.Unregister, notifyUrl string, user *models.User) {
	// 通知游戏服务
	var data UnregisterResp
	data.Account = user.Account
	var errA error
	if notifyUrl != "" {
		errA = s.sendNotify(notifyUrl, data)
		if errA != nil {
			log.Error(ctx, "通知游戏服务失败。 msg: ", errA)
		}
	}

	// 将账号标记成删除
	user.IsDeleted = true
	user.Status = constants.UserStatusDeleted
	errB := models.SaveUser(db, user)
	if errB != nil {
		log.Error(ctx, "删除用户失败。 msg: ", errB)
	}

	// 解除所有绑定关系
	errC := models.ReleaseAllBindings(db, user.ID)
	if errC != nil {
		log.Error(ctx, "解除三方绑定失败。msg: ", errC)
	}

	if errA == nil && errB == nil && errC == nil {
		a.Status = constants.UnregisterStatusDeleted
	} else {
		a.Status = constants.UnregisterStatusProcessingFailed
	}
	a.ActualProcessingTime = time.Now()
	_ = models.SaveUnregister(s.gameDB, a)
}

func (s *UnregisterServices) RetryFailNotify(ctx context.Context) {
	accounts := models.GetUnregisterByStatus(s.gameDB, constants.UnregisterStatusProcessingFailed)
	for _, a := range accounts {
		dbGame, _ := game.GetGameById(a.GameId)
		var config thirdlogin.ThirdLogin
		converter.QueryByGameId(&config, dbGame.Id)

		gdb, err := GetGameDBByProjectId(dbGame.ProjectID)
		if err != nil {
			log.Error(ctx, "database connection failed", "game", dbGame.Name)
			return
		}
		user, _ := models.GetUserByAccount(gdb, a.Account)
		if a.CancellationTime.Unix() < user.LoginTime.Unix() {
			s.withdrawUnregisterNotify(&a, *config.CancelGameServerAddr, user)
		} else {
			s.unregisterNotify(ctx, gdb, &a, *config.GameServerAddr, user)
		}
	}
}

func (s *UnregisterServices) sendNotify(url string, value any) error {
	resp, err := zeusHttp.Post(url, value)
	if err != nil {
		return err
	}
	if resp.StatusCode != http.StatusOK {
		err = errors.New("请求游戏服务失败")
		return err
	}
	return nil
}

type WithdrawUnregisterResp struct {
	Account   string
	LoginTime schema.DateTime
}

type UnregisterResp struct {
	Account string
}

const CoolingOffPeriod = 7

func CronUnregisterTask(c *cron.Cron) {
	acs := InitUnregisterServices()

	spec30min := "30 * * * *"  // 每个小时的30分钟执行
	spec := "10 0 * * *"       // 每天的0点10分执行
	spec1hour := "0 */1 * * *" // 每小时一次
	c.AddFunc(spec30min, func() {
		acs.WithdrawUnregisterNotify(context.Background())
	})

	c.AddFunc(spec, func() {
		acs.UnregisterNotify(context.Background())
	})

	c.AddFunc(spec1hour, func() {
		acs.RetryFailNotify(context.Background())
	})
}
