package services

import (
	"context"
	"errors"
	"net/http"
	"reflect"
	"testing"
	"time"

	"zeus/common"
	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/apple"
	zeusHttp "zeus/pkg/http"
	"zeus/services/game"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/thirdlogin"

	"github.com/gin-gonic/gin"

	. "github.com/agiledragon/gomonkey"
	"github.com/robfig/cron/v3"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestUnregisterServices(t *testing.T) {
	ApplyFunc(common.GetInfoDB, func() *gorm.DB {
		return &gorm.DB{}
	})

	ApplyFunc(game.GetGameByAppid, func(_ string) (*models.Game, error) {
		return &models.Game{}, nil
	})

	ApplyFunc(GetGameDBByProjectId, func(_ int) (*gorm.DB, error) {
		return &gorm.DB{}, nil
	})

	ApplyFunc(models.GetUserByAccount, func(_ *gorm.DB, _ string) (*models.User, error) {
		user := &models.User{}
		user.LoginTime = time.Date(2023, 1, 10, 0, 0, 0, 0, time.UTC)
		return user, nil
	})
	ApplyFunc(models.SaveUnregister, func(db *gorm.DB, args *models.Unregister) error {
		return nil
	})

	ApplyFunc(game.GetGameById, func(id int) (*models.Game, error) {
		return &models.Game{Id: 1, ProjectID: 1}, nil
	})

	ApplyFunc(zeusHttp.Post, func(u string, paramsData any) (resp *http.Response, err error) {
		return &http.Response{}, nil
	})

	ApplyFunc(models.DeleteRoleByUserId, func(db *gorm.DB, userId int) error {
		return nil
	})
	ApplyFunc(models.SaveUser, func(db *gorm.DB, args *models.User) error {
		return nil
	})
	ApplyFunc(models.ReleaseAllBindings, func(db *gorm.DB, userId int) error {
		return nil
	})

	c := &gin.Context{}
	c.Set("db", &gorm.DB{})

	acs := InitUnregisterServices()

	QueryMockFunc := ApplyFunc(converter.QueryByGameId, func(config schemas.BaseConfiger, _ int) {
		thirdLoginConfig := config.(*thirdlogin.ThirdLogin)
		cancelGameServerAddr := ""
		thirdLoginConfig.CancelGameServerAddr = &cancelGameServerAddr
		gameServerAddr := ""
		thirdLoginConfig.GameServerAddr = &gameServerAddr
	})
	defer QueryMockFunc.Reset()

	Convey("Unregister", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetUnregister, func(db *gorm.DB, args map[string]interface{}) *models.Unregister {
				return &models.Unregister{}
			})
			gameId := 119
			account := "test_account"
			output := acs.CreateOrUpdateUnregister(c, gameId, account, constants.UserStatusDeleting)
			So(output, ShouldEqual, nil)
		})

		Convey("has unregister", func() {
			ApplyFunc(models.GetUnregister, func(db *gorm.DB, args map[string]interface{}) *models.Unregister {
				return &models.Unregister{Status: constants.UnregisterStatusDeleted}
			})
			gameId := 119
			account := "test_account"
			output := acs.CreateOrUpdateUnregister(c, gameId, account, constants.UserStatusDeleting)
			So(output, ShouldEqual, nil)
		})
	})

	Convey("RevokeAppleToken", t, func() {
		ApplyFunc(apple.GenerateJWTToken, func(_, _, _, _, _ string) string {
			return "test_jwt_token"
		})

		QueryMockFunc := ApplyFunc(converter.QueryByAppid, func(config schemas.BaseConfiger, _ string) {
			switch config.GetType() {
			case "ThirdLoginApple":
				thirdLoginConfig := config.(*thirdlogin.ThirdLoginApple)
				openStatus := 1
				thirdLoginConfig.OpenStatus = &openStatus
				appleISS := "test_apple_iss"
				thirdLoginConfig.AppleISS = &appleISS
				appleBundleId := "test_apple_iss"
				thirdLoginConfig.AppleBundleId = &appleBundleId
				appleKeyID := "test_apple_key_id"
				thirdLoginConfig.AppleKeyID = &appleKeyID
				appleSecret := "test_apple_secret"
				thirdLoginConfig.AppleSecret = &appleSecret
			}
		})
		defer QueryMockFunc.Reset()

		Convey("one func for succ", func() {
			ApplyMethod(reflect.TypeOf(&apple.AuthClient{}), "RevokeToken", func(_ *apple.AuthClient, _ context.Context, _ string) (*http.Response, error) {
				return &http.Response{StatusCode: http.StatusOK}, nil
			})
			output := acs.RevokeAppleToken(context.Background(), "test_appid", "test_token")
			So(output, ShouldBeNil)
		})

		Convey("RevokeToken Has Error", func() {
			ApplyMethod(reflect.TypeOf(&apple.AuthClient{}), "RevokeToken", func(_ *apple.AuthClient, _ context.Context, _ string) (*http.Response, error) {
				return nil, errors.New("RevokeToken Has Error")
			})
			output := acs.RevokeAppleToken(context.Background(), "test_appid", "test_token")
			So(output, ShouldNotBeNil)
		})

		Convey("RevokeToken response statusCode is not 200", func() {
			ApplyMethod(reflect.TypeOf(&apple.AuthClient{}), "RevokeToken", func(_ *apple.AuthClient, _ context.Context, _ string) (*http.Response, error) {
				return &http.Response{StatusCode: http.StatusBadRequest}, nil
			})
			output := acs.RevokeAppleToken(context.Background(), "test_appid", "test_token")
			So(output, ShouldNotBeNil)
		})
	})

	Convey("WithdrawUnregisterNotify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetUnregisterByStatus, func(db *gorm.DB, status int) []models.Unregister {
				data := []models.Unregister{
					{CancellationTime: time.Date(2023, 1, 9, 0, 0, 0, 0, time.UTC)},
					{CancellationTime: time.Date(2023, 1, 11, 0, 0, 0, 0, time.UTC)},
				}
				return data
			})
			ctx := context.Background()
			acs.WithdrawUnregisterNotify(ctx)
		})

		Convey("GetGameDBByProjectId Failed", func() {
			ApplyFunc(models.GetUnregisterByStatus, func(db *gorm.DB, status int) []models.Unregister {
				data := []models.Unregister{{CancellationTime: time.Date(2023, 1, 9, 0, 0, 0, 0, time.UTC)}}
				return data
			})

			ApplyFunc(GetGameDBByProjectId, func(_ int) (*gorm.DB, error) {
				return &gorm.DB{}, errors.New("GetGameDBByProjectId Has Error")
			})
			ctx := context.Background()
			acs.WithdrawUnregisterNotify(ctx)
		})
	})

	Convey("UnregisterNotify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetUnregisterByStatusAndTime, func(_ *gorm.DB, _ int, _ time.Time) []models.Unregister {
				var data []models.Unregister
				accountCancellation := models.Unregister{}
				accountCancellation.CancellationTime = time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
				data = append(data, accountCancellation)
				return data
			})
		})
		acs.UnregisterNotify(context.Background())
	})

	Convey("RetryFailNotify", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetUnregisterByStatus, func(_ *gorm.DB, _ int) []models.Unregister {
				var data []models.Unregister
				accountCancellation := models.Unregister{}
				accountCancellation.CancellationTime = time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
				data = append(data, accountCancellation)
				accountCancellation1 := models.Unregister{}
				accountCancellation1.CancellationTime = time.Date(2023, 1, 10, 0, 0, 0, 0, time.UTC)
				data = append(data, accountCancellation1)
				return data
			})
		})
		ctx := context.Background()

		acs.RetryFailNotify(ctx)
	})

	Convey("CronUnregisterTask", t, func() {
		Convey("one func for succ", func() {
			secondParser := cron.NewParser(cron.SecondOptional | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
			c := cron.New(cron.WithParser(secondParser), cron.WithChain())
			CronUnregisterTask(c)
		})
	})
}
