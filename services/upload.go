package services

import (
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"zeus/common/conf"
)

func UploadFileToOSS(filePath string, fileName string) (string, error) {
	acceleratedDomain := conf.Conf.StaticTarget.AcceleratedDomain
	endpoint := conf.Conf.StaticTarget.EndPoint
	accessKeyID := conf.Conf.StaticTarget.AccessKeyID
	accessKeySecret := conf.Conf.StaticTarget.AccessKeySecret
	bucketName := conf.Conf.StaticTarget.BucketName

	// 创建OSS客户端
	client, err := oss.New(endpoint, accessKeyID, accessKeySecret)
	if err != nil {
		return "", err
	}

	// 获取存储空间
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		return "", err
	}

	// 上传文件到 OSS
	objectKey := "web_pay_pictures/" + fileName
	err = bucket.PutObjectFromFile(objectKey, filePath)
	if err != nil {
		return "", err
	}
	var ossURL string
	if acceleratedDomain != "" {
		ossURL = fmt.Sprintf("https://%s/%s", acceleratedDomain, objectKey)
	} else {
		ossURL = fmt.Sprintf("https://%s.%s/%s", bucketName, endpoint, objectKey)
	}

	return ossURL, nil
}
