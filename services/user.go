package services

import (
	"math/rand"
	"strconv"
	"time"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/mapper"
	"zeus/services/game"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func GenerateAccountId(c *gin.Context) string {
	db := c.MustGet("db").(*gorm.DB)
	for {
		rand.Seed(time.Now().UnixNano())
		accountId := rand.Intn(**********)
		args := map[string]interface{}{"account": strconv.Itoa(accountId)}
		user, _ := models.GetOneUser(db, args)
		if user.ID == 0 {
			return strconv.Itoa(accountId)
		}
	}
}

func QueryOneUser(c *gin.Context, args map[string]interface{}) (*models.User, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetOneUser(db, args)
}

func GetUserByUserID(c *gin.Context, userID interface{}) (*models.User, error) {
	db := c.MustGet("db").(*gorm.DB)
	gameID := c.MustGet("gameID")
	args := map[string]interface{}{
		"id":      userID,
		"game_id": gameID,
	}
	return models.GetOneUser(db, args)
}

func GetProjectUserByUserID(c *gin.Context, userID interface{}) (*models.User, error) {
	db := c.MustGet("db").(*gorm.DB)
	args := map[string]interface{}{
		"id": userID,
	}
	return models.GetOneUser(db, args)
}

func GetUserByGame(c *gin.Context, bindUserIDs []int, userIDs []int, filter map[string]interface{}, pageSize int, pageNum int, loginTime [2]string, registerTime [2]string) ([]models.User, int64, error) {
	db := c.MustGet("db").(*gorm.DB)
	users, total, err := models.GetUsersByPage(db, pageSize, pageNum, bindUserIDs, userIDs, filter, loginTime, registerTime)
	return users, total, err
}

func SaveUser(c *gin.Context, user *models.User) error {
	db := c.MustGet("db").(*gorm.DB)
	return models.SaveUser(db, user)
}

func SaveUserDevice(c *gin.Context, userDevice *models.UserDevice) error {
	db := c.MustGet("db").(*gorm.DB)
	return models.SaveUserDevice(db, userDevice)
}

func CheckLoginDevice(c *gin.Context, user *models.User, device string) bool {
	db := c.MustGet("db").(*gorm.DB)
	_, err := models.GetUserDeviceByUserID(db, user.ID, device)
	return err == nil
}

func RecordLoginDevice(c *gin.Context, user *models.User, device string) {
	db := c.MustGet("db").(*gorm.DB)
	userDevice, _ := models.GetUserDeviceByUserID(db, user.ID, device)
	if userDevice.ID == 0 {
		ud := models.UserDevice{
			UserID: user.ID,
			Device: device,
		}
		models.SaveUserDevice(db, &ud)
	}
}

type UserServices struct {
	APPID      string `json:"appid"`
	Device     string `json:"device"`
	Platform   string `json:"platform"`
	MobileInfo string `json:"mobile_info"`
	Email      string `json:"email"`
	Phone      string `json:"phone"`
	Password   string `json:"password"`
	Name       string `json:"name"`
}

func (us *UserServices) Map(u interface{}) {
	mapper.Map(us, u)
}

func (us *UserServices) CreateUser(c *gin.Context) (*models.User, error) {
	db := c.MustGet("db").(*gorm.DB)
	dbUser := models.User{}
	dbGame, _ := game.GetGameByAppid(us.APPID)
	dbUser.Account = GenerateAccountId(c)
	dbUser.Platform = us.Platform
	if us.Password != "" {
		dbUser.PwMd5 = us.Password
	} else {
		dbUser.PwMd5 = constants.DefaultPassword
	}
	dbUser.GameID = dbGame.Id
	dbUser.RegisterTime = time.Now()
	dbUser.RegisterIP = c.ClientIP()
	dbUser.Token = GenerateToken()
	dbUser.RegisterDevice = us.Device
	dbUser.MobileInfo = us.MobileInfo
	dbUser.LoginTime = time.Now()
	dbUser.Email = us.Email
	dbUser.Phone = us.Phone
	dbUser.Name = us.Name
	err := models.CreateUser(db, &dbUser)

	RecordLoginDevice(c, &dbUser, us.Device)

	return &dbUser, err
}

func CreateUser(c *gin.Context, user *models.User) error {
	db := c.MustGet("db").(*gorm.DB)
	return models.CreateUser(db, user)
}

func GenerateToken() string {
	letters := []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
	rand.Seed(time.Now().UnixNano())

	b := make([]rune, 32)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

func GetUserByIDs(c *gin.Context, ids []int) ([]models.User, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetUserByIDs(db, ids)
}
