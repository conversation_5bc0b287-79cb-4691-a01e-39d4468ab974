package services

import (
	"errors"
	"math/rand"
	"reflect"
	"testing"

	"zeus/models"
	"zeus/services/game"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestUser(t *testing.T) {
	c := &gin.Context{}
	c.Set("db", &gorm.DB{})
	c.Set("gameID", "1")

	<PERSON>vey("GenerateAccountId", t, func() {
		Convey("one func for succ", func() {
			getUser := ApplyFunc(models.GetOneUser, func(_ *gorm.DB, _ map[string]interface{}) (*models.User, error) {
				return &models.User{}, nil
			})
			defer getUser.Reset()
			randIntn := ApplyFunc(rand.Intn, func(n int) int {
				return 1111
			})
			defer randIntn.Reset()

			output := GenerateAccountId(c)
			expected := "1111"
			So(output, ShouldEqual, expected)
		})
	})

	<PERSON>vey("QueryOneUser", t, func() {
		Convey("one func for succ", func() {
			getUser := ApplyFunc(models.GetOneUser, func(_ *gorm.DB, _ map[string]interface{}) (*models.User, error) {
				return &models.User{ID: 1111}, nil
			})
			defer getUser.Reset()

			args := map[string]interface{}{
				"id": 1111,
			}
			output, _ := QueryOneUser(c, args)
			expected := 1111
			So(output.ID, ShouldEqual, expected)
		})
	})

	Convey("GetUserByUserID", t, func() {
		Convey("one func for succ", func() {
			getUser := ApplyFunc(models.GetOneUser, func(_ *gorm.DB, _ map[string]interface{}) (*models.User, error) {
				return &models.User{ID: 1111}, nil
			})
			defer getUser.Reset()

			userID := 1111
			output, _ := GetUserByUserID(c, userID)
			expected := 1111
			So(output.ID, ShouldEqual, expected)
		})
	})

	Convey("GenerateToken", t, func() {
		Convey("one func for succ", func() {
			randIntn := ApplyFunc(rand.Intn, func(n int) int {
				return 0
			})
			defer randIntn.Reset()

			output := GenerateToken()
			expected := "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
			So(output, ShouldEqual, expected)
		})
	})

	Convey("GetUserByGame", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetUsersByPage, func(db *gorm.DB, _ int, _ int, _ []int, _ []int, _ map[string]interface{}, _ [2]string, _ [2]string) ([]models.User, int64, error) {
				return nil, 0, nil
			})
			users, total, err := GetUserByGame(c, []int{0}, []int{1}, map[string]interface{}{}, 0, 0, [2]string{}, [2]string{})
			So(users, ShouldBeNil)
			So(total, ShouldEqual, 0)
			So(err, ShouldBeNil)
		})
	})

	Convey("SaveUser", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.SaveUser, func(db *gorm.DB, args *models.User) error {
				return nil
			})
			user := &models.User{}

			output := SaveUser(c, user)
			So(output, ShouldEqual, nil)
		})
	})

	Convey("SaveUserDevice", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.SaveUserDevice, func(db *gorm.DB, _ *models.UserDevice) error {
				return nil
			})
			userDevice := &models.UserDevice{}

			output := SaveUserDevice(c, userDevice)
			So(output, ShouldBeNil)
		})
	})

	Convey("CheckLoginDevice", t, func() {
		Convey("one func for succ", func() {
			func1 := ApplyFunc(models.GetUserDeviceByUserID, func(db *gorm.DB, _ int, _ string) (*models.UserDevice, error) {
				return nil, nil
			})
			defer func1.Reset()
			user := &models.User{}
			output := CheckLoginDevice(c, user, "device")
			So(output, ShouldBeTrue)
		})

		Convey("one func for false", func() {
			func1 := ApplyFunc(models.GetUserDeviceByUserID, func(db *gorm.DB, _ int, _ string) (*models.UserDevice, error) {
				return nil, errors.New("device not found")
			})
			defer func1.Reset()
			user := &models.User{}
			output := CheckLoginDevice(c, user, "device")
			So(output, ShouldBeFalse)
		})
	})

	Convey("CreateUser", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.CreateUser, func(db *gorm.DB, args *models.User) error {
				return nil
			})
			user := &models.User{}

			output := CreateUser(c, user)
			So(output, ShouldEqual, nil)
		})
	})
}

func TestUserServices(t *testing.T) {
	c := &gin.Context{}
	c.Set("db", &gorm.DB{})

	ApplyMethod(reflect.TypeOf(c), "ClientIP", func(_ *gin.Context) string {
		return "127.0.0.1"
	})

	Convey("UserServices", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.CreateUser, func(db *gorm.DB, args *models.User) error {
				return nil
			})
			ApplyFunc(game.GetGameByAppid, func(appid string) (*models.Game, error) {
				return &models.Game{}, nil
			})
			ApplyFunc(GenerateAccountId, func(c *gin.Context) string {
				return "2222"
			})

			ApplyFunc(GenerateToken, func() string {
				return "aaaa"
			})
			ApplyFunc(RecordLoginDevice, func(_ *gin.Context, _ *models.User, _ string) {
			})

			us := UserServices{}
			us.Map(us)

			user, _ := us.CreateUser(c)

			output := user.Token
			expected := "aaaa"
			So(output, ShouldEqual, expected)
		})
	})
}
