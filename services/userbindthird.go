package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"zeus/common/constants"
	"zeus/models"
	"zeus/services/settings/store"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func QueryBindThird(c *gin.Context, filter map[string]interface{}) (*models.UserBindThird, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetOneBindThird(db, filter)
}

func QueryAllBindThird(c *gin.Context, filter map[string]interface{}) ([]models.UserBindThird, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetAllBindThird(db, filter)
}

func QueryAllBindThirdWithAreaCode(c *gin.Context, userID int64) []models.UserBindThirdWithAreaCode {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetAllBindThirdWithAreaCode(db, userID)
}

func GetUserWithThirdBindAccount(c *gin.Context, unionID string, _type, gameID int) (models.Users, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetUserByUnionIDAndBindThirdTypeAndBindThirdStatus(db, unionID, _type, constants.BoundStatus, gameID)
}

func GetUserWithThirdBindAccountByOpenId(c *gin.Context, openID string, _type, gameID int) (models.Users, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetUserByOpenIDAndBindThirdTypeAndBindThirdStatus(db, openID, _type, constants.BoundStatus, gameID)
}

func SaveBindThird(c *gin.Context, bindThird *models.UserBindThird) error {
	db := c.MustGet("db").(*gorm.DB)
	return models.SaveBindThird(db, bindThird)
}

type BindList struct {
	IsFbBind     string `json:"is_fb_bind,omitempty"`
	IsGcBind     string `json:"is_gc_bind,omitempty"`
	IsGpBind     string `json:"is_gp_bind,omitempty"`
	IsTwBind     string `json:"is_tw_bind,omitempty"`
	IsLineBind   string `json:"is_line_bind,omitempty"`
	IsGiBind     string `json:"is_gi_bind,omitempty"`
	IsAiBind     string `json:"is_ai_bind,omitempty"`
	IsMobileBind string `json:"is_mobile_bind,omitempty"`
	IsQqBind     string `json:"is_qq_bind,omitempty"`
	IsWechatBind string `json:"is_wechat_bind,omitempty"`
}

func GetBindInfoByUserID(c *gin.Context, userID string) map[string]string {
	db := c.MustGet("db").(*gorm.DB)
	userBindTypeList := make([]int, 12, 20)

	db.Model(&models.UserBindThird{}).
		Where("user_id = ?", userID).
		Where("status = ?", constants.BoundStatus).
		Pluck("type", &userBindTypeList)

	result := make(map[string]string)
	for _, b := range userBindTypeList {
		if v, ok := store.ThirdLoginSwitchMapping[b]; ok {
			result[fmt.Sprintf("is_%s_bind", v.GetAbbrName())] = "1"
		}
	}
	return result
}

func GetBindThirdByArgs(c *gin.Context, filter map[string]interface{}) ([]models.UserBindThird, error) {
	db := c.MustGet("db").(*gorm.DB)
	return models.GetAllBindThird(db, filter)
}

func GetAccountByUnionID(c *gin.Context, unionId string, thirdType string) map[string][]string {
	db := c.MustGet("db").(*gorm.DB)
	unionIds := strings.Split(unionId, ",")

	var userData []struct {
		Account string `json:"account"`
		UnionID string `json:"union_id"`
	}
	userAccountMap := make(map[string][]string)
	db.Raw("select user.account, b.union_id from user right join (select * from user_bind_third where type = ? and union_id in ?) as b  on b.user_id =user.id;", thirdType, unionIds).Scan(&userData)
	for _, d := range userData {
		_, ok := userAccountMap[d.UnionID]
		if ok {
			userAccountMap[d.UnionID] = append(userAccountMap[d.UnionID], d.Account)
		} else {
			var a []string
			a = append(a, d.Account)
			userAccountMap[d.UnionID] = a
		}
	}

	return userAccountMap
}

type BindThirdDetail struct {
	OpenId     string `json:"openid"`
	SessionKey string `json:"session_key"`
	UnionId    string `json:"unionid"`
}

func GetUserBindWechatDetail(c *gin.Context, userId int) (BindThirdDetail, error) {
	var bindThirdDetail BindThirdDetail

	filter := map[string]interface{}{
		"status":  constants.BoundStatus,
		"type":    constants.Wechat,
		"user_id": userId,
	}

	userBindData, _ := GetBindThirdByArgs(c, filter)
	if len(userBindData) >= 1 {
		bindThirdInfo := userBindData[0]
		err := json.Unmarshal([]byte(bindThirdInfo.Detail), &bindThirdDetail)
		if err != nil {
			return bindThirdDetail, err
		}
		return bindThirdDetail, nil
	} else {
		return bindThirdDetail, errors.New("获取用户三方绑定detail失败")
	}
}

func GetUserBindDouyinDetail(c *gin.Context, userId int) (BindThirdDetail, error) {
	var bindThirdDetail BindThirdDetail

	filter := map[string]interface{}{
		"status":  constants.BoundStatus,
		"type":    constants.Douyin,
		"user_id": userId,
	}

	userBindData, _ := GetBindThirdByArgs(c, filter)
	if len(userBindData) >= 1 {
		bindThirdInfo := userBindData[0]
		err := json.Unmarshal([]byte(bindThirdInfo.Detail), &bindThirdDetail)
		if err != nil {
			return bindThirdDetail, err
		}
		return bindThirdDetail, nil
	} else {
		return bindThirdDetail, errors.New("获取用户三方绑定detail失败")
	}
}
