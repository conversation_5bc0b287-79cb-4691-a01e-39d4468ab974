package services

import (
	"reflect"
	"testing"

	"zeus/models"
	_ "zeus/services/settings"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

func TestUserBindThird(t *testing.T) {
	c := &gin.Context{}
	db := &gorm.DB{}
	c.Set("db", db)

	<PERSON>vey("QueryBindThird", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetOneBindThird, func(db *gorm.DB, args map[string]interface{}) (*models.UserBindThird, error) {
				return &models.UserBindThird{ID: 11}, nil
			})
			filter := map[string]interface{}{
				"id": 11,
			}
			userBindThird, _ := QueryBindThird(c, filter)

			output := userBindThird.ID
			expected := 11
			So(output, ShouldEqual, expected)
		})
	})

	<PERSON>vey("QueryAllBindThird", t, func() {
		<PERSON><PERSON>("one func for succ", func() {
			ApplyFunc(models.GetAllBindThird, func(db *gorm.DB, args map[string]interface{}) ([]models.UserBindThird, error) {
				var userBindThirds []models.UserBindThird
				userBindThirds = append(userBindThirds, models.UserBindThird{})
				return userBindThirds, nil
			})
			filter := make(map[string]interface{})

			_, output := QueryAllBindThird(c, filter)
			So(output, ShouldEqual, nil)
		})
	})

	Convey("GetUserWithThirdBindAccount", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetUserByUnionIDAndBindThirdTypeAndBindThirdStatus, func(db *gorm.DB, _ string, _ int, _ int, _ int) (models.Users, error) {
				return models.Users{}, nil
			})
			output, _ := GetUserWithThirdBindAccount(c, "", 0, 0)
			So(output, ShouldResemble, models.Users{})
		})
	})

	Convey("SaveBindThird", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.SaveBindThird, func(db *gorm.DB, args *models.UserBindThird) error {
				return nil
			})

			bindThird := &models.UserBindThird{}
			output := SaveBindThird(c, bindThird)
			So(output, ShouldEqual, nil)
		})
	})

	Convey("GetBindInfoByUserID", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.SaveBindThird, func(db *gorm.DB, args *models.UserBindThird) error {
				return nil
			})

			ApplyMethod(reflect.TypeOf(db), "Model", func(_ *gorm.DB, value interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				return
			})

			ApplyMethod(reflect.TypeOf(db), "Where", func(_ *gorm.DB, query interface{}, args ...interface{}) (tx *gorm.DB) {
				tx = &gorm.DB{}
				return
			})

			ApplyMethod(reflect.TypeOf(db), "Pluck", func(_ *gorm.DB, column string, dest interface{}) (tx *gorm.DB) {
				a := dest.(*[]int)
				*a = []int{1, 2}
				tx = &gorm.DB{}
				return
			})

			userID := "userID"
			output := GetBindInfoByUserID(c, userID)
			So(output["is_fb_bind"], ShouldEqual, "1")
		})
	})

	Convey("GetBindThirdByArgs", t, func() {
		Convey("one func for succ", func() {
			ApplyFunc(models.GetAllBindThird, func(db *gorm.DB, args map[string]interface{}) ([]models.UserBindThird, error) {
				var userBindThirds []models.UserBindThird
				userBindThirds = append(userBindThirds, models.UserBindThird{})
				return userBindThirds, nil
			})
			filter := make(map[string]interface{})

			_, output := GetBindThirdByArgs(c, filter)
			So(output, ShouldEqual, nil)
		})
	})

	Convey("GetAccountByUnionID", t, func() {
		Convey("Get no Account", func() {
			ApplyFunc(models.GetAllBindThird, func(db *gorm.DB, args map[string]interface{}) ([]models.UserBindThird, error) {
				var userBindThirds []models.UserBindThird
				userBindThirds = append(userBindThirds, models.UserBindThird{})
				return userBindThirds, nil
			})

			mockRawMethod := ApplyMethod(reflect.TypeOf(db), "Raw", func(_ *gorm.DB, _ string, _ ...interface{}) (tx *gorm.DB) {
				return db
			})
			defer mockRawMethod.Reset()

			mockScanMethod := ApplyMethod(reflect.TypeOf(db), "Scan", func(_ *gorm.DB, dest interface{}) (tx *gorm.DB) {
				return db
			})
			defer mockScanMethod.Reset()

			output := GetAccountByUnionID(c, "union1", "")
			So(output, ShouldResemble, map[string][]string{})
		})

		Convey("Get One Account", func() {
			ApplyFunc(models.GetAllBindThird, func(db *gorm.DB, args map[string]interface{}) ([]models.UserBindThird, error) {
				var userBindThirds []models.UserBindThird
				userBindThirds = append(userBindThirds, models.UserBindThird{})
				return userBindThirds, nil
			})

			mockRawMethod := ApplyMethod(reflect.TypeOf(db), "Raw", func(_ *gorm.DB, _ string, _ ...interface{}) (tx *gorm.DB) {
				return db
			})
			defer mockRawMethod.Reset()

			mockScanMethod := ApplyMethod(reflect.TypeOf(db), "Scan", func(_ *gorm.DB, dest interface{}) (tx *gorm.DB) {
				destValue := reflect.ValueOf(dest).Elem()

				newElemType := reflect.TypeOf(dest).Elem().Elem()
				newElemValue := reflect.New(newElemType).Elem()
				newElemValue.FieldByName("Account").SetString("user1")
				newElemValue.FieldByName("UnionID").SetString("union1")
				destValue.Set(reflect.Append(destValue, newElemValue))
				return db
			})
			defer mockScanMethod.Reset()

			output := GetAccountByUnionID(c, "union1", "")
			So(output, ShouldResemble, map[string][]string{"union1": {"user1"}})
		})

		Convey("Get Multiple accounts", func() {
			ApplyFunc(models.GetAllBindThird, func(db *gorm.DB, args map[string]interface{}) ([]models.UserBindThird, error) {
				var userBindThirds []models.UserBindThird
				userBindThirds = append(userBindThirds, models.UserBindThird{})
				return userBindThirds, nil
			})

			mockRawMethod := ApplyMethod(reflect.TypeOf(db), "Raw", func(_ *gorm.DB, _ string, _ ...interface{}) (tx *gorm.DB) {
				return db
			})
			defer mockRawMethod.Reset()

			mockScanMethod := ApplyMethod(reflect.TypeOf(db), "Scan", func(_ *gorm.DB, dest interface{}) (tx *gorm.DB) {
				destValue := reflect.ValueOf(dest).Elem()

				newElemType := reflect.TypeOf(dest).Elem().Elem()

				newElemValue1 := reflect.New(newElemType).Elem()
				newElemValue1.FieldByName("Account").SetString("user1")
				newElemValue1.FieldByName("UnionID").SetString("union1")
				destValue.Set(reflect.Append(destValue, newElemValue1))

				newElemValue2 := reflect.New(newElemType).Elem()
				newElemValue2.FieldByName("Account").SetString("user2")
				newElemValue2.FieldByName("UnionID").SetString("union1")
				destValue.Set(reflect.Append(destValue, newElemValue2))

				return db
			})
			defer mockScanMethod.Reset()

			output := GetAccountByUnionID(c, "union1", "")
			So(output, ShouldResemble, map[string][]string{"union1": {"user1", "user2"}})
		})
	})
}
