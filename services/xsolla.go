package services

import (
	"encoding/json"
	"time"

	"zeus/common/constants"
	"zeus/models"
	"zeus/pkg/mapper"
	"zeus/schema/domain"
	"zeus/services/game"
	"zeus/services/settings/converter"
	"zeus/services/settings/payment"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type XsollaServices struct {
	db         *gorm.DB
	ProjectID  string `json:"project_id"`
	MerchantID string `json:"merchant_id"`
}

func (xs *XsollaServices) InitXsollaServices(c *gin.Context, v interface{}) {
	mapper.Map(xs, v)
	xs.db = c.MustGet("db").(*gorm.DB)
}

func (xs *XsollaServices) FindRole(roleID string) (*models.Role, error) {
	gameIds, _ := game.GetGameIdsByXsollaProjectID(xs.ProjectID)
	return models.GetRoleWithGameId(xs.db, roleID, gameIds)
}

func (xs *XsollaServices) IsExistsOrder(OrderID string) bool {
	_, err := models.GetOrderByOrderID(xs.db, OrderID)
	return err == nil
}

func (xs *XsollaServices) CreatOrder(v *domain.XsollaItem) (*models.Order, error) {
	var order models.Order
	role, err := xs.FindRole(v.RoleID)
	if err != nil {
		return nil, err
	}

	user, err := models.GetUserByID(xs.db, role.UserID)
	if err != nil {
		return nil, err
	}

	var payConfig payment.XsollaPay
	converter.QueryByGameId(&payConfig, role.GameID)

	order.GameId = role.GameID
	order.Level = role.Level
	order.RoleName = role.RoleName
	order.RoleId = role.RoleId
	order.ServerId = role.ServerId
	order.ServerName = role.ServerName
	order.UserId = role.UserID
	order.Platform = role.Platform
	order.Vip = role.Vip
	order.PayNotifyUrl = *payConfig.PayNotifyUrl

	order.Account = user.Account

	order.PayWay = constants.PayWayXsolla
	order.PayStatus = constants.PaymentSuccess
	order.CreateTime = time.Now()

	order.OrderId = v.OrderID
	order.PayCurrency = v.Currency
	order.PayId = v.PayID
	order.PayAmount = v.Price

	// 购买商品的ID
	order.ProductId = v.ProductID
	order.PayProductId = v.ProductID

	// TODO 后面确任extend值
	var extend Extend
	extend.OrderId = order.OrderId
	extend.RoleId = order.RoleId
	extend.ServerId = order.ServerId
	extend.Account = order.Account
	extend.ProductId = order.ProductId
	extend.PayWay = v.PayWay
	data, _ := json.Marshal(extend)
	order.Extend = string(data)

	err = models.CreateOrder(xs.db, &order)
	return &order, err
}

type Extend struct {
	OrderId   string `json:"order_id"`
	ProductId string `json:"product_id"`
	RoleId    string `json:"role_id"`
	ServerId  string `json:"server_id"`
	Account   string `json:"account"`
	PayWay    string `json:"pay_way"`
}
