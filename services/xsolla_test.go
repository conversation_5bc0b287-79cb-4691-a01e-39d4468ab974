package services

import (
	"errors"
	"testing"

	"zeus/models"
	"zeus/schema/domain"
	"zeus/services/settings/converter"
	schemas "zeus/services/settings/field"
	"zeus/services/settings/payment"

	. "github.com/agiledragon/gomonkey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
)

type TestXsollaValue struct {
	ProjectID  string `json:"project_id"`
	MerchantID string `json:"merchant_id"`
}

func TestXsollaServices(t *testing.T) {
	Convey("TestXsollaServices", t, func() {
		db := new(gorm.DB)
		c := gin.Context{}
		c.Set("db", db)
		os := new(XsollaServices)
		testValue := TestXsollaValue{
			ProjectID:  "1234",
			MerchantID: "2222",
		}
		os.InitXsollaServices(&c, &testValue)

		Convey("CreatOrder", func() {
			ApplyFunc(models.GetRoleWithGameId, func(_ *gorm.DB, _ string, _ []int) (*models.Role, error) {
				return &models.Role{
					RoleId: "117",
				}, nil
			})

			ApplyFunc(models.GetUserByID, func(db *gorm.DB, id int) (*models.User, error) {
				return &models.User{}, nil
			})

			ApplyFunc(models.CreateOrder, func(_ *gorm.DB, _ *models.Order) error {
				return errors.New("222")
			})
			ApplyFunc(converter.QueryByGameId, func(config schemas.BaseConfiger, _ int) {
				switch config.GetType() {
				case "Payment":
					paymentConfig := config.(*payment.Payment)
					isRechargeOpen := 0
					paymentConfig.IsRechargeOpen = &isRechargeOpen
					dingTalkToken := ""
					paymentConfig.DingTalkToken = &dingTalkToken
					payNotifyKey := ""
					paymentConfig.PayNotifyKey = &payNotifyKey
				case "PaymentXsolla":
					xsollaPayConfig := config.(*payment.XsollaPay)
					payNotifyURL := ""
					xsollaPayConfig.PayNotifyUrl = &payNotifyURL
				}
			})

			v := domain.XsollaItem{
				RoleID: "117",
			}
			order, _ := os.CreatOrder(&v)
			output := order.RoleId
			outputExpect := "117"
			So(output, ShouldEqual, outputExpect)
		})

		Convey("CreatOrder with not exists role", func() {
			ApplyFunc(models.GetRole, func(_ *gorm.DB, _ string) (*models.Role, error) {
				return nil, errors.New("Role not found")
			})

			v := domain.XsollaItem{
				RoleID: "117",
			}
			_, err := os.CreatOrder(&v)
			So(err, ShouldNotBeNil)
		})

		Convey("CreatOrder with not exists user", func() {
			ApplyFunc(models.GetRole, func(_ *gorm.DB, _ string) (*models.Role, error) {
				return &models.Role{
					RoleId: "117",
				}, nil
			})

			ApplyFunc(models.GetUserByID, func(db *gorm.DB, id int) (*models.User, error) {
				return nil, errors.New("User not found")
			})

			v := domain.XsollaItem{
				RoleID: "117",
			}
			_, err := os.CreatOrder(&v)
			So(err, ShouldNotBeNil)
		})

		Convey("IsExistsOrder", func() {
			ApplyFunc(models.GetOrderByOrderID, func(db *gorm.DB, OrderID string) (*models.Order, error) {
				return &models.Order{}, nil
			})
			OrderID := "111222"
			output := os.IsExistsOrder(OrderID)
			outputExpect := true
			So(output, ShouldEqual, outputExpect)
		})
	})
}
