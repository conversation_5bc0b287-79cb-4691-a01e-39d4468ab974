---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zeus
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: zeus
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: zeus
    spec:
      containers:
        - name: zeus
          image: #IMAGE_NAME
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: app-logs
              mountPath: /data/logs
            - name: config
              mountPath: /app/config/
          ports:
            - containerPort: 8000
              protocol: TCP
          env:
            - name: aliyun_logs_zeus-master-stdout
              value: stdout
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: registry-secret-aliyun
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: timezone
        - name: app-logs
          emptyDir: {}
        - name: config
          configMap:
            name: config
            items:
              - key: config.yaml
                path: config.yaml
---
apiVersion: v1
kind: Service
metadata:
  name: zeus
spec:
  selector:
    app: zeus
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 8000

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    ingress.kubernetes.io/proxy-body-size: "0"
    ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/server-snippet: |
      gzip on;
      gzip_types application/json;
      gzip_min_length 256;
  generation: 5
  name: ingress
spec:
  tls:
    - hosts:
        - zeus.youle.game
      secretName: https-secret-youle-game
  rules:
    - host: zeus.youle.game
      http:
        paths:
          - backend:
              serviceName: zeus
              servicePort: 8000
            path: /
---
kind: Secret
apiVersion: v1
metadata:
  name: registry-secret-aliyun
data:
  .dockerconfigjson: >-
    ****************************************************************************************************************************************************************************************************************************************
type: kubernetes.io/dockerconfigjson
