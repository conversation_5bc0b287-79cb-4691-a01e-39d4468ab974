---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zeus-gray
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: zeus-gray
  template:
    metadata:
      labels:
        app: zeus-gray
    spec:
      containers:
        - name: zeus-gray
          image: #IMAGE_NAME
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: app-logs
              mountPath: /data/logs
            - name: config
              mountPath: /app/config/
          ports:
            - containerPort: 8000
              protocol: TCP
          resources:
            limits:
              cpu: 1000m
              memory: 2Gi
            requests:
              cpu: 500m
              memory: 1Gi
          env:
            - name: aliyun_logs_zeus-gray-golang-online-stdout
              value: stdout
            - name: aliyun_logs_zeus-gray-golang-online-logfile
              value: /app/logs/*.log.*
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: registry-secret-aliyun
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: timezone
        - name: app-logs
          emptyDir: {}
        - name: config
          configMap:
            name: zeus-gray
            items:
              - key: config.yaml
                path: config.yaml
---
apiVersion: v1
kind: Service
metadata:
  name: zeus-gray
  annotations:
    prometheus.io/path: /metrics
    prometheus.io/port: "8000"
    prometheus.io/scrape: "true"
spec:
  selector:
    app: zeus-gray
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 8000

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-gray
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-gray
  template:
    metadata:
      labels:
        app: redis-gray
    spec:
      containers:
        - name: redis-gray
          image: redis:latest
          command:
            - redis-server
            - --requirepass "password"
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: 100m
              memory: 100Mi
          ports:
            - containerPort: 6379
              protocol: TCP
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: registry-secret-aliyun
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: redis-gray
spec:
  selector:
    app: redis-gray
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
