---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    ingress.kubernetes.io/proxy-body-size: "0"
    ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/server-snippet: |
      gzip on;
      gzip_types application/json;
      gzip_min_length 256;
    nginx.ingress.kubernetes.io/service-weight: |
      zeus: 90, zeus-gray: 10
  name: abroad.topjoy.com
spec:
  rules:
    - host: abroad.topjoy.com
      http:
        paths:
          - backend:
              service:
                name: zeus
                port:
                  number: 8000
            path: /
            pathType: ImplementationSpecific
          - backend:
              service:
                name: zeus-gray
                port:
                  number: 8000
            path: /
            pathType: ImplementationSpecific
  tls:
    - hosts:
        - abroad.topjoy.com
      secretName: https-secret-topjoy-com
