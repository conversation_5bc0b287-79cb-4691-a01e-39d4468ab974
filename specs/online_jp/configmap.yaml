---
kind: ConfigMap
apiVersion: v1
metadata:
  name: config
data:
  config.yaml: |
    debug: false
    db:
      DriverName: mysql
      Database: sdk
      Port: 3306
      UserName: sdk
      Password: eXyaLPDaFPpTtVer8hiRaRrs
      Host: rm-6we632698ety3sv9b.mysql.japan.rds.aliyuncs.com
      Charset: utf8
    redis:
      Host: r-6wescbq1yfy6dqnaeb.redis.japan.rds.aliyuncs.com:6379
      Database: 0
      Password: lldxguo&lqJ35Bke
      MaxIdle: 60
      MaxActive: 60
      IdleTimeout: 50
    Ucenter:
      APIKey: 9380100115
      APISecret: 314635b93085f16cd9ba05c18abdc9ee
    ThirdPaySecret: 5b2f4b08b9502634de6156f72371ebbb
    RunCronJob: false
    AntiAddiction:
      FestivalTime: [
        "2023-12-30", "2023-12-31", "2024-01-01", "2024-02-10", "2024-02-11", "2024-02-12",
        "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-17", "2024-04-04",
        "2024-04-05", "2024-04-06", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-04",
        "2024-05-05", "2024-06-08", "2024-06-09", "2024-06-10", "2024-09-15", "2024-09-16",
        "2024-09-17", "2024-10-01", "2024-10-02","2024-10-03","2024-10-04","2024-10-05",
        "2024-10-06", "2024-10-07"
      ]
      Exception: [
        "2024-02-04", "2024-02-18",
        "2024-04-07", "2024-04-28",
        "2024-05-11", "2024-09-14",
        "2024-09-29", "2024-10-12"
      ]
      StartTime: "20:00:00"
      EndTime: "21:00:00"
    AntispamInfo:
      ApiURL: "http://as-text-tokyo.dun.163.com/v5/text/check"
    TracerInfo:
      ServiceName: Zeus_jp
      Endpoint: tracing-analysis-dc-jp-internal.aliyuncs.com:8090
      Authentication: hqpxy7gzqa@3f92cb3279c1819_hqpxy7gzqa@53df7ad2afe8301
    GormConfig:
      SlowThreshold: 100
    EmailService:
      User: "<EMAIL>"
      Sender: "TopJoy Global Games"
      Password: "V946EypyqAGcLaQH"
      Host: "smtpdm.aliyun.com:465"
      Templates:
        zh:
          default:
            Subject: "賬號驗證碼"
            Body: "<html><body><p>您的驗證碼為：%s，有效期30分鐘</p></body></html>"
          cn:
            Subject: "账号验证码"
            Body: "<html><body><p>您的验证码为：%s，有效期30分钟</p></body></html>"
        ko:
          default:
            Subject: "계정 인증코드"
            Body: "<html><body><p>인증코드: %s, 유효기간 30분</p></body></html>"
        en:
          default:
            Subject: "Account Verification Code"
            Body: "<html><body><p>Your verification code is %s, valid for 30 minutes.</p></body></html>"
    SMS:
      AccessKey: "LTAI4GFUTSDHecpkYd9S6ciA"
      SecretKey: "******************************"
      Areas:
        - AreaCode: "+86"
          AreaName: "中国大陆"
          TemplateCode: "SMS_463131154"
          SignName: "TopJoyGames"
        - AreaCode: "+886"
          AreaName: "台湾"
          TemplateCode: "SMS_463226002"
          SignName: "TopJoyGames"
        - AreaCode: "+852"
          AreaName: "香港"
          TemplateCode: "SMS_463226002"
          SignName: "TopJoyGames"
        - AreaCode: "+853"
          AreaName: "澳门"
          TemplateCode: "SMS_463226002"
          SignName: "TopJoyGames"
        - AreaCode: "+82"
          AreaName: "韩国"
          TemplateCode: "SMS_463141222"
          SignName: "TopJoyGames"
    AilYunIdentifyCheckAppCode: "678730d8833843e6963568ff891eb2ab"
    StaticTarget:
      AcceleratedDomain: ""
      EndPoint: "oss-cn-beijing.aliyuncs.com"
      AccessKeyID: "LTAIYTGpMvySDV3Q"
      AccessKeySecret: "******************************"
      BucketName: "tsd-test-topjoy"
    PayLimit:
      - Region: "JP"
        Information:
          TextTitle: "支払い限度アラート"
          TextContent: "プレイヤー様、現地の法律に基づき、チャージ前に年齢範囲を選択してください"
          LimitContent:
            - ["16歳未満", "一ヶ月に5000円まで"]
            - ["16歳以上18歳未満", "一ヶ月に20000円まで"]
            - ["18歳以上", "制限なし"]
