---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zeus-api
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: zeus-api
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: zeus-api
    spec:
      containers:
        - name: zeus-api
          image: #IMAGE_NAME
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: app-logs
              mountPath: /data/logs
            - name: config
              mountPath: /app/config/
          ports:
            - containerPort: 8000
              protocol: TCP
          resources:
            limits:
              cpu: 1000m
              memory: 2Gi
            requests:
              cpu: 500m
              memory: 1Gi
          env:
            - name: aliyun_logs_zeus-api-golang-online-stdout
              value: stdout
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: registry-secret-aliyun
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: timezone
        - name: app-logs
          emptyDir: {}
        - name: config
          configMap:
            name: config
            items:
              - key: config.yaml
                path: config.yaml
---
apiVersion: v1
kind: Service
metadata:
  name: zeus-api
  annotations:
    prometheus.io/path: /metrics
    prometheus.io/port: "8000"
    prometheus.io/scrape: "true"
spec:
  selector:
    app: zeus-api
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 8000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    ingress.kubernetes.io/proxy-body-size: "0"
    ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/server-snippet: |
      gzip on;
      gzip_types application/json;
      gzip_min_length 256;
  name: zeus-api.topjoy.com
spec:
  rules:
    - host: zeus-api.topjoy.com
      http:
        paths:
          - backend:
              service:
                name: zeus-api
                port:
                  number: 8000
            path: /
            pathType: ImplementationSpecific
  tls:
    - hosts:
        - zeus-api.topjoy.com
      secretName: https-secret-topjoy-com
