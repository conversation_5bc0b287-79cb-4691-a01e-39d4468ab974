---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zeus-prerelease
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: zeus-prerelease
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: zeus-prerelease
    spec:
      containers:
        - name: zeus-prerelease
          image: #IMAGE_NAME
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: app-logs
              mountPath: /data/logs
            - name: config-prerelease
              mountPath: /app/config/
          ports:
            - containerPort: 8000
              protocol: TCP
          env:
            - name: aliyun_logs_zeus-prerelease-stdout
              value: stdout
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: registry-secret-aliyun
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: timezone
        - name: app-logs
          emptyDir: {}
        - name: config-prerelease
          configMap:
            name: config-prerelease
            items:
              - key: config.yaml
                path: config.yaml
---
apiVersion: v1
kind: Service
metadata:
  name: zeus-prerelease
spec:
  selector:
    app: zeus-prerelease
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 8000

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    ingress.kubernetes.io/proxy-body-size: "0"
    ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/server-snippet: |
      gzip on;
      gzip_types application/json;
      gzip_min_length 256;
  generation: 5
  name: zeus-prerelease
spec:
  tls:
    - hosts:
        - zeus-prerelease.youle.game
      secretName: https-secret-youle-game
  rules:
    - host: zeus-prerelease.youle.game
      http:
        paths:
          - backend:
              serviceName: zeus-prerelease
              servicePort: 8000
            path: /
---
kind: Secret
apiVersion: v1
metadata:
  name: registry-secret-aliyun
data:
  .dockerconfigjson: >-
    ****************************************************************************************************************************************************************************************************************************************
type: kubernetes.io/dockerconfigjson
