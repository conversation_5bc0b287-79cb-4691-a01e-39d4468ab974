---
kind: ConfigMap
apiVersion: v1
metadata:
  name: zeus-press-client
data:
  locustfile.py: |
    import os
    from locust import FastHttpUser, task, between
    import base64
    import copy
    import hashlib
    import json

    APPID = os.environ.get("APP_ID")
    SECRET_KEY = os.environ.get("SECRET_KEY")


    def sign(body):
        sort_keys = sorted(body.keys())
        body_string = ""
        for key in sort_keys:
            body_string += f"{key}{body[key]}"

        body_string += SECRET_KEY
        return hashlib.md5(body_string.encode("utf-8")).hexdigest()


    def format_body(body):
        data = copy.copy(body)
        if not data.get("appid"):
            data.update({"appid": APPID})
        data.update({"sign": sign(data)})
        body_base64 = base64.b64encode(json.dumps(data).encode("ascii"))
        return body_base64


    class QuickstartUser(FastHttpUser):
        @task(weight=1)
        def get_game_info(self):
            self.client.post("/game/info", data=format_body({}))

        @task(weight=0)
        def get_third_list(self):
            self.client.post("/user/third-list", data=format_body({}))

        @task(weight=0)
        def create_google_order(self):
            path = "/order/google-exchange"
            data = {
                "device": "test_device",
                "extend": "",
                "lang": "1",
                "level": "5",
                "pay_notify_url": "test",
                "platform": "android",
                "price": "99",
                "product_id": "com.topjoy.sdk_demo.pay100",
                "role_id": "68712_1111",
                "role_name": "locust",
                "sdk_version": "2.8.4-SNAPSHOT",
                "server_id": "1",
                "server_name": "serverName",
                "user_id": "10001",
                "vip": "1"
            }
            self.client.post(path, data=format_body(data))

        @task(weight=0)
        def test_google_verify(self):
            """[google支付确认]，已完成订单重复确认"""
            path = "/v2/order/google-verify"
            data = {
                "lang": "1",
                "packageName": "com.topjoy.sdk_demo",
                "pay_amount": "1.49",
                "pay_currency": "USD",
                "platform": "android",
                "productId": "com.topjoy.sdk_demo.pay100",
                "purchaseToken": "eakpihfilaobljpcanbgnaaj.AO-J1Ox1D1ony1pbbcf_i9Dd1lji9IL_q1lMgf3McOr1kRSB2DBs8QwCAKDyXCVEQ5sOhbY1TLSVQcjZDnEXjcgGzoURiXdSwA",
                "purchase_data": "{\"orderId\":\"GPA.3330-5464-9384-40093\",\"packageName\":\"com.topjoy.sdk_demo\",\"productId\":\"com.topjoy.sdk_demo.pay100\",\"purchaseTime\":*************,\"purchaseState\":0,\"purchaseToken\":\"eakpihfilaobljpcanbgnaaj.AO-J1Ox1D1ony1pbbcf_i9Dd1lji9IL_q1lMgf3McOr1kRSB2DBs8QwCAKDyXCVEQ5sOhbY1TLSVQcjZDnEXjcgGzoURiXdSwA\",\"obfuscatedAccountId\":\"a_20230809173454_qkBydI\",\"quantity\":1,\"acknowledged\":false,\"developerPayload\":\"a_20230809173454_qkBydI\"}",
                "purchase_sign": "UTQUfVLKwDKuOLcCiBVrjd5cI0Zk7o0g5kiLZx7VkMp5mtrApHm8f6EtrNLNjmZ1aLeLGsFZzCm+dPobW9wvdbf9+FzxDONR+tSPeBDGS\/jrACkUI4j\/JDD9f0MB8HZpx3kEpZGoCZS0froSQTYh7aYnsrOHGpfinQI\/rYePQkr1467rUEDsKrlNMZFU1ILklv0RsV6ScVlq2JMq+4wALO16OzB5lW88x9xk1YVd59ZDXTIM67\/7svA39vSXGKy\/QcZmExBRcAbc\/idf+CiZ4z\/7MrmT3jM3jtsIVBmzm4+f3vi5GPrpD0VRsyiLEo1ZB8\/MBoeOYdldV6mLeeSUXA==",
                "developerPayload": "a_20230809173454_qkBydI",
                "sdk_version": "2.8.0",
            }
            self.client.post(path, data=format_body(data))
