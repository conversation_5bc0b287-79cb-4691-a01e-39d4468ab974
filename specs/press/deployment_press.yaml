---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zeus-press
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: zeus-press
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: zeus-press
    spec:
      containers:
        - name: zeus-press
          image: registry.ap-northeast-1.aliyuncs.com/topjoy-janpan/zeus:press_test
          imagePullPolicy: Always
          volumeMounts:
            - name: config
              mountPath: /app/config/
          ports:
            - containerPort: 8000
              protocol: TCP
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: registry-secret-aliyun
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: timezone
        - name: config
          configMap:
            name: zeus-press
            items:
              - key: config.yaml
                path: config.yaml
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: redis
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:latest
          command:
            - redis-server
            - --requirepass "password"
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: 100m
              memory: 100Mi
          ports:
            - containerPort: 6379
              protocol: TCP
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: registry-secret-aliyun
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: redis
spec:
  selector:
    app: redis
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
---
apiVersion: v1
kind: Service
metadata:
  name: zeus-press
  annotations:
    prometheus.io/path: /metrics
    prometheus.io/port: "8000"
    prometheus.io/scrape: "true"
spec:
  selector:
    app: zeus-press
  ports:
    - protocol: TCP
      port: 8000

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    ingress.kubernetes.io/proxy-body-size: "0"
    ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  name: zeus-press.topjoy.com
spec:
  rules:
    - host: zeus-press.topjoy.com
      http:
        paths:
          - backend:
              service:
                name: zeus-press
                port:
                  number: 8000
            path: /
            pathType: ImplementationSpecific
  tls:
    - hosts:
        - zeus-press.topjoy.com
      secretName: https-secret-topjoy-com
---
kind: Secret
apiVersion: v1
metadata:
  name: registry-secret-aliyun
data:
  .dockerconfigjson: >-
    ************************************************************************************************************************************************************************************************************************************************************
type: kubernetes.io/dockerconfigjson
