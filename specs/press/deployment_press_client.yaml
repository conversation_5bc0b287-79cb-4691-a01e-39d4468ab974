---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: locust
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: locust
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: locust
    spec:
      containers:
        - name: locust
          image: locustio/locust:latest
          imagePullPolicy: Always
          volumeMounts:
            - name: press-files
              mountPath: /home/<USER>/
          ports:
            - containerPort: 8089
              protocol: TCP
          resources:
            limits:
              cpu: 1000m
              memory: 1Gi
          env:
            - name: APP_ID
              value: 7AVS2D5QH2TV
            - name: SECRET_KEY
              value: DF864TCE1XWZE1NH
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - hostPath:
            path: /etc/localtime
            type: ""
          name: timezone
        - name: press-files
          configMap:
            name: zeus-press-client
            items:
              - key: locustfile.py
                path: locustfile.py
---
apiVersion: v1
kind: Service
metadata:
  name: locust
spec:
  selector:
    app: locust
  ports:
    - protocol: TCP
      port: 8089
      targetPort: 8089
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    ingress.kubernetes.io/proxy-body-size: "0"
    ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  name: zeus-press-client.topjoy.com
spec:
  rules:
    - host: zeus-press-client.topjoy.com
      http:
        paths:
          - backend:
              service:
                name: locust
                port:
                  number: 8089
            path: /
            pathType: ImplementationSpecific
  tls:
    - hosts:
        - zeus-press-client.topjoy.com
      secretName: https-secret-topjoy-com
