#!/bin/bash

# Zeus API Swagger 文档启动脚本

set -e

echo "🚀 Zeus API Swagger 文档服务启动脚本"
echo "=================================="

# 检查必要文件
check_files() {
    echo "📋 检查必要文件..."
    
    if [ ! -f "docs/swagger.yaml" ]; then
        echo "❌ 错误: docs/swagger.yaml 文件不存在"
        echo "请先生成 Swagger 文档或确保文件存在"
        exit 1
    fi
    
    if [ ! -f "swagger-ui.html" ]; then
        echo "❌ 错误: swagger-ui.html 文件不存在"
        exit 1
    fi
    
    echo "✅ 文件检查完成"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  警告: 端口 $port 已被占用"
        return 1
    fi
    return 0
}

# 启动方式选择
show_menu() {
    echo ""
    echo "请选择启动方式："
    echo "1) 使用 Docker Compose (推荐)"
    echo "2) 使用 Python HTTP 服务器"
    echo "3) 使用 Node.js HTTP 服务器"
    echo "4) 仅显示访问信息"
    echo "5) 退出"
    echo ""
    read -p "请输入选择 (1-5): " choice
}

# Docker Compose 启动
start_docker() {
    echo "🐳 使用 Docker Compose 启动..."
    
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ 错误: docker-compose 未安装"
        echo "请安装 Docker 和 Docker Compose"
        return 1
    fi
    
    echo "启动 Swagger UI 服务..."
    docker-compose -f docker-compose.swagger.yml up -d
    
    echo ""
    echo "✅ 服务启动成功！"
    echo "📖 Swagger UI: http://localhost:8080"
    echo "✏️  Swagger Editor: http://localhost:8081"
    echo "📁 文件服务器: http://localhost:8082"
    echo ""
    echo "停止服务: docker-compose -f docker-compose.swagger.yml down"
}

# Python HTTP 服务器启动
start_python() {
    echo "🐍 使用 Python HTTP 服务器启动..."
    
    if ! command -v python3 &> /dev/null; then
        echo "❌ 错误: Python 3 未安装"
        return 1
    fi
    
    if ! check_port 8080; then
        echo "请先停止占用端口 8080 的服务"
        return 1
    fi
    
    echo "启动服务在 http://localhost:8080"
    echo "按 Ctrl+C 停止服务"
    echo ""
    python3 -m http.server 8080
}

# Node.js HTTP 服务器启动
start_node() {
    echo "📦 使用 Node.js HTTP 服务器启动..."
    
    if ! command -v npx &> /dev/null; then
        echo "❌ 错误: Node.js/npm 未安装"
        return 1
    fi
    
    if ! check_port 8080; then
        echo "请先停止占用端口 8080 的服务"
        return 1
    fi
    
    echo "启动服务在 http://localhost:8080"
    echo "按 Ctrl+C 停止服务"
    echo ""
    npx http-server -p 8080 -c-1
}

# 显示访问信息
show_info() {
    echo "📋 Zeus API Swagger 文档访问信息"
    echo "================================"
    echo ""
    echo "📖 本地文件访问:"
    echo "   file://$(pwd)/swagger-ui.html"
    echo ""
    echo "🌐 如果 Zeus API 服务正在运行:"
    echo "   http://localhost:8000/swagger/index.html"
    echo ""
    echo "📁 文档文件位置:"
    echo "   - Swagger YAML: docs/swagger.yaml"
    echo "   - Swagger JSON: docs/swagger.json"
    echo "   - HTML UI: swagger-ui.html"
    echo ""
    echo "🔧 生成新文档:"
    echo "   make swagger-gen"
    echo "   或"
    echo "   swag init"
}

# 主函数
main() {
    check_files
    
    while true; do
        show_menu
        
        case $choice in
            1)
                start_docker
                break
                ;;
            2)
                start_python
                break
                ;;
            3)
                start_node
                break
                ;;
            4)
                show_info
                ;;
            5)
                echo "👋 再见！"
                exit 0
                ;;
            *)
                echo "❌ 无效选择，请重新输入"
                ;;
        esac
    done
}

# 运行主函数
main
