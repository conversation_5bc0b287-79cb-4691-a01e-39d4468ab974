#!/bin/bash

# Zeus API 测试脚本

set -e

# 配置
BASE_URL="http://localhost:8000"
CONTENT_TYPE="Content-Type: application/json"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查服务是否运行
check_service() {
    print_header "检查 Zeus API 服务状态"
    
    if curl -s "$BASE_URL/healthz" > /dev/null 2>&1; then
        print_success "Zeus API 服务正在运行"
        return 0
    else
        print_error "Zeus API 服务未运行"
        print_info "请先启动服务: go run main.go"
        return 1
    fi
}

# 测试健康检查接口
test_health() {
    print_header "测试健康检查接口"
    
    response=$(curl -s "$BASE_URL/healthz")
    if echo "$response" | grep -q "ok"; then
        print_success "健康检查接口正常"
        echo "响应: $response"
    else
        print_error "健康检查接口异常"
        echo "响应: $response"
    fi
}

# 测试Swagger文档
test_swagger() {
    print_header "测试 Swagger 文档"
    
    # 测试 Swagger UI
    if curl -s "$BASE_URL/swagger/index.html" | grep -q "swagger" > /dev/null 2>&1; then
        print_success "Swagger UI 可访问"
        print_info "访问地址: $BASE_URL/swagger/index.html"
    else
        print_warning "Swagger UI 可能不可访问"
    fi
    
    # 测试 Swagger JSON
    if curl -s "$BASE_URL/swagger/doc.json" > /dev/null 2>&1; then
        print_success "Swagger JSON 可访问"
    else
        print_warning "Swagger JSON 可能不可访问"
    fi
}

# 测试游戏信息接口
test_game_info() {
    print_header "测试游戏信息接口"
    
    # 示例请求数据
    data='{
        "appid": "test_app_id",
        "sdk_version": "1.0.0"
    }'
    
    print_info "发送请求到 /game/info"
    response=$(curl -s -X POST "$BASE_URL/game/info" \
        -H "$CONTENT_TYPE" \
        -d "$data" || echo "请求失败")
    
    if echo "$response" | grep -q "error_no" > /dev/null 2>&1; then
        print_success "游戏信息接口响应正常"
        echo "响应: $response"
    else
        print_warning "游戏信息接口可能需要配置"
        echo "响应: $response"
    fi
}

# 测试用户注册接口
test_user_register() {
    print_header "测试用户注册接口"
    
    # 示例请求数据
    data='{
        "device": "test_device",
        "platform": "test_platform"
    }'
    
    print_info "发送请求到 /user/register"
    response=$(curl -s -X POST "$BASE_URL/user/register" \
        -H "$CONTENT_TYPE" \
        -d "$data" || echo "请求失败")
    
    if echo "$response" | grep -q "error_no" > /dev/null 2>&1; then
        print_success "用户注册接口响应正常"
        echo "响应: $response"
    else
        print_warning "用户注册接口可能需要配置"
        echo "响应: $response"
    fi
}

# 显示API端点列表
show_endpoints() {
    print_header "主要 API 端点"
    
    echo "🎮 游戏相关:"
    echo "  POST $BASE_URL/game/info"
    echo "  POST $BASE_URL/role/save"
    echo ""
    
    echo "👤 用户相关:"
    echo "  POST $BASE_URL/user/register"
    echo "  POST $BASE_URL/user/login"
    echo "  POST $BASE_URL/user/third-login"
    echo ""
    
    echo "💰 支付相关:"
    echo "  POST $BASE_URL/order/ali-exchange"
    echo "  POST $BASE_URL/order/wechat-exchange"
    echo "  POST $BASE_URL/order/google-exchange"
    echo ""
    
    echo "🛡️ 防沉迷:"
    echo "  POST $BASE_URL/anti-addiction/identity-check"
    echo "  POST $BASE_URL/anti-addiction/pay-limit"
    echo ""
    
    echo "🔧 管理后台:"
    echo "  GET  $BASE_URL/api/v1/login"
    echo "  GET  $BASE_URL/api/v1/game"
    echo "  GET  $BASE_URL/api/v1/orders"
    echo ""
    
    echo "📚 文档:"
    echo "  GET  $BASE_URL/swagger/index.html"
    echo "  GET  $BASE_URL/healthz"
}

# 主菜单
show_menu() {
    echo ""
    echo "Zeus API 测试工具"
    echo "=================="
    echo "1) 检查服务状态"
    echo "2) 测试健康检查"
    echo "3) 测试 Swagger 文档"
    echo "4) 测试游戏信息接口"
    echo "5) 测试用户注册接口"
    echo "6) 显示 API 端点"
    echo "7) 运行所有测试"
    echo "8) 退出"
    echo ""
    read -p "请选择操作 (1-8): " choice
}

# 运行所有测试
run_all_tests() {
    print_header "运行所有测试"
    
    check_service || return 1
    test_health
    test_swagger
    test_game_info
    test_user_register
    
    print_header "测试完成"
    print_info "如需查看详细API文档，请访问: $BASE_URL/swagger/index.html"
}

# 主函数
main() {
    while true; do
        show_menu
        
        case $choice in
            1)
                check_service
                ;;
            2)
                test_health
                ;;
            3)
                test_swagger
                ;;
            4)
                test_game_info
                ;;
            5)
                test_user_register
                ;;
            6)
                show_endpoints
                ;;
            7)
                run_all_tests
                ;;
            8)
                print_info "再见！"
                exit 0
                ;;
            *)
                print_error "无效选择，请重新输入"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

# 如果直接运行脚本，执行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main
fi
