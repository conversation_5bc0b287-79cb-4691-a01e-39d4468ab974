package utils

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func GetClientIP(c *gin.Context) string {
	var ip string
	if c.ClientIP() != "" {
		ip = c.ClientIP()
	} else if c.RemoteIP() != "" {
		ip = c.RemoteIP()
	}
	ips := strings.SplitAfter(ip, ",")
	if len(ips) > 0 {
		for _, v := range ips {
			matched, _ := regexp.MatchString(`^(10|172|192\.168)\.`, v)
			if matched == false {
				ip = v
				break
			}
		}
	}

	return ip
}

func MD5(v string) string {
	d := []byte(v)
	m := md5.New()
	m.Write(d)
	return hex.EncodeToString(m.Sum(nil))
}

func JsonToMap(jsonStr string) (map[string]interface{}, error) {
	m := make(map[string]interface{})
	if len(jsonStr) == 0 {
		return nil, errors.New("json 数据为空")
	}

	decoder := json.NewDecoder(strings.NewReader(jsonStr))
	decoder.UseNumber()
	err := decoder.Decode(&m)
	if err != nil {
		fmt.Println("error:", err)
	}

	for k, v := range m {
		fmt.Printf("%v: %v\n", k, v)
	}

	return m, nil
}

func InArray(target string, str_array []string) bool {
	sort.Strings(str_array)
	index := sort.SearchStrings(str_array, target)
	if index < len(str_array) && str_array[index] == target {
		return true
	}
	return false
}

func GenerateRandomStr(number int) string {
	letters := []rune("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ")
	rand.Seed(time.Now().UnixNano())

	b := make([]rune, number)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

func ConvertStringToInt(s string) int {
	i, err := strconv.Atoi(s)
	if err != nil {
		return 0
	}
	return i
}
